from typing import Any, Iterable, Iterator

from ..sql_db import Cursor

class Graph(dict[str, Node]):
    def add_node(self, name: str, info: dict[str, Any]) -> Node: ...
    def update_from_db(self, cr: Cursor) -> None: ...
    def add_module(self, cr: Cursor, module: str, force: list | None = ...) -> None: ...
    def add_modules(
        self, cr: Cursor, module_list: list[str], force: list | None = ...
    ): ...
    def __iter__(self) -> Iterator[Node]: ...

class Node:
    def __new__(cls, name: str, graph: Graph, info: dict[str, Any]) -> Node: ...
    id: int
    name: str
    graph: Graph
    info: dict[str, Any]
    children: list[Node]
    depth: int
    dbdemo: bool
    state: str
    installed_version: str
    def __init__(
        self, name: str, graph: Graph, info: dict[str, Any] | None
    ) -> None: ...
    @property
    def data(self) -> dict[str, Any]: ...
    def add_child(self, name: str, info: dict[str, Any]): ...
    def __setattr__(self, name: str, value) -> None: ...
    def __iter__(self) -> Iterator[Node]: ...
    def should_have_demo(self) -> bool: ...
    @property
    def parents(self) -> Iterable[Node]: ...
