from re import Pattern
from types import ModuleType
from typing import Any, Literal

from odoo.addons.base.models.ir_module import Module

MANIFEST_NAMES: tuple[str, ...]
README: list[str]
TYPED_FIELD_DEFINITION_RE: Pattern

class UpgradeHook:
    def find_spec(
        self, fullname: str, path: Any | None = ..., target: Any | None = ...
    ): ...
    def load_module(self, name: str) -> ModuleType | None: ...

def initialize_sys_path() -> None: ...
def get_module_path(
    module: str, downloaded: bool = ..., display_warning: bool = ...
) -> str | Literal[False]: ...
def get_resource_path(module: str, *args) -> str | Literal[False]: ...

get_module_resource = get_resource_path
check_resource_path = get_resource_path

def get_resource_from_path(path: str) -> tuple[str, str, str] | None: ...
def get_module_icon(module: str) -> str: ...
def get_module_icon_path(module: Module) -> str: ...
def module_manifest(path: str) -> str | None: ...
def get_module_root(path: str) -> str | None: ...
def load_manifest(module: str, mod_path: str | None = ...) -> dict[str, Any]: ...
def get_manifest(module: str, mod_path: str | None = ...) -> dict[str, Any]: ...
def load_openerp_module(module_name: str) -> None: ...
def get_modules() -> list[str]: ...
def get_modules_with_version() -> dict[str, Any]: ...
def adapt_version(version: str) -> str: ...

current_test: Any

def check_python_external_dependency(pydep) -> None: ...
def check_manifest_dependencies(manifest) -> None: ...
