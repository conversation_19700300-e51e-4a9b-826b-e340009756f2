from typing import Any, Callable

from ..models import MAGIC_COLUMNS as <PERSON><PERSON><PERSON>_COLUMNS
from ..models import BaseModel
from ..sql_db import Cursor
from ..tools import SQL, Query

NOT_OPERATOR: str
OR_OPERATOR: str
AND_OPERATOR: str
DOMAIN_OPERATORS: tuple[str, ...]
TERM_OPERATORS: tuple[str, ...]
NEGATIVE_TERM_OPERATORS: tuple[str, ...]
DOMAIN_OPERATORS_NEGATION: dict[str, str]
TERM_OPERATORS_NEGATION: dict[str, str]
WILDCARD_OPERATORS: tuple[str, ...]
ANY_IN: dict[str, str]
TRUE_LEAF: tuple
FALSE_LEAF: tuple
TRUE_DOMAIN: list[tuple]
FALSE_DOMAIN: list[tuple]
SQL_OPERATORS: dict[str, SQL]

def normalize_domain(domain: list) -> list: ...
def is_false(model, domain: list) -> bool: ...
def combine(operator: str, unit, zero, domains: list[list]) -> list: ...
def AND(domains: list[list]) -> list: ...
def OR(domains: list[list]) -> list: ...
def distribute_not(domain: list) -> list: ...
def domain_combine_anies(domain: list, model: BaseModel) -> list: ...
def prettify_domain(domain: list, pre_indent: int = ...) -> str: ...
def normalize_leaf(element): ...
def is_operator(element) -> bool: ...
def is_leaf(element) -> bool: ...
def is_boolean(element) -> bool: ...
def check_leaf(element) -> None: ...
def get_unaccent_wrapper(cr: Cursor) -> Callable[[Any], str]: ...

class expression:
    root_model: BaseModel
    root_alias: str | None
    expression: list
    query: Query | None
    result: tuple[str, list]
    def __init__(
        self,
        domain: list,
        model: BaseModel,
        alias: str | None = ...,
        query: Query | None = ...,
    ) -> None: ...
    def parse(self): ...
