from functools import wraps as wraps
from typing import IO, Any, Callable, Iterable, Literal, TypeVar

from ..sql_db import Cursor
from ..tools import SQL

_CallableT = TypeVar("_CallableT", bound=Callable)

class DatabaseExists(Warning): ...

def database_identifier(cr: Cursor, name: str) -> SQL: ...
def check_db_management_enabled(method: _CallableT) -> _CallableT: ...
def check_super(passwd: str) -> Literal[True]: ...
def exp_create_database(
    db_name: str,
    demo: bool,
    lang: str,
    user_password: str = ...,
    login: str = ...,
    country_code: str | None = ...,
    phone: str | None = ...,
) -> Literal[True]: ...
def exp_duplicate_database(
    db_original_name: str, db_name: str, neutralize_database: bool = ...
) -> Literal[True]: ...
def exp_drop(db_name: str) -> bool: ...
def exp_dump(db_name: str, format: str) -> str: ...
def dump_db_manifest(cr: Cursor) -> dict[str, Any]: ...
def dump_db(db_name: str, stream, backup_format: str = ...) -> IO | None: ...
def exp_restore(db_name: str, data, copy: bool = ...) -> Literal[True]: ...
def restore_db(
    db: str, dump_file: str, copy: bool = ..., neutralize_database: bool = ...
) -> None: ...
def exp_rename(old_name: str, new_name: str) -> Literal[True]: ...
def exp_change_admin_password(new_password: str) -> Literal[True]: ...
def exp_migrate_databases(databases: Iterable[str]) -> Literal[True]: ...
def exp_db_exist(db_name: str) -> bool: ...
def list_dbs(force: bool = ...) -> list[str]: ...
def list_db_incompatible(databases: Iterable[str]) -> list[str]: ...
def exp_list(document: bool = ...) -> list[str]: ...
def exp_list_lang() -> list[tuple[str, str]]: ...
def exp_list_countries() -> list[tuple[str, str]]: ...
def exp_server_version() -> str: ...
def dispatch(method: str, params): ...
