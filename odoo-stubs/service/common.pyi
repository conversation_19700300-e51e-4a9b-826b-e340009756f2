from logging import Logger
from typing import Any, Literal

RPC_VERSION_1: dict[str, Any]

def exp_login(db: str, login: str, password: str) -> int: ...
def exp_authenticate(
    db: str, login: str, password: str, user_agent_env: dict | None
) -> int: ...
def exp_version() -> dict[str, Any]: ...
def exp_about(extended: bool = ...) -> str | tuple[str, str]: ...
def exp_set_loglevel(loglevel, logger: Logger | None = ...) -> Literal[True]: ...
def dispatch(method: str, params): ...
