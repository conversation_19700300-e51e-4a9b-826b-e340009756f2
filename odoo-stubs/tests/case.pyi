from collections.abc import Generator
from unittest import TestCase as _TestCase

class TestCase(_TestCase):
    __unittest_skip__: bool
    __unittest_skip_why__: str
    def __init__(self, methodName: str = ...) -> None: ...
    def addCleanup(self, function, *args, **kwargs) -> None: ...
    @classmethod
    def addClassCleanup(cls, function, *args, **kwargs) -> None: ...
    def shortDescription(self) -> None: ...
    def subTest(self, msg=..., **params) -> Generator[None, None, None]: ...
    def run(self, result): ...
    def doCleanups(self) -> None: ...
    @classmethod
    def doClassCleanups(cls) -> None: ...
