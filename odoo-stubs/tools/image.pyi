from typing import Any, Iterable, Literal

from PIL import IcoImagePlugin as IcoImagePlugin
from PIL import ImageOps as ImageOps
from PIL.Image import Image

FILETYPE_BASE64_MAGICWORD: dict[bytes, str]
EXIF_TAG_ORIENTATION: int
EXIF_TAG_ORIENTATION_TO_TRANSPOSE_METHODS: dict[int, list]
IMAGE_MAX_RESOLUTION: float

class ImageProcess:
    source: bytes
    operationsCount: int
    image: Image | Literal[False]
    original_format: str
    def __init__(self, source: bytes, verify_resolution: bool = ...) -> None: ...
    def image_quality(
        self, quality: int = ..., output_format: str = ...
    ) -> bytes | Literal[False]: ...
    def resize(
        self, max_width: int = ..., max_height: int = ..., expand: bool = ...
    ) -> ImageProcess: ...
    def crop_resize(
        self,
        max_width: int,
        max_height: int,
        center_x: float = ...,
        center_y: float = ...,
    ) -> ImageProcess: ...
    def colorize(self, color: tuple | None = ...) -> ImageProcess: ...
    def add_padding(self, padding: int) -> ImageProcess: ...

def image_process(
    source: bytes,
    size: tuple[int, int] = ...,
    verify_resolution: bool = ...,
    quality: int = ...,
    expand: bool = ...,
    crop: str | None = ...,
    colorize: bool = ...,
    output_format: str = ...,
) -> bytes: ...
def average_dominant_color(
    colors: list[tuple[Any, Any]], mitigate: int = ..., max_margin: int = ...
) -> tuple[Any, Any]: ...
def image_fix_orientation(image: Image) -> Image: ...
def binary_to_image(source: bytes) -> Image: ...
def base64_to_image(base64_source: str | bytes) -> Image: ...
def image_apply_opt(image: Image, output_format: str, **params) -> bytes: ...
def image_to_base64(image: Image, output_format: str, **params) -> bytes: ...
def get_webp_size(source: bytes) -> tuple[float, float] | None: ...
def is_image_size_above(base64_source_1: bytes, base64_source_2: bytes) -> bool: ...
def image_guess_size_from_field_name(field_name: str) -> tuple[int, int]: ...
def image_data_uri(base64_source: bytes) -> str: ...
def get_saturation(rgb: Iterable[int]) -> float: ...
def get_lightness(rgb: Iterable[int]) -> float: ...
def hex_to_rgb(hx: str) -> tuple[int, int, int]: ...
def rgb_to_hex(rgb: Iterable[int]) -> str: ...
