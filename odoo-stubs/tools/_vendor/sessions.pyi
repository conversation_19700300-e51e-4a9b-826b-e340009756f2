from typing import Any, Generic, TypeVar

from werkzeug.datastructures import CallbackDict

_ModificationTrackingDictT = TypeVar(
    "_ModificationTrackingDictT", bound=ModificationTrackingDict
)
_SessionT = TypeVar("_SessionT", bound=Session)

def generate_key(salt: Any = ...) -> str: ...

class ModificationTrackingDict(CallbackDict):
    modified: bool
    def __init__(self, *args, **kwargs): ...
    def copy(self: _ModificationTrackingDictT) -> _ModificationTrackingDictT: ...
    def __copy__(self: _ModificationTrackingDictT) -> _ModificationTrackingDictT: ...

class Session(ModificationTrackingDict):
    sid: str
    new: bool
    def __init__(self, data, sid, new: bool = ...): ...
    @property
    def should_save(self) -> bool: ...

class SessionStore(Generic[_SessionT]):
    session_class: type[_SessionT]
    def __init__(self, session_class: type[_SessionT] | None = ...): ...
    def is_valid_key(self, key) -> bool: ...
    def generate_key(self, salt: Any = ...) -> str: ...
    def new(self) -> _SessionT: ...
    def save(self, session: _SessionT) -> None: ...
    def save_if_modified(self, session: _SessionT) -> None: ...
    def delete(self, session: _SessionT) -> None: ...
    def get(self, sid: str) -> _SessionT: ...

class FilesystemSessionStore(SessionStore[_SessionT]):
    path: str | None
    filename_template: str
    renew_missing: bool
    mode: int
    def __init__(
        self,
        path: str | None = ...,
        filename_template: str = ...,
        session_class: type[_SessionT] | None = ...,
        renew_missing: bool = ...,
        mode: int = ...,
    ) -> None: ...
    def get_session_filename(self, sid: str) -> str: ...
    def save(self, session: _SessionT) -> None: ...
    def delete(self, session: _SessionT) -> None: ...
    def get(self, sid: str) -> _SessionT: ...
    def list(self) -> list[str]: ...
