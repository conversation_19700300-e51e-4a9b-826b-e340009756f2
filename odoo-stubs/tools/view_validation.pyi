from ast import expr as _expr
from re import Pattern
from typing import Callable, TypeVar

from lxml.etree import RelaxNG, _Element

_CallableT = TypeVar("_CallableT", bound=Callable)

READONLY: Pattern

def get_domain_value_names(domain) -> tuple[set[str], set[str]]: ...
def get_expression_field_names(expression) -> set[str]: ...
def get_dict_asts(expr: str | _expr) -> dict: ...
def valid_view(arch: _Element, **kwargs) -> bool: ...
def validate(*view_types: str) -> Callable[[_CallableT], _CallableT]: ...
def relaxng(view_type: str) -> RelaxNG: ...
def schema_valid(arch: _Element, **kwargs) -> bool: ...
