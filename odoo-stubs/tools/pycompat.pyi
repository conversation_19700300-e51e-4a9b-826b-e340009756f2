from csv import Dialect
from typing import BinaryIO, Iterable, Iterator

class _CsvReader(Iterator[list[str]]):
    dialect: Dialect
    line_num: int
    def __next__(self) -> list[str]: ...

class _CsvWriter:
    dialect: Dialect
    def writerow(self, row: Iterable): ...
    def writerows(self, rows: Iterable[Iterable]) -> None: ...

def csv_reader(stream: BinaryIO, **params) -> _CsvReader: ...
def csv_writer(stream: BinaryIO, **params) -> _CsvWriter: ...
def to_text(source) -> str: ...
