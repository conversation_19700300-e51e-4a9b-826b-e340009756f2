from abc import ABC, abstractmethod
from collections.abc import Iterable
from typing import Any, Collection, Literal

class SetDefinitions:
    def __init__(self, definitions: dict[int, dict]) -> None: ...
    @property
    def empty(self) -> SetExpression: ...
    @property
    def universe(self) -> SetExpression: ...
    def parse(self, refs: str, raise_if_not_found: bool = ...) -> SetExpression: ...
    def from_ids(
        self, ids: Iterable[int], keep_subsets: bool = ...
    ) -> SetExpression: ...
    def from_key(self, key: str) -> SetExpression: ...
    def get_id(self, ref: LeafIdType) -> LeafIdType | None: ...

class SetExpression(ABC):
    @abstractmethod
    def is_empty(self) -> bool: ...
    @abstractmethod
    def is_universal(self) -> bool: ...
    @abstractmethod
    def invert_intersect(self, factor: SetExpression) -> SetExpression | None: ...
    @abstractmethod
    def matches(self, user_group_ids: Iterable[int]) -> bool: ...
    @property
    @abstractmethod
    def key(self) -> str: ...
    @abstractmethod
    def __and__(self, other: SetExpression) -> SetExpression: ...
    @abstractmethod
    def __or__(self, other: SetExpression) -> SetExpression: ...
    @abstractmethod
    def __invert__(self) -> SetExpression: ...
    @abstractmethod
    def __eq__(self, other) -> bool: ...
    @abstractmethod
    def __le__(self, other: SetExpression) -> bool: ...
    @abstractmethod
    def __lt__(self, other: SetExpression) -> bool: ...
    @abstractmethod
    def __hash__(self): ...

class Union(SetExpression):
    def __init__(self, inters: Iterable[Inter] = ..., optimal: bool = ...) -> None: ...
    @property
    def key(self) -> str: ...
    def is_empty(self) -> bool: ...
    def is_universal(self) -> bool: ...
    def invert_intersect(self, factor: SetExpression) -> Union | None: ...
    def __and__(self, other: SetExpression) -> Union: ...
    def __or__(self, other: SetExpression) -> Union: ...
    def __invert__(self) -> Union: ...
    def matches(self, user_group_ids) -> bool: ...
    def __bool__(self) -> bool: ...
    def __eq__(self, other) -> bool: ...
    def __le__(self, other: SetExpression) -> bool: ...
    def __lt__(self, other: SetExpression) -> bool: ...
    def __str__(self) -> str: ...
    def __repr__(self) -> str: ...
    def __hash__(self): ...

class Inter:
    leaves: list
    key: tuple
    def __init__(self, leaves: Iterable[Leaf] = ..., optimal: bool = ...) -> None: ...
    def is_empty(self) -> bool: ...
    def is_universal(self) -> bool: ...
    def matches(self, user_group_ids) -> bool: ...
    def __and__(self, other: Inter) -> Inter: ...
    def __eq__(self, other) -> bool: ...
    def __le__(self, other: Inter) -> bool: ...
    def __lt__(self, other: Inter) -> bool: ...
    def __hash__(self): ...

class Leaf:
    id: LeafIdType
    ref: str | int
    negative: bool
    key: tuple[LeafIdType, bool]
    subsets: set[LeafIdType]
    supersets: set[LeafIdType]
    disjoints: set[LeafIdType]
    inverse: Leaf | None
    def __init__(
        self, leaf_id: LeafIdType, ref: str | int | None = ..., negative: bool = ...
    ) -> None: ...
    def __invert__(self) -> Leaf: ...
    def is_empty(self) -> bool: ...
    def is_universal(self) -> bool: ...
    def isdisjoint(self, other: Leaf) -> bool: ...
    def matches(self, user_group_ids: Collection[int]) -> bool: ...
    def __eq__(self, other) -> bool: ...
    def __le__(self, other: Leaf) -> bool: ...
    def __lt__(self, other: Leaf) -> bool: ...
    def __hash__(self): ...

class UnknownId(str):
    def __lt__(self, other) -> bool: ...
    def __gt__(self, other) -> bool: ...

LeafIdType: int | Literal["*"] | UnknownId
UNIVERSAL_LEAF: Leaf
EMPTY_LEAF: Leaf
EMPTY_INTER: Inter
UNIVERSAL_INTER: Inter
EMPTY_UNION: Union
UNIVERSAL_UNION: Union
