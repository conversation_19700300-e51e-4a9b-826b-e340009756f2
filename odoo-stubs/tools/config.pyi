from optparse import Option, OptionParser
from typing import Any

from passlib.context import CryptContext

crypt_context: CryptContext

class MyOption(Option):
    my_default: Any
    def __init__(self, *opts, **attrs) -> None: ...

DEFAULT_LOG_HANDLER: str

class configmanager:
    options: dict[str, Any]
    blacklist_for_save: set[str]
    casts: dict
    misc: dict
    config_file: str
    parser: OptionParser
    def __init__(self, fname: str | None = ...) -> None: ...
    def parse_config(
        self, args: list[str] | None = ..., *, setup_logging: bool | None = ...
    ) -> None: ...
    def load(self) -> None: ...
    def save(self, keys: Any | None = ...) -> None: ...
    def get(self, key, default: Any | None = ...): ...
    def pop(self, key, default: Any | None = ...): ...
    def get_misc(self, sect, key, default: Any | None = ...): ...
    def __setitem__(self, key, value) -> None: ...
    def __getitem__(self, key): ...
    @property
    def addons_data_dir(self) -> str: ...
    @property
    def session_dir(self) -> str: ...
    def filestore(self, dbname: str) -> str: ...
    def set_admin_password(self, new_password) -> None: ...
    def verify_admin_password(self, password) -> bool: ...

config: configmanager
