import datetime

from ..api import Environment
from ..fields import Field, Many2one
from ..models import Model
from . import SQL

MIN_DATETIME: datetime.datetime
MAX_DATETIME: datetime.datetime

def get_field_variation_date(
    model: Model, field: Field, factor: int, series_alias: str
) -> SQL: ...
def get_field_variation_char(field: Field, postfix: str | SQL | None = ...) -> SQL: ...

class PopulateContext:
    has_session_replication_role: bool
    def __init__(self) -> None: ...
    def ignore_indexes(self, model: Model): ...
    def ignore_fkey_constraints(self, model: Model): ...

def field_needs_variation(model: Model, field: Field) -> bool: ...
def get_field_variation(
    model: Model, field: Field, factor: int, series_alias: str
) -> SQL: ...
def fetch_last_id(model: Model) -> int: ...
def populate_field(
    model: Model,
    field: Field,
    populated: dict[Model, int],
    factors: dict[Model, int],
    table_alias: str = ...,
    series_alias: str = ...,
) -> SQL | None: ...
def populate_model(
    model: Model,
    populated: dict[Model, int],
    factors: dict[Model, int],
    separator_code: str,
) -> None: ...

class Many2oneFieldWrapper(Many2one):
    def __init__(self, model, field_name, comodel_name) -> None: ...

class Many2manyModelWrapper:
    env: Environment
    def __init__(self, env, field) -> None: ...
    def __repr__(self) -> str: ...
    def __eq__(self, other): ...
    def __hash__(self): ...

def infer_many2many_model(
    env: Environment, field: Field
) -> Model | Many2manyModelWrapper: ...
def populate_models(model_factors: dict[Model, int], separator_code: int) -> None: ...
