import datetime
from typing import Itera<PERSON>, Li<PERSON>, <PERSON><PERSON>, TypeVar

import babel
from dateutil.relativedelta import relativedelta

_DateTimeT = TypeVar("_DateTimeT", datetime.date, datetime.datetime)

def date_type(value: _DateTimeT) -> type[_DateTimeT]: ...
def get_month(date: _DateTimeT) -> <PERSON><PERSON>[_DateTimeT, _DateTimeT]: ...
def get_quarter_number(date: _DateTimeT) -> int: ...
def get_quarter(date: _DateTimeT) -> <PERSON><PERSON>[_DateTimeT, _DateTimeT]: ...
def get_fiscal_year(
    date: _DateTimeT, day: int = ..., month: int = ...
) -> <PERSON><PERSON>[_DateTimeT, _DateTimeT]: ...
def get_timedelta(
    qty: int, granularity: Literal["hour", "day", "week", "month", "year"]
) -> relativedelta: ...

Granularity = Literal["year", "quarter", "month", "week", "day", "hour"]

def start_of(value: _DateTimeT, granularity: Granularity) -> _DateTimeT: ...
def end_of(value: _DateTimeT, granularity: Granularity) -> _DateTimeT: ...
def add(value: _DateTimeT, *args, **kwargs) -> _DateTimeT: ...
def subtract(value: _DateTimeT, *args, **kwargs) -> _DateTimeT: ...
def json_default(obj) -> str: ...
def date_range(
    start: datetime.datetime, end: datetime.datetime, step: relativedelta = ...
) -> Iterator[datetime.datetime]: ...
def weeknumber(locale: babel.Locale, date: datetime.date) -> Tuple[int, int]: ...
