<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_attribute_line_1" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_17"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_1'), ref('product_attribute_value_2')])]"/>
    </record>
    <record id="product_template_attribute_line_10" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_16"/>
        <field name="attribute_id" ref="product_attribute_3"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_8'), ref('product_attribute_value_9')])]"/>
    </record>
    <record id="product_template_attribute_line_11" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_23"/>
        <field name="attribute_id" ref="product_attribute_3"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_9')])]"/>
    </record>
    <record id="product_template_attribute_line_12" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_9"/>
        <field name="attribute_id" ref="product_attribute_3"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_8'), ref('product_attribute_value_9')])]"/>
    </record>
    <record id="product_template_attribute_line_13" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_27"/>
        <field name="attribute_id" ref="product_attribute_3"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_8'), ref('product_attribute_value_9')])]"/>
    </record>
    <record id="product_template_attribute_line_14" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_20"/>
        <field name="attribute_id" ref="product_attribute_3"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_9')])]"/>
    </record>
    <record id="product_template_attribute_line_2" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_16"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_4'), ref('product_attribute_value_pink')])]"/>
    </record>
    <record id="product_template_attribute_line_4" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_20"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_yellow'), ref('product_attribute_value_5')])]"/>
    </record>
    <record id="product_template_attribute_line_5" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_22"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_5'), ref('product_attribute_value_4')])]"/>
    </record>
    <record id="product_template_attribute_line_6" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_14"/>
        <field name="attribute_id" ref="product_attribute_3"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_8'), ref('product_attribute_value_9')])]"/>
    </record>
    <record id="product_template_attribute_line_7" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_23"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_light_blue'), ref('product_attribute_value_dark_pink')])]"/>
    </record>
    <record id="product_template_attribute_line_8" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_35"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_2'), ref('product_attribute_value_dark_pink')])]"/>
    </record>
    <record id="product_template_attribute_line_9" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_55"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_dark_blue'), ref('product_attribute_value_light_blue')])]"/>
    </record>
    <record id="product_template_attribute_line_100" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_14"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_pink')])]"/>
    </record>
    <record id="product_template_attribute_line_101" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_9"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_light_blue')])]"/>
    </record>
    <record id="product_template_attribute_line_102" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_8"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_pink')])]"/>
    </record>
    <record id="product_template_attribute_line_103" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_10"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_black')])]"/>
    </record>
    <record id="product_template_attribute_line_104" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_11"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_black')])]"/>
    </record>
    <record id="product_template_attribute_line_105" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_15"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_dark_blue')])]"/>
    </record>
    <record id="product_template_attribute_line_106" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_12"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_blue')])]"/>
    </record>
    <record id="product_template_attribute_line_107" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_21"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_blue')])]"/>
    </record>
    <record id="product_template_attribute_line_108" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_53"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_dark_blue')])]"/>
    </record>
    <record id="product_template_attribute_line_109" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_54"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_blue')])]"/>
    </record>
    <record id="product_template_attribute_line_110" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="product_tmpl_id" ref="product_template_24"/>
        <field name="attribute_id" ref="product_attribute_2"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_yellow')])]"/>
    </record>
</odoo>
