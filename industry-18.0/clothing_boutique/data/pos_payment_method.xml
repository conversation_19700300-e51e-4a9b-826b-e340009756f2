<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="cash" model="account.journal">
        <field name="name">Cash (Clothing boutique)</field>
        <field name="type">cash</field>
    </record>
    <record id="pos_payment_method_1" model="pos.payment.method">
        <field name="name">Cash</field>
        <field name="journal_id" ref="cash"/>
    </record>
    <record id="pos_payment_method_2" model="pos.payment.method">
        <field name="name">Customer Account</field>
        <field name="split_transactions">True</field>
    </record>
</odoo>
