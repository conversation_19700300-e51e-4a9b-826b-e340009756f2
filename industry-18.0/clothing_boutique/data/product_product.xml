<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_10" model="product.product">
        <field name="product_tmpl_id" ref="product_template_10"/>
        <field name="standard_price">15.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_103')])]"/>
    </record>
    <record id="product_product_11" model="product.product">
        <field name="product_tmpl_id" ref="product_template_11"/>
        <field name="standard_price">15.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_104')])]"/>
    </record>
    <record id="product_product_12" model="product.product">
        <field name="product_tmpl_id" ref="product_template_12"/>
        <field name="standard_price">10.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_106')])]"/>
    </record>
    <record id="product_product_13" model="product.product">
        <field name="product_tmpl_id" ref="product_template_13"/>
        <field name="standard_price">20.0</field>
        
    </record>
    <record id="product_product_15" model="product.product">
        <field name="product_tmpl_id" ref="product_template_15"/>
        <field name="standard_price">20.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_105')])]"/>
    </record>
    <record id="product_product_18" model="product.product">
        <field name="product_tmpl_id" ref="product_template_17"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_1')])]"/>
        <field name="standard_price">30.0</field>
    </record>
    <record id="product_product_19" model="product.product">
        <field name="product_tmpl_id" ref="product_template_17"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_2')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/19-image_variant_1024"/>
    </record>
    <record id="product_product_22" model="product.product">
        <field name="product_tmpl_id" ref="product_template_18"/>
        <field name="standard_price">25.0</field>
    </record>
    <record id="product_product_27" model="product.product">
        <field name="product_tmpl_id" ref="product_template_21"/>
        <field name="standard_price">10.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_107')])]"/>
    </record>
    <record id="product_product_29" model="product.product">
        <field name="product_tmpl_id" ref="product_template_22"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_8')])]"/>
        <field name="standard_price">45.0</field>
    </record>
    <record id="product_product_30" model="product.product">
        <field name="product_tmpl_id" ref="product_template_22"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_9')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/30-image_variant_1024"/>
        <field name="standard_price">38.0</field>
    </record>
    <record id="product_product_31" model="product.product">
        <field name="product_tmpl_id" ref="product_template_14"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_10'), ref('product_template_attribute_value_100')])]"/>
        <field name="standard_price">20.0</field>
    </record>
    <record id="product_product_32" model="product.product">
        <field name="product_tmpl_id" ref="product_template_14"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_11'), ref('product_template_attribute_value_100')])]"/>
        <field name="standard_price">15.0</field>
    </record>
    <record id="product_product_34" model="product.product">
        <field name="product_tmpl_id" ref="product_template_23"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_14'), ref('product_template_attribute_value_21')])]"/>
    </record>
    <record id="product_product_35" model="product.product">
        <field name="product_tmpl_id" ref="product_template_23"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_13'), ref('product_template_attribute_value_21')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/35-image_variant_1024"/>
    </record>
    <record id="product_product_36" model="product.product">
        <field name="product_tmpl_id" ref="product_template_24"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_110')])]"/>
        
    </record>
    <record id="product_product_37" model="product.product">
        <field name="product_tmpl_id" ref="product_template_25"/>
    </record>
    <record id="product_product_38" model="product.product">
        <field name="product_tmpl_id" ref="product_template_26"/>
    </record>
    <record id="product_product_40" model="product.product">
        <field name="product_tmpl_id" ref="product_template_28"/>
        <field name="standard_price">50.0</field>
    </record>
    <record id="product_product_41" model="product.product">
        <field name="product_tmpl_id" ref="product_template_29"/>
        <field name="standard_price">45.0</field>
    </record>
    <record id="product_product_42" model="product.product">
        <field name="product_tmpl_id" ref="product_template_30"/>
    </record>
    <record id="product_product_43" model="product.product">
        <field name="product_tmpl_id" ref="product_template_31"/>
    </record>
    <record id="product_product_44" model="product.product">
        <field name="product_tmpl_id" ref="product_template_32"/>
        <field name="standard_price">57.0</field>
    </record>
    <record id="product_product_45" model="product.product">
        <field name="product_tmpl_id" ref="product_template_33"/>
        <field name="standard_price">10.0</field>
    </record>
    <record id="product_product_46" model="product.product">
        <field name="product_tmpl_id" ref="product_template_34"/>
    </record>
    <record id="product_product_47" model="product.product">
        <field name="product_tmpl_id" ref="product_template_35"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_15')])]"/>
    </record>
    <record id="product_product_48" model="product.product">
        <field name="product_tmpl_id" ref="product_template_35"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_16')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/48-image_variant_1024"/>
    </record>
    <record id="product_product_63" model="product.product">
        <field name="product_tmpl_id" ref="product_template_50"/>
    </record>
    <record id="product_product_65" model="product.product">
        <field name="product_tmpl_id" ref="product_template_52"/>
    </record>
    <record id="product_product_66" model="product.product">
        <field name="product_tmpl_id" ref="product_template_53"/>
        <field name="standard_price">10.0</field>
        <field name="barcode">HIGHJEAN001</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_108')])]"/>
    </record>
    <record id="product_product_67" model="product.product">
        <field name="product_tmpl_id" ref="product_template_54"/>
        <field name="standard_price">18.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_109')])]"/>
    </record>
    <record id="product_product_68" model="product.product">
        <field name="product_tmpl_id" ref="product_template_55"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_17')])]"/>
        <field name="standard_price">6.0</field>
    </record>
    <record id="product_product_69" model="product.product">
        <field name="product_tmpl_id" ref="product_template_55"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_18')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/69-image_variant_1024"/>
        <field name="standard_price">5.0</field>
    </record>
    <record id="product_product_70" model="product.product">
        <field name="product_tmpl_id" ref="product_template_16"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_3'), ref('product_template_attribute_value_19')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/70-image_variant_1024"/>
        <field name="standard_price">5.0</field>
    </record>
    <record id="product_product_71" model="product.product">
        <field name="product_tmpl_id" ref="product_template_16"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_3'), ref('product_template_attribute_value_20')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/71-image_variant_1024"/>
    </record>
    <record id="product_product_72" model="product.product">
        <field name="product_tmpl_id" ref="product_template_16"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_4'), ref('product_template_attribute_value_19')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/72-image_variant_1024"/>
    </record>
    <record id="product_product_73" model="product.product">
        <field name="product_tmpl_id" ref="product_template_16"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_4'), ref('product_template_attribute_value_20')])]"/>
        <field name="image_variant_1024" type="base64" file="clothing_boutique/static/src/binary/product_product/73-image_variant_1024"/>
    </record>
    <record id="product_product_74" model="product.product">
        <field name="product_tmpl_id" ref="product_template_9"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_23'), ref('product_template_attribute_value_101')])]"/>
        <field name="standard_price">15.0</field>
    </record>
    <record id="product_product_75" model="product.product">
        <field name="product_tmpl_id" ref="product_template_56"/>
    </record>
    <record id="product_product_76" model="product.product">
        <field name="product_tmpl_id" ref="product_template_57"/>
    </record>
    <record id="product_product_78" model="product.product">
        <field name="product_tmpl_id" ref="product_template_20"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_24'), ref('product_template_attribute_value_27')])]"/>
        <field name="standard_price">19.0</field>
    </record>
    <record id="product_product_79" model="product.product">
        <field name="product_tmpl_id" ref="product_template_27"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_25')])]"/>
        <field name="standard_price">36.0</field>
    </record>
    <record id="product_product_8" model="product.product">
        <field name="product_tmpl_id" ref="product_template_8"/>
        <field name="standard_price">5.0</field>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_102')])]"/>
    </record>
    <record id="product_product_80" model="product.product">
        <field name="product_tmpl_id" ref="product_template_27"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_26')])]"/>
        <field name="standard_price">45.0</field>
    </record>
    <record id="product_product_81" model="product.product">
        <field name="product_tmpl_id" ref="product_template_59"/>
    </record>
    <record id="product_product_82" model="product.product">
        <field name="product_tmpl_id" ref="product_template_60"/>
    </record>
    <record id="product_product_9" model="product.product">
        <field name="product_tmpl_id" ref="product_template_9"/>
        <field name="product_template_attribute_value_ids" eval="[(6, 0, [ref('product_template_attribute_value_22'), ref('product_template_attribute_value_101')])]"/>
        <field name="standard_price">10.0</field>
    </record>
</odoo>
