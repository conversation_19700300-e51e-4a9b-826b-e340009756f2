<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_attribute_value_2" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_1"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_2"/>
        <field name="price_extra">40.0</field>
    </record>
    <record id="product_template_attribute_value_1" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_1"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_1"/>
        <field name="price_extra">50.0</field>
    </record>
    <record id="product_template_attribute_value_19" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_10"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_8"/>
        <field name="price_extra">45.0</field>
    </record>
    <record id="product_template_attribute_value_20" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_10"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_9"/>
        <field name="price_extra">57.0</field>
    </record>
    <record id="product_template_attribute_value_21" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_11"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_9"/>
    </record>
    <record id="product_template_attribute_value_22" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_12"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_8"/>
        <field name="price_extra">30.0</field>
    </record>
    <record id="product_template_attribute_value_23" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_12"/>
        <field name="price_extra">40.0</field>
        <field name="product_attribute_value_id" ref="product_attribute_value_9"/>
    </record>
    <record id="product_template_attribute_value_25" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_13"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_8"/>
        <field name="price_extra">5.0</field>
    </record>
    <record id="product_template_attribute_value_26" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_13"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_9"/>
        <field name="price_extra">10.0</field>
    </record>
    <record id="product_template_attribute_value_27" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_14"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_9"/>
    </record>
    <record id="product_template_attribute_value_3" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_2"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_4"/>
        <field name="price_extra">60.0</field>
    </record>
    <record id="product_template_attribute_value_4" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_2"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_pink"/>
        <field name="price_extra">50.0</field>
    </record>
    <record id="product_template_attribute_value_24" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_4"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_5"/>
        <field name="price_extra">4.0</field>
    </record>
    <record id="product_template_attribute_value_6" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_4"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_yellow"/>
    </record>
    <record id="product_template_attribute_value_9" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_5"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_5"/>
        <field name="price_extra">30.0</field>
    </record>
    <record id="product_template_attribute_value_8" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_5"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_4"/>
        <field name="price_extra">35.0</field>
    </record>
    <record id="product_template_attribute_value_10" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_6"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_8"/>
        <field name="price_extra">45.0</field>
    </record>
    <record id="product_template_attribute_value_11" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_6"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_9"/>
        <field name="price_extra">50.0</field>
    </record>
    <record id="product_template_attribute_value_13" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_7"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_dark_pink"/>
    </record>
    <record id="product_template_attribute_value_14" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_7"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_light_blue"/>
    </record>
    <record id="product_template_attribute_value_15" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_8"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_2"/>
    </record>
    <record id="product_template_attribute_value_16" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_8"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_dark_pink"/>
    </record>
    <record id="product_template_attribute_value_17" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_9"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_light_blue"/>
        <field name="price_extra">10.0</field>
    </record>
    <record id="product_template_attribute_value_18" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_9"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_dark_blue"/>
        <field name="price_extra">15.0</field>
    </record>
    <record id="product_template_attribute_value_100" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_100"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_pink" />
    </record>
    <record id="product_template_attribute_value_101" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_101"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_light_blue" />
    </record>
    <record id="product_template_attribute_value_102" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_102"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_pink" />
    </record>
    <record id="product_template_attribute_value_103" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_103"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_black" />
    </record>
    <record id="product_template_attribute_value_104" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_104"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_black" />
    </record>
    <record id="product_template_attribute_value_105" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_105"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_dark_blue" />
    </record>
    <record id="product_template_attribute_value_106" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_106"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_blue" />
    </record>
    <record id="product_template_attribute_value_107" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_107"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_blue" />
    </record>
    <record id="product_template_attribute_value_108" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_108"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_dark_blue" />
    </record>
    <record id="product_template_attribute_value_109" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_109"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_blue" />
    </record>
    <record id="product_template_attribute_value_110" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_110"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_yellow" />
    </record>
</odoo>
