<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="welcome_article_body">
      <h1>
          <span style="font-size: 36px;">The Glam Boutique</span>
      </h1>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">This setup is for Clothing and Boutique retail shops which encompasses the creation, distribution, and retail of clothing and fashion accessories to a specific segment of people. </p>
      <div data-oe-protected="true" class="o_knowledge_behavior_anchor o_knowledge_behavior_type_toc">
          <div class="o_knowledge_toc_content">
              <a href="#" data-oe-nodeid="0" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">The Glam Boutique</a>
              <a href="#" data-oe-nodeid="1" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Business Flows:</a>
              <a href="#" data-oe-nodeid="2" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 1: Purchase </a>
              <a href="#" data-oe-nodeid="3" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 2: POS with Barcode and BOGO offer.</a>
              <a href="#" data-oe-nodeid="4" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 3: POS → Loyalty points</a>
              <a href="#" data-oe-nodeid="5" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 4 : Website → Seasonal discount → Monsoon Discount offer.</a>
          </div>
      </div>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <h1 style="margin-bottom: 0px;">Business Flows:</h1>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <h2 style="margin-bottom: 0px;">Flow 1: Purchase </h2>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <ul style="margin-bottom: 0px;">
          <li>Through Purchase Application <font class="text-o-color-5">create an RFQ</font> (Request for quotation) with Product “White Flared Dress”</li>
          <li>Send RFQ to your Pre defined Vendor.</li>
          <li>Confirm the RFQ based on the received rates by the vendor.</li>
      </ul>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <h2 style="margin-bottom: 0px;">Flow 2: POS with Barcode and BOGO offer.</h2>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <u>Products with Barcode</u>
      </p>
      <ul style="margin-bottom: 0px;">
          <li>Add the Barcode number in the Product Master and take a print to scan them during the POS session.</li>
          <li>You can also search the Barcode number of that product from the POS session to recognise that product.</li>
      </ul>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <blockquote data-o-mail-quote="1" data-o-mail-quote-node="1" style="margin-bottom: 0px;">
          BOGO Offer (Buy one Get one Free) <br />
          Promotions are popular among retailers because they can increase sales of the products being promoted. Plus, it will attract attention to other items in the store, which is great when you need to move dead stock or boost sales.
          <br />
      </blockquote>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <u>Buy 1 Jeans Get 1 T-shirt Free</u>
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <ul style="margin-bottom: 0px;">
          <li>Create a <font class="text-o-color-5">POS order</font> through POS Application</li>
          <li>Scan “HIGHJEAN001” Barcode number from the search bar and select the “High Waist Jeans” product and customer.</li>
          <li>Click on the reward button and the system will display the "Buy 1 Get 1 Free Product T-Shirt (Purple)”. You can see the free product is added along in the point of sale order. </li>
          <li>Continue the payment Process further with any of the payment methods and validate.</li>
      </ul>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <blockquote style="margin-bottom: 0px;" data-o-mail-quote-node="1" data-o-mail-quote="1">There is a Promotion Program created  in which the reward for Free Product has been defined.</blockquote>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <h2 style="margin-bottom: 0px;">Flow 3: POS → Loyalty points</h2>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">Loyalty points are given to customer on their purchase &amp; later on the customer can redeem discount in exchange of loyalty points</p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <ul style="margin-bottom: 0px;">
          <li>Create a POS order through POS Application, select any customer </li>
          <li>Add any “Jumpsuit” from the western wear category.</li>
          <li>On each order the customer will earn 5 points and that will be added into their balance and when the customer reaches the points up to 200 then in exchange that the customer can claim 5 % discount.</li>
      </ul>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <blockquote style="margin-bottom: 0px;" data-o-mail-quote-node="1" data-o-mail-quote="1">Here Promotion Program has been used for allocating the loyalty points on order and discount while claiming.  </blockquote>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <h2 style="margin-bottom: 0px;">Flow 4 : Website → Seasonal discount → Monsoon Discount offer.</h2>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <p style="margin-bottom: 0px;">
          Nowadays, it has become almost a practice for companies to offer at least one or more seasonal discounts to customers at any particular time of the year like: New Year's Eve discount, winter sale, Christmas sale, summer sale, etc.
      </p>
      <p style="margin-bottom: 0px;">
          <br />
      </p>
      <ul style="margin-bottom: 0px;">
          <li>Go to the Website Application and select Top from the Western wear category. </li>
          <li>Add to cart the selected product and 20% Discount will be applied and further process checkout, set the customer with the shipping address and make the payment.</li>
      </ul>
      <p>
          <br />
      </p>
      <blockquote data-o-mail-quote-node="1" data-o-mail-quote="1">Here promotion program has been used  to define the discount on a particular product category.</blockquote>
    </template>

    <record id="welcome_article" model="knowledge.article">
        <field name="name">Clothing Store</field>
        <field name="internal_permission">write</field>
        <field name="cover_image_position">37.999996948242185</field>
        <field name="icon">👗</field>
        <field name="category">private</field>
        <field name="cover_image_id" ref="knowledge_cover_13"/>
        <field name="is_locked" eval="True"/>
        <field name="is_article_visible_by_everyone" eval="True"/>
        <field name="body"><![CDATA[]]></field>
    </record>


</odoo>
