<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record model="res.config.settings" id="res_config_settings_enable">
        <field name="group_product_pricelist" eval="1"/>
        <field name="group_product_variant" eval="1"/>
        <field name="group_uom" eval="1"/>
        <field name="group_discount_per_so_line" eval="1"/>
        <field name="group_stock_production_lot" eval="1"/>
        <field name="point_of_sale_use_ticket_qr_code" eval="1"/>
        <field name="pos_is_header_or_footer" eval="1"/>
    </record>
    <function model="res.config.settings" name="execute">
        <value eval="[ref('res_config_settings_enable')]"/>
    </function>
</odoo>
