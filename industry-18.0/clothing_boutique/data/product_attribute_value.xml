<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_attribute_value_1" model="product.attribute.value">
        <field name="name">Wine Red</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#751a1a</field>
    </record>
    <record id="product_attribute_value_light_blue" model="product.attribute.value">
        <field name="name">Light blue</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#60a9e1</field>
    </record>
    <record id="product_attribute_value_dark_pink" model="product.attribute.value">
        <field name="name">Dark pink</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#e15699</field>
    </record>
    <record id="product_attribute_value_dark_blue" model="product.attribute.value">
        <field name="name">Dark Blue</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#1446db</field>
    </record>
    <record id="product_attribute_value_blue" model="product.attribute.value">
        <field name="name">Blue</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#3498DB</field>
    </record>
    <record id="product_attribute_value_2" model="product.attribute.value">
        <field name="name">Red</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#eb1e1e</field>
    </record>
    <record id="product_attribute_value_black" model="product.attribute.value">
        <field name="name">Black</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#272020</field>
    </record>
    <record id="product_attribute_value_4" model="product.attribute.value">
        <field name="name">White</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#fdf7f7</field>
    </record>
    <record id="product_attribute_value_5" model="product.attribute.value">
        <field name="name">Purple</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#eda6e7</field>
    </record>
    <record id="product_attribute_value_pink" model="product.attribute.value">
        <field name="name">Pink</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#f287d9</field>
    </record>
    <record id="product_attribute_value_yellow" model="product.attribute.value">
        <field name="name">Yellow</field>
        <field name="attribute_id" ref="product_attribute_2" />
        <field name="html_color">#e1c823</field>
    </record>
    <record id="product_attribute_value_8" model="product.attribute.value">
        <field name="name">M</field>
        <field name="attribute_id" ref="product_attribute_3" />
    </record>
    <record id="product_attribute_value_9" model="product.attribute.value">
        <field name="name">L</field>
        <field name="attribute_id" ref="product_attribute_3" />
    </record>
</odoo>
