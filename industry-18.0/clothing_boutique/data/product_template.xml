<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_10" model="product.template" context="{'create_product_product': False}">
        <field name="name">Black Leather Wide Leg trouser</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_12" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">30.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_7')])]" />
        <field name="website_sequence">10045</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/10-image_1920" />
    </record>
    <record id="product_template_11" model="product.template" context="{'create_product_product': False}">
        <field name="name">Black Top</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_17" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">20.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_10')])]" />
        <field name="website_sequence">10050</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/11-image_1920" />
    </record>
    <record id="product_template_12" model="product.template" context="{'create_product_product': False}">
        <field name="name">Blue Solid Pants</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_12" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">15.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_7')])]" />
        <field name="tracking">lot</field>
        <field name="website_sequence">10055</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/12-image_1920" />
    </record>
    <record id="product_template_13" model="product.template" context="{'create_product_product': False}">
        <field name="name">Chiffon Jumpsuit</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_15" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">35.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_9')])]" />
        <field name="website_sequence">10060</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/13-image_1920" />
    </record>
    <record id="product_template_14" model="product.template" context="{'create_product_product': False}">
        <field name="name">Cream Oversized T-Shirt</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_16" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">35.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_6')])]" />
        <field name="website_sequence">10065</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/14-image_1920" />
    </record>
    <record id="product_template_15" model="product.template" context="{'create_product_product': False}">
        <field name="name">Blue Sequin Suit (Set of 3)</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_9" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">40.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_5')])]" />
        <field name="website_sequence">10070</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/15-image_1920" />
    </record>
    <record id="product_template_16" model="product.template" context="{'create_product_product': False}">
        <field name="name">Denim Jacket</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_14" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">20.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]" />
        <field name="website_sequence">10075</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/16-image_1920" />
    </record>
    <record id="product_template_17" model="product.template" context="{'create_product_product': False}">
        <field name="name">Designer Kurti</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_10" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">45.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_8')])]" />
        <field name="website_sequence">10080</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/17-image_1920" />
    </record>
    <record id="product_template_18" model="product.template" context="{'create_product_product': False}">
        <field name="name">Ruffle Floral Print Top</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_17" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">35.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_10')])]" />
        <field name="website_sequence">10085</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/18-image_1920" />
    </record>
    <record id="product_template_20" model="product.template" context="{'create_product_product': False}">
        <field name="name">T-shirt </field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_16" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">35.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_6')])]" />
        <field name="website_sequence">10090</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/20-image_1920" />
    </record>
    <record id="product_template_21" model="product.template" context="{'create_product_product': False}">
        <field name="name">Full sleeve jumpsuit</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_15" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">15.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_9')])]" />
        <field name="website_sequence">10095</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/21-image_1920" />
    </record>
    <record id="product_template_22" model="product.template" context="{'create_product_product': False}">
        <field name="name">Heart T -shirt</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_16" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">15.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_6')])]" />
        <field name="website_sequence">10100</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/22-image_1920" />
    </record>
    <record id="product_template_23" model="product.template" context="{'create_product_product': False}">
        <field name="name">Linen Wide Leg Pant</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_12" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">45.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_7')])]" />
        <field name="website_sequence">10105</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/23-image_1920" />
    </record>
    <record id="product_template_24" model="product.template" context="{'create_product_product': False}">
        <field name="name">Mustard yellow Kurta with Net Dupatta</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_9" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">50.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_5')])]" />
        <field name="website_sequence">10110</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/24-image_1920" />
    </record>
    <record id="product_template_25" model="product.template" context="{'create_product_product': False}">
        <field name="name">Pink Floral Print Top</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_10" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">25.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_8')])]" />
        <field name="website_sequence">10115</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/25-image_1920" />
    </record>
    <record id="product_template_26" model="product.template" context="{'create_product_product': False}">
        <field name="name">Kurta set</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_9" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">56.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_5')])]" />
        <field name="website_sequence">10120</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/26-image_1920" />
    </record>
    <record id="product_template_27" model="product.template" context="{'create_product_product': False}">
        <field name="name">Pink Round Neck Printed T-shirt</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_16" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">39.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_6')])]" />
        <field name="website_sequence">10125</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/27-image_1920" />
    </record>
    <record id="product_template_28" model="product.template" context="{'create_product_product': False}">
        <field name="name">Red Striped Jumpsuit</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_15" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">80.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_9')])]" />
        <field name="website_sequence">10130</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/28-image_1920" />
    </record>
    <record id="product_template_29" model="product.template" context="{'create_product_product': False}">
        <field name="name">White Flared Dress</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_8" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">89.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]" />
        <field name="website_sequence">10135</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/29-image_1920" />
    </record>
    <record id="product_template_30" model="product.template" context="{'create_product_product': False}">
        <field name="name">White Kurta set with Heavy Thread Work Dupatta (Set Of 3)</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_9" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">83.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_5')])]" />
        <field name="website_sequence">10140</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/30-image_1920" />
    </record>
    <record id="product_template_31" model="product.template" context="{'create_product_product': False}">
        <field name="name">Women Solid Black Casual Jacket</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_14" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">29.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]" />
        <field name="website_sequence">10145</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/31-image_1920" />
    </record>
    <record id="product_template_32" model="product.template" context="{'create_product_product': False}">
        <field name="name">Assymetric kurta With Inner And Trousers</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_8" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">96.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]" />
        <field name="website_sequence">10150</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/32-image_1920" />
    </record>
    <record id="product_template_33" model="product.template" context="{'create_product_product': False}">
        <field name="name">Designer Ethnic wear</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_8" />
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">20.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]" />
        <field name="tracking">lot</field>
        <field name="website_sequence">10155</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/33-image_1920" />
        <field name="service_type">manual</field>
    </record>
    <record id="product_template_34" model="product.template" context="{'create_product_product': False}">
        <field name="name">Red Suit Set</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_9" />
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_5')])]" />
        <field name="website_sequence">10160</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/34-image_1920" />
        <field name="service_type">manual</field>
    </record>
    <record id="product_template_35" model="product.template" context="{'create_product_product': False}">
        <field name="name">Tunic Top</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_10" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_8')])]" />
        <field name="website_sequence">10165</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/35-image_1920" />
    </record>
    <record id="product_template_50" model="product.template" context="{'create_product_product': False}">
        <field name="name">20% on your order</field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10235</field>
    </record>
    <record id="product_template_51" model="product.template" context="{'create_product_product': False}">
        <field name="name">10% on your NEXT ORDER !!</field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10240</field>
    </record>
    <record id="product_template_52" model="product.template" context="{'create_product_product': False}">
        <field name="name">15% on your order</field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10245</field>
    </record>
    <record id="product_template_53" model="product.template" context="{'create_product_product': False}">
        <field name="name">High Waist Jeans</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_18" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">20.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_12')])]" />
        <field name="website_sequence">10250</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/53-image_1920" />
    </record>
    <record id="product_template_54" model="product.template" context="{'create_product_product': False}">
        <field name="name">Loose Jeans</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_18" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">25.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_12')])]" />
        <field name="website_sequence">10255</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/54-image_1920" />
    </record>
    <record id="product_template_55" model="product.template" context="{'create_product_product': False}">
        <field name="name">Skinny Jeans</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_18" />
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">8.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_12')])]" />
        <field name="website_sequence">10260</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/55-image_1920" />
        <field name="service_type">manual</field>
    </record>
    <record id="product_template_56" model="product.template" context="{'create_product_product': False}">
        <field name="name">5% on your order for the loyalty points earned.</field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10265</field>
    </record>
    <record id="product_template_57" model="product.template" context="{'create_product_product': False}">
        <field name="name">Free shipping</field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10265</field>
    </record>
    <record id="product_template_58" model="product.template" context="{'create_product_product': False}">
        <field name="name">Free Product - T-shirt </field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10270</field>
    </record>
    <record id="product_template_59" model="product.template" context="{'create_product_product': False}">
        <field name="name">Free Product - Discount</field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10275</field>
    </record>
    <record id="product_template_60" model="product.template" context="{'create_product_product': False}">
        <field name="name">Free Product - T-shirt </field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10280</field>
    </record>
    <record id="product_template_61" model="product.template" context="{'create_product_product': False}">
        <field name="name">5% on your order Loyalty</field>
        <field name="type">service</field>
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">manual</field>
        <field name="purchase_method">purchase</field>
        <field name="invoice_policy">order</field>
        <field name="list_price" eval="False" />
        <field name="purchase_ok" eval="False" />
        <field name="sale_ok" eval="False" />
        <field name="base_unit_count">1.0</field>
        <field name="website_sequence">10285</field>
    </record>
    <record id="product_template_8" model="product.template" context="{'create_product_product': False}">
        <field name="name">Baby Pink Printed Maxi Dress</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_8" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">10.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]" />
        <field name="website_sequence">10035</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/8-image_1920" />
    </record>
    <record id="product_template_9" model="product.template" context="{'create_product_product': False}">
        <field name="name">Aqua Blue Top and Skirt</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_13" />
        <field name="service_type">manual</field>
        <field name="purchase_method">receive</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True" />
        <field name="list_price">5.0</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_11')])]" />
        <field name="website_sequence">10040</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/product_template/9-image_1920" />
    </record>
</odoo>
