# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* clothing_boutique
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:27+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_51
msgid "10% on your NEXT ORDER !!"
msgstr "10 % en su PRÓXIMo PEDIDO"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_4
#: model:product.template,name:clothing_boutique.product_template_52
msgid "15% on your order"
msgstr "15% en su pedido"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_2
#: model:product.template,name:clothing_boutique.product_template_50
msgid "20% on your order"
msgstr "20 % en su pedido"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_5
msgid "5% on your order"
msgstr "5 % en su pedido"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_61
msgid "5% on your order Loyalty"
msgstr "5 % en su pedido por su fidelidad"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_56
msgid "5% on your order for the loyalty points earned."
msgstr "5 % en su pedido por los puntos de fidelidad que ha obtenido."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<br/>Deliver flowers to live up to your dedication for your loved ones."
msgstr ""
"<br/>Queremos entregar flores que estén a la altura del compromiso y la "
"dedicación que tiene con sus seres queridos."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Siguiente</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Anterior</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"b>Iris DOE</b> • CEO de MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"b>Jane DOE</b> • CEO de MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"b>John DOE</b> • CEO de MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<span style=\"font-size: 36px;\">The Glam Boutique</span>"
msgstr "<span style=\"font-size: 36px;\">The Glam Boutique</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Buy 1 Jeans Get 1 T-shirt Free</u>"
msgstr "<u>Comprar 1 par de vaqueros y recibir 1 camiseta gratis</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Products with Barcode</u>"
msgstr "<u>Productos con códigos de barras</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A small shop with a very large selection of roses. Nice prices and above all"
" a very professional and friendly welcome."
msgstr ""
"Una tienda pequeña pero con una gran selección de rosas. Los precios son "
"buenos, además el personal es muy profesional y amable."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A wide range of high quality products and a warm welcome! I recommend this "
"shop 200%!"
msgstr ""
"Cuentan con una gran variedad de productos de alta calidad. ¡Recomiendo esta"
" tienda 200 %!"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Add any “Jumpsuit” from the western wear category."
msgstr "Añada cualquier “Mono” desde la categoría ropa occidental."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add the Barcode number in the Product Master and take a print to scan them "
"during the POS session."
msgstr ""
"Añada el número de código de barras en el formulario del producto y tome una"
" impresión para escanearlos durante la sesión de TPV."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add to cart the selected product and 20% Discount will be applied and "
"further process checkout, set the customer with the shipping address and "
"make the payment."
msgstr ""
"Añada el producto seleccionado a la cesta y se aplicará el 20 % de "
"descuento. Para continuar con el proceso de pago, configure el cliente con "
"la dirección de envío y haga el pago."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_9
msgid "Aqua Blue Top and Skirt"
msgstr "Top y falda azul"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"As we use local growers, our flowers last longer and smell better. Just like"
" the event you want to celebrate with flowers!"
msgstr ""
"Trabajamos con cultivadores locales, así que nuestras flores duran más y "
"huelen mejor. ¡Son perfectas para esa ocasión especial que quiere celebrar!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_32
msgid "Assymetric kurta With Inner And Trousers"
msgstr "Kurta asimétrica con forro y pantalones"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"BOGO Offer (Buy one Get one Free) <br/>\n"
"          Promotions are popular among retailers because they can increase sales of the products being promoted. Plus, it will attract attention to other items in the store, which is great when you need to move dead stock or boost sales.\n"
"          <br/>"
msgstr ""
"Oferta de dos por el precio de uno<br/>\n"
"Las promociones son muy populares entre vendedores minoristas ya que pueden incrementar las ventas de los productos que se están promocionando. Además, hará que otros artículos de la tienda llamen la atención, lo cual es genial cuando necesita mover stock muerto o aumentar las ventas.\n"
"<br/>"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_8
msgid "Baby Pink Printed Maxi Dress"
msgstr "Vestido maxi rosa pastel con estampado"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Bio Flowers"
msgstr "Flores sin pesticidas"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_black
msgid "Black"
msgstr "Negro"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_10
msgid "Black Leather Wide Leg trouser"
msgstr "Pantalones negros anchos de cuero"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_11
msgid "Black Top"
msgstr "Top negro"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_blue
msgid "Blue"
msgstr "Azul"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_15
msgid "Blue Sequin Suit (Set of 3)"
msgstr "Traje de lentejuelas azules (conjunto de 3 piezas)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_12
msgid "Blue Solid Pants"
msgstr "Pantalones azules"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_7
msgid "Bottom pants and Trousers"
msgstr "Pantalones"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Business Flows:"
msgstr "Flujos empresariales:"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_7
msgid "Buy 1 jeans get 1 T-shirt Free"
msgstr "Comprar 1 par de vaqueros y recibir 1 camiseta gratis"

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_1
msgid "Cash"
msgstr "Efectivo"

#. module: clothing_boutique
#: model:account.journal,name:clothing_boutique.cash
msgid "Cash (Clothing boutique)"
msgstr "Efectivo (tienda de ropa)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_13
msgid "Chiffon Jumpsuit"
msgstr "Mono de chifón"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Click on the reward button and the system will display the \"Buy 1 Get 1 "
"Free Product T-Shirt (Purple)”. You can see the free product is added along "
"in the point of sale order."
msgstr ""
"Haga clic en el botón de recompensar y el sistema “Compre 1 y obtenga 1 "
"gratis: Producto gratis - Camiseta (morada)”. Puede ver que el producto "
"gratis se añade al pedido de TPV."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_11
msgid "Co ords sets"
msgstr "Conjuntos"

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_2
msgid "Colour"
msgstr "Color"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Confirm the RFQ based on the received rates by the vendor."
msgstr ""
"Confirme la solicitud de presupuesto según las tarifas recibidas por el "
"proveedor."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Contact us"
msgstr "Contáctenos"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Continue the payment Process further with any of the payment methods and "
"validate."
msgstr ""
"Continue con el proceso de pago con cualquier método de pago y valídelo."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_14
msgid "Cream Oversized T-Shirt"
msgstr "Camiseta oversize color crema"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Create a <font class=\"text-o-color-5\">POS order</font> through POS "
"Application"
msgstr ""
"Cree un <font class=\"text-o-color-5\">pedido de TPV</font> en la aplicación"
" Punto de venta."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Create a POS order through POS Application, select any customer"
msgstr ""
"Cree un pedido de TPV con la aplicación Punto de Venta, seleccione a "
"cualquier cliente."

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_2
msgid "Customer Account"
msgstr "Cuenta de cliente"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_blue
msgid "Dark Blue"
msgstr "Azul oscuro"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_pink
msgid "Dark pink"
msgstr "Rosa oscuro"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_16
msgid "Denim Jacket"
msgstr "Chaqueta vaquera"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_33
msgid "Designer Ethnic wear"
msgstr "Ropa étnica de diseñador"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_17
msgid "Designer Kurti"
msgstr "Kurti de diseñador"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Discover more"
msgstr "Descubra más"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Don't offer pesticides to your beloved one! By choosing our flowers, we "
"guarantee you organic flowers."
msgstr ""
"¡No exponga a sus seres queridos a pesticidas! Si elige nuestras flores, le "
"garantizamos que recibirán flores orgánicas."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Eco-Friendly Packaging"
msgstr "Embalaje ecológico"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_4
msgid "Ethnic Dresses"
msgstr "Vestidos étnicos"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 1: Purchase"
msgstr "Flujo 1: Compra"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 2: POS with Barcode and BOGO offer."
msgstr ""
"Flujo 2: TPV con código de barras y una oferta de comprar dos al precio de "
"uno."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 3: POS → Loyalty points"
msgstr "Flujo 3: TPV → Puntos de fidelidad"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 4 : Website → Seasonal discount → Monsoon Discount offer."
msgstr ""
"Flujo 4 : Sitio web → Descuentos por temporada → Oferta de descuento Monzón"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_59
msgid "Free Product - Discount"
msgstr "Producto gratis - Descuento"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_58
#: model:product.template,name:clothing_boutique.product_template_60
msgid "Free Product - T-shirt "
msgstr "Producto gratis - Camiseta"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_9
msgid "Free Product - T-shirt  (Purple)"
msgstr "Producto gratis - Camiseta (morada)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_57
msgid "Free shipping"
msgstr "Envío gratuito"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"From your local producer to your door, discover how we deliver our multiple "
"sorts of flowers and bring you happiness."
msgstr ""
"De las manos del productor local hasta su puerta, conozca cómo entregamos "
"nuestra variedad de flores y le enviamos felicidad."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_21
msgid "Full sleeve jumpsuit"
msgstr "Mono de manga larga"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Go to the Website Application and select Top from the Western wear category."
msgstr ""
"Vaya a la aplicación Sitio web y seleccione un top de la categoría “Ropa "
"occidental”."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_22
msgid "Heart T -shirt"
msgstr "Camiseta con corazón"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here Promotion Program has been used for allocating the loyalty points on "
"order and discount while claiming."
msgstr ""
"Aquí se ha utilizado el programa de promoción para conceder puntos de "
"fidelidad en el pedido y el descuento al canjearlos."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here promotion program has been used  to define the discount on a particular"
" product category."
msgstr ""
"Aquí se ha utilizado el programa de promoción para definir el descuento en "
"una categoría de producto en particular."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_53
msgid "High Waist Jeans"
msgstr "Vaqueros de tiro alto"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_1
msgid "Indian Wear"
msgstr "Ropa india"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_3
msgid "Jackets"
msgstr "Chaquetas"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_12
msgid "Jeans"
msgstr "Vaqueros"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_9
msgid "Jumpsuits"
msgstr "Monos"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_26
msgid "Kurta set"
msgstr "Set de kurta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Learn more"
msgstr "Descubre más "

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_light_blue
msgid "Light blue"
msgstr "Azul claro"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_23
msgid "Linen Wide Leg Pant"
msgstr "Pantalones de lino de pierna ancha"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Live life in full bloom"
msgstr "Florece mientras disfruta la vida"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Local Producers"
msgstr "Productores locales"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_54
msgid "Loose Jeans"
msgstr "Vaqueros sueltos"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_5
msgid "Loyalty points"
msgstr "Puntos de fidelidad"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Loyalty points are given to customer on their purchase &amp; later on the "
"customer can redeem discount in exchange of loyalty points"
msgstr ""
"Los puntos de fidelidad se otorgan al cliente en su compra. Después, el "
"cliente puede canjear su descuento por puntos de fidelidad."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_2
msgid "Mega Discount Offer on Tops!!"
msgstr "¡Descuentos increíbles en tops!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_24
msgid "Mustard yellow Kurta with Net Dupatta"
msgstr "Kurta amarilla con dupatta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Next"
msgstr "Siguiente"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Nowadays, it has become almost a practice for companies to offer at least "
"one or more seasonal discounts to customers at any particular time of the "
"year like: New Year's Eve discount, winter sale, Christmas sale, summer "
"sale, etc."
msgstr ""
"Hoy en día, se ha convertido casi en una costumbre que las empresas ofrezcan"
" a sus clientes al menos uno o varios descuentos estacionales en "
"determinadas épocas del año, como los descuentos de Nochevieja, las rebajas "
"de invierno, las rebajas de Navidad, las rebajas de verano, etc."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_4
msgid "OFFER15"
msgstr "OFERTA15"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"On each order the customer will earn 5 points and that will be added into "
"their balance and when the customer reaches the points up to 200 then in "
"exchange that the customer can claim 5 % discount."
msgstr ""
"En cada pedido, el cliente ganará 5 puntos, que se añadirán a su saldo. "
"Cuando el cliente alcance los 200 puntos a cambio el cliente podrá reclamar "
"un 5 % de descuento."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Our Mission"
msgstr "Nuestra misión"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_pink
msgid "Pink"
msgstr "Rosado"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_25
msgid "Pink Floral Print Top"
msgstr "Top rosa con estampado floral"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_27
msgid "Pink Round Neck Printed T-shirt"
msgstr "Camiseta rosa de cuello redondo con estampado"

#. module: clothing_boutique
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_2
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_4
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_5
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_7
msgid "Points"
msgstr "Puntos"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Previous"
msgstr "Anterior"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_5
msgid "Purple"
msgstr "Morado"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_2
msgid "Red"
msgstr "Rojo"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_28
msgid "Red Striped Jumpsuit"
msgstr "Mono de rayas rojas"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_34
msgid "Red Suit Set"
msgstr "Conjunto de traje rojo"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_18
msgid "Ruffle Floral Print Top"
msgstr "Blusa de volantes con estampado floral"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Scan “HIGHJEAN001” Barcode number from the search bar and select the “High "
"Waist Jeans” product and customer."
msgstr ""
"Escanee el número de código de barras “HIGHJEAN001” desde la barra de "
"búsqueda y seleccione el producto “Vaqueros de tiro alto” y al cliente."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Send RFQ to your Pre defined Vendor."
msgstr "Envíe una solicitud de presupuesto a su proveedor predefinido."

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_3
msgid "Size"
msgstr "Tamaño"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_55
msgid "Skinny Jeans"
msgstr "Vaqueros Skinny"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_5
msgid "Suit sets"
msgstr "Conjuntos de trajes"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_20
msgid "T-shirt "
msgstr "Camiseta"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_6
msgid "T-shirts"
msgstr "Camisetas"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "The Glam Boutique"
msgstr "The Glam Boutique"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "The Orchid Concept"
msgstr "Nuestro concepto"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "There are always flowers <br/>for those who want to see them."
msgstr "Siempre hay flores <br/>para quienes quieren verlas."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"There is a Promotion Program created  in which the reward for Free Product "
"has been defined."
msgstr ""
"Hay un programa de promoción creado en el que ya se definió la recompensa "
"por Producto gratis."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"This setup is for Clothing and Boutique retail shops which encompasses the "
"creation, distribution, and retail of clothing and fashion accessories to a "
"specific segment of people."
msgstr ""
"Esta configuración es para tiendas de ropa y boutiques minoristas que "
"también incluyen la creación, distribución y venta de ropa y accesorios de "
"moda a un segmento de personas específico."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Through Purchase Application <font class=\"text-o-color-5\">create an "
"RFQ</font> (Request for quotation) with Product “White Flared Dress”"
msgstr ""
"Con la aplicación Compra <font class=\"text-o-color-5\">cree una solicitud "
"de presupuesto</font> con el producto “Vestido acampanado blanco”."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"To do so, we collaborate with local producers who care about the environment"
" and cultivate their flowers with love."
msgstr ""
"Para lograrlo, colaboramos con productores locales que se preocupan por el "
"medio ambiente y cultivan sus flores con cariño."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_10
msgid "Tops"
msgstr "Tops"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_8
msgid "Tops and Tunics"
msgstr "Tops y túnicas"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_35
msgid "Tunic Top"
msgstr "Túnica"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Very nice, colourful shop with many choices in the choir of a very pretty "
"town."
msgstr ""
"Una tienda muy bonita y colorida con mucha variedad. Aprovechen para "
"recorrer la ciudad, es muy pintoresca."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"We use 100% recycled materials. We believe the best gifts should also do "
"good for the planet."
msgstr ""
"Utilizamos materiales 100 % reciclados. Creemos que los mejores regalos "
"también benefician al planeta."

#. module: clothing_boutique
#: model_terms:web_tour.tour,rainbow_man_message:clothing_boutique.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Bienvenido! Disfrute del sitio."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_2
msgid "Western wear"
msgstr "Ropa occidental"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_4
msgid "White"
msgstr "Blanco"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_29
msgid "White Flared Dress"
msgstr "Vestido acampanado blanco"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_30
msgid "White Kurta set with Heavy Thread Work Dupatta (Set Of 3)"
msgstr "Kurta blanca con dupatta de hilo grueso (conjunto de 3)"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_1
msgid "Wine Red"
msgstr "Rojo vino"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_31
msgid "Women Solid Black Casual Jacket"
msgstr "Chaqueta negra ligera para mujer"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_yellow
msgid "Yellow"
msgstr "Amarillo"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"You can also search the Barcode number of that product from the POS session "
"to recognise that product."
msgstr ""
"También puede buscar el número del código de barras de ese producto desde la"
" sesión de TPV para reconocer al producto."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "assorted-color hanged clothes during daytime"
msgstr "ropa colgada de colores durante el día"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "white and brown floral long sleeve shirt"
msgstr "camisa de manga larga floral blanca y marrón"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "woman in black and white dress holding happy birthday signage"
msgstr ""
"una mujer en vestido blanco y negro sosteniendo un cartel de feliz "
"cumpleaños"
