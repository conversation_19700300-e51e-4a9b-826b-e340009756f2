# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* clothing_boutique
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:27+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_51
msgid "10% on your NEXT ORDER !!"
msgstr "10% przy NASTĘPNYM ZAMÓWIENIU!"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_4
#: model:product.template,name:clothing_boutique.product_template_52
msgid "15% on your order"
msgstr "15% na zamówienie"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_2
#: model:product.template,name:clothing_boutique.product_template_50
msgid "20% on your order"
msgstr "20% na Twoje zamówienie"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_5
msgid "5% on your order"
msgstr "5% na Twoje zamówienie"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_61
msgid "5% on your order Loyalty"
msgstr "5% na Twoje zamówienie - Program lojalnościowy"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_56
msgid "5% on your order for the loyalty points earned."
msgstr "5% na Twoje zamówienie za zebrane punkty lojalnościowe."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<br/>Deliver flowers to live up to your dedication for your loved ones."
msgstr "<br/>Dostarcz kwiaty, aby wyrazić swoje poświęcenie ukochanym osobom."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Następny</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Poprzedni</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE </b>- dyrektor generalny "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> - dyrektor generalny "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> - dyrektor generalny "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<span style=\"font-size: 36px;\">The Glam Boutique</span>"
msgstr "<span style=\"font-size: 36px;\">Glam Boutique</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Buy 1 Jeans Get 1 T-shirt Free</u>"
msgstr "<u>Kup 1 parę jeansów i otrzymaj 1 t-shirt gratis</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Products with Barcode</u>"
msgstr "<u>Produkty z kodami kreskowymi</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A small shop with a very large selection of roses. Nice prices and above all"
" a very professional and friendly welcome."
msgstr ""
"Mały sklepik z szerokim wyborem róż. Dobre ceny i przede wszystkim bardzo "
"przyjazne i profesjonalne powitanie."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A wide range of high quality products and a warm welcome! I recommend this "
"shop 200%!"
msgstr ""
"Szeroki wybór wysokiej jakości produktów i miła obsługa! Polecam ten sklep "
"na 200%!"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Add any “Jumpsuit” from the western wear category."
msgstr "Dodaj dowolny \"kombinezon\" z kategorii odzieży zachodniej."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add the Barcode number in the Product Master and take a print to scan them "
"during the POS session."
msgstr ""
"Dodaj numer kodu kreskowego w Głównym Produkcie i wydrukuj go, aby "
"zeskanować podczas sesji POS."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add to cart the selected product and 20% Discount will be applied and "
"further process checkout, set the customer with the shipping address and "
"make the payment."
msgstr ""
"Dodaj wybrany produkt do koszyka, a rabat 20% zostanie naliczony. Następnie "
"przejdź do kasy, wprowadź dane klienta wraz z adresem dostawy i dokonaj "
"płatności"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_9
msgid "Aqua Blue Top and Skirt"
msgstr "Top i spódnica Aqua Blue"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"As we use local growers, our flowers last longer and smell better. Just like"
" the event you want to celebrate with flowers!"
msgstr ""
"Ponieważ korzystamy z usług lokalnych hodowców, nasze kwiaty są trwalsze i "
"lepiej pachną. Tak jak wydarzenie, które chcesz uczcić kwiatami!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_32
msgid "Assymetric kurta With Inner And Trousers"
msgstr "Asymetryczna kurta z wkładką i spodniami"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"BOGO Offer (Buy one Get one Free) <br/>\n"
"          Promotions are popular among retailers because they can increase sales of the products being promoted. Plus, it will attract attention to other items in the store, which is great when you need to move dead stock or boost sales.\n"
"          <br/>"
msgstr ""
"Oferta BOGO (Kup jeden, drugi gratis) <br/>\n"
"          Promocje tego typu są popularne wśród sprzedawców detalicznych, ponieważ mogą zwiększyć sprzedaż promowanych produktów. Dodatkowo przyciągają uwagę do innych artykułów w sklepie, co jest świetnym rozwiązaniem, gdy trzeba pozbyć się zalegających towarów lub zwiększyć sprzedaż.\n"
"           <br/>"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_8
msgid "Baby Pink Printed Maxi Dress"
msgstr "Sukienka maxi z nadrukiem Baby Pink"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Bio Flowers"
msgstr "Kwiaty Bio"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_black
msgid "Black"
msgstr "Czarny"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_10
msgid "Black Leather Wide Leg trouser"
msgstr "Czarne skórzane spodnie z szeroką nogawką"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_11
msgid "Black Top"
msgstr "Czarny top"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_blue
msgid "Blue"
msgstr "Niebieski"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_15
msgid "Blue Sequin Suit (Set of 3)"
msgstr "Niebieski cekinowy garnitur (3-częściowy)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_12
msgid "Blue Solid Pants"
msgstr "Niebieskie jednolite spodnie"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_7
msgid "Bottom pants and Trousers"
msgstr "Spodnie"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Business Flows:"
msgstr "Przepływy biznesowe:"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_7
msgid "Buy 1 jeans get 1 T-shirt Free"
msgstr "Kup 1 parę jeansów i otrzymaj 1 t-shirt gratis"

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_1
msgid "Cash"
msgstr "Gotówka"

#. module: clothing_boutique
#: model:account.journal,name:clothing_boutique.cash
msgid "Cash (Clothing boutique)"
msgstr "Gotówka (sklep odzieżowy)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_13
msgid "Chiffon Jumpsuit"
msgstr "Kombinezon z szyfonu"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Click on the reward button and the system will display the \"Buy 1 Get 1 "
"Free Product T-Shirt (Purple)”. You can see the free product is added along "
"in the point of sale order."
msgstr ""
"Kliknij przycisk nagrody, a system wyświetli produkt „Kup 1, otrzymaj 1 za "
"darmo – T-shirt (fioletowy)”. Zobaczysz, że darmowy produkt został dodany do"
" zamówienia w systemie punktu sprzedaży."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_11
msgid "Co ords sets"
msgstr "Zestawy koordynowane"

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_2
msgid "Colour"
msgstr "Kolor"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Confirm the RFQ based on the received rates by the vendor."
msgstr "Potwierdź RFQ na podstawie stawek otrzymanych od dostawcy."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Contact us"
msgstr "Skontaktuj się z nami"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Continue the payment Process further with any of the payment methods and "
"validate."
msgstr "Kontynuuj proces płatności z dowolną z metod płatności i potwierdź."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_14
msgid "Cream Oversized T-Shirt"
msgstr "Kremowy t-shirt oversize"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Create a <font class=\"text-o-color-5\">POS order</font> through POS "
"Application"
msgstr ""
"Stwórz <font class=\"text-o-color-5\">zamówienie POS</font> przez aplikację "
"POS."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Create a POS order through POS Application, select any customer"
msgstr "Stwórz zamówienie POS przez aplikację POS, wybierz dowolnego klienta"

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_2
msgid "Customer Account"
msgstr "Konto klienta"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_blue
msgid "Dark Blue"
msgstr "Ciemny niebieski"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_pink
msgid "Dark pink"
msgstr "Ciemny róż"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_16
msgid "Denim Jacket"
msgstr "Kurtka jeansowa"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_33
msgid "Designer Ethnic wear"
msgstr "Projektant Ethnic wear"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_17
msgid "Designer Kurti"
msgstr "Projektant Kurti"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Discover more"
msgstr "Odkryj więcej"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Don't offer pesticides to your beloved one! By choosing our flowers, we "
"guarantee you organic flowers."
msgstr ""
"Nie dawaj swojej ukochanej pestycydów! Wybierając nasze kwiaty, wybierasz "
"organiczne produkty."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Eco-Friendly Packaging"
msgstr "Ekologiczne opakowanie"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_4
msgid "Ethnic Dresses"
msgstr "Sukienki etno"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 1: Purchase"
msgstr "Proces 1: Zakup"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 2: POS with Barcode and BOGO offer."
msgstr "Proces 2: POS z kodem kreskowym i ofertą BOGO."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 3: POS → Loyalty points"
msgstr "Proces 3: POS → Punkty lojalnościowe"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 4 : Website → Seasonal discount → Monsoon Discount offer."
msgstr ""
"Proces 4: Strona internetowa → Obniżka sezonowa → Oferta zniżkowa Monsoon"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_59
msgid "Free Product - Discount"
msgstr "Darmowy produkt - Zniżka"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_58
#: model:product.template,name:clothing_boutique.product_template_60
msgid "Free Product - T-shirt "
msgstr "Darmowy produkt - T-shirt"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_9
msgid "Free Product - T-shirt  (Purple)"
msgstr "Darmowy produkt - T-shirt (fioletowy)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_57
msgid "Free shipping"
msgstr "Darmowa dostawa"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"From your local producer to your door, discover how we deliver our multiple "
"sorts of flowers and bring you happiness."
msgstr ""
"Od Twojego lokalnego producenta wprost pod Twe drzwi, odkryj jak dostarczamy"
" różnorodne rodzaje kwiatów i sprawiamy Ci szczęście."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_21
msgid "Full sleeve jumpsuit"
msgstr "Kombinezon na długi rękaw"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Go to the Website Application and select Top from the Western wear category."
msgstr ""
"Idź do aplikacji Strona Internetowa i wybierz top z kategorii odzież "
"zachodnia."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_22
msgid "Heart T -shirt"
msgstr "T-shirt z sercem"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here Promotion Program has been used for allocating the loyalty points on "
"order and discount while claiming."
msgstr ""
"W tym przypadku program promocyjny został użyty do przyznawania punktów "
"lojalnościowych za zamówienie oraz rabatu podczas składania reklamacji."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here promotion program has been used  to define the discount on a particular"
" product category."
msgstr ""
"W tym przypadku program promocyjny został użyty do zdefiniowania zniżki w "
"konkretnej kategorii produktu."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_53
msgid "High Waist Jeans"
msgstr "Jeansy z wysoką talią"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_1
msgid "Indian Wear"
msgstr "Odzież indyjska"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_3
msgid "Jackets"
msgstr "Kurtki"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_12
msgid "Jeans"
msgstr "Dżinsy"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_9
msgid "Jumpsuits"
msgstr "Kombinezony"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_26
msgid "Kurta set"
msgstr "Zestaw Kurta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Learn more"
msgstr "Dowiedz się więcej"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_light_blue
msgid "Light blue"
msgstr "Jasnoniebieski"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_23
msgid "Linen Wide Leg Pant"
msgstr "Szerokie spodnie lniane"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Live life in full bloom"
msgstr "Żyj pełnią życia"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Local Producers"
msgstr "Lokalni producenci"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_54
msgid "Loose Jeans"
msgstr "Luźne jeansy"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_5
msgid "Loyalty points"
msgstr "Punkty lojalnościowe"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Loyalty points are given to customer on their purchase &amp; later on the "
"customer can redeem discount in exchange of loyalty points"
msgstr ""
"Punkty lojalnościowe są przyznawane klientom podczas zamówienia, a następnie"
" klient może skorzystać ze zniżki w zamian za punkty."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_2
msgid "Mega Discount Offer on Tops!!"
msgstr "Mega oferta zniżkowa na koszulki!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_24
msgid "Mustard yellow Kurta with Net Dupatta"
msgstr "Musztardowo-żółta Kurta z Net Dupatta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Next"
msgstr "Następny"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Nowadays, it has become almost a practice for companies to offer at least "
"one or more seasonal discounts to customers at any particular time of the "
"year like: New Year's Eve discount, winter sale, Christmas sale, summer "
"sale, etc."
msgstr ""
"W dzisiejszych czasach stało się niemal powszechną praktyką, że firmy "
"oferują klientom przynajmniej jeden lub więcej sezonowych rabatów w "
"określonym czasie roku, takich jak: rabat sylwestrowy, wyprzedaż zimowa, "
"wyprzedaż świąteczna, wyprzedaż letnia itp."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_4
msgid "OFFER15"
msgstr "OFFER15"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"On each order the customer will earn 5 points and that will be added into "
"their balance and when the customer reaches the points up to 200 then in "
"exchange that the customer can claim 5 % discount."
msgstr ""
"Za każde zamówienie klient otrzyma 5 punktów, które zostaną dodane do jego "
"salda. Gdy klient uzbiera 200 punktów, będzie mógł wymienić je na rabat w "
"wysokości 5%."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Our Mission"
msgstr "Nasza misja"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_pink
msgid "Pink"
msgstr "Różowy"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_25
msgid "Pink Floral Print Top"
msgstr "Różowy top z kwiatowym nadrukiem"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_27
msgid "Pink Round Neck Printed T-shirt"
msgstr "Różowy t-shirt z okrągłym dekoltem i nadrukiem"

#. module: clothing_boutique
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_2
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_4
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_5
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_7
msgid "Points"
msgstr "Punkty"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Previous"
msgstr "Poprzedni"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_5
msgid "Purple"
msgstr "Purpurowy"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_2
msgid "Red"
msgstr "Czerwony"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_28
msgid "Red Striped Jumpsuit"
msgstr "Czerwony kombinezon w paski"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_34
msgid "Red Suit Set"
msgstr "Czerwony komplet garniturowy"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_18
msgid "Ruffle Floral Print Top"
msgstr "Kwiecisty top z falbanami"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Scan “HIGHJEAN001” Barcode number from the search bar and select the “High "
"Waist Jeans” product and customer."
msgstr ""
"Zeskanuj numer kodu kreskowego “HIGHJEAN001” z paska wyszukiwania i wybierz "
"\"Jeansy z wysoką talią\" oraz klienta."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Send RFQ to your Pre defined Vendor."
msgstr "Wyślij RFQ do wcześniej zdefiniowanego dostawcy."

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_3
msgid "Size"
msgstr "Rozmiar"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_55
msgid "Skinny Jeans"
msgstr "Jeansy rurki"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_5
msgid "Suit sets"
msgstr "Komplety garniturowe"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_20
msgid "T-shirt "
msgstr "T-shirt "

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_6
msgid "T-shirts"
msgstr "T-shirty"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "The Glam Boutique"
msgstr "Glam Boutique"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "The Orchid Concept"
msgstr "The Orchid Concept"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "There are always flowers <br/>for those who want to see them."
msgstr "Zawsze są kwiaty dla tych, <br/>którzy chcą je zobaczyć."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"There is a Promotion Program created  in which the reward for Free Product "
"has been defined."
msgstr ""
"Stworzono Program Promocyjny, w którym zdefiniowano nagrodę za Produkt "
"Gratis."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"This setup is for Clothing and Boutique retail shops which encompasses the "
"creation, distribution, and retail of clothing and fashion accessories to a "
"specific segment of people."
msgstr ""
"Ta konfiguracja dotyczy sklepów odzieżowych i butików, które obejmują "
"tworzenie, dystrybucję i sprzedaż detaliczną odzieży oraz akcesoriów "
"modowych określonemu segmentowi klientów."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Through Purchase Application <font class=\"text-o-color-5\">create an "
"RFQ</font> (Request for quotation) with Product “White Flared Dress”"
msgstr ""
"<font class=\"text-o-color-5\">Stwórz RFQ</font> (Żądanie wyceny) z "
"produktem \"Biała rozszerzana sukienka\" przez aplikację Zakupy."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"To do so, we collaborate with local producers who care about the environment"
" and cultivate their flowers with love."
msgstr ""
"W tym celu współpracujemy z lokalnymi producentami, którzy dbają o "
"środowisko i uprawiają swoje kwiaty z miłością."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_10
msgid "Tops"
msgstr "Topy"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_8
msgid "Tops and Tunics"
msgstr "Topy i tuniki"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_35
msgid "Tunic Top"
msgstr "Tunika"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Very nice, colourful shop with many choices in the choir of a very pretty "
"town."
msgstr ""
"Fajny, kolorowy sklep z szerokim wyborem w centrum urokliwego miasteczka."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"We use 100% recycled materials. We believe the best gifts should also do "
"good for the planet."
msgstr ""
"Używamy w 100% recyklowanych materiałów. Wierzymy, że najlepsze prezenty "
"powinny być także przyjazne dla planety."

#. module: clothing_boutique
#: model_terms:web_tour.tour,rainbow_man_message:clothing_boutique.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Witaj! Miłego zwiedzania."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_2
msgid "Western wear"
msgstr "Odzież zachodnia"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_4
msgid "White"
msgstr "Biały"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_29
msgid "White Flared Dress"
msgstr "Biała rozszerzana sukienka"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_30
msgid "White Kurta set with Heavy Thread Work Dupatta (Set Of 3)"
msgstr ""
"Biały zestaw kurty z dupattą ozdobioną gęstym haftem (zestaw 3 częściowy)"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_1
msgid "Wine Red"
msgstr "Czerwień wina"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_31
msgid "Women Solid Black Casual Jacket"
msgstr "Kobieca jednolita czarna kurtka casualowa"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_yellow
msgid "Yellow"
msgstr "Żółty"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"You can also search the Barcode number of that product from the POS session "
"to recognise that product."
msgstr ""
"Możesz także wyszukać numer kodu kreskowego danego produktu w sesji POS, aby"
" rozpoznać ten produkt."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "assorted-color hanged clothes during daytime"
msgstr "różnokolorowe wiszące ubrania w ciągu dnia"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "white and brown floral long sleeve shirt"
msgstr "biało-brązowa kwiecista koszulka na długi rękaw"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "woman in black and white dress holding happy birthday signage"
msgstr ""
"kobieta w czarno-białej sukience trzymająca znak z napisem \"happy "
"birthday\""
