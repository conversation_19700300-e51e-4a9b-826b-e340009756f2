# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* clothing_boutique
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:27+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_51
msgid "10% on your NEXT ORDER !!"
msgstr "10% CHO ĐƠN HÀNG TIẾP THEO!"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_4
#: model:product.template,name:clothing_boutique.product_template_52
msgid "15% on your order"
msgstr "15% cho đơn hàng của bạn"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_2
#: model:product.template,name:clothing_boutique.product_template_50
msgid "20% on your order"
msgstr "20% cho đơn hàng của bạn"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_5
msgid "5% on your order"
msgstr "5% cho đơn hàng của bạn"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_61
msgid "5% on your order Loyalty"
msgstr "Chương trình khách hàng thân thiết: 5% cho đơn hàng của bạn"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_56
msgid "5% on your order for the loyalty points earned."
msgstr "5% cho đơn hàng ứng với số điểm thưởng bạn đã có."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<br/>Deliver flowers to live up to your dedication for your loved ones."
msgstr ""
"<br/>Tặng hoa để thể hiện sự quan tâm của bạn tới những người thân yêu."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Tiếp</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Trước</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Nam DOE</b> • CEO of MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Luân DOE</b> • CEO of "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Long DOE</b> • CEO of "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<span style=\"font-size: 36px;\">The Glam Boutique</span>"
msgstr "<span style=\"font-size: 36px;\">Cửa Hàng Thời Trang Sang Trọng</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Buy 1 Jeans Get 1 T-shirt Free</u>"
msgstr "<u>Mua 1 quần jean tặng 1 áo phông miễn phí</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Products with Barcode</u>"
msgstr "<u>Sản phẩm có mã vạch</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A small shop with a very large selection of roses. Nice prices and above all"
" a very professional and friendly welcome."
msgstr ""
"Một cửa hàng nhỏ với rất nhiều loại hoa hồng. Giá cả phải chăng và trên hết "
"là sự chào đón rất chuyên nghiệp và thân thiện."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A wide range of high quality products and a warm welcome! I recommend this "
"shop 200%!"
msgstr ""
"Một loạt các sản phẩm chất lượng cao và sự chào đón nồng nhiệt! Tôi giới "
"thiệu cửa hàng này 200%!"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Add any “Jumpsuit” from the western wear category."
msgstr "Thêm bất kỳ “Jumpsuit” nào từ danh mục đồ âu."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add the Barcode number in the Product Master and take a print to scan them "
"during the POS session."
msgstr "Thêm số mã vạch vào Dữ liệu sản phẩm và in để quét trong phiên POS."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add to cart the selected product and 20% Discount will be applied and "
"further process checkout, set the customer with the shipping address and "
"make the payment."
msgstr ""
"Thêm sản phẩm đã chọn vào giỏ hàng, chiết khấu 20% sẽ được áp dụng, sau đó "
"tiến hành thanh toán, nhập địa chỉ giao hàng cho khách hàng và thực hiện "
"thanh toán."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_9
msgid "Aqua Blue Top and Skirt"
msgstr "Áo và váy màu xanh nước biển"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"As we use local growers, our flowers last longer and smell better. Just like"
" the event you want to celebrate with flowers!"
msgstr ""
"Vì chúng tôi tuyển dụng người trồng hoa địa phương nên hoa của chúng tôi "
"tươi lâu hơn và có mùi thơm hơn. Vô cùng phù hợp với sự kiện mà bạn đang "
"muốn tổ chức cùng sự hiện diện của những đoá hoa này!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_32
msgid "Assymetric kurta With Inner And Trousers"
msgstr "Áo kurta bất đối xứng có áo trong và quần dài"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"BOGO Offer (Buy one Get one Free) <br/>\n"
"          Promotions are popular among retailers because they can increase sales of the products being promoted. Plus, it will attract attention to other items in the store, which is great when you need to move dead stock or boost sales.\n"
"          <br/>"
msgstr ""
"Chương trình khuyến mại BOGO (Mua 1 tặng 1) <br/>\n"
"          được các nhà bán lẻ ưa chuộng vì chúng có thể tăng doanh số bán sản phẩm đang được quảng bá. Bên cạnh đó, nó sẽ thu hút khách hàng chú ý đến các mặt hàng khác trong cửa hàng, điều này rất hữu ích khi bạn cần giải quyết tồn kho hoặc thúc đẩy doanh số bán hàng.\n"
"          <br/>"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_8
msgid "Baby Pink Printed Maxi Dress"
msgstr "Đầm maxi màu hồng"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Bio Flowers"
msgstr "Hoa hữu cơ"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_black
msgid "Black"
msgstr "Đen"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_10
msgid "Black Leather Wide Leg trouser"
msgstr "Quần da ống rộng đen"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_11
msgid "Black Top"
msgstr "Áo đen"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_blue
msgid "Blue"
msgstr "Xanh dương"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_15
msgid "Blue Sequin Suit (Set of 3)"
msgstr "Bộ đồ sequin xanh (Bộ 3 món)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_12
msgid "Blue Solid Pants"
msgstr "Quần xanh trơn"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_7
msgid "Bottom pants and Trousers"
msgstr "Quần dài và Quần tây"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Business Flows:"
msgstr "Chu trình kinh doanh:"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_7
msgid "Buy 1 jeans get 1 T-shirt Free"
msgstr "Mua 1 quần jean tặng 1 áo phông miễn phí"

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_1
msgid "Cash"
msgstr "Tiền mặt"

#. module: clothing_boutique
#: model:account.journal,name:clothing_boutique.cash
msgid "Cash (Clothing boutique)"
msgstr "Tiền mặt (Cửa hàng quần áo)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_13
msgid "Chiffon Jumpsuit"
msgstr "Jumpsuit vải chiffon"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Click on the reward button and the system will display the \"Buy 1 Get 1 "
"Free Product T-Shirt (Purple)”. You can see the free product is added along "
"in the point of sale order."
msgstr ""
"Nhấp vào nút phần thưởng và hệ thống sẽ hiển thị \"Mua 1 tặng 1 miễn phí Áo "
"phông (màu tím)”. Bạn có thể thấy sản phẩm miễn phí được thêm vào cùng với "
"đơn hàng POS."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_11
msgid "Co ords sets"
msgstr "Bộ đồ"

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_2
msgid "Colour"
msgstr "Màu sắc"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Confirm the RFQ based on the received rates by the vendor."
msgstr "Xác nhận YCBG dựa trên mức giá mà nhà cung cấp nhận được."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Contact us"
msgstr "Liên hệ"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Continue the payment Process further with any of the payment methods and "
"validate."
msgstr ""
"Tiếp tục quá trình thanh toán bằng bất kỳ phương thức thanh toán nào và xác "
"nhận."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_14
msgid "Cream Oversized T-Shirt"
msgstr "Áo thun oversize màu kem"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Create a <font class=\"text-o-color-5\">POS order</font> through POS "
"Application"
msgstr ""
"Tạo một <font class=\"text-o-color-5\">đơn hàng POS</font> qua ứng dụng POS"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Create a POS order through POS Application, select any customer"
msgstr "Tạo đơn hàng POS thông qua ứng dụng POS, chọn bất kỳ khách hàng nào"

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_2
msgid "Customer Account"
msgstr "Tài khoản khách hàng"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_blue
msgid "Dark Blue"
msgstr "Xanh tím than "

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_pink
msgid "Dark pink"
msgstr "Hồng đậm"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_16
msgid "Denim Jacket"
msgstr "Áo khoác denim"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_33
msgid "Designer Ethnic wear"
msgstr "Thiết kế trang phục đồng bào dân tộc"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_17
msgid "Designer Kurti"
msgstr "Kurti thiết kế "

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Discover more"
msgstr "Khám phá thêm"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Don't offer pesticides to your beloved one! By choosing our flowers, we "
"guarantee you organic flowers."
msgstr ""
"Đừng tặng thuốc trừ sâu cho người thân yêu của bạn! Khi chọn hoa của chúng "
"tôi, chúng tôi đảm bảo bạn sẽ nhận được hoa hữu cơ."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Eco-Friendly Packaging"
msgstr "Bao bì thân thiện với môi trường"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_4
msgid "Ethnic Dresses"
msgstr "Váy đồng bào dân tộc"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 1: Purchase"
msgstr "Chu trình 1: Mua hàng"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 2: POS with Barcode and BOGO offer."
msgstr "Chu trình 2: POS với Mã vạch và khuyến mại BOGO."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 3: POS → Loyalty points"
msgstr "Chu trình 3: POS → Điểm thưởng"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 4 : Website → Seasonal discount → Monsoon Discount offer."
msgstr ""
"Chu trình 4: Trang web → Giảm giá theo mùa → Khuyến mại giảm giá mùa mưa."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_59
msgid "Free Product - Discount"
msgstr "Sản phẩm miễn phí - Chiết khấu"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_58
#: model:product.template,name:clothing_boutique.product_template_60
msgid "Free Product - T-shirt "
msgstr "Sản phẩm miễn phí - Áo phông"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_9
msgid "Free Product - T-shirt  (Purple)"
msgstr "Sản phẩm miễn phí - Áo phông (Màu tím)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_57
msgid "Free shipping"
msgstr "Miễn phí vận chuyển"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"From your local producer to your door, discover how we deliver our multiple "
"sorts of flowers and bring you happiness."
msgstr ""
"Từ nhà sản xuất địa phương đến tận nhà bạn, hãy khám phá cách chúng tôi giao"
" nhiều loại hoa khác nhau và mang lại hạnh phúc cho bạn."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_21
msgid "Full sleeve jumpsuit"
msgstr "Jumpsuit dài tay"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Go to the Website Application and select Top from the Western wear category."
msgstr "Vào Ứng dụng Trang web và chọn Áo từ danh mục Đồ Âu."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_22
msgid "Heart T -shirt"
msgstr "Áo phông trái tim"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here Promotion Program has been used for allocating the loyalty points on "
"order and discount while claiming."
msgstr ""
"Ở đây, Chương trình khuyến mại được sử dụng để phân bổ điểm khách hàng thân "
"thiết cho đơn hàng và chiết khấu khi khách hàng đổi điểm."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here promotion program has been used  to define the discount on a particular"
" product category."
msgstr ""
"Ở đây, chương trình khuyến mại được sử dụng để xác định chiết khấu cho từng "
"danh mục sản phẩm."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_53
msgid "High Waist Jeans"
msgstr "Quần jean cạp cao"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_1
msgid "Indian Wear"
msgstr "Trang phục Ấn Độ"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_3
msgid "Jackets"
msgstr "Áo khoác"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_12
msgid "Jeans"
msgstr "Quần bò"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_9
msgid "Jumpsuits"
msgstr "Bộ đồ liền thân"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_26
msgid "Kurta set"
msgstr "Bộ Kurta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Learn more"
msgstr "Tìm hiểu thêm"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_light_blue
msgid "Light blue"
msgstr "Xanh dương nhạt"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_23
msgid "Linen Wide Leg Pant"
msgstr "Quần ống rộng vải lanh"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Live life in full bloom"
msgstr "Sống rực rỡ như những đoá hoa"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Local Producers"
msgstr "Nhà sản xuất địa phương"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_54
msgid "Loose Jeans"
msgstr "Quần jeans ống rộng"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_5
msgid "Loyalty points"
msgstr "Điểm thưởng"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Loyalty points are given to customer on their purchase &amp; later on the "
"customer can redeem discount in exchange of loyalty points"
msgstr ""
"Điểm thưởng được trao cho khách hàng khi mua hàng và sau đó khách hàng có "
"thể đổi điểm để được giảm giá"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_2
msgid "Mega Discount Offer on Tops!!"
msgstr "Siêu khuyến mại áo!!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_24
msgid "Mustard yellow Kurta with Net Dupatta"
msgstr "Kurta màu vàng mù tạt có khăn voan Dupatta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Next"
msgstr "Tiếp theo"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Nowadays, it has become almost a practice for companies to offer at least "
"one or more seasonal discounts to customers at any particular time of the "
"year like: New Year's Eve discount, winter sale, Christmas sale, summer "
"sale, etc."
msgstr ""
"Ngày nay, việc các công ty cung cấp ít nhất một hoặc nhiều chương trình giảm"
" giá theo mùa cho khách hàng vào bất kỳ thời điểm nào trong năm như: Giảm "
"giá đêm giao thừa, giảm giá mùa đông, giảm giá Giáng sinh, giảm giá mùa "
"hè,... gần như đã trở thành một điều hiển nhiên."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_4
msgid "OFFER15"
msgstr "OFFER15"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"On each order the customer will earn 5 points and that will be added into "
"their balance and when the customer reaches the points up to 200 then in "
"exchange that the customer can claim 5 % discount."
msgstr ""
"Với mỗi đơn hàng, khách hàng sẽ kiếm được 5 điểm và số điểm đó sẽ được cộng "
"vào số dư của họ và khi khách hàng đạt tới 200 điểm thì họ có thể nhận giảm "
"giá 5%."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Our Mission"
msgstr "Sứ mệnh của chúng tôi"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_pink
msgid "Pink"
msgstr "Hồng"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_25
msgid "Pink Floral Print Top"
msgstr "Áo hoa màu hồng"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_27
msgid "Pink Round Neck Printed T-shirt"
msgstr "Áo thun in hình cổ tròn màu hồng"

#. module: clothing_boutique
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_2
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_4
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_5
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_7
msgid "Points"
msgstr "Điểm"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Previous"
msgstr "Trước đó"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_5
msgid "Purple"
msgstr "Tím"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_2
msgid "Red"
msgstr "Đỏ"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_28
msgid "Red Striped Jumpsuit"
msgstr "Jumpsuit sọc đỏ"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_34
msgid "Red Suit Set"
msgstr "Bộ suit đỏ"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_18
msgid "Ruffle Floral Print Top"
msgstr "Áo hoa cổ bèo"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Scan “HIGHJEAN001” Barcode number from the search bar and select the “High "
"Waist Jeans” product and customer."
msgstr ""
"Quét mã vạch “HIGHJEAN001” từ thanh tìm kiếm rồi chọn sản phẩm “Quần jean "
"cạp cao” và khách hàng."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Send RFQ to your Pre defined Vendor."
msgstr "Gửi YCBG đến Nhà cung cấp đã được xác định trước của bạn."

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_3
msgid "Size"
msgstr "Kích thước"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_55
msgid "Skinny Jeans"
msgstr "Quần jeans bó"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_5
msgid "Suit sets"
msgstr "Bộ vest"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_20
msgid "T-shirt "
msgstr "Áo phông"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_6
msgid "T-shirts"
msgstr "Áo phông"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "The Glam Boutique"
msgstr "Cửa Hàng Thời Trang Sang Trọng"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "The Orchid Concept"
msgstr "The Orchid Concept"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "There are always flowers <br/>for those who want to see them."
msgstr "Hoa luôn nở <br/>cho người muốn ngắm nhìn."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"There is a Promotion Program created  in which the reward for Free Product "
"has been defined."
msgstr ""
"Có một Chương trình khuyến mại được tạo ra, trong đó phần thưởng cho Sản "
"phẩm miễn phí đã được xác định."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"This setup is for Clothing and Boutique retail shops which encompasses the "
"creation, distribution, and retail of clothing and fashion accessories to a "
"specific segment of people."
msgstr ""
"Thiết lập ngành này dành cho các cửa hàng bán lẻ quần áo và cửa hàng thời "
"trang bao gồm việc thiết kế, phân phối cũng như bán lẻ quần áo và phụ kiện "
"thời trang cho một phân khúc khách hàng cụ thể."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Through Purchase Application <font class=\"text-o-color-5\">create an "
"RFQ</font> (Request for quotation) with Product “White Flared Dress”"
msgstr ""
"Thông qua ứng dụng Mua hàng, <font class=\"text-o-color-5\">tạo YCBG</font> "
"(Yêu cầu báo giá) với Sản phẩm “Váy xòe trắng”"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"To do so, we collaborate with local producers who care about the environment"
" and cultivate their flowers with love."
msgstr ""
"Để làm được như vậy, chúng tôi hợp tác với những nhà sản xuất địa phương "
"quan tâm đến môi trường và trồng hoa bằng tình yêu thương."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_10
msgid "Tops"
msgstr "Hàng đầu"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_8
msgid "Tops and Tunics"
msgstr "Áo và Tunic"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_35
msgid "Tunic Top"
msgstr "Áo tunic"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Very nice, colourful shop with many choices in the choir of a very pretty "
"town."
msgstr ""
"Có vô số lựa chọn trong một cửa hàng đẹp đẽ và rực rỡ sắc màu nằm tại một "
"thị trấn rất xinh đẹp."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"We use 100% recycled materials. We believe the best gifts should also do "
"good for the planet."
msgstr ""
"Chúng tôi sử dụng 100% vật liệu tái chế. Chúng tôi tin rằng những món quà "
"tốt nhất cũng nên mang lại lợi ích cho hành tinh."

#. module: clothing_boutique
#: model_terms:web_tour.tour,rainbow_man_message:clothing_boutique.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Chào mừng bạn! Chúc bạn một chuyến khám phá bổ ích!"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_2
msgid "Western wear"
msgstr "Đồ Âu"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_4
msgid "White"
msgstr "Trắng"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_29
msgid "White Flared Dress"
msgstr "Váy xoè trắng"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_30
msgid "White Kurta set with Heavy Thread Work Dupatta (Set Of 3)"
msgstr "Bộ Kurta trắng kèm khăn choàng Dupatta dày (Bộ 3 món)"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_1
msgid "Wine Red"
msgstr "Đỏ rượu"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_31
msgid "Women Solid Black Casual Jacket"
msgstr "Áo khoác nữ trơn màu đen thường ngày"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_yellow
msgid "Yellow"
msgstr "Vàng"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"You can also search the Barcode number of that product from the POS session "
"to recognise that product."
msgstr ""
"Bạn cũng có thể tìm kiếm số mã vạch của sản phẩm đó từ phiên POS để nhận "
"diện sản phẩm."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "assorted-color hanged clothes during daytime"
msgstr "quần áo đủ màu sắc treo vào ban ngày"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "white and brown floral long sleeve shirt"
msgstr "áo sơ mi dài tay họa tiết hoa màu trắng và nâu"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "woman in black and white dress holding happy birthday signage"
msgstr "người phụ nữ mặc váy đen trắng cầm biển hiệu chúc mừng sinh nhật"
