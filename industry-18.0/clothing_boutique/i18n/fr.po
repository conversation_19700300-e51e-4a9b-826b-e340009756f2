# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* clothing_boutique
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:27+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_51
msgid "10% on your NEXT ORDER !!"
msgstr "10% de réduction sur votre PROCHAINE COMMANDE !!"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_4
#: model:product.template,name:clothing_boutique.product_template_52
msgid "15% on your order"
msgstr "15% de remise sur votre commande"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_2
#: model:product.template,name:clothing_boutique.product_template_50
msgid "20% on your order"
msgstr "20% de réduction sur votre commande"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_5
msgid "5% on your order"
msgstr "5% de réduction sur votre commande"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_61
msgid "5% on your order Loyalty"
msgstr "5% de remise de fidélité sur votre commande"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_56
msgid "5% on your order for the loyalty points earned."
msgstr ""
"remise de 5% sur votre commande grâce aux points de fidélité accumulés."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<br/>Deliver flowers to live up to your dedication for your loved ones."
msgstr "<br/>Livrez des fleurs à la hauteur de votre amour pour vos proches."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Suivant</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Précédent</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO de "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO de "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO de "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<span style=\"font-size: 36px;\">The Glam Boutique</span>"
msgstr "<span style=\"font-size: 36px;\">La Boutique Glam</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Buy 1 Jeans Get 1 T-shirt Free</u>"
msgstr "<u>Achetez 1 jeans et recevez 1 tee-shirt gratuit</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Products with Barcode</u>"
msgstr "<u>Produits avec code-barres</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A small shop with a very large selection of roses. Nice prices and above all"
" a very professional and friendly welcome."
msgstr ""
"Une petite boutique avec un très grand choix de roses. Des prix intéressants"
" et surtout un accueil très professionnel et sympathique."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A wide range of high quality products and a warm welcome! I recommend this "
"shop 200%!"
msgstr ""
"Une large variété de produits de haute qualité et un accueil chaleureux ! Je"
" recommande cette boutique à 200 % !"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Add any “Jumpsuit” from the western wear category."
msgstr ""
"Ajoutez n'importe quelle \"Combinaison\" de la catégorie vêtements "
"occidentaux."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add the Barcode number in the Product Master and take a print to scan them "
"during the POS session."
msgstr ""
"Ajoutez le numéro de code-barres dans la fiche produit et imprimez-le pour "
"le scanner lors de la session PdV."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add to cart the selected product and 20% Discount will be applied and "
"further process checkout, set the customer with the shipping address and "
"make the payment."
msgstr ""
"Ajoutez le produit sélectionné au panier, la remise de 20 % sera appliquée. "
"Passez ensuite en caisse, indiquez le client et l'adresse de livraison, puis"
" effectuez le paiement."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_9
msgid "Aqua Blue Top and Skirt"
msgstr "Top et jupe bleu Aqua"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"As we use local growers, our flowers last longer and smell better. Just like"
" the event you want to celebrate with flowers!"
msgstr ""
"Comme nous faisons appel à des producteurs locaux, nos fleurs durent plus "
"longtemps et sentent meilleur. Tout comme l'événement que vous souhaitez "
"célébrer avec des fleurs !"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_32
msgid "Assymetric kurta With Inner And Trousers"
msgstr "Kurta assymétrique avec partie interne et pantalon"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"BOGO Offer (Buy one Get one Free) <br/>\n"
"          Promotions are popular among retailers because they can increase sales of the products being promoted. Plus, it will attract attention to other items in the store, which is great when you need to move dead stock or boost sales.\n"
"          <br/>"
msgstr ""
"Offre 1+1 Gratuit <br/>\n"
"          Les promotions sont très appréciées des commerçants car elles permettent d'augmenter les ventes des produits en question. De plus, elles attirent l'attention sur d'autres articles du magasin, ce qui est très utile pour écouler les stocks dormants ou stimuler les ventes.\n"
"          <br/>"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_8
msgid "Baby Pink Printed Maxi Dress"
msgstr "Robe longue rose à imprimés"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Bio Flowers"
msgstr "Fleurs bio"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_black
msgid "Black"
msgstr "Noir"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_10
msgid "Black Leather Wide Leg trouser"
msgstr "Pantalon large en cuir noir"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_11
msgid "Black Top"
msgstr "Haut noir"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_blue
msgid "Blue"
msgstr "Bleu"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_15
msgid "Blue Sequin Suit (Set of 3)"
msgstr "Costume à sequins bleue (ensemble de 3)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_12
msgid "Blue Solid Pants"
msgstr "Pantalon uni bleu"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_7
msgid "Bottom pants and Trousers"
msgstr "Dessous et pantalons"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Business Flows:"
msgstr "Flux d'activités :"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_7
msgid "Buy 1 jeans get 1 T-shirt Free"
msgstr "Achetez 1 jeans et recevez 1 tee-shirt gratuit"

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_1
msgid "Cash"
msgstr "Espèces"

#. module: clothing_boutique
#: model:account.journal,name:clothing_boutique.cash
msgid "Cash (Clothing boutique)"
msgstr "Espèces (magasin de vêtements)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_13
msgid "Chiffon Jumpsuit"
msgstr "Combinaison en mousseline"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Click on the reward button and the system will display the \"Buy 1 Get 1 "
"Free Product T-Shirt (Purple)”. You can see the free product is added along "
"in the point of sale order."
msgstr ""
"Cliquez sur le bouton de récompense pour que le système affiche le tee-shirt"
" \"1 + 1 Gratuit Tee-Shirt (Mauve)”. Le produit gratuit sera ajouté à la "
"commande du point de vente."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_11
msgid "Co ords sets"
msgstr "Ensembles assortis"

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_2
msgid "Colour"
msgstr "Couleur"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Confirm the RFQ based on the received rates by the vendor."
msgstr ""
"Confirmez la demande de prix sur base des tarifs reçus par le vendeur."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Contact us"
msgstr "Contactez-nous"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Continue the payment Process further with any of the payment methods and "
"validate."
msgstr ""
"Poursuivez le processus de paiement avec un des modes de paiement proposés "
"puis validez."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_14
msgid "Cream Oversized T-Shirt"
msgstr "Tee-shirt de couleur crème et coupe large"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Create a <font class=\"text-o-color-5\">POS order</font> through POS "
"Application"
msgstr ""
"Créez une <font class=\"text-o-color-5\">commande PdV</font> avec "
"l'application PdV"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Create a POS order through POS Application, select any customer"
msgstr ""
"Créez une commande PdV avec l'application PdV, sélectionnez n'importe quel "
"client."

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_2
msgid "Customer Account"
msgstr "Compte client"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_blue
msgid "Dark Blue"
msgstr "Bleu foncé"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_pink
msgid "Dark pink"
msgstr "Rose foncé"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_16
msgid "Denim Jacket"
msgstr "Veste en jeans"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_33
msgid "Designer Ethnic wear"
msgstr "Vêtements de créateur et de style ethnique"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_17
msgid "Designer Kurti"
msgstr "Kurti de créateur"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Discover more"
msgstr "Découvrir plus"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Don't offer pesticides to your beloved one! By choosing our flowers, we "
"guarantee you organic flowers."
msgstr ""
"N'offrez pas de pesticides à votre bien-aimé(e) ! Choisissez nos fleurs, car"
" nous vous garantissons des fleurs biologiques."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Eco-Friendly Packaging"
msgstr "Emballage écologique"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_4
msgid "Ethnic Dresses"
msgstr "Robes de style ethnique"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 1: Purchase"
msgstr "Flux 1 : Achat"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 2: POS with Barcode and BOGO offer."
msgstr "Flux 2 : PdV avec code-barres et offre 1+1 Gratuit."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 3: POS → Loyalty points"
msgstr "Flux 3 : PdV → LPoints de fidélité"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 4 : Website → Seasonal discount → Monsoon Discount offer."
msgstr "Flux 4 : Site Web → Remise saisonnière → Promotion Monsoon"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_59
msgid "Free Product - Discount"
msgstr "Produit gratuit - Réduction"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_58
#: model:product.template,name:clothing_boutique.product_template_60
msgid "Free Product - T-shirt "
msgstr "Produit gratuit - Tee-shirt "

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_9
msgid "Free Product - T-shirt  (Purple)"
msgstr "Produit gratuit - Tee-shirt (violet)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_57
msgid "Free shipping"
msgstr "Expédition gratuite"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"From your local producer to your door, discover how we deliver our multiple "
"sorts of flowers and bring you happiness."
msgstr ""
"De notre producteur local au pas de votre porte, découvrez comment nous "
"livrons nos multiples sortes de fleurs et vous apportons du bonheur."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_21
msgid "Full sleeve jumpsuit"
msgstr "Combinaison à manches longues"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Go to the Website Application and select Top from the Western wear category."
msgstr ""
"Allez dans l'application Site web et sélectionnez Haut dans la catégorie "
"Vêtements occidentaux."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_22
msgid "Heart T -shirt"
msgstr "Tee -shirt cœur"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here Promotion Program has been used for allocating the loyalty points on "
"order and discount while claiming."
msgstr ""
"Ici, le programme de promotion a été utilisé pour attribuer des points de "
"fidélité lors de la commande et des remises sur demande."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here promotion program has been used  to define the discount on a particular"
" product category."
msgstr ""
"Ici, le programme de promotion a été utilisé pour définir la remise sur une "
"catégorie de produits en particulier."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_53
msgid "High Waist Jeans"
msgstr "Jeans taille haute"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_1
msgid "Indian Wear"
msgstr "Vêtements indiens"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_3
msgid "Jackets"
msgstr "Vestes"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_12
msgid "Jeans"
msgstr "Jeans"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_9
msgid "Jumpsuits"
msgstr "Combinaisons"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_26
msgid "Kurta set"
msgstr "Ensemble kurta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Learn more"
msgstr "En savoir plus"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_light_blue
msgid "Light blue"
msgstr "Bleu clair"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_23
msgid "Linen Wide Leg Pant"
msgstr "Pantalon large en lin"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Live life in full bloom"
msgstr "Vivre la vie en pleine floraison"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Local Producers"
msgstr "Producteurs locaux"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_54
msgid "Loose Jeans"
msgstr "Jeans ample"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_5
msgid "Loyalty points"
msgstr "Points de fidélité"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Loyalty points are given to customer on their purchase &amp; later on the "
"customer can redeem discount in exchange of loyalty points"
msgstr ""
"Le client reçoit des points de fidélité lors de son achat et peut ensuite "
"échanger ses points de fidélité contre une remise."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_2
msgid "Mega Discount Offer on Tops!!"
msgstr "Réduction exceptionnelle sur les hauts !"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_24
msgid "Mustard yellow Kurta with Net Dupatta"
msgstr "Kurta jaune moutarde avec Dupatta en maille"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Next"
msgstr "Suivant"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Nowadays, it has become almost a practice for companies to offer at least "
"one or more seasonal discounts to customers at any particular time of the "
"year like: New Year's Eve discount, winter sale, Christmas sale, summer "
"sale, etc."
msgstr ""
"De nos jours, il est presque devenu habituel pour les entreprises d'offrir "
"au moins une ou plusieurs remises saisonnières à leurs clients à un moment "
"donné de l'année : remise de fin d'année, soldes d'hiver, soldes de Noël, "
"soldes d'été, etc."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_4
msgid "OFFER15"
msgstr "OFFER15"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"On each order the customer will earn 5 points and that will be added into "
"their balance and when the customer reaches the points up to 200 then in "
"exchange that the customer can claim 5 % discount."
msgstr ""
"À chaque commande, le client gagne 5 points qui sont ajoutés à son solde. "
"Lorsqu'il atteint 200 points, il peut bénéficier de 5 % de réduction."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Our Mission"
msgstr "Notre mission"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_pink
msgid "Pink"
msgstr "Rose"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_25
msgid "Pink Floral Print Top"
msgstr "Top rose à imprimé floral"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_27
msgid "Pink Round Neck Printed T-shirt"
msgstr "T-shirt imprimé rose à col rond"

#. module: clothing_boutique
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_2
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_4
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_5
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_7
msgid "Points"
msgstr "Points"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Previous"
msgstr "Précédent"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_5
msgid "Purple"
msgstr "Violet"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_2
msgid "Red"
msgstr "Rouge"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_28
msgid "Red Striped Jumpsuit"
msgstr "Combinaison rayée rouge"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_34
msgid "Red Suit Set"
msgstr "Ensemble costume rouge"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_18
msgid "Ruffle Floral Print Top"
msgstr "Top à imprimé floral à volants"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Scan “HIGHJEAN001” Barcode number from the search bar and select the “High "
"Waist Jeans” product and customer."
msgstr ""
"Scannez le numéro de code-barres \"HIGHJEAN001\" dans la barre de recherche "
"et sélectionnez le produit \"Jeans taille haute\" et le client."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Send RFQ to your Pre defined Vendor."
msgstr "Envoyez une demande de prix à votre fournisseur prédéfini."

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_3
msgid "Size"
msgstr "Taille"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_55
msgid "Skinny Jeans"
msgstr "Jeans moulant"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_5
msgid "Suit sets"
msgstr "Costumes"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_20
msgid "T-shirt "
msgstr "Tee-shirt "

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_6
msgid "T-shirts"
msgstr "T-shirts"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "The Glam Boutique"
msgstr "La Glam Boutique"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "The Orchid Concept"
msgstr "L'Orchid Concept"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "There are always flowers <br/>for those who want to see them."
msgstr "Il y a toujours des fleurs <br/>pour ceux qui veulent les voir."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"There is a Promotion Program created  in which the reward for Free Product "
"has been defined."
msgstr ""
"Un programme de promotion a été mis en place, qui prévoit l'attribution d'un"
" produit gratuit en récompense."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"This setup is for Clothing and Boutique retail shops which encompasses the "
"creation, distribution, and retail of clothing and fashion accessories to a "
"specific segment of people."
msgstr ""
"Cette configuration concerne les magasins de vêtements et les boutiques. "
"Ceci comprend la création, la distribution et la vente au détail de "
"vêtements et d'accessoires de mode à un public spécifique."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Through Purchase Application <font class=\"text-o-color-5\">create an "
"RFQ</font> (Request for quotation) with Product “White Flared Dress”"
msgstr ""
"Dans l'application Achats, <font class=\"text-o-color-5\">créez une demande "
"de prix</font> pour le produit \"Robe évasée blanche\"."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"To do so, we collaborate with local producers who care about the environment"
" and cultivate their flowers with love."
msgstr ""
"Nous collaborons donc avec des producteurs locaux qui sont soucieux de "
"l'environnement et qui cultivent leurs fleurs avec amour."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_10
msgid "Tops"
msgstr "Hauts"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_8
msgid "Tops and Tunics"
msgstr "Hauts et tuniques"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_35
msgid "Tunic Top"
msgstr "Haut tunique"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Very nice, colourful shop with many choices in the choir of a very pretty "
"town."
msgstr ""
"Très belle boutique colorée avec beaucoup de choix au cœur d'une très jolie "
"ville."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"We use 100% recycled materials. We believe the best gifts should also do "
"good for the planet."
msgstr ""
"Nous utilisons des matériaux 100 % recyclés. Nous pensons que les plus beaux"
" cadeaux sont également bons pour la planète."

#. module: clothing_boutique
#: model_terms:web_tour.tour,rainbow_man_message:clothing_boutique.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Bienvenue ! Amusez-vous en explorant."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_2
msgid "Western wear"
msgstr "Vêtements occidentaux"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_4
msgid "White"
msgstr "Blanc"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_29
msgid "White Flared Dress"
msgstr "Robe évasée blanche"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_30
msgid "White Kurta set with Heavy Thread Work Dupatta (Set Of 3)"
msgstr ""
"Ensemble Kurta blanc avec Dupatta avec finition fil lourd (ensemble 3 "
"pièces)"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_1
msgid "Wine Red"
msgstr "Bordeaux"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_31
msgid "Women Solid Black Casual Jacket"
msgstr "Veste décontractée unie noire pour femmes"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_yellow
msgid "Yellow"
msgstr "Jaune"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"You can also search the Barcode number of that product from the POS session "
"to recognise that product."
msgstr ""
"Vous pouvez également rechercher le numéro de code-barres de ce produit "
"depuis la session PdV pour identifier ce produit."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "assorted-color hanged clothes during daytime"
msgstr "vêtements suspendus de couleurs assorties, pendant la journée"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "white and brown floral long sleeve shirt"
msgstr "chemise à manches longues à fleurs blanches et brunes"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "woman in black and white dress holding happy birthday signage"
msgstr ""
"femme en robe noire et blanche tenant un panneau « Joyeux anniversaire »."
