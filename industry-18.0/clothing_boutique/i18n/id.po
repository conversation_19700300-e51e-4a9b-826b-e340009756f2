# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* clothing_boutique
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:27+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_51
msgid "10% on your NEXT ORDER !!"
msgstr "10% on your NEXT ORDER !!"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_4
#: model:product.template,name:clothing_boutique.product_template_52
msgid "15% on your order"
msgstr "15% pada order Anda"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_2
#: model:product.template,name:clothing_boutique.product_template_50
msgid "20% on your order"
msgstr "20% on your order"

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_5
msgid "5% on your order"
msgstr "5% on your order"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_61
msgid "5% on your order Loyalty"
msgstr "5% on your order Loyalty"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_56
msgid "5% on your order for the loyalty points earned."
msgstr "5% on your order for the loyalty points earned."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<br/>Deliver flowers to live up to your dedication for your loved ones."
msgstr ""
"<br/>Deliver flowers to live up to your dedication for your loved ones."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                <span class=\"visually-hidden\">Next</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                <span class=\"visually-hidden\">Previous</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO dari "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO dari "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO dari "
"MyCompany</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<span style=\"font-size: 36px;\">The Glam Boutique</span>"
msgstr "<span style=\"font-size: 36px;\">The Glam Boutique</span>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Buy 1 Jeans Get 1 T-shirt Free</u>"
msgstr "<u>Beli 1 Jean Dapatkan 1 T-shirt Gratis</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "<u>Products with Barcode</u>"
msgstr "<u>Produk dengan Barcode</u>"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A small shop with a very large selection of roses. Nice prices and above all"
" a very professional and friendly welcome."
msgstr ""
"A small shop with a very large selection of roses. Nice prices and above all"
" a very professional and friendly welcome."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"A wide range of high quality products and a warm welcome! I recommend this "
"shop 200%!"
msgstr ""
"A wide range of high quality products and a warm welcome! I recommend this "
"shop 200%!"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Add any “Jumpsuit” from the western wear category."
msgstr "Tambahkan  “Jumpsuit” apa pun dari kategori western wear."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add the Barcode number in the Product Master and take a print to scan them "
"during the POS session."
msgstr ""
"Tambahkan nomor Barcode di Product Master dan ambil print untuk discan pada "
"sesi POS."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Add to cart the selected product and 20% Discount will be applied and "
"further process checkout, set the customer with the shipping address and "
"make the payment."
msgstr ""
"Tambahkan ke keranjang produk terpilih dan Diskon 20% akan diterapkan. "
"Lanjutkan proses checkout, tetapkan pelanggan dengan alamat pengiriman dan "
"lakukan pembayaran."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_9
msgid "Aqua Blue Top and Skirt"
msgstr "Aqua Blue Top and Skirt"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"As we use local growers, our flowers last longer and smell better. Just like"
" the event you want to celebrate with flowers!"
msgstr ""
"As we use local growers, our flowers last longer and smell better. Just like"
" the event you want to celebrate with flowers!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_32
msgid "Assymetric kurta With Inner And Trousers"
msgstr "Assymetric kurta With Inner And Trousers"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"BOGO Offer (Buy one Get one Free) <br/>\n"
"          Promotions are popular among retailers because they can increase sales of the products being promoted. Plus, it will attract attention to other items in the store, which is great when you need to move dead stock or boost sales.\n"
"          <br/>"
msgstr ""
"Tawaran BOGO (Buy one Get one Free) <br/>\n"
"          Promosi populer di antara retailer karena mereka dapat meningkatkan sales produk yang dipromosikan. Terlebih lagi, hal tersebut akan meningkatkan perhatian ke barang-barang lain di toko, yang merupakan hal bagus saat Anda harus memindahkan stok mati atau mendorong sales.\n"
"          <br/>"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_8
msgid "Baby Pink Printed Maxi Dress"
msgstr "Baby Pink Printed Maxi Dress"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Bio Flowers"
msgstr "Bio Flowers"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_black
msgid "Black"
msgstr "Hitam"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_10
msgid "Black Leather Wide Leg trouser"
msgstr "Black Leather Wide Leg trouser"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_11
msgid "Black Top"
msgstr "Black Top"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_blue
msgid "Blue"
msgstr "Biru"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_15
msgid "Blue Sequin Suit (Set of 3)"
msgstr "Blue Sequin Suit (Set of 3)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_12
msgid "Blue Solid Pants"
msgstr "Blue Solid Pants"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_7
msgid "Bottom pants and Trousers"
msgstr "Bottom pants and Trousers"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Business Flows:"
msgstr "Alur Bisnis:"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_7
msgid "Buy 1 jeans get 1 T-shirt Free"
msgstr "Buy 1 jeans get 1 T-shirt Free"

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_1
msgid "Cash"
msgstr "Cash"

#. module: clothing_boutique
#: model:account.journal,name:clothing_boutique.cash
msgid "Cash (Clothing boutique)"
msgstr "Cash (Clothing boutique)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_13
msgid "Chiffon Jumpsuit"
msgstr "Chiffon Jumpsuit"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Click on the reward button and the system will display the \"Buy 1 Get 1 "
"Free Product T-Shirt (Purple)”. You can see the free product is added along "
"in the point of sale order."
msgstr ""
"Klik pada tombol hadiah dan sistem akan menampilkan \"Produk Buy 1 Get 1 "
"Free T-Shirt (Ungu)”. Anda dapat melihat produk gratis ditambahkan di "
"pesanan POS."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_11
msgid "Co ords sets"
msgstr "Co ords sets"

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_2
msgid "Colour"
msgstr "Colour"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Confirm the RFQ based on the received rates by the vendor."
msgstr "Konfirmasi RFQ berdasarkan harga yang diterima oleh vendor."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Contact us"
msgstr "Hubungi kami"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Continue the payment Process further with any of the payment methods and "
"validate."
msgstr ""
"Lanjutkan Proses pembayaran lebih lanjut dengan metode pembayaran apa pun "
"dan validasi."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_14
msgid "Cream Oversized T-Shirt"
msgstr "Cream Oversized T-Shirt"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Create a <font class=\"text-o-color-5\">POS order</font> through POS "
"Application"
msgstr ""
"Buat <font class=\"text-o-color-5\">pesanan POS</font> melalui Aplikasi POS"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Create a POS order through POS Application, select any customer"
msgstr "Buat pesanan POS melalui Aplikasi POS, lalu pilih pelanggan apa pun."

#. module: clothing_boutique
#: model:pos.payment.method,name:clothing_boutique.pos_payment_method_2
msgid "Customer Account"
msgstr "Akun Pelanggan"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_blue
msgid "Dark Blue"
msgstr "Dark Blue"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_dark_pink
msgid "Dark pink"
msgstr "Dark pink"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_16
msgid "Denim Jacket"
msgstr "Denim Jacket"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_33
msgid "Designer Ethnic wear"
msgstr "Designer Ethnic wear"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_17
msgid "Designer Kurti"
msgstr "Designer Kurti"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Discover more"
msgstr "Temukan lebih lanjut"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Don't offer pesticides to your beloved one! By choosing our flowers, we "
"guarantee you organic flowers."
msgstr ""
"Don't offer pesticides to your beloved one! By choosing our flowers, we "
"guarantee you organic flowers."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Eco-Friendly Packaging"
msgstr "Eco-Friendly Packaging"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_4
msgid "Ethnic Dresses"
msgstr "Ethnic Dresses"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 1: Purchase"
msgstr "Alur 1: Purchase"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 2: POS with Barcode and BOGO offer."
msgstr "Alur 2: POS dengan Barcode dan tawaran BOGO."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 3: POS → Loyalty points"
msgstr "Alur 3: POS → Poin loyalitas"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Flow 4 : Website → Seasonal discount → Monsoon Discount offer."
msgstr "Alur 4 : Website → Diskon musiman → Tawaran Diskon Musim Hujan."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_59
msgid "Free Product - Discount"
msgstr "Free Product - Discount"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_58
#: model:product.template,name:clothing_boutique.product_template_60
msgid "Free Product - T-shirt "
msgstr "Free Product - T-shirt "

#. module: clothing_boutique
#: model:loyalty.reward,description:clothing_boutique.loyalty_reward_9
msgid "Free Product - T-shirt  (Purple)"
msgstr "Free Product - T-shirt  (Purple)"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_57
msgid "Free shipping"
msgstr "Pengiriman grati"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"From your local producer to your door, discover how we deliver our multiple "
"sorts of flowers and bring you happiness."
msgstr ""
"From your local producer to your door, discover how we deliver our multiple "
"sorts of flowers and bring you happiness."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_21
msgid "Full sleeve jumpsuit"
msgstr "Full sleeve jumpsuit"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Go to the Website Application and select Top from the Western wear category."
msgstr "Kunjungi Aplikasi Website dan pilih Atasan dari kategori Baju Barat."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_22
msgid "Heart T -shirt"
msgstr "Heart T -shirt"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here Promotion Program has been used for allocating the loyalty points on "
"order and discount while claiming."
msgstr ""
"Di sini Program Promosi telah digunakan untuk mengalokasikan poin loyalitas "
"pada pesanan dan diskon selagi mengeklaim."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Here promotion program has been used  to define the discount on a particular"
" product category."
msgstr ""
"Berikut program promosi telah digunakan untuk mendefinisikan diskon pada "
"kategori produk tertentu."

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_53
msgid "High Waist Jeans"
msgstr "High Waist Jeans"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_1
msgid "Indian Wear"
msgstr "Indian Wear"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_3
msgid "Jackets"
msgstr "Jackets"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_12
msgid "Jeans"
msgstr "Jeans"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_9
msgid "Jumpsuits"
msgstr "Jumpsuits"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_26
msgid "Kurta set"
msgstr "Kurta set"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Learn more"
msgstr "Ketahui lebih lanjut"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_light_blue
msgid "Light blue"
msgstr "Biru Cerah"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_23
msgid "Linen Wide Leg Pant"
msgstr "Linen Wide Leg Pant"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Live life in full bloom"
msgstr "Live life in full bloom"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Local Producers"
msgstr "Local Producers"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_54
msgid "Loose Jeans"
msgstr "Loose Jeans"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_5
msgid "Loyalty points"
msgstr "Loyalty points"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Loyalty points are given to customer on their purchase &amp; later on the "
"customer can redeem discount in exchange of loyalty points"
msgstr ""
"Poin loyalitas diberikan ke pelangan pada pembelian mereka & nanti pelanggan"
" dapat menebus diskon sebagai ganti poin loyalitas"

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_2
msgid "Mega Discount Offer on Tops!!"
msgstr "Mega Discount Offer on Tops!!"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_24
msgid "Mustard yellow Kurta with Net Dupatta"
msgstr "Mustard yellow Kurta with Net Dupatta"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Next"
msgstr "Next"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Nowadays, it has become almost a practice for companies to offer at least "
"one or more seasonal discounts to customers at any particular time of the "
"year like: New Year's Eve discount, winter sale, Christmas sale, summer "
"sale, etc."
msgstr ""
"Akhir-akhir ini, merupakan praktek standar bagi perusahaan untuk menawarkan "
"setidaknya satu atau lebih diskon musiman ke pelanggan pada waktu tertentu "
"seperti: diskon malam tahun baru, Christmas sale, musim panas, dsb."

#. module: clothing_boutique
#: model:loyalty.program,name:clothing_boutique.loyalty_program_4
msgid "OFFER15"
msgstr "OFFER15"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"On each order the customer will earn 5 points and that will be added into "
"their balance and when the customer reaches the points up to 200 then in "
"exchange that the customer can claim 5 % discount."
msgstr ""
"Pada setiap pesanan pelanggan akan menerima 5 poin yang akan ditambahkan ke "
"saldo mereka dan ketika pelanggan mencapai 200 poin pelanggan dapat "
"menukarkannya untuk diskon 5%."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Our Mission"
msgstr "Misi Kami"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_pink
msgid "Pink"
msgstr "Pink"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_25
msgid "Pink Floral Print Top"
msgstr "Pink Floral Print Top"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_27
msgid "Pink Round Neck Printed T-shirt"
msgstr "Pink Round Neck Printed T-shirt"

#. module: clothing_boutique
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_2
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_4
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_5
#: model:loyalty.program,portal_point_name:clothing_boutique.loyalty_program_7
msgid "Points"
msgstr "Poin-Poin"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "Previous"
msgstr "Sebelum"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_5
msgid "Purple"
msgstr "Ungu"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_2
msgid "Red"
msgstr "Merah"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_28
msgid "Red Striped Jumpsuit"
msgstr "Red Striped Jumpsuit"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_34
msgid "Red Suit Set"
msgstr "Red Suit Set"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_18
msgid "Ruffle Floral Print Top"
msgstr "Ruffle Floral Print Top"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Scan “HIGHJEAN001” Barcode number from the search bar and select the “High "
"Waist Jeans” product and customer."
msgstr ""
"Scan nomor Barcode “HIGHJEAN001” dari kolom pencarian dan pilih produk dan "
"pelanggan “High Waist Jeans”."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "Send RFQ to your Pre defined Vendor."
msgstr "Kirim RFQ ke vendor yang sudah didefinisikan sebelumnya."

#. module: clothing_boutique
#: model:product.attribute,name:clothing_boutique.product_attribute_3
msgid "Size"
msgstr "Ukuran"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_55
msgid "Skinny Jeans"
msgstr "Skinny Jeans"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_5
msgid "Suit sets"
msgstr "Suit sets"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_20
msgid "T-shirt "
msgstr "T-shirt "

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_6
msgid "T-shirts"
msgstr "T-Shirt"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid "The Glam Boutique"
msgstr "The Glam Boutique"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "The Orchid Concept"
msgstr "The Orchid Concept"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "There are always flowers <br/>for those who want to see them."
msgstr "There are always flowers <br/>for those who want to see them."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"There is a Promotion Program created  in which the reward for Free Product "
"has been defined."
msgstr ""
"Terdapat Program Promosi yang dibuat di mana hadiah untuk Produk Gratis "
"telah didefinisikan."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"This setup is for Clothing and Boutique retail shops which encompasses the "
"creation, distribution, and retail of clothing and fashion accessories to a "
"specific segment of people."
msgstr ""
"Setup ini untuk toko retail Baju dan Butik yang mencakup pembuatan, "
"distribusi, dan retail toko dan aksesoris fashion untuk segmen spesifik."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"Through Purchase Application <font class=\"text-o-color-5\">create an "
"RFQ</font> (Request for quotation) with Product “White Flared Dress”"
msgstr ""
"Melalui Aplikasi Purchase <font class=\"text-o-color-5\">buat RFQ</font> "
"(Permintaan quotation) dengan Produk “White Flared Dress”"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"To do so, we collaborate with local producers who care about the environment"
" and cultivate their flowers with love."
msgstr ""
"To do so, we collaborate with local producers who care about the environment"
" and cultivate their flowers with love."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_10
msgid "Tops"
msgstr "Tops"

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_8
msgid "Tops and Tunics"
msgstr "Tops and Tunics"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_35
msgid "Tunic Top"
msgstr "Tunic Top"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"Very nice, colourful shop with many choices in the choir of a very pretty "
"town."
msgstr ""
"Very nice, colourful shop with many choices in the choir of a very pretty "
"town."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid ""
"We use 100% recycled materials. We believe the best gifts should also do "
"good for the planet."
msgstr ""
"We use 100% recycled materials. We believe the best gifts should also do "
"good for the planet."

#. module: clothing_boutique
#: model_terms:web_tour.tour,rainbow_man_message:clothing_boutique.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Welcome! Happy exploring."

#. module: clothing_boutique
#: model:product.public.category,name:clothing_boutique.product_public_category_2
msgid "Western wear"
msgstr "Western wear"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_4
msgid "White"
msgstr "Putih"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_29
msgid "White Flared Dress"
msgstr "White Flared Dress"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_30
msgid "White Kurta set with Heavy Thread Work Dupatta (Set Of 3)"
msgstr "White Kurta set with Heavy Thread Work Dupatta (Set Of 3)"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_1
msgid "Wine Red"
msgstr "Wine Red"

#. module: clothing_boutique
#: model:product.template,name:clothing_boutique.product_template_31
msgid "Women Solid Black Casual Jacket"
msgstr "Women Solid Black Casual Jacket"

#. module: clothing_boutique
#: model:product.attribute.value,name:clothing_boutique.product_attribute_value_yellow
msgid "Yellow"
msgstr "Kuning"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.welcome_article_body
msgid ""
"You can also search the Barcode number of that product from the POS session "
"to recognise that product."
msgstr ""
"Anda juga dapat mencari nomor Barcode produk tersebut dari sesi POS untuk "
"mengenali produk tersebut."

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "assorted-color hanged clothes during daytime"
msgstr "assorted-color hanged clothes during daytime"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "white and brown floral long sleeve shirt"
msgstr "white and brown floral long sleeve shirt"

#. module: clothing_boutique
#: model_terms:ir.ui.view,arch_db:clothing_boutique.homepage
msgid "woman in black and white dress holding happy birthday signage"
msgstr "woman in black and white dress holding happy birthday signage"
