<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="configurator_1_s_cover_default_image" model="ir.attachment">
        <field name="name">clothing_boutique.s_cover_default_image</field>
        <field name="datas" type="base64" file="clothing_boutique/static/src/binary/ir_attachment/403-website.s_cover_default_image"/>
        <field name="key">clothing_boutique.s_cover_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_578" model="ir.attachment">
         <field name="name">unsplash_KfqgYzoH3Vk_boutique.jpg</field>
         <field name="datas" type="base64" file="clothing_boutique/static/src/binary/ir_attachment/578-unsplash_KfqgYzoH3Vk_boutique.jpg"/>
         <field name="key">clothing_boutique.clothing_hanger_1</field>
         <field name="website_id" ref="website.default_website"/>
         <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_579" model="ir.attachment">
         <field name="name">unsplash_7F7kEHj72MQ_boutique.jpg</field>
         <field name="datas" type="base64" file="clothing_boutique/static/src/binary/ir_attachment/579-unsplash_7F7kEHj72MQ_boutique.jpg"/>
         <field name="key">clothing_boutique.clothing_hanger_2</field>
         <field name="website_id" ref="website.default_website"/>
         <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_581" model="ir.attachment">
        <field name="name">unsplash_Ysc3ra1Xvpw_boutique.jpg</field>
        <field name="datas" type="base64" file="clothing_boutique/static/src/binary/ir_attachment/581-unsplash_Ysc3ra1Xvpw_boutique.jpg"/>
        <field name="key">clothing_boutique.sale_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_three_columns_default_image_3" model="ir.attachment">
        <field name="name">clothing_boutique.s_three_columns_default_image_3</field>
        <field name="datas" type="base64" file="clothing_boutique/static/src/binary/ir_attachment/427-website.s_three_columns_default_image_3"/>
        <field name="key">clothing_boutique.s_three_columns_default_image_3</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_banner_default_image" model="ir.attachment">
        <field name="name">clothing_boutique.s_banner_default_image</field>
        <field name="datas" type="base64" file="clothing_boutique/static/src/binary/ir_attachment/413-website.s_banner_default_image"/>
        <field name="key">clothing_boutique.s_banner_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
</odoo>
