<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_8" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_9" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_10" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_11" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_12" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_13" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_14" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_15" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_16" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_17" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_18" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_20" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_21" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_22" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_23" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_24" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_25" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_26" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_27" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_28" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_29" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_30" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_31" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_32" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_33" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_34" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_35" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_53" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_54" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_55" model="product.template" >
        <field name="is_published" eval="True"/>
    </record>
</odoo>
