<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="loyalty_program_2" model="loyalty.program">
        <field name="name">Mega Discount Offer on Tops!!</field>
        <field name="trigger">auto</field>
        <field name="trigger_product_ids" eval="[(6, 0, [ref('product_product_11')])]"/>
        <field name="pos_ok" eval="False"/>
        <field name="date_from" eval="DateTime.now() + timedelta(weeks=-1)"/>
        <field name="date_to" eval="DateTime.now() + timedelta(weeks=3)"/>
        <field name="program_type">promotion</field>
    </record>
    <record id="loyalty_program_4" model="loyalty.program">
        <field name="name">OFFER15</field>
        <field name="program_type">coupons</field>
        <field name="trigger">with_code</field>
        <field name="pos_ok" eval="False"/>
        <field name="ecommerce_ok" eval="False"/>
    </record>
    <record id="loyalty_program_5" model="loyalty.program">
        <field name="name">Loyalty points</field>
        <field name="program_type">loyalty</field>
        <field name="applies_on">both</field>
        <field name="trigger">auto</field>
        <field name="portal_visible" eval="True"/>
        <field name="pos_config_ids" eval="[(6, 0, [ref('pos_config_glam')])]"/>
        <field name="is_nominative" eval="True"/>
        <field name="ecommerce_ok" eval="False"/>
        <field name="sale_ok" eval="False"/>
    </record>
    <record id="loyalty_program_7" model="loyalty.program">
        <field name="name">Buy 1 jeans get 1 T-shirt Free</field>
        <field name="program_type">buy_x_get_y</field>
        <field name="trigger">auto</field>
        <field name="pos_config_ids" eval="[(6, 0, [ref('pos_config_glam')])]"/>
    </record>
</odoo>
