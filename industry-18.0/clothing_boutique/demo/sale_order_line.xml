<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_line_1" model="sale.order.line" context="{'skip_task_generation': True, 'skip_purchase_generation': True}">
        <field name="order_id" ref="sale_order_1"/>
        <field name="product_id" ref="product_product_9"/>
        <field name="price_unit">35.0</field>
        <field name="product_uom_qty">1.0</field>
    </record>

    <record id="sale_order_line_2" model="sale.order.line" context="{'skip_task_generation': True, 'skip_purchase_generation': True}">
        <field name="order_id" ref="sale_order_1"/>
        <field name="product_id" ref="product_product_65"/>
        <field name="price_unit">-5.25</field>
        <field name="product_uom_qty">1.0</field>
        <field name="points_cost">1.0</field>
        <field name="reward_identifier_code">1489404745</field>
    </record>
    <record id="sale_order_line_4" model="sale.order.line" context="{'skip_task_generation': True, 'skip_purchase_generation': True}">
        <field name="order_id" ref="sale_order_2"/>
        <field name="product_id" ref="product_product_44"/>
        <field name="price_unit">96.0</field>
        <field name="product_uom_qty">1.0</field>
    </record>
</odoo>
