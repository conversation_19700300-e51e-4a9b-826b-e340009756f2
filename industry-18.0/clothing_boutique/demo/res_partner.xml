<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="res_partner_10" model="res.partner">
        <field name="name">Wholesale Fashion Square</field>
        <field name="is_company" eval="True" />
        <field name="website">http://wholesalefashionsquare.com</field>
        <field name="email"><EMAIL></field>
        <field name="city">Vernon</field>
        <field name="state_id" ref="base.state_us_5" />
        <field name="country_id" ref="base.us" />
        <field name="phone">******-811-4911</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/res_partner/10-image_1920" />
    </record>
    <record id="res_partner_11" model="res.partner">
        <field name="name">Lush Clothing</field>
        <field name="is_company" eval="True" />
        <field name="website">http://lushclothing.com</field>
        <field name="email"><EMAIL></field>
        <field name="street">South San Pedro Streets</field>
        <field name="city">Los Angeles</field>
        <field name="state_id" ref="base.state_us_5" />
        <field name="country_id" ref="base.us" />
        <field name="phone">******-748-8555</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/res_partner/11-image_1920" />
    </record>
    <record id="res_partner_12" model="res.partner">
        <field name="name">TASHA APPAREL WHOLESALE</field>
        <field name="is_company" eval="True" />
        <field name="website">http://tashaapparel.com</field>
        <field name="email"><EMAIL></field>
        <field name="street">McKinley Avenue</field>
        <field name="city">Los Angeles</field>
        <field name="state_id" ref="base.state_us_5" />
        <field name="country_id" ref="base.us" />
        <field name="phone">******-531-9398</field>
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/res_partner/12-image_1920" />
    </record>
    <record id="res_partner_13" model="res.partner">
        <field name="name">Aditi</field>
        <field name="email"><EMAIL></field>
        <field name="street">Sector 7 </field>
        <field name="city">Gandhinagar</field>
        <field name="state_id" ref="base.state_us_2" />
        <field name="country_id" ref="base.us" />
        <field name="zip">382007</field>
    </record>
    <record id="res_partner_14" model="res.partner">
        <field name="name">Ahana</field>
        <field name="street">Street 30</field>
        <field name="city">Chicago</field>
        <field name="state_id" ref="base.state_us_2" />
        <field name="country_id" ref="base.us" />
        <field name="zip">60007</field>
    </record>
    <record id="res_partner_15" model="res.partner">
        <field name="name">Olivia</field>
        <field name="street">street 49</field>
        <field name="city">Houston</field>
        <field name="state_id" ref="base.state_us_2" />
        <field name="country_id" ref="base.us" />
        <field name="zip">86556</field>
    </record>
    <record id="res_partner_16" model="res.partner">
        <field name="name">Emma</field>
        <field name="street">street 30</field>
        <field name="city">Houston</field>
        <field name="state_id" ref="base.state_us_aa" />
        <field name="country_id" ref="base.us" />
        <field name="zip">85001</field>
    </record>
    <record id="res_partner_17" model="res.partner">
        <field name="name">Amelia</field>
        <field name="country_id" ref="base.us" />
    </record>
    <record id="res_partner_18" model="res.partner">
        <field name="name">Sophia</field>
        <field name="country_id" ref="base.us" />
    </record>
    <record id="res_partner_19" model="res.partner">
        <field name="name">Isabella</field>
        <field name="country_id" ref="base.us" />
    </record>
    <record id="res_partner_20" model="res.partner">
        <field name="name">Georgina</field>
        <field name="country_id" ref="base.us" />
    </record>
    <record id="res_partner_21" model="res.partner">
        <field name="name">Inaya</field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_22" model="res.partner">
        <field name="name">Carla</field>
        <field name="street">street 2</field>
        <field name="city">Angeles</field>
        <field name="state_id" ref="base.state_us_1" />
        <field name="country_id" ref="base.us" />
        <field name="zip">85001</field>
    </record>
    <record id="res_partner_23" model="res.partner">
        <field name="name">Luna</field>
        <field name="country_id" ref="base.us" />
    </record>
    <record id="res_partner_24" model="res.partner">
        <field name="name">Kaira</field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_25" model="res.partner">
        <field name="name">Parul</field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_26" model="res.partner">
        <field name="name">Ramya</field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_27" model="res.partner">
        <field name="name">Saachi</field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_28" model="res.partner">
        <field name="name">Vineeta</field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_29" model="res.partner">
        <field name="name">Zoya</field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_7" model="res.partner">
        <field name="name">Masters fashion</field>
        <field name="is_company" eval="True" />
        <field name="email"><EMAIL></field>
        <field name="country_id" ref="base.us" />
    </record>
    <record id="res_partner_8" model="res.partner">
        <field name="name">Bombay Rayon</field>
        <field name="is_company" eval="True" />
        <field name="email"><EMAIL></field>
        <field name="country_id" ref="base.in" />
    </record>
    <record id="res_partner_9" model="res.partner">
        <field name="name">Wholesale Central</field>
        <field name="is_company" eval="True" />
        <field name="email"><EMAIL></field>
        <field name="country_id" ref="base.us" />
    </record>
    <record id="base.main_partner" model="res.partner">
        <field name="name">The Glam Boutique</field>
        <field name="is_company" eval="True" />
        <field name="website">http://<EMAIL></field>
        <field name="email"><EMAIL></field>
        <field name="state_id" ref="base.state_us_2" />
        <field name="country_id" ref="base.us" />
        <field name="image_1920" type="base64" file="clothing_boutique/static/src/binary/res_partner/1-image_1920" />
        <field name="is_published" eval="True" />
    </record>
</odoo>
