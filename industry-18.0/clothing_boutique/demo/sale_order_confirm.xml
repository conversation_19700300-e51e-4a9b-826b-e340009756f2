<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="sale.order" name="action_confirm" eval="[[ref('sale_order_1'), ref('sale_order_2')]]"/>
    <function model="stock.picking" name="button_validate">
        <value model="stock.picking" eval="(
            obj().env.ref('clothing_boutique.sale_order_1') + obj().env.ref('clothing_boutique.sale_order_2')
        ).picking_ids.ids"/>
    </function>
    <record id="sale_advance_payment_inv_1" model="sale.advance.payment.inv">
        <field name="advance_payment_method" >delivered</field>
        <field name="sale_order_ids" eval="[Command.set([ref('sale_order_1')])]"/>
    </record>
    <function name="create_invoices" model="sale.advance.payment.inv" eval="[[ref('sale_advance_payment_inv_1')]]"/>
</odoo>
