<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="ir.module.module" name="_theme_load" context="{'apply_new_theme': True}">
        <value model="ir.module.module" eval="obj().env['ir.module.module'].search([('name', '=', 'theme_orchid')]).ids"/>
        <value model="ir.module.module" eval="obj().env.ref('website.default_website')"/>
    </function>
    <function model="ir.ui.view" name="write">
        <value model="ir.ui.view" eval="obj().env['website'].with_context(website_id=obj().env.ref('website.default_website').id).viewref('website.homepage').id"/>
        <value model="ir.ui.view" eval="{'arch': obj().env.ref('clothing_boutique.homepage').arch}"/>
    </function>
    <function model="ir.ui.view" name="write">
        <value model="ir.ui.view" eval="obj().env['website'].with_context(website_id=obj().env.ref('website_sale.products_categories').id).viewref('website_sale.products_categories').id"/>
        <value model="ir.ui.view" eval="{'active': True}"/>
    </function>
    <function model="web_editor.assets" name="make_scss_customization">
        <value eval="'/website/static/src/scss/options/colors/user_color_palette.scss'"/>
        <value eval="{'o-color-1': '#D2A768', 'o-color-2': '#000000', 'o-color-3': '#E6E6E6', 'o-color-4': '#FFFFFF', 'o-color-5': '#9E7D4E', 'copyright-custom': 'rgba(0, 0, 0, 0.15)'}"/>
    </function>
</odoo>
