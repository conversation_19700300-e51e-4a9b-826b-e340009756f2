<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_supplierinfo_1" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_32"/>
        <field name="partner_id" ref="res_partner_7"/>
        <field name="price">70.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_10" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_template_20"/>
        <field name="partner_id" ref="res_partner_11"/>
    </record>
    <record id="product_supplierinfo_11" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_9"/>
        <field name="partner_id" ref="res_partner_8"/>
        <field name="price">10.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_12" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_13"/>
        <field name="partner_id" ref="res_partner_9"/>
        <field name="price">20.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_13" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_21"/>
        <field name="partner_id" ref="res_partner_9"/>
        <field name="price">10.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_14" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_28"/>
        <field name="partner_id" ref="res_partner_9"/>
        <field name="price">50.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_15" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_template_29"/>
        <field name="partner_id" ref="res_partner_11"/>
        <field name="price">45.0</field>
    </record>
    <record id="product_supplierinfo_16" model="product.supplierinfo">
        <field name="sequence">2</field>
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_29"/>
        <field name="partner_id" ref="res_partner_7"/>
        <field name="price">45.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_2" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_53"/>
        <field name="partner_id" ref="res_partner_9"/>
        <field name="price">10.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_3" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_55"/>
        <field name="partner_id" ref="res_partner_7"/>
        <field name="price">6.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_4" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_54"/>
        <field name="partner_id" ref="res_partner_7"/>
        <field name="price">18.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_5" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_11"/>
        <field name="partner_id" ref="res_partner_12"/>
        <field name="price">15.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_6" model="product.supplierinfo">
        <field name="delay" eval="False"/>
        <field name="product_tmpl_id" ref="product_template_18"/>
        <field name="partner_id" ref="res_partner_12"/>
        <field name="price">25.0</field>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_7" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_template_14"/>
        <field name="partner_id" ref="res_partner_11"/>
    </record>
    <record id="product_supplierinfo_8" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_template_22"/>
        <field name="partner_id" ref="res_partner_11"/>
    </record>
    <record id="product_supplierinfo_9" model="product.supplierinfo">
        <field name="product_tmpl_id" ref="product_template_27"/>
        <field name="partner_id" ref="res_partner_11"/>
    </record>
</odoo>
