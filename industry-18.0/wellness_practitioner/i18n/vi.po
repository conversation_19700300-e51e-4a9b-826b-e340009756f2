# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* wellness_practitioner
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:14+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: wellness_practitioner
#: model:mail.template,body_html:wellness_practitioner.booking_suggestion
msgid ""
"\n"
"            <p>\n"
"                Hello,\n"
"            </p>\n"
"            <p>\n"
"                Thank you for your presence. May I invite you to schedule your next appointment if needed?\n"
"            </p>\n"
"            <p>\n"
"                Looking forward to meeting you.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Schedule an Appointment</a>\n"
"            </p>\n"
"        "
msgstr ""
"\n"
"            <p>\n"
"                Xin chào,\n"
"            </p>\n"
"            <p>\n"
"                Cảm ơn sự hiện diện của bạn. Tôi có thể mời bạn lên lịch hẹn tiếp theo (nếu cần) không?\n"
"            </p>\n"
"            <p>\n"
"                Tôi rất mong được gặp bạn.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Lên lịch hẹn</a>\n"
"            </p>\n"
"        "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ", and feel free to"
msgstr "của chúng tôi, và đừng ngần ngại"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "1. Create a new opportunity"
msgstr "1. Tạo một cơ hội"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "2. Book an appointment"
msgstr "2. Đặt lịch hẹn"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "3. Verify your schedule"
msgstr "3. Xác nhận lịch trình"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "4. Take notes during your appointment"
msgstr "4. Ghi chú trong cuộc hẹn"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "5. Check your invoices"
msgstr "5. Kiểm tra hoá đơn"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Use Case</span></font></strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Trường hợp sử dụng</span></font></strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>Cơ sở</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further?</strong></span>"
msgstr ""
"<span class=\"h1-fs\"><strong>Bạn có muốn tiến xa hơn không?</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Manage your schedule and your availability in the "
"</span></font><font class=\"text-o-color-1\">Calendar App</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">.</span></font></strong>"
msgstr ""
"<strong><font class=\"text-black\"><span style=\"font-weight: normal;\">Quản"
" lý lịch trình và khung giờ trống của bạn trong </span></font><font "
"class=\"text-o-color-1\">Ứng dụng Lịch</font><font class=\"text-"
"black\"><span style=\"font-weight: normal;\">.</span></font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><span class=\"display-4-fs\">Odoo for Therapy "
"Practices</span></strong>"
msgstr ""
"<strong><span class=\"display-4-fs\">Odoo Dành Cho Trị Liệu</span></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"A free, personalized, integrated<strong><font class=\"text-o-color-1\"> "
"Website </font></strong>in a few clicks. Get new leads or direct appointment"
" online in a few clicks."
msgstr ""
"Một <strong><font class=\"text-o-color-1\">Trang web</font></strong> miễn "
"phí, được cá nhân hóa, tích hợp chỉ với vài cú nhấp chuột. Nhận lead mới "
"hoặc đặt lịch hẹn online chỉ trong vài phút."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Add an upfront payment in your Appointment Type."
msgstr "Thêm khoản trả trước vào Loại lịch hẹn của bạn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Add the Subscription App and invoice automatically based on a subscription "
"type."
msgstr "Thêm ứng dụng Đăng ký và tự động lập hóa đơn dựa trên loại đăng ký."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"After each appointment, an automatic email will be sent to suggest your "
"patient to already book its next appointment."
msgstr ""
"Sau mỗi lịch hẹn, một email tự động sẽ được gửi đến để gợi ý bệnh nhân của "
"bạn đặt lịch hẹn tiếp theo."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"As you qualify Emily's needs, you update the lead status to \"Qualified\" in"
" the CRM pipeline."
msgstr ""
"Khi bạn xác định được nhu cầu của Emily, bạn sẽ cập nhật trạng thái của lead"
" thành \"Đã đánh giá chất lượng\" trong chu trình CRM."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Auto-post your invoice on a regular basis."
msgstr "Tự động ghi sổ hóa đơn của bạn theo định kỳ."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Automated recurring invoicing with the <strong><font class=\"text-o-"
"color-1\">Subscription App</font></strong>."
msgstr ""
"Tự động lập hóa đơn định kỳ với <strong><font class=\"text-o-color-1\">ứng "
"dụng Đăng ký</font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Basics"
msgstr "Cơ sở"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Be careful, using the \"Log Note\" in your contact with be displayed only "
"for you and your eventual team. If you select the \"Send message\" option, "
"this will notify your customer (which can be really interesting also)."
msgstr ""
"Hãy cẩn thận, tính năng \"Ghi chú nhật ký\" trong danh bạ chỉ được hiển thị "
"cho bạn và nhóm cuối cùng của bạn. Nếu bạn chọn tùy chọn \"Gửi tin nhắn\", "
"thì hệ thống sẽ thông báo cho khách hàng của bạn (tính năng này cũng có thể "
"thực sự thú vị)."

#. module: wellness_practitioner
#: model:mail.template,name:wellness_practitioner.booking_suggestion
msgid "Book your next appointment"
msgstr "Đặt lịch hẹn tiếp theo của bạn"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By installing this package, you'll have access to a range of essential apps,"
" including CRM, Appointment and Invoicing. Let's explore how these modules "
"can streamline your therapy practice operations."
msgstr ""
"Bằng cách cài đặt gói này, bạn sẽ có quyền truy cập vào một loạt các ứng "
"dụng thiết yếu, bao gồm CRM, Lịch hẹn và Hoá đơn. Hãy cùng khám phá cách các"
" phân hệ này có thể hợp lý hóa lĩnh vực trị liệu của bạn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few contacts."
msgstr ""
"Bằng cách tải lên dữ liệu demo của phân hệ này, cơ sở dữ liệu của bạn đã có "
"sẵn một số liên hệ."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Khám phá thêm về Odoo bằng cách tìm hiểu"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Do you want to go further?"
msgstr "Bạn có muốn tiến xa hơn không?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Ease your invoicing process with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr ""
"Lập hoá đơn dễ dàng với <strong><font class=\"text-o-color-1\">ứng dụng Hoá "
"đơn</font></strong>."

#. module: wellness_practitioner
#: model:calendar.alarm,name:wellness_practitioner.alarm_mail_1
msgid "Follow up"
msgstr "Theo sát"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Getting Started\n"
"            To begin, you'll find the key applications you need on your Odoo dashboard. Let's take a closer look at how you can use these tools."
msgstr ""
"Bắt đầu\n"
"Để bắt đầu, bạn sẽ tìm thấy các ứng dụng chính cần thiết trên trang chủ Odoo của mình. Hãy cùng xem xét kỹ hơn cách bạn có thể sử dụng các công cụ này."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you navigate to the Invoice App, you can manage your invoices. You have "
"many possibilities to streamline your invoicing process:"
msgstr ""
"Nếu bạn đi đến ứng dụng Hóa đơn, bạn có thể quản lý hóa đơn của mình. Bạn có"
" nhiều cách để hợp lý hóa quy trình lập hóa đơn của mình:"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you open Emily's opportunity in your CRM, you can see in the Chatter (on "
"the right column) that she received an email from you with a call to action "
"to book an appointment."
msgstr ""
"Nếu bạn mở cơ hội của Emily trong CRM, bạn có thể thấy trong mục Cửa sổ trò "
"chuyện (ở cột bên phải) rằng cô ấy đã nhận được email từ bạn kèm theo lời "
"kêu gọi hành động để đặt lịch hẹn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to adapt your flow and set new automation rules, click the Gear "
"Icon in the top of a CRM column and select \"Automation\", or add automated "
"email in your Appointment Type."
msgstr ""
"Nếu bạn muốn điều chỉnh chu trình của mình và thiết lập các quy tắc tự động "
"hóa mới, hãy nhấp vào biểu tượng bánh răng ở đầu cột CRM và chọn \"Tự động "
"hóa\" hoặc thêm email tự động vào Loại lịch hẹn của bạn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to share your calendar easily, navigate to the Appointment App "
"and select \"Share\" on the Edit Button."
msgstr ""
"Nếu bạn muốn chia sẻ lịch của mình một cách dễ dàng, hãy đi đến Ứng dụng "
"Lịch hẹn và chọn \"Chia sẻ\" trên nút Chỉnh sửa."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"In the <strong><font class=\"text-o-color-1\">CRM App</font></strong>, you "
"create a new lead for Emily, capturing her contact information and the "
"reason for her inquiry."
msgstr ""
"Trong <strong><font class=\"text-o-color-1\">ứng dụng CRM</font></strong>, "
"bạn tạo một lead mới cho Emily, ghi lại thông tin liên hệ và lý do yêu cầu "
"của cô ấy."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_5
msgid "Inactive"
msgstr "Không hoạt động"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Invoice yourself, manually."
msgstr "Tự lập hoá đơn thủ công."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Let's Try"
msgstr "Hãy thử"

#. module: wellness_practitioner
#: model:mail.template,subject:wellness_practitioner.booking_suggestion
msgid "Looking forward to meeting you"
msgstr "Chúng tôi rất mong được gặp bạn"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo<strong><font "
"class=\"text-o-color-1\"> Marketing Automation "
"</font></strong>,<strong><font class=\"text-o-color-1\">Email "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong>, and <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."
msgstr ""
"Quản lý tất cả kênh truyền thông của bạn ở một nơi với Odoo <strong><font "
"class=\"text-o-color-1\">Tự động hoá Marketing</font></strong>, "
"<strong><font class=\"text-o-color-1\">Marketing qua Email</font></strong>, "
"<strong><font class=\"text-o-color-1\">Marketing qua MXH</font></strong> và "
"<strong><font class=\"text-o-color-1\">Marketing qua SMS</font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"Quản lý nghiệp vụ kế toán của bạn dễ dàng hơn bao giờ hết với môi trường "
"tích hợp hoàn toàn. (<strong><font class=\"text-o-color-1\">Ứng dụng Kế "
"toán</font></strong>)"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your team's and integrate them in all processes by adding "
"the<strong><font class=\"text-o-color-1\"> Employees App</font></strong>."
msgstr ""
"Quản lý đội ngũ của bạn và tích hợp họ vào tất cả các quy trình bằng cách "
"thêm <strong><font class=\"text-o-color-1\">Ứng dụng Nhân "
"viên</font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo for Therapy Practices"
msgstr "Odoo Dành Cho Trị Liệu"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone to "
"manage your entire business in it."
msgstr ""
"Odoo được tích hợp hoàn toàn vào một ứng dụng. Tải xuống và dùng chiếc điện "
"thoại của bạn để quản lý toàn bộ hoạt động kinh doanh."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo cung cấp cho bạn những khả năng vô hạn, chẳng hạn như:"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Tất nhiên, đây chỉ là thông tin tổng quan về những tính năng có trong gói "
"này. Hãy thoải mái thêm ứng dụng mới, xóa/sửa đổi dữ liệu demo và kiểm thử "
"các chức năng!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Once the appointment is booked, navigate to the <strong><font class=\"text-"
"o-color-1\">Calendar App</font></strong>. You may find the new appointment. "
"You can also move it (Emily will be notified) or cancel it."
msgstr ""
"Sau khi đặt lịch hẹn, đi đến <strong><font class=\"text-o-color-1\">ứng dụng"
" Lịch</font></strong>. Bạn có thể tìm thấy lịch hẹn mới. Bạn cũng có thể di "
"chuyển (Emily sẽ được thông báo) hoặc hủy lịch hẹn đó."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Organise your events and connect with your patients easily with the "
"<strong><font class=\"text-o-color-1\">Events App </font></strong>."
msgstr ""
"Tổ chức các sự kiện và kết nối với bệnh nhân của bạn một cách dễ dàng với "
"<strong><font class=\"text-o-color-1\">Ứng dụng Sự kiện</font></strong>."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_6
msgid "Redirect to Another Practitioner"
msgstr "Chuyển hướng đến một chuyên gia khác"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Schedule a demo"
msgstr "Lên lịch một buổi demo"

#. module: wellness_practitioner
#: model:ir.actions.server,name:wellness_practitioner.ir_act_server_1
msgid "Send email: Book your next appointment"
msgstr "Gửi email: Đặt lịch hẹn tiếp theo của bạn"

#. module: wellness_practitioner
#: model:mail.template,description:wellness_practitioner.booking_suggestion
msgid "Sent to all attendees if a reminder is set"
msgstr "Gửi tới tất cả người tham dự nếu họ đặt nhắc nhở"

#. module: wellness_practitioner
#: model:base.automation,name:wellness_practitioner.base_automation_1
msgid "Stage is set to \"Qualified\""
msgstr "Giai đoạn được đặt thành \"Đã đánh giá chất lượng\""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointment App</font></strong> "
"is automatically updated based on your Calendar. You can easily lock slots, "
"change appointment hoursand send communication to your patients fromthe "
"Calendar App."
msgstr ""
"<strong><font class=\"text-o-color-1\">Ứng dụng Lịch hẹn</font></strong> "
"được tự động cập nhật dựa trên Lịch của bạn. Bạn có thể dễ dàng khóa các "
"khung giờ, thay đổi giờ hẹn và liên hệ với bệnh nhân của mình từ ứng dụng "
"Lịch."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointments App</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> manages your "
"appointment types.</span></font><font class=\"text-o-color-1\"/></strong>"
msgstr ""
"<strong><font class=\"text-o-color-1\">Ứng dụng Lịch hẹn</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> quản lý các loại "
"cuộc hẹn của bạn.</span></font><font class=\"text-o-color-1\"/></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">CRM App</font></strong>(Customer "
"Relationship Management) is the heart of your therapy practice's patient "
"management."
msgstr ""
"<strong><font class=\"text-o-color-1\">Ứng dụng CRM</font></strong> (Quản lý"
" quan hệ khách hàng) là trung tâm quản lý bệnh nhân tại trung tâm trị liệu "
"của bạn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The Odoo Calendar App is compatible with Outlook and Google Calendar. "
"Activate the sync in the Calendar App settings."
msgstr ""
"Ứng dụng Odoo Lịch tương thích với Outlook và Google Calendar. Kích hoạt "
"đồng bộ hóa trong phần cài đặt ứng dụng Lịch."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Tất cả đều miễn phí trong gói đăng ký hiện tại của bạn; hãy thoải mái khám "
"phá! 🙃"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"This module provides a comprehensive suite of applications tailored to the "
"needs of therapy practices, empowering you to manage your business "
"efficiently and deliver exceptional patient care."
msgstr ""
"Phân hệ này cung cấp một bộ ứng dụng toàn diện phù hợp với nhu cầu trị liệu,"
" giúp bạn quản lý doanh nghiệp hiệu quả và cung cấp dịch vụ chăm sóc bệnh "
"nhân đặc biệt."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "To do so, go to CRM &gt; Configuration &gt; Sales Teams."
msgstr "Để tiến hành, hãy đi đến CRM &gt; Cấu hình &gt; Đội ngũ Sales."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"To reduce your no show rate drastically, appointments automatically comes "
"with reminders by Email and SMS."
msgstr ""
"Để giảm đáng kể tỷ lệ vắng mặt, lịch hẹn sẽ tự động được nhắc nhở qua Email "
"và SMS."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Try it !"
msgstr "Dùng thử!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Use Case"
msgstr "Trường hợp vận dụng"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Using Odoo, you can easily take notes during your appointments, you can "
"either use the \"Log Note\" button on your contact record or create a "
"specific knowledge article (like this one) for each of your patient."
msgstr ""
"Khi sử dụng Odoo, bạn có thể dễ dàng ghi chú trong các lịch cuộc, bạn có thể"
" sử dụng nút \"Ghi chú nhật ký\" trên hồ sơ liên hệ hoặc tạo một bài viết "
"kiến ​​thức cụ thể (như bài này) cho từng bệnh nhân."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Welcome to the <strong><font class=\"text-o-color-1\">Odoo Therapy Practice "
"package</font></strong>!"
msgstr ""
"Chào mừng bạn đến với <strong><font class=\"text-o-color-1\">gói Odoo Trị "
"liệu</font></strong>!"

#. module: wellness_practitioner
#: model_terms:web_tour.tour,rainbow_man_message:wellness_practitioner.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Chào mừng bạn! Chúc bạn một chuyến khám phá bổ ích!"

#. module: wellness_practitioner
#: model:appointment.type,name:wellness_practitioner.appointment_type_1
msgid "Wellness Session"
msgstr "Buổi chăm sóc sức khỏe"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Bạn có muốn thảo luận với chúng tôi về thiết lập Odoo của mình hoặc tìm hiểu"
" cụ thể hơn không?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Bạn có thể truy cập mọi ứng dụng đã cài đặt trong cơ sở dữ liệu Odoo trên "
"trang chủ của mình."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can also configure an email alias so everyone sending email to this one "
"will create a new opportunity for you."
msgstr ""
"Bạn cũng có thể cấu hình một bí danh email để bất kỳ ai gửi email đến bí "
"danh này đều tạo ra một cơ hội mới cho bạn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can set the consultation with an upfront online payment. Activate it in "
"your Appointment Type Settingsby setting up an upfront payment method."
msgstr ""
"Bạn có thể thiết lập tư vấn bằng cách thanh toán online trước. Kích hoạt "
"trong cài đặt Loại lịch hẹn của bạn bằng cách thiết lập phương thức thanh "
"toán trước."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Vậy là bạn đã hoàn thành trường hợp vận dụng bản demo đó! Có hàng triệu cách"
" khác để điều chỉnh thiết lập Odoo cho phù hợp với nhu cầu kinh doanh của "
"bạn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Therapy Practice package and check the related box."
msgstr ""
"Bạn chưa nhập dữ liệu demo? Đừng lo! Bạn vẫn có thể làm được bằng cách đi "
"đến Ứng dụng &gt; Ngành &gt; Nâng cấp gói Thực hành trị liệu của bạn và đánh"
" dấu vào ô liên quan."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You may also create a personalized website in a few clicks by adding the "
"Website App, your appointment page will be automatically added."
msgstr ""
"Bạn cũng có thể tạo một trang web cá nhân chỉ bằng vài cú nhấp chuột bằng "
"cách thêm Ứng dụng Trang web, trang lịch hẹn của bạn sẽ được tự động thêm "
"vào."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You receive a call from a new potential patient, Emily, who is interested in"
" your therapy services."
msgstr ""
"Bạn nhận được cuộc gọi từ một bệnh nhân tiềm năng mới, Emily, người quan tâm"
" đến dịch vụ trị liệu của bạn."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"Bạn sẽ có thể dùng thử các chu trình sau để có cái nhìn tổng quan về nhiều "
"chu trình kinh doanh mà bạn có thể thực hiện nhanh chóng bằng gói này khi sử"
" dụng Odoo."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "academy"
msgstr "khoá học"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "and"
msgstr "và"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "documentation"
msgstr "tài liệu"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "if you need help!<br/>"
msgstr "nếu bạn cần trợ giúp!<br/>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "request a demo"
msgstr "đặt lịch demo"
