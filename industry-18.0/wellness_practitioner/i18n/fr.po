# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* wellness_practitioner
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:14+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Manon <PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: wellness_practitioner
#: model:mail.template,body_html:wellness_practitioner.booking_suggestion
msgid ""
"\n"
"            <p>\n"
"                Hello,\n"
"            </p>\n"
"            <p>\n"
"                Thank you for your presence. May I invite you to schedule your next appointment if needed?\n"
"            </p>\n"
"            <p>\n"
"                Looking forward to meeting you.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Schedule an Appointment</a>\n"
"            </p>\n"
"        "
msgstr ""
"\n"
"            <p>\n"
"                Bonjour,\n"
"            </p>\n"
"            <p>\n"
"                Nous vous remercions de votre présence. Nous vous invitons à prendre votre prochain rendez-vous si nécessaire.\n"
"            </p>\n"
"            <p>\n"
"                Nous nous réjouissons de vous rencontrer.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Planifier un rendez-vous</a>\n"
"            </p>\n"
"        "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ", and feel free to"
msgstr "et n'hésitez pas à"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "1. Create a new opportunity"
msgstr "1. Créer une nouvelle opportunité"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "2. Book an appointment"
msgstr "2. Prendre un rendez-vous"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "3. Verify your schedule"
msgstr "3. Vérifier votre emploi du temps"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "4. Take notes during your appointment"
msgstr "4. Prendre des notes durant un rendez-vous"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "5. Check your invoices"
msgstr "5. Consulter vos factures"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Use Case</span></font></strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Cas d'utilisation</span></font></strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>Les bases</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further?</strong></span>"
msgstr ""
"<span class=\"h1-fs\"><strong>Vous souhaitez aller plus loin "
"?</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Manage your schedule and your availability in the "
"</span></font><font class=\"text-o-color-1\">Calendar App</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">.</span></font></strong>"
msgstr ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Gérez votre emploi du temps et vos disponibilités dans "
"l'application </span></font><font class=\"text-o-"
"color-1\">Calendrier</font><font class=\"text-black\"><span style=\"font-"
"weight: normal;\">.</span></font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><span class=\"display-4-fs\">Odoo for Therapy "
"Practices</span></strong>"
msgstr ""
"<strong><span class=\"display-4-fs\">Odoo pour les pratiques "
"thérapeutiques</span></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"A free, personalized, integrated<strong><font class=\"text-o-color-1\"> "
"Website </font></strong>in a few clicks. Get new leads or direct appointment"
" online in a few clicks."
msgstr ""
"Un<strong><font class=\"text-o-color-1\"> Site web </font></strong>gratuit, "
"personnalisé et intégré en quelques clics. Obtenez immédiatement de "
"nouvelles pistes ou des rendez-vous en ligne en quelques clics."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Add an upfront payment in your Appointment Type."
msgstr "Ajoutez un paiement anticipé dans votre type de rendez-vous."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Add the Subscription App and invoice automatically based on a subscription "
"type."
msgstr ""
"Ajoutez l'application Abonnement et facturez automatiquement sur la base "
"d'un type d'abonnement."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"After each appointment, an automatic email will be sent to suggest your "
"patient to already book its next appointment."
msgstr ""
"Après chaque rendez-vous, un e-mail automatique sera envoyé pour suggérer à "
"votre patient de réserver son prochain rendez-vous."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"As you qualify Emily's needs, you update the lead status to \"Qualified\" in"
" the CRM pipeline."
msgstr ""
"Au fur et à mesure que vous identifiez les besoins d'Emily, la piste est "
"mise à jour et devient \" Qualifiée \" dans le pipeline CRM."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Auto-post your invoice on a regular basis."
msgstr "Publiez automatiquement votre facture à intervalles réguliers."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Automated recurring invoicing with the <strong><font class=\"text-o-"
"color-1\">Subscription App</font></strong>."
msgstr ""
"Facturation récurrente automatisée avec l'application <strong><font "
"class=\"text-o-color-1\">Abonnement</font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Basics"
msgstr "Les bases"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Be careful, using the \"Log Note\" in your contact with be displayed only "
"for you and your eventual team. If you select the \"Send message\" option, "
"this will notify your customer (which can be really interesting also)."
msgstr ""
"Attention, lors de l'utilisation de la fonction \" Enregistrer une note \", "
"celle-ci ne s'affichera que pour vous et votre équipe éventuelle. Si vous "
"sélectionnez l'option \" Envoyer un message \", votre client en sera informé"
" (ce qui peut également être très intéressant)."

#. module: wellness_practitioner
#: model:mail.template,name:wellness_practitioner.booking_suggestion
msgid "Book your next appointment"
msgstr "Réserver votre prochain rendez-vous"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By installing this package, you'll have access to a range of essential apps,"
" including CRM, Appointment and Invoicing. Let's explore how these modules "
"can streamline your therapy practice operations."
msgstr ""
"En installant ce pack, vous aurez accès à une série d'applications "
"essentielles, notamment CRM, Rendez-vous et Facturation. Voyons comment ces "
"modules peuvent simplifier les opérations de votre cabinet thérapeutique."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few contacts."
msgstr ""
"En téléchargeant les données de démonstration de ce module, votre base de "
"données a été enrichie de quelques contacts."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Découvrez-en plus sur Odoo en vous plongeant dans notre"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Do you want to go further?"
msgstr "Vous souhaitez aller plus loin ?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Ease your invoicing process with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr ""
"Facilitez votre processus de facturation avec l'application <strong><font "
"class=\"text-o-color-1\">Facuration</font></strong>."

#. module: wellness_practitioner
#: model:calendar.alarm,name:wellness_practitioner.alarm_mail_1
msgid "Follow up"
msgstr "Relance"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Getting Started\n"
"            To begin, you'll find the key applications you need on your Odoo dashboard. Let's take a closer look at how you can use these tools."
msgstr ""
"Démarrage\n"
"            Pour commencer, vous trouverez les applications essentielles dont vous avez besoin sur votre tableau de bord Odoo. Découvrons maintenant comment utiliser ces outils"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you navigate to the Invoice App, you can manage your invoices. You have "
"many possibilities to streamline your invoicing process:"
msgstr ""
"Si vous naviguez vers l'application Facturation, il vous sera possible de "
"gérer vos factures. De nombreuses possibilités s'offrent à vous pour "
"optimiser votre processus de facturation :"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you open Emily's opportunity in your CRM, you can see in the Chatter (on "
"the right column) that she received an email from you with a call to action "
"to book an appointment."
msgstr ""
"Si vous ouvrez l'opportunité d'Emily dans votre CRM, vous pouvez voir dans "
"le Chatter (dans la colonne de droite) qu'elle a reçu un e-mail de votre "
"part avec un appel à l'action pour prendre rendez-vous."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to adapt your flow and set new automation rules, click the Gear "
"Icon in the top of a CRM column and select \"Automation\", or add automated "
"email in your Appointment Type."
msgstr ""
"Si vous souhaitez adapter votre flux et définir de nouvelles règles "
"d'automatisation, cliquez sur l'icône en forme d'engrenage en haut d'une "
"colonne CRM et sélectionnez \" Automatisation \", ou ajoutez un e-mail "
"automatisé dans votre type de rendez-vous."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to share your calendar easily, navigate to the Appointment App "
"and select \"Share\" on the Edit Button."
msgstr ""
"Si vous souhaitez partager facilement votre calendrier, accédez à "
"l'application Rendez-vous et sélectionnez \" Partager \" sur le bouton de "
"modification."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"In the <strong><font class=\"text-o-color-1\">CRM App</font></strong>, you "
"create a new lead for Emily, capturing her contact information and the "
"reason for her inquiry."
msgstr ""
"Dans l'application <strong><font class=\"text-o-"
"color-1\">CRM</font></strong>, créez une nouvelle piste pour Emily, en "
"indiquant ses coordonnées et le motif de sa demande."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_5
msgid "Inactive"
msgstr "Inactif"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Invoice yourself, manually."
msgstr "Facturez vous-même, manuellement."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Let's Try"
msgstr "Faisons un essai"

#. module: wellness_practitioner
#: model:mail.template,subject:wellness_practitioner.booking_suggestion
msgid "Looking forward to meeting you"
msgstr "Nous nous réjouissons de vous rencontrer"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo<strong><font "
"class=\"text-o-color-1\"> Marketing Automation "
"</font></strong>,<strong><font class=\"text-o-color-1\">Email "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong>, and <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."
msgstr ""
"Gérez tous vos canaux de communication depuis un seul endroit avec les "
"applications Odoo<strong><font class=\"text-o-color-1\"> Marketing "
"Automation </font></strong>,<strong><font class=\"text-o-color-1\">E-mail "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong>, et <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"Gérez votre comptabilité plus facilement que jamais grâce à un environnement"
" totalement intégré. (<strong><font class=\"text-o-"
"color-1\">Comptabilité</font></strong>)"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your team's and integrate them in all processes by adding "
"the<strong><font class=\"text-o-color-1\"> Employees App</font></strong>."
msgstr ""
"Gérez votre équipe et intégrez-la dans tous les processus grâce à "
"l'application<strong><font class=\"text-o-color-1\"> "
"Employés</font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo for Therapy Practices"
msgstr "Odoo pour les cabinets thérapeutiques"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone to "
"manage your entire business in it."
msgstr ""
"Odoo est entièrement intégré dans une application. Téléchargez-la pour "
"pouvoir gérer toute votre entreprise depuis votre téléphone."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo offre une infinité de possibilités, telles que :"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Bien entendu, il ne s'agit que d'un aperçu des fonctionnalités incluses dans"
" ce paquet. N'hésitez pas à ajouter de nouvelles applications, à "
"supprimer/modifier les données de démonstration et à tout tester !"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Once the appointment is booked, navigate to the <strong><font class=\"text-"
"o-color-1\">Calendar App</font></strong>. You may find the new appointment. "
"You can also move it (Emily will be notified) or cancel it."
msgstr ""
"Une fois le rendez-vous pris, naviguez vers l'application <strong><font "
"class=\"text-o-color-1\">Calendrier</font></strong>. Vous y trouverez le "
"nouveau rendez-vous. Vous pouvez également le déplacer (Emily en sera "
"informée) ou l'annuler."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Organise your events and connect with your patients easily with the "
"<strong><font class=\"text-o-color-1\">Events App </font></strong>."
msgstr ""
"Organisez vos événements et communiquez avec vos patients en toute "
"simplicité grâce à l'application<strong><font class=\"text-o-"
"color-1\">Événements </font></strong>."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_6
msgid "Redirect to Another Practitioner"
msgstr "Redirection vers un autre praticien"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Schedule a demo"
msgstr "Planifier une démonstration"

#. module: wellness_practitioner
#: model:ir.actions.server,name:wellness_practitioner.ir_act_server_1
msgid "Send email: Book your next appointment"
msgstr "Envoyer un e-mail : Réserver votre prochain rendez-vous"

#. module: wellness_practitioner
#: model:mail.template,description:wellness_practitioner.booking_suggestion
msgid "Sent to all attendees if a reminder is set"
msgstr "Envoyé à tous les participants si un rappel est planifié"

#. module: wellness_practitioner
#: model:base.automation,name:wellness_practitioner.base_automation_1
msgid "Stage is set to \"Qualified\""
msgstr "L'étape est définie sur \" Qualifié \""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointment App</font></strong> "
"is automatically updated based on your Calendar. You can easily lock slots, "
"change appointment hoursand send communication to your patients fromthe "
"Calendar App."
msgstr ""
"L'application <strong><font class=\"text-o-color-1\">Rendez-"
"vous</font></strong> est automatiquement mise à jour en fonction de votre "
"calendrier. Vous pouvez facilement réserver des créneaux, modifier les "
"heures de rendez-vous et envoyer des communications à vos patients à partir "
"de l'application Calendrier."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointments App</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> manages your "
"appointment types.</span></font><font class=\"text-o-color-1\"/></strong>"
msgstr ""
"L'application <strong><font class=\"text-o-color-1\">Rendez-vous</font><font"
" class=\"text-black\"><span style=\"font-weight: normal;\"> permet de gérer "
"les types de rendez-vous.</span></font><font class=\"text-o-"
"color-1\"/></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">CRM App</font></strong>(Customer "
"Relationship Management) is the heart of your therapy practice's patient "
"management."
msgstr ""
"L'application <strong><font class=\"text-o-"
"color-1\">CRM</font></strong>(Customer Relationship Management) est au cœur "
"de la gestion des patients de votre cabinet thérapeutique."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The Odoo Calendar App is compatible with Outlook and Google Calendar. "
"Activate the sync in the Calendar App settings."
msgstr ""
"L'application Calendrier Odoo est compatible avec Outlook et Google "
"Calendar. Activez la synchronisation dans les paramètres de l'application "
"Calendrier."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Tous ces services sont compris dans votre abonnement actuel ; n'hésitez pas "
"à les découvrir ! 🙃"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"This module provides a comprehensive suite of applications tailored to the "
"needs of therapy practices, empowering you to manage your business "
"efficiently and deliver exceptional patient care."
msgstr ""
"Ce module propose une suite complète d'applications adaptées aux besoins des"
" cabinets thérapeutiques, vous permettant de gérer efficacement votre "
"activité et de prodiguer des soins exceptionnels à vos patients."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "To do so, go to CRM &gt; Configuration &gt; Sales Teams."
msgstr "Pour ce faire, allez dans CRM > Configuration > Équipes commerciales."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"To reduce your no show rate drastically, appointments automatically comes "
"with reminders by Email and SMS."
msgstr ""
"Afin de faire baisser considérablement le taux d'absentéisme, les rendez-"
"vous sont automatiquement accompagnés de rappels par e-mail et par SMS."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Try it !"
msgstr "Faites un essai !"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Use Case"
msgstr "Cas d'utilisation"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Using Odoo, you can easily take notes during your appointments, you can "
"either use the \"Log Note\" button on your contact record or create a "
"specific knowledge article (like this one) for each of your patient."
msgstr ""
"Odoo vous permet de prendre facilement des notes lors de vos rendez-vous. "
"Pour cela, utilisez le bouton \" Ajouter une note \" de votre fiche contact "
"ou créer un article Connaissance spécifique (comme celui-ci) pour chacun de "
"vos patients."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Welcome to the <strong><font class=\"text-o-color-1\">Odoo Therapy Practice "
"package</font></strong>!"
msgstr ""
"Découvrez le pack <strong><font class=\"text-o-color-1\">Cabinet "
"thérapeutique d'Odoo</font></strong> !"

#. module: wellness_practitioner
#: model_terms:web_tour.tour,rainbow_man_message:wellness_practitioner.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Bienvenue ! Amusez-vous en explorant."

#. module: wellness_practitioner
#: model:appointment.type,name:wellness_practitioner.appointment_type_1
msgid "Wellness Session"
msgstr "Séance wellness"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Vous souhaitez discuter avec nous de l'installation d'Odoo ou approfondir la"
" question ?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Vous pouvez accéder à toutes les applications installées dans votre base de "
"données Odoo depuis votre tableau de bord principal."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can also configure an email alias so everyone sending email to this one "
"will create a new opportunity for you."
msgstr ""
"Vous pouvez également configurer un alias de messagerie afin de créer une "
"nouvelle opportunité à partir de tous ceux qui envoient des e-mails à cet "
"alias."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can set the consultation with an upfront online payment. Activate it in "
"your Appointment Type Settingsby setting up an upfront payment method."
msgstr ""
"La consultation peut s'accompagner d'un paiement anticipé en ligne. Activez "
"cette option dans les paramètres du type de rendez-vous en définissant un "
"mode de paiement anticipé."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Vous avez terminé la démonstration de ce cas d'utilisation ! Il existe des "
"millions d'autres façons d'adapter votre configuration Odoo aux besoins de "
"votre entreprise."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Therapy Practice package and check the related box."
msgstr ""
"Vous n'avez pas importé de données de démonstration ? Vous pouvez encore le "
"faire. Allez dans Applications > Industries > Mettre à jour votre pack "
"Cabinet thérapeutique et cochez la case correspondante."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You may also create a personalized website in a few clicks by adding the "
"Website App, your appointment page will be automatically added."
msgstr ""
"Vous pouvez également créer un site web personnalisé en quelques clics en "
"ajoutant l'application Site web, votre page de rendez-vous sera "
"automatiquement ajoutée."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You receive a call from a new potential patient, Emily, who is interested in"
" your therapy services."
msgstr ""
"Vous recevez un appel d'une nouvelle patiente potentielle, Emily, qui est "
"intéressée par vos services thérapeutiques."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"Exécutez les flux suivants et découvrez les différents flux que vous pouvez "
"rapidement mettre en œuvre avec ce pack dans Odoo."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "academy"
msgstr "academy"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "and"
msgstr "et"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "documentation"
msgstr "documentation"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "if you need help!<br/>"
msgstr "si vous avez besoin d'aide ! <br/>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "request a demo"
msgstr "demander une démonstration"
