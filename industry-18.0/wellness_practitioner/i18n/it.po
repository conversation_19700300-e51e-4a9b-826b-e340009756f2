# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* wellness_practitioner
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:14+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: wellness_practitioner
#: model:mail.template,body_html:wellness_practitioner.booking_suggestion
msgid ""
"\n"
"            <p>\n"
"                Hello,\n"
"            </p>\n"
"            <p>\n"
"                Thank you for your presence. May I invite you to schedule your next appointment if needed?\n"
"            </p>\n"
"            <p>\n"
"                Looking forward to meeting you.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Schedule an Appointment</a>\n"
"            </p>\n"
"        "
msgstr ""
"\n"
"            <p>\n"
"                Ciao,\n"
"            </p>\n"
"            <p>\n"
"                Grazie per la tua presenza. Posso invitarti a fissare il prossimo appuntamento, se necessario?\n"
"            </p>\n"
"            <p>\n"
"                Non vedo l'ora di rivederti.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Prenota un appuntamento</a>\n"
"            </p>\n"
"        "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ", and feel free to"
msgstr "e non esitare a"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "1. Create a new opportunity"
msgstr "1. Creare una nuova opportunità"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "2. Book an appointment"
msgstr "2. Prenotare un appuntamento"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "3. Verify your schedule"
msgstr "3. Verificare le prenotazioni"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "4. Take notes during your appointment"
msgstr "4. Prendere appunti durante le sedute"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "5. Check your invoices"
msgstr "5. Controllare le fatture"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Use Case</span></font></strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Caso d'uso</span></font></strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>Informazioni di base</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further?</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>Vuoi saperne di più?</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Manage your schedule and your availability in the "
"</span></font><font class=\"text-o-color-1\">Calendar App</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">.</span></font></strong>"
msgstr ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Gestisci gli appuntamenti e le disponibilità dall'app "
"</span></font><font class=\"text-o-color-1\">Calendario</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">.</span></font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><span class=\"display-4-fs\">Odoo for Therapy "
"Practices</span></strong>"
msgstr ""
"<strong><span class=\"display-4-fs\">Odoo per studi di "
"terapie</span></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"A free, personalized, integrated<strong><font class=\"text-o-color-1\"> "
"Website </font></strong>in a few clicks. Get new leads or direct appointment"
" online in a few clicks."
msgstr ""
"Un <strong><font class=\"text-o-color-1\">Sito web</font></strong> gratis, "
"personalizzato e integrato in pochi clic. Ottieni nuovi lead o appuntamenti "
"online in pochi clic."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Add an upfront payment in your Appointment Type."
msgstr "Aggiungi il pagamento anticipato al Tipo di appuntamento."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Add the Subscription App and invoice automatically based on a subscription "
"type."
msgstr ""
"Aggiungi l'app Abbonamenti ed emetti fatture automaticamente in base al tipo"
" di abbonamento."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"After each appointment, an automatic email will be sent to suggest your "
"patient to already book its next appointment."
msgstr ""
"Dopo ogni appuntamento, verrà inviata un'e-mail automatica  per suggerire al"
" paziente di prenotare già il prossimo appuntamento."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"As you qualify Emily's needs, you update the lead status to \"Qualified\" in"
" the CRM pipeline."
msgstr ""
"Dopo aver effettuato la qualifica dei bisogni di Emily, aggiorna lo stato "
"del lead nella pipeline CRM, impostandolo su \"Qualificato\"."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Auto-post your invoice on a regular basis."
msgstr "Pubblica automaticamente le fatture e in modo regolare."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Automated recurring invoicing with the <strong><font class=\"text-o-"
"color-1\">Subscription App</font></strong>."
msgstr ""
"Fatturazione automatizzata e ricorrente grazie all'app <strong><font "
"class=\"text-o-color-1\">Abbonamenti</font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Basics"
msgstr "Informazioni di base"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Be careful, using the \"Log Note\" in your contact with be displayed only "
"for you and your eventual team. If you select the \"Send message\" option, "
"this will notify your customer (which can be really interesting also)."
msgstr ""
"Attenzione, se utilizzi \"Registra nota\" nel contatto, il messaggio sarà "
"visualizzato solo per te e per l'eventuale team. Se si seleziona l'opzione "
"“Invia messaggio”, il cliente riceverà una notifica (che può essere davvero "
"interessante)."

#. module: wellness_practitioner
#: model:mail.template,name:wellness_practitioner.booking_suggestion
msgid "Book your next appointment"
msgstr "Prenota il prossimo appuntamento"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By installing this package, you'll have access to a range of essential apps,"
" including CRM, Appointment and Invoicing. Let's explore how these modules "
"can streamline your therapy practice operations."
msgstr ""
"Installando il pacchetto, avrai accesso a una vasta gamma di app essenziali "
"come CRM, Appuntamenti e Fatturazione. Scopriamo in che modo questi moduli "
"possono ottimizzare le tue operazioni."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few contacts."
msgstr ""
"In seguito al caricamento dei dati demo del modulo, nel database saranno "
"presenti alcuni contatti."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Scopri di più su Odoo esplorando il nostro"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Do you want to go further?"
msgstr "Vuoi saperne di più?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Ease your invoicing process with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr ""
"Facilita il processo di fatturazione grazie all'app <strong><font "
"class=\"text-o-color-1\">Fatturazione</font></strong>."

#. module: wellness_practitioner
#: model:calendar.alarm,name:wellness_practitioner.alarm_mail_1
msgid "Follow up"
msgstr "Sollecito"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Getting Started\n"
"            To begin, you'll find the key applications you need on your Odoo dashboard. Let's take a closer look at how you can use these tools."
msgstr ""
"Primi passi\n"
"            Per iniziare, troverai le applicazioni principali nella dashboard Odoo. Vediamo come possiamo utilizzare questi strumenti."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you navigate to the Invoice App, you can manage your invoices. You have "
"many possibilities to streamline your invoicing process:"
msgstr ""
"Esplorando l'app Fatturazione, potrai scoprire come gestire le fatture. Ci "
"sono vari modi per ottimizzare il processo:"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you open Emily's opportunity in your CRM, you can see in the Chatter (on "
"the right column) that she received an email from you with a call to action "
"to book an appointment."
msgstr ""
"Se apri l'opportunità di Emily nel CRM, potrai vedere dal chatter (nella "
"colonna a destra) che ha ricevuto un'e-mail da te con un pulsante call to "
"action per prenotare un appuntamento."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to adapt your flow and set new automation rules, click the Gear "
"Icon in the top of a CRM column and select \"Automation\", or add automated "
"email in your Appointment Type."
msgstr ""
"Se vuoi adattare il flusso e impostare nuove regole di automazione, fai clic"
" sull'icona dell'ingranaggio nella parte superiore di una colonna del CRM e "
"seleziona “Automazione” oppure aggiungi un'e-mail automatizzata nel tipo di "
"appuntamento."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to share your calendar easily, navigate to the Appointment App "
"and select \"Share\" on the Edit Button."
msgstr ""
"Se vuoi condividere facilmente il calendario, accedi all'app Appuntamenti e "
"seleziona “Condividi” sul pulsante Modifica."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"In the <strong><font class=\"text-o-color-1\">CRM App</font></strong>, you "
"create a new lead for Emily, capturing her contact information and the "
"reason for her inquiry."
msgstr ""
"Nell'app <strong><font class=\"text-o-color-1\">CRM</font></strong>, puoi "
"creare un nuovo lead per Emily acquisendo le informazioni di contatto e il "
"motivo della richiesta."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_5
msgid "Inactive"
msgstr "Non attivo"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Invoice yourself, manually."
msgstr "Crea una fattura per te stesso manualmente."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Let's Try"
msgstr "Proviamo"

#. module: wellness_practitioner
#: model:mail.template,subject:wellness_practitioner.booking_suggestion
msgid "Looking forward to meeting you"
msgstr "Non vediamo l'ora di conoscerti"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo<strong><font "
"class=\"text-o-color-1\"> Marketing Automation "
"</font></strong>,<strong><font class=\"text-o-color-1\">Email "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong>, and <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."
msgstr ""
"Gestisci tutti i canali di comunicazione in un unico posto grazie alle app "
"di Odoo: <strong><font class=\"text-o-color-1\"> Automazione Marketing "
"</font></strong>,<strong><font class=\"text-o-color-1\">E.mail "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong> e <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"Gestisci facilmente la contabilità in un ambiente completamente integrato. "
"(<strong><font class=\"text-o-color-1\">App Contabilità</font></strong>)"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your team's and integrate them in all processes by adding "
"the<strong><font class=\"text-o-color-1\"> Employees App</font></strong>."
msgstr ""
"Gestisci i membri del team e integrali in tutti i processi aggiungendoli "
"all'app <strong><font class=\"text-o-color-1\"> Dipendenti</font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo for Therapy Practices"
msgstr "Odoo per studi di terapie"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone to "
"manage your entire business in it."
msgstr ""
"Odoo è interamente integrato in un'app. Scaricala per trasformare il tuo "
"telefono e gestire tutta l'attività."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo ti offre possibilità infinite, come:"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Naturalmente, questa è solo una panoramica delle funzionalità incluse nel "
"pacchetto. Sentiti libero di aggiungere nuove applicazioni, "
"cancellare/modificare i dati demo e provare tutto!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Once the appointment is booked, navigate to the <strong><font class=\"text-"
"o-color-1\">Calendar App</font></strong>. You may find the new appointment. "
"You can also move it (Emily will be notified) or cancel it."
msgstr ""
"Una volta prenotato l'appuntamento, apri l'app <strong><font class=\"text-o-"
"color-1\">Calendario</font></strong>, qui troverai il nuovo appuntamento. "
"Inoltre, è possibile spostarlo (Emily riceverà una notifica) o annullarlo. "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Organise your events and connect with your patients easily with the "
"<strong><font class=\"text-o-color-1\">Events App </font></strong>."
msgstr ""
"Organizza eventi e parla con i pazienti in modo semplice grazie al modulo "
"<strong><font class=\"text-o-color-1\">Eventi</font></strong>."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_6
msgid "Redirect to Another Practitioner"
msgstr "Indirizza a un altro professionista"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Schedule a demo"
msgstr "Pianifica una demo"

#. module: wellness_practitioner
#: model:ir.actions.server,name:wellness_practitioner.ir_act_server_1
msgid "Send email: Book your next appointment"
msgstr "Invia e-mail: prenota il prossimo appuntamento"

#. module: wellness_practitioner
#: model:mail.template,description:wellness_practitioner.booking_suggestion
msgid "Sent to all attendees if a reminder is set"
msgstr "E-mail inviata ai partecipanti se viene configurato un promemoria"

#. module: wellness_practitioner
#: model:base.automation,name:wellness_practitioner.base_automation_1
msgid "Stage is set to \"Qualified\""
msgstr "La fase è impostata su \"Qualificata\""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointment App</font></strong> "
"is automatically updated based on your Calendar. You can easily lock slots, "
"change appointment hoursand send communication to your patients fromthe "
"Calendar App."
msgstr ""
"L'app <strong><font class=\"text-o-color-1\">Appuntamenti</font></strong> "
"viene aggiornata automaticamente in base al Calendario. Puoi bloccare slot "
"in modo semplice, modificare l'orario degli appuntamenti e inviare "
"comunicazioni ai pazienti dall'app Calendario."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointments App</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> manages your "
"appointment types.</span></font><font class=\"text-o-color-1\"/></strong>"
msgstr ""
"L'applicazione <strong><font class=\"text-o-"
"color-1\">Appuntamenti</font><font class=\"text-black\"><span style=\"font-"
"weight: normal;\"> gestisce i tipi di appuntamento.</span></font><font "
"class=\"text-o-color-1\"/></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">CRM App</font></strong>(Customer "
"Relationship Management) is the heart of your therapy practice's patient "
"management."
msgstr ""
"L'applicazione <strong><font class=\"text-o-color-1\">CRM</font></strong> "
"(Customer Relationship Management) è il fulcro della gestione delle terapie "
"dei pazienti."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The Odoo Calendar App is compatible with Outlook and Google Calendar. "
"Activate the sync in the Calendar App settings."
msgstr ""
"L'applicazione Calendario di Odoo è compatibile con Outlook e Google "
"Calendar. Attiva la sincronizzazione nelle impostazioni dell'app."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Tutto gratuito nel tuo abbonamento attuale. Cosa aspetti? Fai un giro! 🙃"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"This module provides a comprehensive suite of applications tailored to the "
"needs of therapy practices, empowering you to manage your business "
"efficiently and deliver exceptional patient care."
msgstr ""
"Il modulo offre una suite completa di applicazioni personalizzate per le "
"esigenze degli studi di terapia, consentendo di gestire l'attività in modo "
"efficiente e di fornire un'assistenza eccezionale ai pazienti."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "To do so, go to CRM &gt; Configuration &gt; Sales Teams."
msgstr "Per farlo, apri l'app CRM &gt; Configurazione &gt; Team di vendita."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"To reduce your no show rate drastically, appointments automatically comes "
"with reminders by Email and SMS."
msgstr ""
"Per ridurre drasticamente il tasso di mancata presentazione, gli "
"appuntamenti vengono automaticamente ricordati via e-mail e SMS."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Try it !"
msgstr "Prova!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Use Case"
msgstr "Caso d'uso"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Using Odoo, you can easily take notes during your appointments, you can "
"either use the \"Log Note\" button on your contact record or create a "
"specific knowledge article (like this one) for each of your patient."
msgstr ""
"Utilizzando Odoo, puoi prendere appunti in modo semplice durante gli "
"appuntamenti usando il pulsante \"Registra nota\" sul record del contatto "
"oppure creando un articolo knowledge specifico (come questo) per ogni "
"paziente."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Welcome to the <strong><font class=\"text-o-color-1\">Odoo Therapy Practice "
"package</font></strong>!"
msgstr ""
"Benvenuto nel <strong><font class=\"text-o-color-1\">pacchetto Odoo studio "
"di terapie</font></strong>!"

#. module: wellness_practitioner
#: model_terms:web_tour.tour,rainbow_man_message:wellness_practitioner.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Benvenuto! Buona visita."

#. module: wellness_practitioner
#: model:appointment.type,name:wellness_practitioner.appointment_type_1
msgid "Wellness Session"
msgstr "Sessione di benessere"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Vorresti parlare della configurazione di Odoo oppure ottenere nuove "
"informazioni?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Puoi accedere a tutte le app installate nel tuo database Odoo dalla "
"dashboard principale. "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can also configure an email alias so everyone sending email to this one "
"will create a new opportunity for you."
msgstr ""
"È possibile configurare un alias e-mail in modo che tutte le persone che "
"inviano un'e-mail si trasformeranno in nuove opportunità."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can set the consultation with an upfront online payment. Activate it in "
"your Appointment Type Settingsby setting up an upfront payment method."
msgstr ""
"È possibile configurare il pagamento anticipato per la consulenza. Attivalo "
"nelle impostazioni del Tipo di appuntamento configurando un metodo per il "
"pagamento anticipato."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Hai completato il caso d'uso demo! Ci sono milioni di altri modi per "
"adattare la configurazione di Odoo ai bisogni del tuo business."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Therapy Practice package and check the related box."
msgstr ""
"Non hai importato i dati demo? Puoi ancora farlo. Vai su App &gt; Industrie "
"&gt; Aggiorna il pacchetto Studio di terapie e spunta la casella "
"corrispondente."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You may also create a personalized website in a few clicks by adding the "
"Website App, your appointment page will be automatically added."
msgstr ""
"È inoltre possibile creare un sito web personalizzato in pochi clic "
"aggiungendo l'applicazione Sito web. La pagina dedicata agli appuntamenti "
"verrà aggiunta automaticamente."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You receive a call from a new potential patient, Emily, who is interested in"
" your therapy services."
msgstr ""
"Ricevi una chiamata da un nuovo potenziale paziente: Emily, interessata ai "
"servizi di terapia."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"Dovresti essere in grado di eseguire i seguenti flussi per avere una "
"panoramica di quelli che puoi sfruttare con il pacchetto utilizzando Odoo."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "academy"
msgstr "academy"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "and"
msgstr "e"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "documentation"
msgstr "documentazione"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "if you need help!<br/>"
msgstr "se hai bisogno di aiuto!<br/>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "request a demo"
msgstr "richiedi una demo"
