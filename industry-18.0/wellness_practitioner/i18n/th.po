# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* wellness_practitioner
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>hip<PERSON>, 2024
# Wil O<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:14+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>iam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: wellness_practitioner
#: model:mail.template,body_html:wellness_practitioner.booking_suggestion
msgid ""
"\n"
"            <p>\n"
"                Hello,\n"
"            </p>\n"
"            <p>\n"
"                Thank you for your presence. May I invite you to schedule your next appointment if needed?\n"
"            </p>\n"
"            <p>\n"
"                Looking forward to meeting you.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Schedule an Appointment</a>\n"
"            </p>\n"
"        "
msgstr ""
"\n"
"            <p>\n"
"                สวัสดีค่ะ\n"
"            </p>\n"
"            <p>\n"
"                ขอขอบคุณที่เข้าร่วม เราขอเชิญคุณกำหนดเวลาการนัดหมายครั้งต่อไป\n"
"            </p>\n"
"            <p>\n"
"                เราหวังว่าจะได้พบคุณเร็วๆ นี้อีกครั้ง\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">กำหนดการนัดหมาย</a>\n"
"            </p>\n"
"        "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ", and feel free to"
msgstr "และโปรด"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "1. Create a new opportunity"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "2. Book an appointment"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "3. Verify your schedule"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "4. Take notes during your appointment"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "5. Check your invoices"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Use Case</span></font></strong></span>"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>พื้นฐาน</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further?</strong></span>"
msgstr ""
"<span class=\"h1-fs\"><strong>คุณต้องการที่จะไปต่อหรือไม่?</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Manage your schedule and your availability in the "
"</span></font><font class=\"text-o-color-1\">Calendar App</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">.</span></font></strong>"
msgstr ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">จัดการตารางเวลาและความพร้อมของคุณใน</span></font><font "
"class=\"text-o-color-1\">แอปปฏิทิน</font><font class=\"text-black\"><span "
"style=\"font-weight: normal;\"></span></font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><span class=\"display-4-fs\">Odoo for Therapy "
"Practices</span></strong>"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"A free, personalized, integrated<strong><font class=\"text-o-color-1\"> "
"Website </font></strong>in a few clicks. Get new leads or direct appointment"
" online in a few clicks."
msgstr ""
"<strong><font class=\"text-o-"
"color-1\">เว็บไซต์</font></strong>ที่ปรับแต่งได้และฟรีภายในไม่กี่คลิก "
"รับลูกค้ารายใหม่หรือนัดหมายออนไลน์โดยตรงภายในไม่กี่คลิก"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Add an upfront payment in your Appointment Type."
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Add the Subscription App and invoice automatically based on a subscription "
"type."
msgstr ""
"เพิ่มแอปสมัครสมาชิกและออกใบแจ้งหนี้โดยอัตโนมัติตามประเภทการสมัครสมาชิก"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"After each appointment, an automatic email will be sent to suggest your "
"patient to already book its next appointment."
msgstr ""
"หลังการนัดหมายแต่ละครั้ง "
"ระบบจะส่งอีเมลอัตโนมัติเพื่อแนะนำให้ผู้ป่วยของคุณจองการนัดหมายครั้งต่อไปแล้ว"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"As you qualify Emily's needs, you update the lead status to \"Qualified\" in"
" the CRM pipeline."
msgstr ""
"เมื่อคุณตอบสนองความต้องการของ Emily แล้ว "
"คุณจะอัปเดตสถานะของลูกค้าเป้าหมายเป็น \"ผ่านการประเมิน\" ในไปป์ไลน์ CRM"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Auto-post your invoice on a regular basis."
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Automated recurring invoicing with the <strong><font class=\"text-o-"
"color-1\">Subscription App</font></strong>."
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Basics"
msgstr "พื้นฐาน"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Be careful, using the \"Log Note\" in your contact with be displayed only "
"for you and your eventual team. If you select the \"Send message\" option, "
"this will notify your customer (which can be really interesting also)."
msgstr ""
"ระวังการใช้ \"บันทึก\" "
"ในรายชื่อติดต่อของคุณอาจแสดงเฉพาะคุณและทีมงานของคุณเท่านั้น "
"หากคุณเลือกตัวเลือก \"ส่งข้อความ\" ระบบจะแจ้งให้ลูกค้าของคุณทราบ "
"(ซึ่งอาจน่าสนใจ)"

#. module: wellness_practitioner
#: model:mail.template,name:wellness_practitioner.booking_suggestion
msgid "Book your next appointment"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By installing this package, you'll have access to a range of essential apps,"
" including CRM, Appointment and Invoicing. Let's explore how these modules "
"can streamline your therapy practice operations."
msgstr ""
"เมื่อติดตั้งแพ็คเกจนี้ คุณจะสามารถเข้าถึงแอปที่จำเป็นอีกได้มากมาย รวมถึงแอป "
"CRM การนัดหมาย และการออกใบแจ้งหนี้ "
"มาร่วมค้นพบกันว่าโมดูลเหล่านี้จะช่วยปรับปรุงการดำเนินงานของคลินิกบำบัดของคุณได้อย่างไร"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few contacts."
msgstr ""
"โดยการอัปโหลดข้อมูลสาธิตของโมดูลนี้ "
"ฐานข้อมูลของคุณก็จะเต็มไปด้วยข้อมูลติดต่อบางส่วน"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "ค้นพบเพิ่มเติมเกี่ยวกับ Odoo โดยเข้าไปที่"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Do you want to go further?"
msgstr "คุณต้องการดำเนินการต่อหรือเปล่า?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Ease your invoicing process with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr ""

#. module: wellness_practitioner
#: model:calendar.alarm,name:wellness_practitioner.alarm_mail_1
msgid "Follow up"
msgstr "ติดตามผล"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Getting Started\n"
"            To begin, you'll find the key applications you need on your Odoo dashboard. Let's take a closer look at how you can use these tools."
msgstr ""
"การเริ่มต้น\n"
"            ในการเริ่มต้น คุณจะพบแอปพลิเคชันหลักที่คุณต้องการบนแดชบอร์ด Odoo ของคุณ มาดูกันว่าคุณสามารถใช้เครื่องมือเหล่านี้ได้อย่างไร"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you navigate to the Invoice App, you can manage your invoices. You have "
"many possibilities to streamline your invoicing process:"
msgstr ""
"หากคุณไปที่แอปใบแจ้งหนี้ คุณสามารถจัดการใบแจ้งหนี้ของคุณได้ "
"คุณมีทางเลือกมากมายในการปรับกระบวนการออกใบแจ้งหนี้ของคุณให้มีประสิทธิภาพ:"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you open Emily's opportunity in your CRM, you can see in the Chatter (on "
"the right column) that she received an email from you with a call to action "
"to book an appointment."
msgstr ""
"หากคุณเปิดโอกาสทางการขายของ Emily ใน CRM คุณจะเห็นในช่องแชท "
"(ในคอลัมน์ด้านขวา) "
"ว่าเธอได้รับอีเมลจากคุณพร้อมคำกระตุ้นการดำเนินการเพื่อจองการนัดหมาย"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to adapt your flow and set new automation rules, click the Gear "
"Icon in the top of a CRM column and select \"Automation\", or add automated "
"email in your Appointment Type."
msgstr ""
"หากคุณต้องการปรับเปลี่ยนโฟล์วและตั้งกฎการทำงานอัตโนมัติใหม่ "
"ให้คลิกที่ไอคอนรูปเฟืองที่ด้านบนของคอลัมน์ CRM และเลือก "
"\"การทำงานอัตโนมัติ\" หรือเพิ่มอีเมลอัตโนมัติในประเภทการนัดหมายของคุณ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to share your calendar easily, navigate to the Appointment App "
"and select \"Share\" on the Edit Button."
msgstr ""
"หากคุณต้องการแชร์ปฏิทินของคุณได้อย่างง่ายดาย ให้ไปที่แอปนัดหมาย และเลือก "
"\"แชร์\" บนปุ่มแก้ไข"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"In the <strong><font class=\"text-o-color-1\">CRM App</font></strong>, you "
"create a new lead for Emily, capturing her contact information and the "
"reason for her inquiry."
msgstr ""
"ใน<strong><font class=\"text-o-color-1\">แอป CRM</font></strong> "
"คุณจะสร้างลูกค้าเป้าหมายรายใหม่ให้กับ Emily "
"โดยรวบรวมข้อมูลการติดต่อและเหตุผลในการสอบถามของเธอ"

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_5
msgid "Inactive"
msgstr "ปิดใช้งาน"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Invoice yourself, manually."
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Let's Try"
msgstr ""

#. module: wellness_practitioner
#: model:mail.template,subject:wellness_practitioner.booking_suggestion
msgid "Looking forward to meeting you"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo<strong><font "
"class=\"text-o-color-1\"> Marketing Automation "
"</font></strong>,<strong><font class=\"text-o-color-1\">Email "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong>, and <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."
msgstr ""
"จัดการช่องทางการสื่อสารทั้งหมดของคุณในที่เดียวด้วย <strong><font "
"class=\"text-o-color-1\">การตลาดแบบอัตโนมัติ</font></strong>, <strong><font "
"class=\"text-o-color-1\">อีเมลมาร์เก็ตติ้ง</font></strong>, <strong><font "
"class=\"text-o-color-1\">การตลาดผ่านโซเชียล</font></strong> และ "
"<strong><font class=\"text-o-color-1\">การตลาดผ่าน SMS</font></strong> ของ "
"Odoo"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"จัดการบัญชีของคุณได้ง่ายกว่าที่เคยด้วยสภาพแวดล้อมที่รวมระบบอย่างสมบูรณ์ "
"(<strong><font class=\"text-o-color-1\">แอประบบบัญชี</font></strong>)"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your team's and integrate them in all processes by adding "
"the<strong><font class=\"text-o-color-1\"> Employees App</font></strong>."
msgstr ""
"จัดการทีมของคุณและผสานรวมพวกเขาเข้าในทุกกระบวนการโดยการเพิ่มพวกเขาไปที่<strong><font"
" class=\"text-o-color-1\"> แอปข้อมูลพนักงาน </font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo for Therapy Practices"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone to "
"manage your entire business in it."
msgstr ""
"Odoo ได้รวมระบบเข้ากับแอปอย่างสมบูรณ์ "
"ดาวน์โหลดเพื่อให้โทรศัพท์ของคุณจัดการธุรกิจทั้งหมดได้"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo มอบความเป็นไปได้ที่ไม่มีที่สิ้นสุดให้กับคุณ เช่น:"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"แน่นอนว่านี่เป็นเพียงภาพรวมของฟีเจอร์ที่รวมอยู่ในแพ็คเกจนี้เท่านั้น "
"เพิ่มแอปใหม่ ลบ/แก้ไขข้อมูลสาธิต และทดสอบทุกอย่างได้ตามใจชอบ!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Once the appointment is booked, navigate to the <strong><font class=\"text-"
"o-color-1\">Calendar App</font></strong>. You may find the new appointment. "
"You can also move it (Emily will be notified) or cancel it."
msgstr ""
"เมื่อจองการนัดหมายแล้ว ให้ไปที่<strong><font class=\"text-o-"
"color-1\">แอปปฏิทิน</font></strong> คุณจะพบการนัดหมายใหม่ "
"คุณสามารถย้ายการนัดหมาย ( Emily จะได้รับการแจ้งเตือน) หรือยกเลิกได้"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Organise your events and connect with your patients easily with the "
"<strong><font class=\"text-o-color-1\">Events App </font></strong>."
msgstr ""
"จัดระเบียบกิจกรรมของคุณและเชื่อมต่อกับคนไข้ของคุณได้อย่างง่ายดายด้วย<strong><font"
" class=\"text-o-color-1\">แอปกิจกรรม</font></strong>"

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_6
msgid "Redirect to Another Practitioner"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Schedule a demo"
msgstr "กำหนดเวลาสาธิต"

#. module: wellness_practitioner
#: model:ir.actions.server,name:wellness_practitioner.ir_act_server_1
msgid "Send email: Book your next appointment"
msgstr ""

#. module: wellness_practitioner
#: model:mail.template,description:wellness_practitioner.booking_suggestion
msgid "Sent to all attendees if a reminder is set"
msgstr "ส่งถึงผู้เข้าร่วมทุกคนหากมีการตั้งค่าการแจ้งเตือนไว้"

#. module: wellness_practitioner
#: model:base.automation,name:wellness_practitioner.base_automation_1
msgid "Stage is set to \"Qualified\""
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointment App</font></strong> "
"is automatically updated based on your Calendar. You can easily lock slots, "
"change appointment hoursand send communication to your patients fromthe "
"Calendar App."
msgstr ""
"<strong><font class=\"text-o-color-1\">แอปการนัดหมาย</font></strong> "
"ได้รับการอัปเดตโดยอัตโนมัติตามปฏิทินของคุณ คุณสามารถล็อกช่วงเวลา "
"เปลี่ยนเวลานัดหมาย "
"และส่งการสื่อสารถึงผู้ป่วยของคุณจากแอปปฏิทินได้อย่างง่ายดาย"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointments App</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> manages your "
"appointment types.</span></font><font class=\"text-o-color-1\"/></strong>"
msgstr ""
"<strong><font class=\"text-o-color-1\">แอปการนัดหมาย</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">ช่วยจัดการประเภทการนัดหมายของคุณ</span></font><font class=\"text-"
"o-color-1\"/></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">CRM App</font></strong>(Customer "
"Relationship Management) is the heart of your therapy practice's patient "
"management."
msgstr ""
"<strong><font class=\"text-o-color-1\">แอป CRM</font></strong> "
"(การจัดการความสัมพันธ์ลูกค้า) "
"ถือเป็นหัวใจสำคัญของการจัดการผู้ป่วยของคลินิกการบำบัดของคุณ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The Odoo Calendar App is compatible with Outlook and Google Calendar. "
"Activate the sync in the Calendar App settings."
msgstr ""
"แอปปฏิทินของ Odoo เข้ากันได้กับ Outlook และ Google Calendar "
"เปิดใช้งานการซิงค์ในการตั้งค่าแอปปฏิทิน"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "ทั้งหมดนี้ฟรีในการสมัครสมาชิกปัจจุบันของคุณ อย่าลังเลที่จะลองใช้ดู! 🙃"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"This module provides a comprehensive suite of applications tailored to the "
"needs of therapy practices, empowering you to manage your business "
"efficiently and deliver exceptional patient care."
msgstr ""
"โมดูลนี้ประกอบด้วยชุดแอปพลิเคชันที่ครอบคลุมซึ่งออกแบบมาเพื่อให้ตรงกับความต้องการของการบำบัด"
" "
"ช่วยให้คุณสามารถจัดการธุรกิจได้อย่างมีประสิทธิภาพและมอบการดูแลผู้ป่วยที่ยอดเยี่ยม"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "To do so, go to CRM &gt; Configuration &gt; Sales Teams."
msgstr "หากต้องการดำเนินการดังกล่าว ให้ไปที่ CRM &gt; การกำหนดค่า &gt; ทีมขาย"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"To reduce your no show rate drastically, appointments automatically comes "
"with reminders by Email and SMS."
msgstr ""
"เพื่อลดอัตราการไม่แสดงตัวของคุณ "
"การนัดหมายจะมาพร้อมกับการแจ้งเตือนทางอีเมลและ SMS โดยอัตโนมัติ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Try it !"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Use Case"
msgstr "กรณีการใช้งาน"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Using Odoo, you can easily take notes during your appointments, you can "
"either use the \"Log Note\" button on your contact record or create a "
"specific knowledge article (like this one) for each of your patient."
msgstr ""
"คุณสามารถจดบันทึกระหว่างการนัดหมายได้อย่างง่ายดายโดยใช้ Odoo "
"ซึ่งคุณสามารถใช้ปุ่ม \"จดบันทึก\" "
"ในรายชื่อผู้ติดต่อของคุณหรือสร้างบทความความรู้เฉพาะ (เช่นบทความนี้) "
"สำหรับผู้ป่วยแต่ละคนก็ได้"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Welcome to the <strong><font class=\"text-o-color-1\">Odoo Therapy Practice "
"package</font></strong>!"
msgstr ""

#. module: wellness_practitioner
#: model_terms:web_tour.tour,rainbow_man_message:wellness_practitioner.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ยินดีต้อนรับ! ขอให้สนุกกับการค้นพบ"

#. module: wellness_practitioner
#: model:appointment.type,name:wellness_practitioner.appointment_type_1
msgid "Wellness Session"
msgstr ""

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"คุณต้องการพูดคุยเกี่ยวกับการตั้งค่า Odoo "
"ของคุณกับเราหรือพูดคุยเพิ่มเติมหรือไม่"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"คุณสามารถเข้าถึงทุกแอปที่ติดตั้งในฐานข้อมูล Odoo ของคุณได้บนแดชบอร์ดหลัก"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can also configure an email alias so everyone sending email to this one "
"will create a new opportunity for you."
msgstr ""
"คุณสามารถกำหนดค่าชื่ออีเมลได้ "
"เพื่อให้ทุกคนที่ส่งอีเมลถึงบัญชีนี้จะสร้างโอกาสทางการขายใหม่ให้กับคุณได้"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can set the consultation with an upfront online payment. Activate it in "
"your Appointment Type Settingsby setting up an upfront payment method."
msgstr ""
"คุณสามารถตั้งค่าการปรึกษาหารือด้วยการชำระเงินล่วงหน้าทางออนไลน์ได้ "
"เปิดใช้งานได้ในการตั้งค่าประเภทการนัดหมายโดยตั้งค่าวิธีการชำระเงินล่วงหน้า"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"คุณได้ทำกรณีตัวอย่างการใช้งานสำเร็จแล้ว! "
"มีอีกนับล้านวิธีในการปรับแต่งการตั้งค่า Odoo "
"ให้เหมาะกับความต้องการทางธุรกิจของคุณ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Therapy Practice package and check the related box."
msgstr ""
"คุณไม่ได้นำเข้าข้อมูลสาธิตใช่หรือไม่? คุณยังสามารถนำเข้าได้ ไปที่ แอป &gt; "
"อุตสาหกรรม &gt; อัปเกรดแพ็กเกจการปฏิบัติทางการบำบัดของคุณ "
"และทำเครื่องหมายในช่องที่เกี่ยวข้อง"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You may also create a personalized website in a few clicks by adding the "
"Website App, your appointment page will be automatically added."
msgstr ""
"คุณสามารถสร้างเว็บไซต์ส่วนตัวของคุณได้ในไม่กี่คลิก โดยการเพิ่มแอปเว็บไซต์ "
"หน้าการนัดหมายของคุณจะถูกเพิ่มโดยอัตโนมัติ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You receive a call from a new potential patient, Emily, who is interested in"
" your therapy services."
msgstr "คุณได้รับโทรศัพท์จากผู้ป่วยรายใหม่ เอมิลี่ เธอสนใจบริการบำบัดของคุณ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"คุณควรสามารถดำเนินการตามโฟลว์ต่อไปนี้ได้เพื่อให้มีภาพรวมของโฟลว์ต่างๆ "
"ที่คุณสามารถดำเนินการได้อย่างรวดเร็วด้วยแพ็คเกจนี้โดยใช้ Odoo"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "academy"
msgstr "สถาบันการศึกษา"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "and"
msgstr "และ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "documentation"
msgstr "เอกสารกำกับ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "if you need help!<br/>"
msgstr "หากคุณต้องการความช่วยเหลือ! <br/>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "request a demo"
msgstr "ขอสาธิตการใช้งาน"
