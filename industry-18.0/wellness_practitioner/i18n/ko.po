# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* wellness_practitioner
# 
# Translators:
# Wil Odoo, 2024
# Sarah <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:14+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: wellness_practitioner
#: model:mail.template,body_html:wellness_practitioner.booking_suggestion
msgid ""
"\n"
"            <p>\n"
"                Hello,\n"
"            </p>\n"
"            <p>\n"
"                Thank you for your presence. May I invite you to schedule your next appointment if needed?\n"
"            </p>\n"
"            <p>\n"
"                Looking forward to meeting you.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Schedule an Appointment</a>\n"
"            </p>\n"
"        "
msgstr ""
"\n"
"            <p>\n"
"                안녕하세요,\n"
"            </p>\n"
"            <p>\n"
"                참석해 주셔서 감사합니다. 필요하신 경우 다음 일정을 예약하실 수 있도록 초대를 보내드리겠습니다.\n"
"            </p>\n"
"            <p>\n"
"                곧 만나뵐 수 있기를 기대하겠습니다.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">일정 예약하기</a>\n"
"            </p>\n"
"        "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ", and feel free to"
msgstr ", 자유롭게"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "1. Create a new opportunity"
msgstr "1. 새로운 기회 만들기"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "2. Book an appointment"
msgstr "2. 일정 예약하기"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "3. Verify your schedule"
msgstr "3. 일정 확인하기"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "4. Take notes during your appointment"
msgstr "4. 예약 중 메모하기"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "5. Check your invoices"
msgstr "5. 청구서 확인"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Use Case</span></font></strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">사용 사례</span></font></strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>베이직</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further?</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>더 자세히 알아보시겠습니까?</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Manage your schedule and your availability in the "
"</span></font><font class=\"text-o-color-1\">Calendar App</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">.</span></font></strong>"
msgstr ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\"><font class=\"text-o-color-1\">캘린더 앱</font><font class=\"text-"
"black\"><span style=\"font-weight: normal;\">에서 일정과 근무 가능 여부를 "
"관리하세요.</span></font></span></font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><span class=\"display-4-fs\">Odoo for Therapy "
"Practices</span></strong>"
msgstr "<strong><span class=\"display-4-fs\">치료 실습을 위한 Odoo</span></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"A free, personalized, integrated<strong><font class=\"text-o-color-1\"> "
"Website </font></strong>in a few clicks. Get new leads or direct appointment"
" online in a few clicks."
msgstr ""
"단 몇 번의 클릭만으로 맞춤화된 무료 통합 <strong><font class=\"text-o-"
"color-1\">웹사이트</font></strong>를 구축할 수 있습니다. 클릭 몇 번으로 온라인에서 새로운 잠재고객을 확보하거나 "
"직접 일정을 잡아보세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Add an upfront payment in your Appointment Type."
msgstr "예약 유형에 선불 결제 옵션을 포함하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Add the Subscription App and invoice automatically based on a subscription "
"type."
msgstr "정기구독 앱을 추가하고 정기구독 유형에 따라 자동 청구서 기능을 사용 설정합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"After each appointment, an automatic email will be sent to suggest your "
"patient to already book its next appointment."
msgstr "각 예약 후에는 환자가 다음 예약을 설정하도록 권장하는 자동 이메일이 전송됩니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"As you qualify Emily's needs, you update the lead status to \"Qualified\" in"
" the CRM pipeline."
msgstr "Emily의 요구 사항을 평가하면서 CRM 파이프라인에서 잠재고객 상태를 \"적격\"으로 업데이트합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Auto-post your invoice on a regular basis."
msgstr "정기적으로 청구서를 자동 게시합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Automated recurring invoicing with the <strong><font class=\"text-o-"
"color-1\">Subscription App</font></strong>."
msgstr ""
"<strong><font class=\"text-o-color-1\">구독 앱</font></strong>으로 반복 청구서를 자동 "
"발행합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Basics"
msgstr "베이직"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Be careful, using the \"Log Note\" in your contact with be displayed only "
"for you and your eventual team. If you select the \"Send message\" option, "
"this will notify your customer (which can be really interesting also)."
msgstr ""
"연락처에서 \"로그 노트\" 옵션을 사용하면 사용자와 담당 팀에게만 표시된다는 점에 주의하세요. \"메시지 보내기\" 옵션을 선택하면 "
"고객에게도 알림이 전송되므로 더욱 유용할 수 있습니다."

#. module: wellness_practitioner
#: model:mail.template,name:wellness_practitioner.booking_suggestion
msgid "Book your next appointment"
msgstr "다음 일정 예약하기"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By installing this package, you'll have access to a range of essential apps,"
" including CRM, Appointment and Invoicing. Let's explore how these modules "
"can streamline your therapy practice operations."
msgstr ""
"이 패키지를 설치하면 CRM, 일정 예약, 청구서를 비롯한 다양한 필수 앱에 액세스할 수 있습니다. 이러한 모듈을 통해 어떻게 진료소를 "
"능률적으로 운영할 수 있는지 살펴보세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few contacts."
msgstr "이 모듈의 데모 데이터를 업로드하면 데이터베이스가 몇 개의 연락처로 채워집니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Odoo에 대해 자세히 알아보세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Do you want to go further?"
msgstr "더 자세히 알아보시겠습니까?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Ease your invoicing process with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr ""
"<strong><font class=\"text-o-color-1\">청구서 앱</font></strong>으로 인보이스 발행 프로세스를"
" 간편하게 처리하세요."

#. module: wellness_practitioner
#: model:calendar.alarm,name:wellness_practitioner.alarm_mail_1
msgid "Follow up"
msgstr "후속 조치"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Getting Started\n"
"            To begin, you'll find the key applications you need on your Odoo dashboard. Let's take a closer look at how you can use these tools."
msgstr ""
"시작하기\n"
"            먼저 Odoo 현황판에서 필수 앱들을 찾을 수 있습니다. 이러한 도구를 어떻게 활용할 수 있는지 자세히 살펴보겠습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you navigate to the Invoice App, you can manage your invoices. You have "
"many possibilities to streamline your invoicing process:"
msgstr "청구서 앱으로 이동하면 청구서를 관리하고 청구 프로세스를 간편하게 처리할 수 있는 다양한 옵션을 살펴볼 수 있습니다:"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you open Emily's opportunity in your CRM, you can see in the Chatter (on "
"the right column) that she received an email from you with a call to action "
"to book an appointment."
msgstr ""
"CRM에서 Emily의 영업기회를 열면 채팅 (오른쪽 열)에서 Emily가 일정 예약을 위한 클릭 유도 문안이 포함된 이메일을 받았다는 "
"것을 확인할 수 있습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to adapt your flow and set new automation rules, click the Gear "
"Icon in the top of a CRM column and select \"Automation\", or add automated "
"email in your Appointment Type."
msgstr ""
"워크플로우를 조정하고 새 자동화 규칙을 설정하려면 CRM 열 상단의 톱니바퀴 아이콘을 클릭하고 '자동화'를 선택하거나 일정 유형에 자동 "
"이메일을 추가하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to share your calendar easily, navigate to the Appointment App "
"and select \"Share\" on the Edit Button."
msgstr "캘린더를 쉽게 공유하려면 일정 예약 앱으로 이동하여 편집 버튼에서 \"공유\"를 선택합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"In the <strong><font class=\"text-o-color-1\">CRM App</font></strong>, you "
"create a new lead for Emily, capturing her contact information and the "
"reason for her inquiry."
msgstr ""
"<strong><font class=\"text-o-color-1\">CRM 앱</font></strong>에서 Emily의 연락처 "
"정보와 문의 내용을 파악하여 Emily에 대한 새 리드를 생성합니다."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_5
msgid "Inactive"
msgstr "비활성"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Invoice yourself, manually."
msgstr "직접 수동으로 청구서를 발행하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Let's Try"
msgstr "다음"

#. module: wellness_practitioner
#: model:mail.template,subject:wellness_practitioner.booking_suggestion
msgid "Looking forward to meeting you"
msgstr "만나뵙기를 기다리겠습니다"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo<strong><font "
"class=\"text-o-color-1\"> Marketing Automation "
"</font></strong>,<strong><font class=\"text-o-color-1\">Email "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong>, and <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."
msgstr ""
"Odoo로 모든 커뮤니케이션 채널을 한곳에서 관리하세요. <strong><font class=\"text-o-color-1\">마케팅 "
"자동화</font></strong>, <strong><font class=\"text-o-color-1\">이메일 "
"마케팅</font></strong>, <strong><font class=\"text-o-color-1\">소셜 미디어 "
"마케팅</font></strong>, <strong><font class=\"text-o-color-1\">SMS "
"마케팅</font></strong>."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"완전히 통합된 환경으로 보다 효율적으로 회계를 관리하세요. (<strong><font class=\"text-o-color-1\">회계 "
"앱</font></strong>)"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your team's and integrate them in all processes by adding "
"the<strong><font class=\"text-o-color-1\"> Employees App</font></strong>."
msgstr ""
"<strong><font class=\"text-o-color-1\">직원 앱</font></strong>을 추가하여 팀을 관리하고 모든"
" 프로세스에 통합하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo for Therapy Practices"
msgstr "치료 실습을 위한 Odoo"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone to "
"manage your entire business in it."
msgstr "Odoo는 모바일 앱과도 완벽하게 통합되어 있습니다. 스마트폰에 앱을 다운로드하여 비즈니스 전체를 관리하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo는 다음과 같은 무한한 가능성을 제공합니다 :"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"이 개요에서는 이 패키지에 포함된 기능을 중점적으로 설명합니다. 자유롭게 새 앱을 추가하고, 데모 데이터를 수정 또는 삭제하고, 모든 "
"기능을 테스트해 보세요!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Once the appointment is booked, navigate to the <strong><font class=\"text-"
"o-color-1\">Calendar App</font></strong>. You may find the new appointment. "
"You can also move it (Emily will be notified) or cancel it."
msgstr ""
"일정이 예약되면 <strong><font class=\"text-o-color-1\">캘린더 앱</font></strong>으로 이동하여"
" 새 일정을 확인할 수 있습니다. 일정을 다른 날짜로 옮기거나 취소할 경우 Emily에게 알림이 전송됩니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Organise your events and connect with your patients easily with the "
"<strong><font class=\"text-o-color-1\">Events App </font></strong>."
msgstr ""
"<strong><font class=\"text-o-color-1\">행사 앱</font></strong>을 사용하여 손쉽게 이벤트를 "
"구성하고 고객과 소통하세요."

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_6
msgid "Redirect to Another Practitioner"
msgstr "다른 담당자에게 리디렉션"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Schedule a demo"
msgstr "데모 예약하기"

#. module: wellness_practitioner
#: model:ir.actions.server,name:wellness_practitioner.ir_act_server_1
msgid "Send email: Book your next appointment"
msgstr "이메일 전송: 다음 일정 예약하기"

#. module: wellness_practitioner
#: model:mail.template,description:wellness_practitioner.booking_suggestion
msgid "Sent to all attendees if a reminder is set"
msgstr "알림 설정 시 모든 참석자에게 전송"

#. module: wellness_practitioner
#: model:base.automation,name:wellness_practitioner.base_automation_1
msgid "Stage is set to \"Qualified\""
msgstr "단계가 \"적격\"으로 설정되어 있습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointment App</font></strong> "
"is automatically updated based on your Calendar. You can easily lock slots, "
"change appointment hoursand send communication to your patients fromthe "
"Calendar App."
msgstr ""
"<strong><font class=\"text-o-color-1\">일정 예약 앱</font></strong>은 캘린더에 따라 자동으로"
" 업데이트됩니다. 캘린더 앱에서 쉽게 시간대를 잠그고, 예약 시간을 변경하고, 환자에게 메시지를 보낼 수 있습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointments App</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> manages your "
"appointment types.</span></font><font class=\"text-o-color-1\"/></strong>"
msgstr "일정 예약 앱에서 일정 유형을 관리합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">CRM App</font></strong>(Customer "
"Relationship Management) is the heart of your therapy practice's patient "
"management."
msgstr ""
"<strong><font class=\"text-o-color-1\">CRM 앱</font></strong>(고객 관계 관리)은 진료실 "
"환자 관리의 핵심입니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The Odoo Calendar App is compatible with Outlook and Google Calendar. "
"Activate the sync in the Calendar App settings."
msgstr "Odoo 캘린더 앱은 Outlook 및 Google 캘린더와 호환됩니다. 캘린더 앱 설정에서 동기화를 활성화하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "현재 구독 중인 요금제에서 모두 무료로 제공되니 자유롭게 살펴보세요! 🙃"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"This module provides a comprehensive suite of applications tailored to the "
"needs of therapy practices, empowering you to manage your business "
"efficiently and deliver exceptional patient care."
msgstr ""
"이 모듈은 치료 진료에 맞게 설계된 종합적인 앱 제품군을 제공하여 비즈니스를 효율적으로 관리하고 탁월한 환자 치료를 제공할 수 있도록 "
"지원합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "To do so, go to CRM &gt; Configuration &gt; Sales Teams."
msgstr "그러려면 CRM &gt; 설정 &gt; 영업 팀으로 이동합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"To reduce your no show rate drastically, appointments automatically comes "
"with reminders by Email and SMS."
msgstr "노쇼 비율을 크게 낮추기 위해 일정 예약에는 이메일과 SMS를 통한 자동 미리 알림이 제공됩니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Try it !"
msgstr "사용해 보세요!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Use Case"
msgstr "사용 사례"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Using Odoo, you can easily take notes during your appointments, you can "
"either use the \"Log Note\" button on your contact record or create a "
"specific knowledge article (like this one) for each of your patient."
msgstr ""
"Odoo를 사용하면 연락처 기록의 \"로그 노트\" 버튼을 사용하거나 각 환자에 대한 특정 지식 문서 (예: 이 문서)를 생성하여 진료 "
"중에 쉽게 메모를 할 수 있습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Welcome to the <strong><font class=\"text-o-color-1\">Odoo Therapy Practice "
"package</font></strong>!"
msgstr ""
"<strong><font class=\"text-o-color-1\">Odoo 테라피 실습 패키지</font></strong>에 오신 "
"것을 환영합니다!"

#. module: wellness_practitioner
#: model_terms:web_tour.tour,rainbow_man_message:wellness_practitioner.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "환영합니다! 마음껏 둘러 보세요."

#. module: wellness_practitioner
#: model:appointment.type,name:wellness_practitioner.appointment_type_1
msgid "Wellness Session"
msgstr "웰빙 세션"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "Odoo 설정에 대해 논의하거나 추가 기능을 살펴보고 싶으신가요?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr "메인 대시보드에서 Odoo 데이터베이스에 설치된 모든 앱에 액세스하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can also configure an email alias so everyone sending email to this one "
"will create a new opportunity for you."
msgstr "또한 이메일 별칭을 설정하여 이 별칭으로 전송되는 모든 이메일이 자동으로 새 기회를 생성하도록 할 수도 있습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can set the consultation with an upfront online payment. Activate it in "
"your Appointment Type Settingsby setting up an upfront payment method."
msgstr "선불 온라인 결제로 상담을 구성할 수 있습니다. 예약 유형 설정에서 선불 결제 방법을 설정하여 이 옵션을 활성화하세요."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr "데모 사용 사례를 완료했습니다! 비즈니스 요구에 맞게 Odoo 설정을 조정할 수 있는 다른 방법은 무수히 많습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Therapy Practice package and check the related box."
msgstr ""
"데모 데이터를 아직 가져오지 않으셨나요? 괜찮습니다, 지금 바로 가져올 수 있습니다. 앱  &gt; 산업  &gt; 치료 실습 패키지 "
"업그레이드로 이동하여 해당 체크박스를 선택합니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You may also create a personalized website in a few clicks by adding the "
"Website App, your appointment page will be automatically added."
msgstr "웹사이트 앱을 추가하면 몇 번의 클릭만으로도 맞춤 웹사이트를 만들 수 있으며, 예약 페이지가 자동으로 포함됩니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You receive a call from a new potential patient, Emily, who is interested in"
" your therapy services."
msgstr "귀하의 치료 서비스에 관심이 있는 새로운 잠재고객 Emily로부터 전화를 받습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"다음 플로우를 실행하면 Odoo를 사용하여 이 패키지로 효율적으로 실행할 수 있는 다양한 프로세스에 대한 개요를 확인할 수 있습니다."

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "academy"
msgstr "아카데미"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "and"
msgstr "그리고"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "documentation"
msgstr "관련 문서"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "if you need help!<br/>"
msgstr "if you need help!<br/>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "request a demo"
msgstr "데모 요청하기"
