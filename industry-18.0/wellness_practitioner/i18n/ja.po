# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* wellness_practitioner
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 06:14+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Junko <PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: wellness_practitioner
#: model:mail.template,body_html:wellness_practitioner.booking_suggestion
msgid ""
"\n"
"            <p>\n"
"                Hello,\n"
"            </p>\n"
"            <p>\n"
"                Thank you for your presence. May I invite you to schedule your next appointment if needed?\n"
"            </p>\n"
"            <p>\n"
"                Looking forward to meeting you.\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">Schedule an Appointment</a>\n"
"            </p>\n"
"        "
msgstr ""
"\n"
"            <p>\n"
"                こんにちは。\n"
"            </p>\n"
"            <p>\n"
"                ご来院いただきありがとうございます。必要であれば、次回の予約をお取りしますか？\n"
"            </p>\n"
"            <p>\n"
"                お会いできるのを心待ちにしております。\n"
"            </p>\n"
"            </br>\n"
"            <p>\n"
"                <a class=\"btn btn-secondary\" href=\"/book/wellness_session\">アポイントメントをスケジュール</a>\n"
"            </p>\n"
"        "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ", and feel free to"
msgstr "お気軽に"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "1. Create a new opportunity"
msgstr "1. 新規案件を作成する"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "2. Book an appointment"
msgstr "2. アポイントメントを予約する"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "3. Verify your schedule"
msgstr "3. スケジュールを確認する"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "4. Take notes during your appointment"
msgstr "4. アポイントメント中にメモを取る"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "5. Check your invoices"
msgstr "5. 顧客請求書を確認する"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🌐</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🎉</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🪂</i>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">Use Case</span></font></strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong><font class=\"text-800\"><span "
"class=\"h1-fs\">使用例</span></font></strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Basics</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>基本</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "<span class=\"h1-fs\"><strong>Do you want to go further?</strong></span>"
msgstr "<span class=\"h1-fs\"><strong>更に進みますか?</strong></span>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\">Manage your schedule and your availability in the "
"</span></font><font class=\"text-o-color-1\">Calendar App</font><font "
"class=\"text-black\"><span style=\"font-weight: "
"normal;\">.</span></font></strong>"
msgstr ""
"<strong><font class=\"text-black\"><span style=\"font-weight: "
"normal;\"><font class=\"text-o-color-1\">カレンダアプリ</font><font class=\"text-"
"black\"><span style=\"font-weight: "
"normal;\">でスケジュールと利用可能を管理しましょう。</span></font></span></font></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"<strong><span class=\"display-4-fs\">Odoo for Therapy "
"Practices</span></strong>"
msgstr "<strong><span class=\"display-4-fs\">治療業務用Odoo</span></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"A free, personalized, integrated<strong><font class=\"text-o-color-1\"> "
"Website </font></strong>in a few clicks. Get new leads or direct appointment"
" online in a few clicks."
msgstr ""
"無料でパーソナライズされた、統合済<strong><font class=\"text-o-color-1\"> ウェブサイト "
"</font></strong>を数クリックで。新規リード獲得やオンラインでの直接予約が数クリックで可能です。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Add an upfront payment in your Appointment Type."
msgstr "アポイントメントタイプに前払の支払いを追加します。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Add the Subscription App and invoice automatically based on a subscription "
"type."
msgstr "サブスクリプションアプリを追加し、サブスクリプションタイプに基づいて顧客請求書を自動的に作成しましょう。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"After each appointment, an automatic email will be sent to suggest your "
"patient to already book its next appointment."
msgstr "各アポイントメント後に、自動Eメールが送信され、患者に次の予約を促します。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"As you qualify Emily's needs, you update the lead status to \"Qualified\" in"
" the CRM pipeline."
msgstr " Emilyのニーズを満たす条件を満たすと、CRMパイプラインのリードステータスを\"見込あり\" に更新します。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Auto-post your invoice on a regular basis."
msgstr "顧客請求書を定期的に自動記帳します。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Automated recurring invoicing with the <strong><font class=\"text-o-"
"color-1\">Subscription App</font></strong>."
msgstr ""
" <strong><font class=\"text-o-"
"color-1\">サブスクリプションアプリ</font></strong>での定期的な請求の自動化。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Basics"
msgstr "基本"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Be careful, using the \"Log Note\" in your contact with be displayed only "
"for you and your eventual team. If you select the \"Send message\" option, "
"this will notify your customer (which can be really interesting also)."
msgstr ""
"連絡先として\"ログメモ\"を使用すると、あなたとあなたのチームメンバーのみに表示されるのでご注意下さい。\"メッセージ送信\"オプションを選択すると、顧客にも通知されるので、場合によっては非常に便利です。"

#. module: wellness_practitioner
#: model:mail.template,name:wellness_practitioner.booking_suggestion
msgid "Book your next appointment"
msgstr "次回アポイントメントを予約"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By installing this package, you'll have access to a range of essential apps,"
" including CRM, Appointment and Invoicing. Let's explore how these modules "
"can streamline your therapy practice operations."
msgstr ""
"このパッケージをインストールすると、CRM、アポイントメント、顧客請求など、さまざまな必須アプリにアクセスできるようになります。これらのモジュールによりいかに治療業務を効率化できるか見てみましょう。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few contacts."
msgstr "このモジュールのデモデータをアップロードすることで、データベースにいくつかの連絡先が追加されました。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Odooについて更に知りましょう。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Do you want to go further?"
msgstr "進みますか？"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Ease your invoicing process with the <strong><font class=\"text-o-"
"color-1\">Invoice App</font></strong>."
msgstr "<strong><font class=\"text-o-color-1\">請求書アプリ</font></strong>で請求プロセスを簡単に。"

#. module: wellness_practitioner
#: model:calendar.alarm,name:wellness_practitioner.alarm_mail_1
msgid "Follow up"
msgstr "フォローアップ"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Getting Started\n"
"            To begin, you'll find the key applications you need on your Odoo dashboard. Let's take a closer look at how you can use these tools."
msgstr ""
"はじめに\n"
"            まず最初に、Odooのステータスボードから必要なアプリを探すことができます。これらのツールをどのように活用できるかを詳しく見てみましょう。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you navigate to the Invoice App, you can manage your invoices. You have "
"many possibilities to streamline your invoicing process:"
msgstr "請求書アプリに移動すると、顧客請求書の管理ができます。請求プロセスを合理化する方法は沢山あります。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you open Emily's opportunity in your CRM, you can see in the Chatter (on "
"the right column) that she received an email from you with a call to action "
"to book an appointment."
msgstr "CRMでEmilyの案件を開くと、チャター(右列)に、予約を促すEメールを彼女が受信したことが表示されます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to adapt your flow and set new automation rules, click the Gear "
"Icon in the top of a CRM column and select \"Automation\", or add automated "
"email in your Appointment Type."
msgstr ""
" ワークフローを変更し、新しい自動化規則を設定したい場合は、CRM列の上部にある歯車アイコンをクリックし、 "
"\"自動化\"を選択するか、予約タイプに自動Eメールを追加します。    "

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"If you want to share your calendar easily, navigate to the Appointment App "
"and select \"Share\" on the Edit Button."
msgstr "カレンダーを簡単に共有したい場合は、予定アプリに移動し、編集ボタンの\"共有\"を選択します。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"In the <strong><font class=\"text-o-color-1\">CRM App</font></strong>, you "
"create a new lead for Emily, capturing her contact information and the "
"reason for her inquiry."
msgstr ""
"<strong><font class=\"text-o-"
"color-1\">CRMアプリ</font></strong>で、Emilyの連絡先情報と問い合わせの理由を記録し、新しいリードを作成します。"

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_5
msgid "Inactive"
msgstr "無効"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Invoice yourself, manually."
msgstr "手動で顧客請求書を作成して下さい。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Let's Try"
msgstr "試してみましょう"

#. module: wellness_practitioner
#: model:mail.template,subject:wellness_practitioner.booking_suggestion
msgid "Looking forward to meeting you"
msgstr "お目にかかれるのを楽しみにしております"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo<strong><font "
"class=\"text-o-color-1\"> Marketing Automation "
"</font></strong>,<strong><font class=\"text-o-color-1\">Email "
"Marketing</font></strong>,<strong><font class=\"text-o-color-1\"> Social "
"Media Marketing </font></strong>, and <strong><font class=\"text-o-"
"color-1\">SMS Marketing </font></strong>."
msgstr ""
"Odoo<strong><font class=\"text-o-color-1\"> マーケティング自動化 "
"</font></strong>で、<strong><font class=\"text-o-"
"color-1\">Eメールマーケティング</font></strong>,<strong><font class=\"text-o-"
"color-1\"> ソーシャルメディアマーケティング </font></strong>, そして <strong><font "
"class=\"text-o-color-1\">SMS マーケティング "
"</font></strong>など、全てのコミュニケーションチャンネルを一元管理できます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<strong><font class=\"text-o-color-1\">Accounting "
"App</font></strong>)"
msgstr ""
"完全に統合された環境で、これまで以上に簡単に会計を管理できます。(<strong><font class=\"text-o-"
"color-1\">会計アプリ</font></strong>)"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Manage your team's and integrate them in all processes by adding "
"the<strong><font class=\"text-o-color-1\"> Employees App</font></strong>."
msgstr ""
"<strong><font class=\"text-o-"
"color-1\">従業員アプリ</font></strong>を追加してチームを管理し、全てのプロセスに統合しましょう。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo for Therapy Practices"
msgstr "Odoo セラピー業"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone to "
"manage your entire business in it."
msgstr "Odooはアプリに完全に統合されています。アプリをダウンロードして、あなたの携帯電話をビジネス全体を管理するマネジャーに変えましょう。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odooは無限の可能性をご提案します。例えば、"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr "これはパッケージに含まれる機能の概要にすぎません。新しいアプリを追加したり、デモデータを削除/変更したり、自由にテストしてみましょう!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Once the appointment is booked, navigate to the <strong><font class=\"text-"
"o-color-1\">Calendar App</font></strong>. You may find the new appointment. "
"You can also move it (Emily will be notified) or cancel it."
msgstr ""
"予約が確定したら、<strong><font class=\"text-o-"
"color-1\">カレンダアプリ</font></strong>に移動します。新しいアポイントメントが表示されます。アポイントメントを移動(エミリーに通知されます)や取消も可能です。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Organise your events and connect with your patients easily with the "
"<strong><font class=\"text-o-color-1\">Events App </font></strong>."
msgstr ""
"<font class=\"text-o-color-1\">イベントアプリ "
"</font></strong>で、イベントを予定し、患者と繋がることができます。 "

#. module: wellness_practitioner
#: model:crm.stage,name:wellness_practitioner.crm_stage_6
msgid "Redirect to Another Practitioner"
msgstr "別の担当医に転院"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Schedule a demo"
msgstr "デモをスケジュールする"

#. module: wellness_practitioner
#: model:ir.actions.server,name:wellness_practitioner.ir_act_server_1
msgid "Send email: Book your next appointment"
msgstr "Eメールを送信:次のアポイントメント予約をしましょう"

#. module: wellness_practitioner
#: model:mail.template,description:wellness_practitioner.booking_suggestion
msgid "Sent to all attendees if a reminder is set"
msgstr "リマインダが設定されている場合、参加者全員に送信"

#. module: wellness_practitioner
#: model:base.automation,name:wellness_practitioner.base_automation_1
msgid "Stage is set to \"Qualified\""
msgstr "ステージは \"見込あり\"に設定されています"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointment App</font></strong> "
"is automatically updated based on your Calendar. You can easily lock slots, "
"change appointment hoursand send communication to your patients fromthe "
"Calendar App."
msgstr ""
"<strong><font class=\"text-o-color-1\">アポイントメントアプリ</font></strong> "
"はカレンダに基づいて自動的に更新されます。カレンダアプリから簡単に、スロットをロックしたり、アポイントメントの時間を変更したり、患者とコミュニケーションを取ったりすることができます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">Appointments App</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> manages your "
"appointment types.</span></font><font class=\"text-o-color-1\"/></strong>"
msgstr ""
" <strong><font class=\"text-o-color-1\">アポイントメントアプリ</font><font "
"class=\"text-black\"><span style=\"font-weight: normal;\"> "
"はアポイントメントタイプを管理します。</span></font><font class=\"text-o-color-1\"/></strong>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The <strong><font class=\"text-o-color-1\">CRM App</font></strong>(Customer "
"Relationship Management) is the heart of your therapy practice's patient "
"management."
msgstr ""
"<strong><font class=\"text-o-color-1\">CRMアプリ</font></strong>(顧客関係管理) "
"は治療業務の患者管理の中心を担います。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"The Odoo Calendar App is compatible with Outlook and Google Calendar. "
"Activate the sync in the Calendar App settings."
msgstr "Odooカレンダアプリは、OutlookおよびGoogleカレンダと互換性があります。カレンダアプリの設定で同期を有効化して下さい。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "これらは全て、現在ご契約中のサブスクリプションで無料でご利用いただけます!🙃"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"This module provides a comprehensive suite of applications tailored to the "
"needs of therapy practices, empowering you to manage your business "
"efficiently and deliver exceptional patient care."
msgstr ""
"このモジュールは、治療業務のニーズに合わせてカスタマイズされた包括的なアプリケーションスイートを提供します。業務を効率的に管理し、優れた患者ケアを提供できるサポートします。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "To do so, go to CRM &gt; Configuration &gt; Sales Teams."
msgstr "そのためには、CRM &gt; 設定 &gt; 販売チームに移動します。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"To reduce your no show rate drastically, appointments automatically comes "
"with reminders by Email and SMS."
msgstr "ノーショー率を大幅に下げるために、アポイントメントにはメールとSMSによる自動リマインダが提供されます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Try it !"
msgstr "試して見ましょう!"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Use Case"
msgstr "使用例"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Using Odoo, you can easily take notes during your appointments, you can "
"either use the \"Log Note\" button on your contact record or create a "
"specific knowledge article (like this one) for each of your patient."
msgstr ""
"Odooを使用すると、アポイントメント時に簡単にメモを取ることができます。連絡先記録の \"メモを記録\" ボタンを使用するか、各患者に対して "
"(この記事のような) 特定のナレッジ記事を作成することができます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"Welcome to the <strong><font class=\"text-o-color-1\">Odoo Therapy Practice "
"package</font></strong>!"
msgstr ""
"<strong><font class=\"text-o-color-1\">Odoo治療事務パッケージ</font></strong>へようこそ!"

#. module: wellness_practitioner
#: model_terms:web_tour.tour,rainbow_man_message:wellness_practitioner.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ようこそ！"

#. module: wellness_practitioner
#: model:appointment.type,name:wellness_practitioner.appointment_type_1
msgid "Wellness Session"
msgstr "ウェルネスセッション"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "Odooのセットアップについてご相談、またはそれ以上をご希望ですか?"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr "メインダッシュボードからOdooデータベースにインストールされている全てのアプリにアクセスできます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can also configure an email alias so everyone sending email to this one "
"will create a new opportunity for you."
msgstr " また、Eメールエイリアスを設定することで、この誰かがこのアドレスにEメールを送信する度に、あなた用に新規案件が作成されます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You can set the consultation with an upfront online payment. Activate it in "
"your Appointment Type Settingsby setting up an upfront payment method."
msgstr "オンライン前払いによる相談を設定することができます。予約タイプ設定で前払い方法を設定して、有効化して下さい。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr "使用例のデモは終了です! ビジネスニーズに応じて、Odooのセットアップを適応させる方法は他にも沢山あります。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Therapy Practice package and check the related box."
msgstr ""
"デモデータのインポートはまだ可能です。アプリ &gt; インダストリー &gt; "
"に行き、治療業務パッケージをアップグレードし、関連するボックスにチェックを入れて下さい。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You may also create a personalized website in a few clicks by adding the "
"Website App, your appointment page will be automatically added."
msgstr "ウェブサイトアプリを追加すると、数回のクリックでパーソナライズされたウェブサイトを作成でき、予約ページも自動的に追加されます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You receive a call from a new potential patient, Emily, who is interested in"
" your therapy services."
msgstr "潜在的な新規の患者であるEmilyから、あなたの治療サービスに興味があるという電話がかかってきます。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr "Odooを使用してこのパッケージで素早く実行できる様々なフローの概要を知るには、以下のフローを実行できなければなりません。"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "academy"
msgstr "アカデミー"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "and"
msgstr "と"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "documentation"
msgstr "ドキュメント"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "if you need help!<br/>"
msgstr "サポートが必要な場合! <br/>"

#. module: wellness_practitioner
#: model_terms:ir.ui.view,arch_db:wellness_practitioner.welcome_article_body
msgid "request a demo"
msgstr "デモをリクエストして下さい。"
