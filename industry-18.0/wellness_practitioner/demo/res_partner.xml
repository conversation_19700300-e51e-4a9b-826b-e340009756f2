<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
  <record id="base.main_partner" model="res.partner" forcecreate="1">
    <field name="name">Therapist Industry Test</field>
    <field name="vat">BE0477472701</field>
    <field name="country_id" ref="base.be"/>
    <field name="company_registry">0477472701</field>
    <field name="website">http://odoo.com</field>
    <field name="zip">1160</field>
    <field name="email"><EMAIL></field>
    <field name="phone">******-691-3277</field>
    <field name="is_company" eval="True"/>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Georges_Orwellness.png"/>
  </record>
  <record id="res_partner_14" model="res.partner">
    <field name="name"><PERSON></field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/<PERSON>_<PERSON>.jpg"/>
  </record>
  <record id="res_partner_15" model="res.partner">
    <field name="name">Alex Fernandez</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Alex_Fernandez.jpg"/>
  </record>
  <record id="res_partner_16" model="res.partner">
    <field name="name">Sophia Nguyen</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/SophiaNguyen.jpg"/>
  </record>
  <record id="res_partner_17" model="res.partner">
    <field name="name">Michael Goldstein</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Michael_Goldstein.jpg"/>
  </record>
  <record id="res_partner_18" model="res.partner">
    <field name="name">Olivia Ramirez</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Olivia_Ramirez.jpg"/>
  </record>
  <record id="res_partner_19" model="res.partner">
    <field name="name">Daniel Takahashi</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Daniel_Takahashi.jpg"/>
  </record>
  <record id="res_partner_20" model="res.partner">
    <field name="name">Isabella Moreno</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Isabella_Moreno.jpg"/>
  </record>
  <record id="res_partner_21" model="res.partner">
    <field name="name">Jacob Stein</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Jacob_Stein.jpg"/>
  </record>
  <record id="res_partner_22" model="res.partner">
    <field name="name">Ava Flores</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Ava_Flores.jpg"/>
  </record>
  <record id="res_partner_23" model="res.partner">
    <field name="name">Lucas Nakamura</field>
    <field name="email"><EMAIL></field>
    <field name="image_1920" type="base64" file="wellness_practitioner/static/src/binary/res_partner/Lucas_Nakamura.jpg"/>
  </record>
</odoo>
