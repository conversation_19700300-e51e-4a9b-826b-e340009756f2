<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_task_3" model="project.task">
        <field name="name">Initial Consultation and Goal Setting</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="state">03_approved</field>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_4" model="project.task">
        <field name="name">Fitness Assessment and Baseline Measurements</field>
        <field name="sequence">11</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_5" model="project.task">
        <field name="name">Customized Workout Plan Creation</field>
        <field name="sequence">12</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_6" model="project.task">
        <field name="name">Session 1: Introduction to Program and Form Check</field>
        <field name="sequence">13</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_7" model="project.task">
        <field name="name">Session 2: Strength Training Focus</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_8" model="project.task">
        <field name="name">Session 3: Cardiovascular Endurance</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_9" model="project.task">
        <field name="name">Session 4: Flexibility and Mobility</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_10" model="project.task">
        <field name="name">Session 5: Progress Check and Plan Adjustment</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_11" model="project.task">
        <field name="name">Session 6: Intensified Strength Training</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_12" model="project.task">
        <field name="name">Session 7: High-Intensity Interval Training (HIIT)</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_13" model="project.task">
        <field name="name">Session 8: Functional Movement and Balance</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_14" model="project.task">
        <field name="name">Session 9: Nutrition Review and Guidance</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_15" model="project.task">
        <field name="name">Session 10: Final Assessment and Future Planning</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_16" model="project.task">
        <field name="name">Progress Report and Results Summary</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_17" model="project.task">
        <field name="name">Client Feedback and Testimonial Request</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_18" model="project.task">
        <field name="name">Initial Consultation and Goal Setting</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_19" model="project.task">
        <field name="name">Comprehensive Fitness Assessment</field>
        <field name="sequence">11</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_20" model="project.task">
        <field name="name">Body Composition Analysis</field>
        <field name="sequence">12</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_21" model="project.task">
        <field name="name">Customized 3-Month Workout Plan Creation</field>
        <field name="sequence">13</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_22" model="project.task">
        <field name="name">Personalized Nutrition Plan Development</field>
        <field name="sequence">14</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_23" model="project.task">
        <field name="name">Week 1: Program Introduction and Technique Training</field>
        <field name="sequence">15</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_24" model="project.task">
        <field name="name">Week 2: Establishing Workout Routine</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_25" model="project.task">
        <field name="name">Week 3: Nutrition Habits Check-in</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_26" model="project.task">
        <field name="name">Week 4: Progress Photos and Measurements</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_27" model="project.task">
        <field name="name">Month 1 Review and Plan Adjustment</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_28" model="project.task">
        <field name="name">Week 5: Increasing Workout Intensity</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_29" model="project.task">
        <field name="name">Week 6: Addressing Weak Points</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_30" model="project.task">
        <field name="name">Week 7: Nutrition Plan Refinement</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_31" model="project.task">
        <field name="name">Week 8: Midpoint Assessment and Progress Review</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_32" model="project.task">
        <field name="name">Month 2 Evaluation and Program Update</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_33" model="project.task">
        <field name="name">Week 9: Advanced Training Techniques Introduction</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_34" model="project.task">
        <field name="name">Week 10: Mental Health and Motivation Check</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_35" model="project.task">
        <field name="name">Week 11: Final Push Strategy</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_36" model="project.task">
        <field name="name">Week 12: Final Fitness Assessment</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_37" model="project.task">
        <field name="name">Before and After Comparison</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_38" model="project.task">
        <field name="name">Long-term Maintenance Plan Creation</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_39" model="project.task">
        <field name="name">Client Success Story Documentation</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_40" model="project.task">
        <field name="name">Follow-up Session Scheduling</field>
        <field name="project_id" ref="project_project_3"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_41" model="project.task">
        <field name="name">Initial Sport-Specific Assessment</field>
        <field name="sequence">11</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_42" model="project.task">
        <field name="name">Performance Goal Setting</field>
        <field name="sequence">15</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_43" model="project.task">
        <field name="name">Current Skill Level Evaluation</field>
        <field name="sequence">12</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_44" model="project.task">
        <field name="name">Customized Training Plan Development</field>
        <field name="sequence">13</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_45" model="project.task">
        <field name="name">Nutrition Plan for Athletic Performance</field>
        <field name="sequence">14</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_46" model="project.task">
        <field name="name">Speed and Agility Baseline Testing</field>
        <field name="sequence">16</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_47" model="project.task">
        <field name="name">Strength and Power Assessment</field>
        <field name="sequence">17</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_48" model="project.task">
        <field name="name">Cardiovascular Endurance Evaluation</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_49" model="project.task">
        <field name="name">Flexibility and Mobility Screening</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_50" model="project.task">
        <field name="name">Sport-Specific Drill Design</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_51" model="project.task">
        <field name="name">Technical Skills Enhancement Sessions</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_52" model="project.task">
        <field name="name">Tactical Awareness Training</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_53" model="project.task">
        <field name="name">Mental Preparation and Visualization Techniques</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_54" model="project.task">
        <field name="name">Recovery and Injury Prevention Strategies</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_55" model="project.task">
        <field name="name">Mid-Program Performance Review</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_56" model="project.task">
        <field name="name">Advanced Skill Development</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_57" model="project.task">
        <field name="name">Game Situation Simulations</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_58" model="project.task">
        <field name="name">Video Analysis of Technique</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_59" model="project.task">
        <field name="name">Team Integration Strategies (if applicable)</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_60" model="project.task">
        <field name="name">Final Performance Assessment</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_61" model="project.task">
        <field name="name">Personalized Off-Season Training Plan</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_62" model="project.task">
        <field name="name">Competition Preparation Guidelines</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
    <record id="project_task_63" model="project.task">
        <field name="name">Long-term Athletic Development Roadmap</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="stage_id" ref="project_task_type_9"/>
    </record>
</odoo>
