<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="appointment_type_1" model="appointment.type">
        <field name="name">60-min Personal Training Session</field>
        <field name="staff_user_ids" eval="[(4, ref('base.user_admin'))]"/>
        <field name="appointment_tz" model="res.users" eval="obj().env.ref('base.user_admin').tz or 'Europe/Brussels'"/>
        <field name="category">recurring</field>
        <field name="avatars_display">hide</field>
        <field name="slot_ids" eval="[
            (0, 0, {'weekday': '1', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '1', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '2', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '2', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '3', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '3', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '4', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '4', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '5', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '5', 'start_hour': 14.0, 'end_hour': 17.0}),
        ]"/>
    </record>
    <record id="appointment_type_2" model="appointment.type">
        <field name="name">30-min Fitness Assessment</field>
        <field name="staff_user_ids" eval="[(4, ref('base.user_admin'))]"/>
        <field name="appointment_tz" model="res.users" eval="obj().env.ref('base.user_admin').tz or 'Europe/Brussels'"/>
        <field name="appointment_duration">0.5</field>
        <field name="category">recurring</field>
        <field name="avatars_display">hide</field>
        <field name="has_payment_step" eval="True"/>
        <field name="product_id" ref="product_product_11"/>
        <field name="slot_ids" eval="[
            (0, 0, {'weekday': '1', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '1', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '2', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '2', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '3', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '3', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '4', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '4', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '5', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '5', 'start_hour': 14.0, 'end_hour': 17.0}),
        ]"/>
    </record>
    <record id="appointment_type_3" model="appointment.type">
        <field name="name">90-min Sport-Specific Coaching</field>
        <field name="staff_user_ids" eval="[(4, ref('base.user_admin'))]"/>
        <field name="appointment_tz" model="res.users" eval="obj().env.ref('base.user_admin').tz or 'Europe/Brussels'"/>
        <field name="appointment_duration">1.5</field>
        <field name="category">recurring</field>
        <field name="avatars_display">hide</field>
        <field name="slot_ids" eval="[
            (0, 0, {'weekday': '1', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '1', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '2', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '2', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '3', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '3', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '4', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '4', 'start_hour': 14.0, 'end_hour': 17.0}),
            (0, 0, {'weekday': '5', 'start_hour': 9.0, 'end_hour': 12.0}),
            (0, 0, {'weekday': '5', 'start_hour': 14.0, 'end_hour': 17.0}),
        ]"/>
    </record>
</odoo>
