<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_7" model="product.product">
        <field name="name">10-Session Personal Training Package</field>
        <field name="service_tracking">project_only</field>
        <field name="list_price">800.0</field>
        <field name="purchase_ok" eval="False" />
        <field name="type">service</field>
        <field name="invoice_policy">order</field>
        <field name="project_template_id" ref="project_project_2" />
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/product_template/7-image_1920" />
    </record>
    <record id="product_product_8" model="product.product">
        <field name="name">Single Training Session</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/product_template/8-image_1920" />
        <field name="list_price">100.0</field>
        <field name="purchase_ok" eval="False" />
        <field name="uom_id" ref="uom.product_uom_hour" />
        <field name="uom_po_id" ref="uom.product_uom_hour" />
        <field name="type">service</field>
        <field name="invoice_policy">order</field>
    </record>
    <record id="product_product_9" model="product.product">
        <field name="name">3-Month Transformation Program</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/product_template/7-image_1920" />
        <field name="service_tracking">project_only</field>
        <field name="list_price">1500.0</field>
        <field name="purchase_ok" eval="False" />
        <field name="type">service</field>
        <field name="invoice_policy">order</field>
        <field name="project_template_id" ref="project_project_3" />
    </record>
    <record id="product_product_10" model="product.product">
        <field name="name">Sport-Specific Performance Package</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/product_template/7-image_1920" />
        <field name="service_tracking">task_in_project</field>
        <field name="list_price">1000.0</field>
        <field name="purchase_ok" eval="False" />
        <field name="type">service</field>
        <field name="invoice_policy">order</field>
        <field name="project_template_id" ref="project_project_4" />
    </record>
    <record id="product_product_11" model="product.product">
        <field name="name">Fitness Assessment</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/product_template/8-image_1920" />
        <field name="list_price">75.0</field>
        <field name="purchase_ok" eval="False" />
        <field name="type">service</field>
        <field name="invoice_policy">order</field>
    </record>
</odoo>
