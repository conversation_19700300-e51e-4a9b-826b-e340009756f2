<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_project_2" model="project.project">
        <field name="name">10-Session Personal Training Package</field>
        <field name="allow_billable" eval="True"/>
        <field name="favorite_user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_9'), ref('project_task_type_12')])]"/>
        <field name="tag_ids" eval="[Command.set([ref('project_tags_1')])]"/>
    </record>
    <record id="project_project_3" model="project.project">
        <field name="name">3-Month Transformation Program</field>
        <field name="allow_billable" eval="True"/>
        <field name="favorite_user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_9'), ref('project_task_type_12')])]"/>
        <field name="tag_ids" eval="[Command.set([ref('project_tags_1')])]"/>
    </record>
    <record id="project_project_4" model="project.project">
        <field name="name">Sport-Specific Performance Package</field>
        <field name="allow_billable" eval="True"/>
        <field name="favorite_user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_9'), ref('project_task_type_12')])]"/>
        <field name="tag_ids" eval="[Command.set([ref('project_tags_1')])]"/>
    </record>
</odoo>
