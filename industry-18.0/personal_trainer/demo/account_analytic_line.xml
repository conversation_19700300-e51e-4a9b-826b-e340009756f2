<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="account_analytic_line_1" model="account.analytic.line">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="timesheet_invoice_type">billable_fixed</field>
        <field name="name">/</field>
        <field name="date" eval="(datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="task_id" search="[('sale_line_id', '=', ref('sale_order_line_1'))]"/>
        <field name="product_uom_id" ref="uom.product_uom_hour" />
        <field name="unit_amount">2.0</field>
        <field name="department_id" ref="hr.dep_administration" />
    </record>
    <record id="account_analytic_line_2" model="account.analytic.line">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="timesheet_invoice_type">billable_fixed</field>
        <field name="name">/</field>
        <field name="date" eval="(datetime.today() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
        <field name="task_id" search="[('sale_line_id', '=', ref('sale_order_line_1'))]"/>
        <field name="product_uom_id" ref="uom.product_uom_hour" />
        <field name="unit_amount">1.0</field>
        <field name="department_id" ref="hr.dep_administration" />
    </record>
    <record id="account_analytic_line_3" model="account.analytic.line">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="timesheet_invoice_type">billable_fixed</field>
        <field name="task_id" search="[('sale_line_id', '=', ref('sale_order_line_4'))]"/>
        <field name="name">/</field>
        <field name="date" eval="(datetime.today() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
        <field name="product_uom_id" ref="uom.product_uom_hour" />
        <field name="unit_amount">2.0</field>
        <field name="department_id" ref="hr.dep_administration" />
    </record>
    <record id="account_analytic_line_4" model="account.analytic.line">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="timesheet_invoice_type">billable_fixed</field>
        <field name="task_id" search="[('sale_line_id', '=', ref('sale_order_line_4'))]"/>
        <field name="name">/</field>
        <field name="date" eval="(datetime.today() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
        <field name="product_uom_id" ref="uom.product_uom_hour" />
        <field name="unit_amount">0.5</field>
        <field name="department_id" ref="hr.dep_administration" />
    </record>
</odoo>
