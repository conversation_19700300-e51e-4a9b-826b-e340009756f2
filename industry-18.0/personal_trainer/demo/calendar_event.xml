<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="calendar_event_1" model="calendar.event">
        <field name="name"><PERSON> - 60-min Personal Training Session Booking</field>
        <field name="start_date" eval="DateTime.today().date()"/>
        <field name="appointment_type_id" ref="appointment_type_1"/>
        <field name="appointment_booker_id" ref="base.partner_admin"/>
        <field name="partner_ids" eval="[(6, 0, [ref('base.partner_admin')])]"/>
        <field name="start" model="appointment.type" eval="pytz.timezone(obj().env.ref('personal_trainer.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(days=1, hours=9)
            ).astimezone(pytz.UTC).replace(tzinfo=None)"/>
        <field name="duration">1.0</field>
        <field name="stop" model="appointment.type" eval="pytz.timezone(obj().env.ref('personal_trainer.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(days=1, hours=10)
            ).astimezone(pytz.UTC).replace(tzinfo=None)"/>
        <field name="description"><![CDATA[
            <ul>
                <li>Phone: +000</li>
                <li>Email: <EMAIL></li>
            </ul>
        ]]></field>
    </record>
    <record id="calendar_event_2" model="calendar.event">
        <field name="name">Lena Martinez - 60-min Personal Training Session Booking</field>
        <field name="appointment_type_id" ref="appointment_type_1"/>
        <field name="appointment_booker_id" ref="base.partner_admin"/>
        <field name="partner_ids" eval="[(6, 0, [ref('base.partner_admin')])]"/>
        <field name="start" model="appointment.type" eval="pytz.timezone(obj().env.ref('personal_trainer.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(days=2, hours=14)
            ).astimezone(pytz.UTC).replace(tzinfo=None)"/>
        <field name="duration">1.0</field>
        <field name="stop" model="appointment.type" eval="pytz.timezone(obj().env.ref('personal_trainer.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(days=2, hours=15)
            ).astimezone(pytz.UTC).replace(tzinfo=None)"/>
        <field name="start_date" eval="DateTime.today().date()"/>
        <field name="description"><![CDATA[
            <ul>
                <li>Phone: +00</li>
                <li>Email: <EMAIL></li>
            </ul>
        ]]></field>
    </record>
    <record id="calendar_event_3" model="calendar.event">
        <field name="name">Clayton Smouth - 90-min Sport-Specific Coaching Booking</field>
        <field name="start_date" eval="DateTime.today().date()"/>
        <field name="appointment_type_id" ref="appointment_type_3"/>
        <field name="appointment_booker_id" ref="base.partner_admin"/>
        <field name="partner_ids" eval="[(6, 0, [ref('base.partner_admin')])]"/>
        <field name="start" model="appointment.type" eval="pytz.timezone(obj().env.ref('personal_trainer.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(days=3, hours=8)
            ).astimezone(pytz.UTC).replace(tzinfo=None)"/>
        <field name="duration">1.5</field>
        <field name="stop" model="appointment.type" eval="pytz.timezone(obj().env.ref('personal_trainer.appointment_type_1').appointment_tz).localize(
                DateTime.today().date() + relativedelta(days=3, hours=9)
            ).astimezone(pytz.UTC).replace(tzinfo=None)"/>
        <field name="description"><![CDATA[
            <ul>
                <li>Phone: +00</li>
                <li>Email: <EMAIL></li>
            </ul>
        ]]></field>
    </record>
</odoo>
