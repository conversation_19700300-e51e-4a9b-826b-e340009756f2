<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="res_partner_13" model="res.partner">
        <field name="name"><PERSON></field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/13-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_14" model="res.partner">
        <field name="name"><PERSON></field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/14-image_1920"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_15" model="res.partner">
        <field name="name"><PERSON></field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/15-image_1920"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_16" model="res.partner">
        <field name="name">Sarah Davis</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/16-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_17" model="res.partner">
        <field name="name">Tom Wilson</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/17-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_18" model="res.partner">
        <field name="name">Lisa Anderson</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/18-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_19" model="res.partner">
        <field name="name">David Lee</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/19-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_20" model="res.partner">
        <field name="name">Amanda White</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/20-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_21" model="res.partner">
        <field name="name">Chris Taylor</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/21-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_22" model="res.partner">
        <field name="name">Jessica Martinez</field>
        <field name="image_1920" type="base64" file="personal_trainer/static/src/binary/res_partner/22-image_1920"/>
        <field name="user_id" ref="base.user_admin"/>
        <field name="email"><EMAIL></field>
    </record>
</odoo>
