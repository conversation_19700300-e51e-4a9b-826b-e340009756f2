<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_line_1" model="sale.order.line">
        <field name="order_id" ref="sale_order_1"/>
        <field name="product_id" ref="product_product_9"/>
        <field name="product_uom" ref="uom.product_uom_hour"/>
    </record>
    <record id="sale_order_line_2" model="sale.order.line">
        <field name="order_id" ref="sale_order_2"/>
        <field name="product_id" ref="product_product_11"/>
        <field name="product_uom" ref="uom.product_uom_hour"/>
    </record>
    <record id="sale_order_line_3" model="sale.order.line">
        <field name="order_id" ref="sale_order_2"/>
        <field name="product_id" ref="product_product_7"/>
        <field name="product_uom" ref="uom.product_uom_hour"/>
    </record>
    <record id="sale_order_line_4" model="sale.order.line">
        <field name="order_id" ref="sale_order_3"/>
        <field name="product_id" ref="product_product_7"/>
    </record>
</odoo>
