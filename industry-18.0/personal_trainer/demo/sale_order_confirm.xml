<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="sale.order" name="action_confirm">
        <value eval="[ref('sale_order_1')]"/>
    </function>

    <function name="write" model="project.project">
        <value model="project.project" eval="obj().env.ref('personal_trainer.sale_order_1').project_id.id"/>
        <value eval="{'tag_ids': [(5, 0, 0)]}"/>
    </function>

    <function model="sale.order" name="action_confirm">
        <value eval="[ref('sale_order_3')]"/>
    </function>

    <function name="write" model="project.project">
        <value model="project.project" eval="obj().env.ref('personal_trainer.sale_order_3').project_id.id"/>
        <value eval="{'tag_ids': [(5, 0, 0)]}"/>
    </function>
</odoo>
