# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* personal_trainer
# 
# Translators:
# Sarah <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 04:52+0000\n"
"PO-Revision-Date: 2024-11-24 02:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_7_product_template
#: model:project.project,name:personal_trainer.project_project_2
msgid "10-Session Personal Training Package"
msgstr "10회 개인 트레이닝 패키지"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_9_product_template
#: model:project.project,name:personal_trainer.project_project_3
msgid "3-Month Transformation Program"
msgstr "3개월 혁신 프로그램"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_2
msgid "30-min Fitness Assessment"
msgstr "30분 체력 진단"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_1
msgid "60-min Personal Training Session"
msgstr "60분 개인 트레이닝 세션"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_3
msgid "90-min Sport-Specific Coaching"
msgstr "90분 스포츠별 맞춤 코칭"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Personal "
"Trainer</strong></span>"
msgstr "<span class=\"display-4-fs\"><strong>개인 트레이너를 위한 Odoo</strong></span>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Configure Appointment Types</strong>: In the Appointment app, create"
" types that match your services."
msgstr "<strong>일정 유형 설정</strong>: 일정 예약 앱에서 서비스와 일치하는 일정 유형을 생성합니다."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Create Client Profiles</strong>: As you get inquiries, add them to "
"your CRM and Contacts."
msgstr "<strong>고객 프로필 만들기</strong>: 문의가 들어오면 CRM과 연락처에 고객 프로필을 추가합니다."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Invoice Your Clients</strong>: Use the Invoicing app to bill clients"
" for completed services."
msgstr "<strong>고객에게 청구서 보내기</strong>: 청구서 앱을 활용하여 완료된 서비스에 대해 고객에게 청구하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Manage Your Calendar</strong>: Use the Calendar app to block off "
"your availability and personal time."
msgstr ""
"<strong>캘린더 관리</strong>: 캘린더 앱을 사용하여 예약 가능한 시간은 열어두고 개인 시간을 차단하여 일정을 관리합니다."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Set Up Your Services</strong>: Navigate to Sales &gt; Products and "
"set up your coaching services."
msgstr "<strong>서비스 설정하기</strong>: 판매 &amp;gt; 제품으로 이동하여 코칭 서비스를 구성합니다."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Start Tracking Projects</strong>: For services that require ongoing "
"management, create a project in the Project app."
msgstr ""
"<strong>프로젝트 추적 시작</strong>: 지속적인 관리가 필요한 서비스의 경우, 프로젝트 앱에서 프로젝트를 생성하고 "
"모니터링하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Appointment Scheduling 📆"
msgstr "일정 예약 📆"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Basics"
msgstr "베이직"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Client Management (CRM) 🎯"
msgstr "고객 관리 (CRM) 🎯"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Collaborate with clients on their goals"
msgstr "고객과 목표에 대해 협업"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Convert leads into clients"
msgstr "리드를 고객으로 전환"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Create a stunning <strong>Website</strong> to showcase your coaching "
"services and allow clients to book sessions online."
msgstr ""
"전문적인 <strong>웹사이트</strong>를 만들어 코칭 서비스를 소개하고 고객이 온라인으로 세션을 예약할 수 있도록 하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create and manage your coaching services:"
msgstr "코칭 서비스를 생성하고 관리하세요:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create tasks for each stage of a client's journey"
msgstr "고객 여정의 각 단계에 맞는 작업 만들기"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define pricing and project tracking needs for each service"
msgstr "각 서비스에 대한 가격을 설정하고 프로젝트를 추적합니다."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define your availability"
msgstr "사용 가능성 정의"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Develop and sell online fitness courses with the <strong>eLearning</strong> "
"platform, expanding your reach beyond in-person training."
msgstr ""
"<strong>온라인 학습</strong> 플랫폼으로 온라인 피트니스 강좌를 개설 및 판매하여 대면 교육을 넘어 도달 범위를 확장하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Do you want to go further?"
msgstr "더 자세히 알아보시겠습니까?"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_12
msgid "Done"
msgstr "완료"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Easily bill clients for your services:"
msgstr "고객에게 서비스에 대한 비용을 간편하게 청구하세요:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Encourage clients to book through your Appointment page to reduce scheduling"
" back-and-forth."
msgstr "고객이 예약 페이지를 통해 직접 예약하도록 유도하여 시간을 변경해야 하는 번거로움을 최소화하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Engage clients with targeted campaigns using <strong>Email "
"Marketing</strong> and <strong>Social Marketing</strong>."
msgstr ""
"<strong>이메일 마케팅</strong>과 <strong>소셜 미디어 마케팅</strong>을 통해 타겟팅된 캠페인으로 고객의 참여를"
" 유도하세요."

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_11_product_template
msgid "Fitness Assessment"
msgstr "체력 평가"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Follow up on inquiries"
msgstr "문의에 대한 후속 조치"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Gather valuable client feedback and track progress effortlessly using "
"<strong>Surveys</strong>."
msgstr "<strong>설문조사</strong>를 통해 귀중한 고객 피드백을 수집하고 진행 상황을 손쉽게 모니터링하세요. "

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Generate invoices automatically from sales orders"
msgstr "판매 주문에서 청구서를 자동으로 생성하기"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Getting Started"
msgstr "시작하기"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"If you want to push your quotation building much further and add all the "
"power of Spreadsheet beneath any line of your Sales Orders, use the quote "
"calculation feature."
msgstr "견적 프로세스를 개선하고 판매 주문의 모든 항목에 대해 스프레드시트의 모든 기능을 활용하려면 견적 계산 기능을 활용하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Invoicing 🧾"
msgstr "청구서 발행 🧾"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Let clients book directly through your booking page"
msgstr "고객이 온라인 예약 페이지를 통해 직접 예약할 수 있도록 하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage leads and opportunities"
msgstr "잠재 고객 및 기회 관리"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage your services 👟"
msgstr "서비스 관리 👟"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Manage your team of coaches and staff efficiently with "
"<strong>Employees</strong>."
msgstr "<strong>직원</strong> 앱을 사용하여 코치와 직원으로 구성된 팀을 효율적으로 관리하세요."

#. module: personal_trainer
#: model:crm.stage,name:personal_trainer.crm_stage_5
msgid "Negotiation"
msgstr "협상"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_9
msgid "New"
msgstr "신규"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as:"
msgstr "Odoo는 다음과 같이 무한한 가능성을 제공합니다:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Organize group classes, workshops, and fitness events easily with the "
"<strong>Events</strong> app."
msgstr "<strong>행사</strong> 앱으로 그룹 수업, 워크샵, 피트니스 행사를 쉽게 계획하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Project Tracking 📈"
msgstr "프로젝트 추적 📈"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Reach us"
msgstr "연락하기"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Regularly update your CRM to keep track of potential clients and follow-ups."
msgstr "CRM을 정기적으로 업데이트하여 잠재 고객과 후속 조치를 추적하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Remember, Odoo is flexible and can adapt to your specific needs. Don't "
"hesitate to explore and customize as your coaching business grows!"
msgstr "Odoo는 코칭 비즈니스를 유연하게 수용하도록 설계되었습니다. 비즈니스 확장에 따라 다양한 기능을 살펴보세요!"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up appointment types (e.g., \"60-min Personal Training\", \"30-min "
"Assessment\")"
msgstr "예약 유형 설정 (예: \"60분 개인 트레이닝\", \"30분 평가\")"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up services like \"10-Session Personal Training Package\" or \"3-Month "
"Transformation Program\""
msgstr "\"10회 개인 트레이닝 패키지\" 또는 \"3개월 변신 프로그램\"과 같은 서비스를 설정하세요. "

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_8_product_template
msgid "Single Training Session"
msgstr "단일 트레이닝 세션"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_10_product_template
#: model:project.project,name:personal_trainer.project_project_4
msgid "Sport-Specific Performance Package"
msgstr "스포츠별 퍼포먼스 패키지"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Struggling with your setup? Reach us to get a demo!"
msgstr "설정에 문제가 있으신가요? 데모를 요청하세요!"

#. module: personal_trainer
#: model:project.project,label_tasks:personal_trainer.project_project_2
#: model:project.project,label_tasks:personal_trainer.project_project_3
#: model:project.project,label_tasks:personal_trainer.project_project_4
msgid "Tasks"
msgstr "작업"

#. module: personal_trainer
#: model:project.tags,name:personal_trainer.project_tags_1
msgid "Template"
msgstr "서식"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "현재 구독 중인 요금제에서 모두 무료로 제공되니 자유롭게 살펴보세요! 🙃"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Tips for Success"
msgstr "성공을 위한 팁"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Track fitness equipment and sell merchandise through your integrated "
"<strong>eCommerce</strong> store."
msgstr "통합 <strong>이커머스</strong> 스토어를 통해 피트니스 장비를 추적하고 상품을 판매하세요. "

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track payments and send reminders"
msgstr "결제 추적 및 알림 보내기 "

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track potential and current clients:"
msgstr "잠재 고객과 현재 고객을 추적합니다:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track progress and set milestones"
msgstr "진행 상황 추적 및 마일스톤 설정"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Appointment app to allow clients to book sessions easily:"
msgstr "예약 앱을 사용하여 고객이 세션을 쉽게 예약할 수 있도록 하세요:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Project app to manage longer-term client programs:"
msgstr "프로젝트 앱을 사용하여 장기 고객 프로그램을 관리하세요:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the project templates for standardized programs to save time."
msgstr "표준화된 프로그램용 프로젝트 템플릿을 사용하여 시간을 절약하세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Utilize the invoicing reminders to ensure timely payments."
msgstr "청구서 미리 알림을 활용하여 적시에 결제할 수 있도록 합니다."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Welcome to your new Odoo Personal Trainer package! This guide will help you "
"navigate the key features and get your coaching business up and running "
"efficiently."
msgstr ""
"새로운 Odoo 개인 트레이너 패키지를 소개합니다! 이 가이드는 코칭 비즈니스를 빠르게 설정하고 효율적으로 관리하는 데 도움이 될 "
"것입니다."

#. module: personal_trainer
#: model_terms:web_tour.tour,rainbow_man_message:personal_trainer.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "환영합니다! 마음껏 둘러 보세요."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "Odoo 설정에 대해 논의하거나 추가 기능을 살펴보고 싶으신가요?"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Appointment"
msgstr "🎓 일정"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Invoicing"
msgstr "🎓 청구서"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Project"
msgstr "🎓 프로젝트"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 판매"
