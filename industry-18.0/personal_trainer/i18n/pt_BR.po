# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* personal_trainer
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 04:52+0000\n"
"PO-Revision-Date: 2024-11-24 02:20+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_7_product_template
#: model:project.project,name:personal_trainer.project_project_2
msgid "10-Session Personal Training Package"
msgstr "Pacote de 10 sessões de treinamento pessoal"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_9_product_template
#: model:project.project,name:personal_trainer.project_project_3
msgid "3-Month Transformation Program"
msgstr "Programa de transformação de 3 meses"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_2
msgid "30-min Fitness Assessment"
msgstr "Avaliação de condicionamento físico de 30 minutos"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_1
msgid "60-min Personal Training Session"
msgstr "Sessão de treinamento pessoal de 60 minutos"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_3
msgid "90-min Sport-Specific Coaching"
msgstr "Treinamento de 90 minutos específico para esportes"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Personal "
"Trainer</strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong>Odoo para Personal "
"Trainer</strong></span>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Configure Appointment Types</strong>: In the Appointment app, create"
" types that match your services."
msgstr ""
"<strong>Configurar tipos de agendamento</strong>: No aplicativo "
"Compromissos, crie tipos que correspondam aos seus serviços."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Create Client Profiles</strong>: As you get inquiries, add them to "
"your CRM and Contacts."
msgstr ""
"<strong>Criar perfis de clientes</strong>: Conforme receber contatos, "
"adicione-os aos apps CRM e Contatos."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Invoice Your Clients</strong>: Use the Invoicing app to bill clients"
" for completed services."
msgstr ""
"<strong>Faturar seus clientes</strong>: Use o aplicativo Faturamento para "
"cobrar dos clientes os serviços concluídos."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Manage Your Calendar</strong>: Use the Calendar app to block off "
"your availability and personal time."
msgstr ""
"<strong>Gerenciar seu calendário</strong>: Use o aplicativo Calendário para "
"bloquear sua disponibilidade e seu tempo pessoal."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Set Up Your Services</strong>: Navigate to Sales &gt; Products and "
"set up your coaching services."
msgstr ""
"<strong>Configurar seus serviços</strong>: Navegue até Vendas > Produtos e "
"configure seus serviços de treinamento."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Start Tracking Projects</strong>: For services that require ongoing "
"management, create a project in the Project app."
msgstr ""
"<strong>Começar a monitorar projetos</strong>: Para serviços que exigem "
"gerenciamento contínuo, crie um projeto no aplicativo Projeto."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Appointment Scheduling 📆"
msgstr "Agendamento de Compromissos 📆"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Basics"
msgstr "Elementos básicos"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Client Management (CRM) 🎯"
msgstr "Gerenciamento de clientes (CRM) 🎯"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Collaborate with clients on their goals"
msgstr "Colabore com os clientes para alcançar as metas deles"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Convert leads into clients"
msgstr "Converter leads em clientes"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Create a stunning <strong>Website</strong> to showcase your coaching "
"services and allow clients to book sessions online."
msgstr ""
"Crie um <strong>Site</strong> impressionante para apresentar seus serviços "
"de treinamento e permitir que os clientes agendem sessões on-line."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create and manage your coaching services:"
msgstr "Crie e gerencie seus serviços de treinamento:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create tasks for each stage of a client's journey"
msgstr "Crie tarefas para cada estágio da jornada do cliente"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define pricing and project tracking needs for each service"
msgstr ""
"Defina os requisitos de preços e de acompanhamento de projetos para cada "
"serviço"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define your availability"
msgstr "Defina a disponibilidade"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Develop and sell online fitness courses with the <strong>eLearning</strong> "
"platform, expanding your reach beyond in-person training."
msgstr ""
"Desenvolva e venda cursos fitness on-line com a plataforma "
"<strong>e-Learning</strong>, expandindo seu alcance para além do treinamento"
" presencial."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Do you want to go further?"
msgstr "Quer ir mais além?"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_12
msgid "Done"
msgstr "Concluído"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Easily bill clients for your services:"
msgstr "Fature com facilidade os clientes pelos serviços:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Encourage clients to book through your Appointment page to reduce scheduling"
" back-and-forth."
msgstr ""
"Incentive os clientes a fazer a reserva pela sua página de agendamento para "
"reduzir as idas e vindas no cronograma."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Engage clients with targeted campaigns using <strong>Email "
"Marketing</strong> and <strong>Social Marketing</strong>."
msgstr ""
"Envolva os clientes com campanhas direcionadas pelos apps <strong>Marketing "
"por e-mail</strong> e <strong>Redes Sociais</strong>."

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_11_product_template
msgid "Fitness Assessment"
msgstr "Avaliação física"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Follow up on inquiries"
msgstr "Acompanhamento de dúvidas"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Gather valuable client feedback and track progress effortlessly using "
"<strong>Surveys</strong>."
msgstr ""
"Obtenha valiosos feedbacks de clientes e acompanhe o progresso sem esforço "
"pelo app <strong>Pesquisas</strong>."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Generate invoices automatically from sales orders"
msgstr "Gere faturas automaticamente a partir de pedidos de vendas"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Getting Started"
msgstr "Primeiros passos"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"If you want to push your quotation building much further and add all the "
"power of Spreadsheet beneath any line of your Sales Orders, use the quote "
"calculation feature."
msgstr ""
"Se você quiser ir muito além na criação de cotações e acrescentar todo o "
"poder do app Planilhas a qualquer linha de seus pedidos de vendas, use o "
"recurso de cálculo de cotações."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Invoicing 🧾"
msgstr "Faturamento 🧾"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Let clients book directly through your booking page"
msgstr ""
"Permita que os clientes marquem horários diretamente em sua página de "
"reservas"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage leads and opportunities"
msgstr "Gerenciar leads e oportunidades"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage your services 👟"
msgstr "Gerencie seus serviços 👟"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Manage your team of coaches and staff efficiently with "
"<strong>Employees</strong>."
msgstr ""
"Gerencie sua equipe de treinadores e funcionários de forma eficiente com o "
"app <strong>Funcionários</strong>."

#. module: personal_trainer
#: model:crm.stage,name:personal_trainer.crm_stage_5
msgid "Negotiation"
msgstr "Negociação"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_9
msgid "New"
msgstr "Novo"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as:"
msgstr "O Odoo oferece infinitas possibilidades, como:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Organize group classes, workshops, and fitness events easily with the "
"<strong>Events</strong> app."
msgstr ""
"Organize aulas em grupo, workshops e eventos fitness facilmente com o app "
"<strong>Eventos</strong>."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Project Tracking 📈"
msgstr "Monitoramento de projetos 📈"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Reach us"
msgstr "Entre em contato"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Regularly update your CRM to keep track of potential clients and follow-ups."
msgstr ""
"Atualize regularmente seu CRM para manter o controle de clientes potenciais "
"e acompanhamentos."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Remember, Odoo is flexible and can adapt to your specific needs. Don't "
"hesitate to explore and customize as your coaching business grows!"
msgstr ""
"Lembre-se, o Odoo é flexível e pode se adaptar às suas necessidades "
"específicas. Não hesite em explorar mais e personalizar conforme seu negócio"
" de treinamento for crescendo!"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up appointment types (e.g., \"60-min Personal Training\", \"30-min "
"Assessment\")"
msgstr ""
"Configure tipos de compromissos (ex.: \"Treinamento pessoal de 60 minutos\","
" \"Avaliação de 30 minutos\")"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up services like \"10-Session Personal Training Package\" or \"3-Month "
"Transformation Program\""
msgstr ""
"Configurar serviços como \"Pacote de treinamento pessoal de 10 sessões\" ou "
"\"Programa de transformação de 3 meses\""

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_8_product_template
msgid "Single Training Session"
msgstr "Sessão única de treinamento"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_10_product_template
#: model:project.project,name:personal_trainer.project_project_4
msgid "Sport-Specific Performance Package"
msgstr "Pacote de desempenho específico para esportes"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Struggling with your setup? Reach us to get a demo!"
msgstr ""
"Dificuldade com a configuração? Entre em contato conosco para uma "
"demonstração!"

#. module: personal_trainer
#: model:project.project,label_tasks:personal_trainer.project_project_2
#: model:project.project,label_tasks:personal_trainer.project_project_3
#: model:project.project,label_tasks:personal_trainer.project_project_4
msgid "Tasks"
msgstr "Tarefas"

#. module: personal_trainer
#: model:project.tags,name:personal_trainer.project_tags_1
msgid "Template"
msgstr "Modelo"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Todos são gratuitos em sua assinatura atual; sinta-se à vontade para "
"explorar! 🙃"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Tips for Success"
msgstr "Dicas para o sucesso"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Track fitness equipment and sell merchandise through your integrated "
"<strong>eCommerce</strong> store."
msgstr ""
"Monitore equipamentos de ginástica e venda mercadorias em sua loja de "
"<strong>e-Commerce</strong> integrada."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track payments and send reminders"
msgstr "Monitore os pagamentos e envie lembretes"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track potential and current clients:"
msgstr "Monitore clientes potenciais e atuais:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track progress and set milestones"
msgstr "Acompanhe o progresso e estabeleça marcos"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Appointment app to allow clients to book sessions easily:"
msgstr ""
"Use o aplicativo Compromissos para permitir que os clientes agendem sessões "
"facilmente:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Project app to manage longer-term client programs:"
msgstr ""
"Use o aplicativo Projetos para gerenciar programas de clientes de longo "
"prazo:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the project templates for standardized programs to save time."
msgstr ""
"Use modelos de projeto com programas padronizados para economizar tempo."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Utilize the invoicing reminders to ensure timely payments."
msgstr "Utilize os lembretes de faturamento para garantir pagamentos em dia."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Welcome to your new Odoo Personal Trainer package! This guide will help you "
"navigate the key features and get your coaching business up and running "
"efficiently."
msgstr ""
"Bem-vindo ao seu novo pacote Odoo Personal Trainer! Este guia o ajudará a "
"navegar pelos principais recursos e a colocar seu negócio de treinamento em "
"funcionamento com eficiência."

#. module: personal_trainer
#: model_terms:web_tour.tour,rainbow_man_message:personal_trainer.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Boas-vindas! Boa exploração."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "Quer conversar sobre sua configuração do Odoo ou ir ainda mais além?"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Appointment"
msgstr "🎓 Compromissos"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Invoicing"
msgstr "🎓 Faturamento"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Project"
msgstr "🎓 Projeto"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 Vendas"
