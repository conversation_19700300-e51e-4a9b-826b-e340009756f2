# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* personal_trainer
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 04:52+0000\n"
"PO-Revision-Date: 2024-11-24 02:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_7_product_template
#: model:project.project,name:personal_trainer.project_project_2
msgid "10-Session Personal Training Package"
msgstr "Pacchetto 10 sessioni con personal trainer"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_9_product_template
#: model:project.project,name:personal_trainer.project_project_3
msgid "3-Month Transformation Program"
msgstr "Programma trasformazione 3 mesi"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_2
msgid "30-min Fitness Assessment"
msgstr "Valutazione idoneità fisica 30 min"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_1
msgid "60-min Personal Training Session"
msgstr "Sessione con personal trainer 60 min"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_3
msgid "90-min Sport-Specific Coaching"
msgstr "90 minuti di coaching specifico per lo sport"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Personal "
"Trainer</strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong>Odoo per personal "
"trainer</strong></span>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Configure Appointment Types</strong>: In the Appointment app, create"
" types that match your services."
msgstr ""
"<strong>Configura i tipi di appuntamento</strong>: nell'app Appuntamenti, "
"crea tipi che corrispondono ai tuoi servizi."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Create Client Profiles</strong>: As you get inquiries, add them to "
"your CRM and Contacts."
msgstr ""
"<strong>Crea profili cliente</strong>: non appena ricevi richieste, "
"aggiungile al CRM e ai contatti."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Invoice Your Clients</strong>: Use the Invoicing app to bill clients"
" for completed services."
msgstr ""
"<strong>Emetti fatture</strong>: utilizza l'app Fatturazione per creare "
"fatture clienti per i servizi completati."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Manage Your Calendar</strong>: Use the Calendar app to block off "
"your availability and personal time."
msgstr ""
"<strong>Gestisci il tuo calendario</strong>: usa l'app Calendario per "
"stabilire le tue disponibilità."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Set Up Your Services</strong>: Navigate to Sales &gt; Products and "
"set up your coaching services."
msgstr ""
"<strong>Configura i tuoi servizi</strong>: apri l'app Vendite &gt; Prodotti "
"e configura i servizi di coaching."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Start Tracking Projects</strong>: For services that require ongoing "
"management, create a project in the Project app."
msgstr ""
"<strong>Inizia a monitorare i progetti</strong>: per i servizi che "
"richiedono una gestione continua, crea un progetto nell'applicazione "
"Progetti."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Appointment Scheduling 📆"
msgstr "Programmazione appuntamenti 📆"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Basics"
msgstr "Informazioni di base"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Client Management (CRM) 🎯"
msgstr "Gestione clienti (CRM) 🎯"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Collaborate with clients on their goals"
msgstr "Collabora con i clienti per raggiungere gli obiettivi."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Convert leads into clients"
msgstr "Converti lead in clienti."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Create a stunning <strong>Website</strong> to showcase your coaching "
"services and allow clients to book sessions online."
msgstr ""
"Crea un <strong>sito web</strong> mozzafiato per mostrare i tuoi servizi di "
"coaching e permettere ai clienti di prenotare le sessioni online."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create and manage your coaching services:"
msgstr "Crea e gestisci i servizi di coaching:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create tasks for each stage of a client's journey"
msgstr "Crea lavori/attività per ogni fase del percorso di un cliente"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define pricing and project tracking needs for each service"
msgstr ""
"Definisci i prezzi e le esigenze di monitoraggio dei progetti per ogni "
"servizio."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define your availability"
msgstr "Stabilisci le disponibilità"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Develop and sell online fitness courses with the <strong>eLearning</strong> "
"platform, expanding your reach beyond in-person training."
msgstr ""
"Sviluppa e vendi corsi fitness online grazie alla piattaforma "
"<strong>E-learning</strong> espandendo il aggio d'azione al di là degli "
"allenamenti in presenza."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Do you want to go further?"
msgstr "Vuoi saperne di più?"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_12
msgid "Done"
msgstr "Completato"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Easily bill clients for your services:"
msgstr "Emetti fatture per i tuoi servizi in modo semplice:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Encourage clients to book through your Appointment page to reduce scheduling"
" back-and-forth."
msgstr ""
"Incoraggia i clienti a prenotare attraverso la pagina degli appuntamenti per"
" ridurre il tira e molla della programmazione."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Engage clients with targeted campaigns using <strong>Email "
"Marketing</strong> and <strong>Social Marketing</strong>."
msgstr ""
"Attrai clienti grazie a campagne specifiche utilizzando le app "
"<strong>E-mail marketing</strong> e <strong>Social marketing</strong>."

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_11_product_template
msgid "Fitness Assessment"
msgstr "Valutazione idoneità fisica"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Follow up on inquiries"
msgstr "Segui le richieste di informazioni"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Gather valuable client feedback and track progress effortlessly using "
"<strong>Surveys</strong>."
msgstr ""
"Raccogli i feedback dei clienti e traccia i progressi senza sforzi "
"utilizzando il modulo <strong>Sondaggi</strong>."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Generate invoices automatically from sales orders"
msgstr "Genera fatture cliente automaticamente dagli ordini di vendita"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Getting Started"
msgstr "Primi passi"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"If you want to push your quotation building much further and add all the "
"power of Spreadsheet beneath any line of your Sales Orders, use the quote "
"calculation feature."
msgstr ""
"Se vuoi mettere a punto preventivi professionali e aggiungere tutta la "
"potenza dei fogli di calcolo in ogni riga degli Ordini di vendita, utilizza "
"la funzione di calcolo dei preventivi."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Invoicing 🧾"
msgstr "Fatturazione 🧾"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Let clients book directly through your booking page"
msgstr "Permetti ai clienti di prenotare direttamente dalla pagina dedicata."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage leads and opportunities"
msgstr "Gestisci lead e opportunità"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage your services 👟"
msgstr "Gestisci i servizi 👟"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Manage your team of coaches and staff efficiently with "
"<strong>Employees</strong>."
msgstr ""
"Gestisci il team di coach e il personale in maniera efficace grazie all'app "
"<strong>Dipendenti</strong>."

#. module: personal_trainer
#: model:crm.stage,name:personal_trainer.crm_stage_5
msgid "Negotiation"
msgstr "Negoziazione"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_9
msgid "New"
msgstr "Nuova"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as:"
msgstr "Odoo ti offre possibilità infinite, come:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Organize group classes, workshops, and fitness events easily with the "
"<strong>Events</strong> app."
msgstr ""
"Organizza corsi di gruppo, workshop ed eventi fitness facilmente grazie "
"all'app <strong>Eventi</strong>."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Project Tracking 📈"
msgstr "Monitoraggio progetti 📈"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Reach us"
msgstr "Contattaci"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Regularly update your CRM to keep track of potential clients and follow-ups."
msgstr ""
"Aggiorna regolarmente il CRM per tenere traccia dei potenziali clienti e dei"
" follow-up."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Remember, Odoo is flexible and can adapt to your specific needs. Don't "
"hesitate to explore and customize as your coaching business grows!"
msgstr ""
"Ricorda che Odoo è flessibile e può adattarsi alle tue esigenze specifiche. "
"Non esitare a esplorare e personalizzare la tua attività di coaching man "
"mano che cresce!"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up appointment types (e.g., \"60-min Personal Training\", \"30-min "
"Assessment\")"
msgstr ""
"Configura i tipi di appuntamento (ad es., \"Allenamento con personal trainer"
" 60 min\", \"Valutazione 30 min\")"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up services like \"10-Session Personal Training Package\" or \"3-Month "
"Transformation Program\""
msgstr ""
"Configura servizi come \"Pacchetto 10 sessioni con personal trainer\" o "
"\"Programma trasformazione 3 mesi\""

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_8_product_template
msgid "Single Training Session"
msgstr "Sessione di allenamento singola"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_10_product_template
#: model:project.project,name:personal_trainer.project_project_4
msgid "Sport-Specific Performance Package"
msgstr "Pacchetto allenamento sport specifico"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Struggling with your setup? Reach us to get a demo!"
msgstr ""
"Hai problemi con la configurazione? Contattaci per ottenere una "
"dimostrazione tecnica!"

#. module: personal_trainer
#: model:project.project,label_tasks:personal_trainer.project_project_2
#: model:project.project,label_tasks:personal_trainer.project_project_3
#: model:project.project,label_tasks:personal_trainer.project_project_4
msgid "Tasks"
msgstr "Lavori"

#. module: personal_trainer
#: model:project.tags,name:personal_trainer.project_tags_1
msgid "Template"
msgstr "Modello"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Tutto gratuito nel tuo abbonamento attuale. Cosa aspetti? Fai un giro! 🙃"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Tips for Success"
msgstr "Consigli per avere successo"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Track fitness equipment and sell merchandise through your integrated "
"<strong>eCommerce</strong> store."
msgstr ""
"Monitora l'attrezzatura e vendi della merce grazie al negozio creato con il "
"modulo <strong>E-commerce</strong>."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track payments and send reminders"
msgstr "Traccia i pagamenti e invia promemoria."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track potential and current clients:"
msgstr "Traccia clienti potenziali e attuali:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track progress and set milestones"
msgstr "Monitora i progressi e stabilisci milestone da raggiungere"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Appointment app to allow clients to book sessions easily:"
msgstr ""
"Utilizza l'app Appuntamenti per permettere ai clienti di prenotare sessioni "
"facilmente:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Project app to manage longer-term client programs:"
msgstr "Usa l'app Progetti per gestire i programmi a lungo termine:"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the project templates for standardized programs to save time."
msgstr ""
"Utilizza i modelli di progetto per programmi standard così da risparmiare "
"tempo."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Utilize the invoicing reminders to ensure timely payments."
msgstr ""
"Utilizza i promemoria relativa alla fatturazione per garantire i pagamenti."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Welcome to your new Odoo Personal Trainer package! This guide will help you "
"navigate the key features and get your coaching business up and running "
"efficiently."
msgstr ""
"Benvenuto nel nuovo pacchetto Odoo Personal Trainer! Questa guida ti aiuterà"
" a nscoprire le funzioni principali e a rendere efficiente la tua attività "
"di coaching."

#. module: personal_trainer
#: model_terms:web_tour.tour,rainbow_man_message:personal_trainer.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Benvenuto! Buona visita."

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Vorresti parlare della configurazione di Odoo oppure ottenere nuove "
"informazioni?"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Appointment"
msgstr "🎓 Appuntamenti"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Invoicing"
msgstr "🎓 Fatturazione"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Project"
msgstr "🎓 Progetti"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 Vendite"
