# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* personal_trainer
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 04:52+0000\n"
"PO-Revision-Date: 2024-11-24 02:20+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_7_product_template
#: model:project.project,name:personal_trainer.project_project_2
msgid "10-Session Personal Training Package"
msgstr "10 節私人訓練套餐"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_9_product_template
#: model:project.project,name:personal_trainer.project_project_3
msgid "3-Month Transformation Program"
msgstr "3 個月改造計劃"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_2
msgid "30-min Fitness Assessment"
msgstr "30 分鐘體格評估"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_1
msgid "60-min Personal Training Session"
msgstr "60 分鐘私人訓練課節"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_3
msgid "90-min Sport-Specific Coaching"
msgstr "90 分鐘專項運動指導"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Personal "
"Trainer</strong></span>"
msgstr "<span class=\"display-4-fs\"><strong>Odoo 私人教練數據套裝</strong></span>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Configure Appointment Types</strong>: In the Appointment app, create"
" types that match your services."
msgstr "<strong>配置預約類型</strong>：在預約應用程式中，按照你提供的服務種類，建立不同的服務類型。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Create Client Profiles</strong>: As you get inquiries, add them to "
"your CRM and Contacts."
msgstr "<strong>建立客戶個人檔案</strong>：收到查詢時，可將相關資訊新增至客戶關係及聯絡人應用程式。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Invoice Your Clients</strong>: Use the Invoicing app to bill clients"
" for completed services."
msgstr "<strong>向客戶開立發票</strong>：使用發票應用程式，為已完成的服務向客戶開立賬單及收取費用。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Manage Your Calendar</strong>: Use the Calendar app to block off "
"your availability and personal time."
msgstr "<strong>管理日曆</strong>：使用日曆應用程式，劃出你的空閒時段及個人時間。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Set Up Your Services</strong>: Navigate to Sales &gt; Products and "
"set up your coaching services."
msgstr "<strong>設置不同服務</strong>：前往銷售應用程式 &gt; 產品，設定你的各類教練服務。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Start Tracking Projects</strong>: For services that require ongoing "
"management, create a project in the Project app."
msgstr "<strong>追蹤專案項目</strong>：對於需要持續管理的服務，可在專案管理應用程式中，建立個別專案。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Appointment Scheduling 📆"
msgstr "預約排程 📆"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Basics"
msgstr "基礎知識"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Client Management (CRM) 🎯"
msgstr "客戶管理（CRM）🎯"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Collaborate with clients on their goals"
msgstr "與客戶攜手，實現他們的目標"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Convert leads into clients"
msgstr "將潛在顧客轉化為客戶"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Create a stunning <strong>Website</strong> to showcase your coaching "
"services and allow clients to book sessions online."
msgstr "製作一個美觀動人的<strong>網站</strong>，展示你的教練服務詳情，以及讓客戶在網上預約時段。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create and manage your coaching services:"
msgstr "建立及管理你的教練服務："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create tasks for each stage of a client's journey"
msgstr "為客戶旅程的每個階段，建立個別任務"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define pricing and project tracking needs for each service"
msgstr "為每項服務定義價錢，並設定專案追蹤需求"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define your availability"
msgstr "設定你的空閒時段"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Develop and sell online fitness courses with the <strong>eLearning</strong> "
"platform, expanding your reach beyond in-person training."
msgstr "使用<strong>網上學習</strong>平台，設計及銷售線上健身課程，擴大接觸面至一對一訓練之外的客戶。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Do you want to go further?"
msgstr "想再進一步嗎？"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_12
msgid "Done"
msgstr "完成"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Easily bill clients for your services:"
msgstr "輕鬆向客戶開立服務賬單並收取費用："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Encourage clients to book through your Appointment page to reduce scheduling"
" back-and-forth."
msgstr "鼓勵客戶透過預約網頁預訂課節，減少來回溝通及無謂的更改。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Engage clients with targeted campaigns using <strong>Email "
"Marketing</strong> and <strong>Social Marketing</strong>."
msgstr "使用<strong>電郵推廣</strong>及<strong>社交媒體推廣</strong>，以針對性的推廣活動吸引客戶。"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_11_product_template
msgid "Fitness Assessment"
msgstr "體格評估"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Follow up on inquiries"
msgstr "跟進查詢"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Gather valuable client feedback and track progress effortlessly using "
"<strong>Surveys</strong>."
msgstr "使用<strong>網上調查</strong>，收集客戶的寶貴意見，並輕鬆追蹤進度。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Generate invoices automatically from sales orders"
msgstr "自動按照銷售單產生發票"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Getting Started"
msgstr "新手入門"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"If you want to push your quotation building much further and add all the "
"power of Spreadsheet beneath any line of your Sales Orders, use the quote "
"calculation feature."
msgstr "若想進一步推進報價建立流程，並將試算表的所有強大功能，加入銷售單並套用至任何資料行，你可使用報價計算功能。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Invoicing 🧾"
msgstr "開立發票 🧾"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Let clients book directly through your booking page"
msgstr "讓客戶透過你的預訂頁面直接預訂"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage leads and opportunities"
msgstr "管理潛在客戶及銷售機會"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage your services 👟"
msgstr "管理你的服務 👟"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Manage your team of coaches and staff efficiently with "
"<strong>Employees</strong>."
msgstr "使用<strong>員工</strong>應用程式，高效率管理教練及員工團隊。"

#. module: personal_trainer
#: model:crm.stage,name:personal_trainer.crm_stage_5
msgid "Negotiation"
msgstr "商議"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_9
msgid "New"
msgstr "新增"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as:"
msgstr "Odoo 為你提供無限可能，例如："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Organize group classes, workshops, and fitness events easily with the "
"<strong>Events</strong> app."
msgstr "使用<strong>活動</strong>應用程式，輕鬆舉辦小組課堂、工作坊、健身活動等。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Project Tracking 📈"
msgstr "專案追蹤 📈"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Reach us"
msgstr "聯絡我們"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Regularly update your CRM to keep track of potential clients and follow-ups."
msgstr "定期更新你的客戶關係應用程式，以追蹤潛在客戶及跟進行動。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Remember, Odoo is flexible and can adapt to your specific needs. Don't "
"hesitate to explore and customize as your coaching business grows!"
msgstr "記住，Odoo 是很靈活的，可以適應你的任何特定需求。隨着你的教練業務發展，歡迎進一步探索其他功能，以及自訂系統的操作方式！"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up appointment types (e.g., \"60-min Personal Training\", \"30-min "
"Assessment\")"
msgstr "設定預約類型（例：「60 分鐘私人訓練」、「30 分鐘評估」等）"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up services like \"10-Session Personal Training Package\" or \"3-Month "
"Transformation Program\""
msgstr "設立不同服務，例如：「10 節私人訓練套餐」或「3 個月改造計劃」"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_8_product_template
msgid "Single Training Session"
msgstr "單次訓練課節"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_10_product_template
#: model:project.project,name:personal_trainer.project_project_4
msgid "Sport-Specific Performance Package"
msgstr "專項運動表現套餐"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Struggling with your setup? Reach us to get a demo!"
msgstr "設定時遇到困難？歡迎聯絡我們，觀看功能示範！"

#. module: personal_trainer
#: model:project.project,label_tasks:personal_trainer.project_project_2
#: model:project.project,label_tasks:personal_trainer.project_project_3
#: model:project.project,label_tasks:personal_trainer.project_project_4
msgid "Tasks"
msgstr "任務"

#. module: personal_trainer
#: model:project.tags,name:personal_trainer.project_tags_1
msgid "Template"
msgstr "範本文字"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "這些項目在你目前的服務計劃中，全部都是免費的，儘管隨意探索！🙃"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Tips for Success"
msgstr "小提示：成功秘訣"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Track fitness equipment and sell merchandise through your integrated "
"<strong>eCommerce</strong> store."
msgstr "運用整合不同功能的<strong>電子商務</strong>網店，追蹤健身器材狀況，以及銷售商品。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track payments and send reminders"
msgstr "追蹤付款情況，適當時發送提醒"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track potential and current clients:"
msgstr "追蹤潛在及目前的客戶："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track progress and set milestones"
msgstr "追蹤進度，設立里程碑"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Appointment app to allow clients to book sessions easily:"
msgstr "使用預約應用程式，讓客戶輕鬆預訂時段："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Project app to manage longer-term client programs:"
msgstr "使用專案管理應用程式，管理較長期客戶的服務計劃："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the project templates for standardized programs to save time."
msgstr "對於標準化的服務計劃，可使用專案範本，以節省時間。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Utilize the invoicing reminders to ensure timely payments."
msgstr "利用發票提醒功能，確保客戶及時付款。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Welcome to your new Odoo Personal Trainer package! This guide will help you "
"navigate the key features and get your coaching business up and running "
"efficiently."
msgstr "歡迎使用 Odoo 私人教練套裝！本指南將幫助你了解如何使用關鍵功能，以及如何讓你的教練業務高效率起動及運作。"

#. module: personal_trainer
#: model_terms:web_tour.tour,rainbow_man_message:personal_trainer.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "歡迎！請享受探索過程。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "想與我們討論如何設定你的 Odoo 配置，甚至更進一步嗎？"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Appointment"
msgstr "🎓 預約"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 客戶關係"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Invoicing"
msgstr "🎓 開立發票"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Project"
msgstr "🎓 專案管理"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 銷售"
