# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* personal_trainer
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 04:52+0000\n"
"PO-Revision-Date: 2024-11-24 02:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_7_product_template
#: model:project.project,name:personal_trainer.project_project_2
msgid "10-Session Personal Training Package"
msgstr "10 节私人训练套餐"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_9_product_template
#: model:project.project,name:personal_trainer.project_project_3
msgid "3-Month Transformation Program"
msgstr "3个月改造计划"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_2
msgid "30-min Fitness Assessment"
msgstr "30 分钟健身评估"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_1
msgid "60-min Personal Training Session"
msgstr "60 分钟私人训练课程"

#. module: personal_trainer
#: model:appointment.type,name:personal_trainer.appointment_type_3
msgid "90-min Sport-Specific Coaching"
msgstr "90 分钟运动专项指导"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Personal "
"Trainer</strong></span>"
msgstr "<span class=\"display-4-fs\"><strong>Odoo 私人教练数据套装</strong></span>"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Configure Appointment Types</strong>: In the Appointment app, create"
" types that match your services."
msgstr "<strong>配置预约类型</strong>：在预约应用程序中，按照您提供的服务种类，建立不同的服务类型。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Create Client Profiles</strong>: As you get inquiries, add them to "
"your CRM and Contacts."
msgstr "<strong>创建客户个人档案</strong>：收到查询时，可将相关信息新增至客户关系管理及联系人应用程序。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Invoice Your Clients</strong>: Use the Invoicing app to bill clients"
" for completed services."
msgstr "<strong>向客户开具发票</strong>：使用发票应用程序，为已完成的服务向客户开立账单及收取费用。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Manage Your Calendar</strong>: Use the Calendar app to block off "
"your availability and personal time."
msgstr "<strong>管理您的日历</strong>：使用日历应用来封锁你的空闲时间和私人时间。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Set Up Your Services</strong>: Navigate to Sales &gt; Products and "
"set up your coaching services."
msgstr "<strong>设置不同服务</strong>：前往销售应用程序&gt; 产品，并设置您的各类教练服务。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"<strong>Start Tracking Projects</strong>: For services that require ongoing "
"management, create a project in the Project app."
msgstr "<strong>开始追踪项目</strong>：对于需要持续管理的服务，可在项目管理应用程序中，建立个别项目。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Appointment Scheduling 📆"
msgstr "预约排程 📆"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Basics"
msgstr "基础"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Client Management (CRM) 🎯"
msgstr "客户管理（CRM）🎯"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Collaborate with clients on their goals"
msgstr "与客户携手，实现他们的目标"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Convert leads into clients"
msgstr "将潜在客户转化为客户"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Create a stunning <strong>Website</strong> to showcase your coaching "
"services and allow clients to book sessions online."
msgstr "制作一个美观动人的<strong>网站</strong>，展示您的教练服务详情，以及让客户在网上预约时段。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create and manage your coaching services:"
msgstr "创建并管理您的教练服务："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Create tasks for each stage of a client's journey"
msgstr "为客户旅程的每个阶段创建任务"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define pricing and project tracking needs for each service"
msgstr "确定每项服务的定价和项目追踪需求"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Define your availability"
msgstr "设置您的空闲时段"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Develop and sell online fitness courses with the <strong>eLearning</strong> "
"platform, expanding your reach beyond in-person training."
msgstr "使用<strong>网上学习</strong>平台，设计及销售线上健身课程，扩大接触面至一对一训练之外的客户。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Do you want to go further?"
msgstr "想再进一步吗？"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_12
msgid "Done"
msgstr "已完成"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Easily bill clients for your services:"
msgstr "轻松向客户开立服务账单并收取费用："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Encourage clients to book through your Appointment page to reduce scheduling"
" back-and-forth."
msgstr "鼓励客户通过网页预订，减少重复沟通及更改。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Engage clients with targeted campaigns using <strong>Email "
"Marketing</strong> and <strong>Social Marketing</strong>."
msgstr "利用<strong>电子邮件营销</strong>和<strong>社交营销</strong>开展有针对性的活动，吸引客户。"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_11_product_template
msgid "Fitness Assessment"
msgstr "健身评估"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Follow up on inquiries"
msgstr "跟进查询"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Gather valuable client feedback and track progress effortlessly using "
"<strong>Surveys</strong>."
msgstr "使用 <strong>调查</strong>轻松收集宝贵的客户反馈并跟踪进度。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Generate invoices automatically from sales orders"
msgstr "根据销售订单自动生成发票"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Getting Started"
msgstr "入门"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"If you want to push your quotation building much further and add all the "
"power of Spreadsheet beneath any line of your Sales Orders, use the quote "
"calculation feature."
msgstr "如果您想更进一步建立报价单，并在销售订单的任何一行下添加电子表格的所有功能，请使用报价计算功能。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Invoicing 🧾"
msgstr "发票 🧾"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Let clients book directly through your booking page"
msgstr "让客户通过您的预订页面直接预订"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage leads and opportunities"
msgstr "管理线索和机会"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Manage your services 👟"
msgstr "管理您的服务 👟"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Manage your team of coaches and staff efficiently with "
"<strong>Employees</strong>."
msgstr "使用<strong>员工</strong>高效管理您的教练和员工团队。"

#. module: personal_trainer
#: model:crm.stage,name:personal_trainer.crm_stage_5
msgid "Negotiation"
msgstr "协商"

#. module: personal_trainer
#: model:project.task.type,name:personal_trainer.project_task_type_9
msgid "New"
msgstr "新建"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as:"
msgstr "Odoo 为您提供无限可能，例如："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Organize group classes, workshops, and fitness events easily with the "
"<strong>Events</strong> app."
msgstr "使用 <strong>活动</strong>应用程序，轻松组织集体课程、研讨会和健身活动。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Project Tracking 📈"
msgstr "项目跟踪 📈"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Reach us"
msgstr "联系我们"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Regularly update your CRM to keep track of potential clients and follow-ups."
msgstr "定期更新客户关系管理系统，跟踪潜在客户和后续行动。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Remember, Odoo is flexible and can adapt to your specific needs. Don't "
"hesitate to explore and customize as your coaching business grows!"
msgstr "请记住，Odoo 非常灵活，可以适应您的特定需求。请不要犹豫，随着您教练业务的发展，不断探索和定制！"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up appointment types (e.g., \"60-min Personal Training\", \"30-min "
"Assessment\")"
msgstr "设置预约类型（如 “60 分钟个人培训”、“30 分钟评估 ”等）"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Set up services like \"10-Session Personal Training Package\" or \"3-Month "
"Transformation Program\""
msgstr "设置 “10 节私人培训套餐 ”或 “3 个月转型计划 ”等服务"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_8_product_template
msgid "Single Training Session"
msgstr "单次培训课程"

#. module: personal_trainer
#: model:product.template,name:personal_trainer.product_product_10_product_template
#: model:project.project,name:personal_trainer.project_project_4
msgid "Sport-Specific Performance Package"
msgstr "运动型高性能套件"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Struggling with your setup? Reach us to get a demo!"
msgstr "正在为设置而烦恼？联系我们，获取演示！"

#. module: personal_trainer
#: model:project.project,label_tasks:personal_trainer.project_project_2
#: model:project.project,label_tasks:personal_trainer.project_project_3
#: model:project.project,label_tasks:personal_trainer.project_project_4
msgid "Tasks"
msgstr "任务"

#. module: personal_trainer
#: model:project.tags,name:personal_trainer.project_tags_1
msgid "Template"
msgstr "模板"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "这些都是您当前订阅中的免费内容；请随意浏览！🙃"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Tips for Success"
msgstr "成功秘诀"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Track fitness equipment and sell merchandise through your integrated "
"<strong>eCommerce</strong> store."
msgstr "跟踪健身器材，并通过集成的<strong>电子商务</strong>商店销售商品。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track payments and send reminders"
msgstr "跟踪付款并发送提醒"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track potential and current clients:"
msgstr "跟踪潜在和现有客户："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Track progress and set milestones"
msgstr "跟踪进度并设定里程碑"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Appointment app to allow clients to book sessions easily:"
msgstr "使用预约应用程序，让客户轻松预约课程："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the Project app to manage longer-term client programs:"
msgstr "使用项目应用程序管理长期客户计划："

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Use the project templates for standardized programs to save time."
msgstr "使用标准化程序的项目模板，以节省时间。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Utilize the invoicing reminders to ensure timely payments."
msgstr "利用发票提醒功能确保及时付款。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid ""
"Welcome to your new Odoo Personal Trainer package! This guide will help you "
"navigate the key features and get your coaching business up and running "
"efficiently."
msgstr "欢迎使用 Odoo 私人教练套装！ 本指南将帮助您了解如何使用关键功能，以及如何让您的教练业务高效运作。"

#. module: personal_trainer
#: model_terms:web_tour.tour,rainbow_man_message:personal_trainer.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "欢迎！祝您探索愉快。"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "想与我们讨论您的 Odoo 设置或更进一步吗？"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Appointment"
msgstr "🎓 预约"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Invoicing"
msgstr "🎓 发票"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Project"
msgstr "🎓 项目"

#. module: personal_trainer
#: model_terms:ir.ui.view,arch_db:personal_trainer.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 销售"
