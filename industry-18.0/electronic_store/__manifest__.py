{
    'name': 'Electronic Store',
    'version': '1.0',
    'category': 'Retail',
    'description': """
        This module provides essential features to manage an electronic store.
        It includes product categories, products, sales orders, and more for electronic products.
    """,
    'depends': [
        'crm',
        'documents_fsm',
        'documents_project_sale',
        'helpdesk_account',
        'helpdesk_repair',
        'industry_fsm_sale_report',
        'industry_fsm_stock',
        'knowledge',
        'point_of_sale',
        'purchase_stock',
        'sale_management',
        'sale_purchase',
        'stock_barcode',
        'web_studio',
        'website_sale',
        'theme_buzzy',
    ],
    'data': [
        'data/helpdesk_config.xml',
        'data/res_config_settings.xml',
        'data/base_automation.xml',
        'data/ir_actions_server.xml',
        'data/ir_attachment_pre.xml',
        'data/ir_model.xml',
        'data/ir_model_fields.xml',
        'data/ir_ui_view.xml',
        'data/qweb_view.xml',
        'data/ir_actions_act_window.xml',
        'data/ir_model_access.xml',
        'data/ir_rule.xml',
        'data/product_public_category.xml',
        'data/documents_folder.xml',
        'data/product_category.xml',
        'data/pos_category.xml',
        'data/worksheet_template.xml',
        'data/project_project.xml',
        'data/product_product.xml',
        'data/pos_payment_method.xml',
        'data/pos_config.xml',
        'data/sale_order_template.xml',
        'data/sale_order_template_line.xml',
        'data/knowledge_cover.xml',
        'data/knowledge_article.xml',
        'data/knowledge_article_favorite.xml',
        'data/mail_message.xml',
        'data/knowledge_tour.xml',
    ],
    'demo': [
        'demo/res_partner.xml',
        'demo/account_analytic_plan.xml',
        'demo/account_analytic_account.xml',
        'demo/helpdesk_ticket.xml',
        'demo/repair_order.xml',
        'demo/website_ir_attachment.xml',
        'demo/website.xml',
        'demo/crm_lead.xml',
        'demo/product_supplierinfo.xml',
        'demo/product_product.xml',
        'demo/purchase_order.xml',
        'demo/purchase_order_line.xml',
        'demo/sale_order.xml',
        'demo/sale_order_line.xml',
        'demo/website_view.xml',
        'demo/website_page.xml',
        'demo/website_menu.xml',
        'demo/stock_lot.xml',
        'demo/stock_warehouse.xml',
        'demo/purchase_order_post.xml',
        'demo/sale_order_post.xml',
        'demo/crm_lead_post.xml',
        'demo/pos_session.xml',
        'demo/pos_order.xml',
        'demo/pos_order_line.xml',
        'demo/pos_confirm.xml',
        'demo/website_theme_apply.xml',
        'demo/x_worksheet_template.xml',
        'demo/payment_provider_demo_post.xml'
    ],
    'license': 'OPL-1',
    'assets': {
        'web.assets_backend': [
            'electronic_store/static/src/js/my_tour.js',
        ]
    },
    'author': 'Odoo S.A.',
    "cloc_exclude": [
        "data/qweb_view.xml",
        "data/knowledge_article.xml",
        "static/src/js/my_tour.js",
        "demo/website_view.xml",
    ],
    'images': ['images/main.png'],
}
