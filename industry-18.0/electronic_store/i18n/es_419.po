# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* electronic_store
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:34+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1048_product_template
msgid "1 Year Extended Warranty"
msgstr "Garantía extendida de 1 año"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1049_product_template
msgid "3 Year Extended Warranty"
msgstr "Garantía extendida de 3 años"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "73455 Twentynine Palms Highway,"
msgstr "73455 Twentynine Palms Highway,"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<font style=\"color: var(--color) !important;\">Assign serial numbers</font>"
msgstr ""
"<font style=\"color: var(--color) !important;\">Asigne números de "
"serie</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<font style=\"color: var(--color) !important;\">Go to the reception</font>"
msgstr "<font style=\"color: var(--color) !important;\">Ir a la recepción</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\" data-bs-original-title=\"\" aria-describedby=\"tooltip846286\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\" data-bs-original-title=\"\" aria-describedby=\"tooltip846286\"/>\n"
"                                                <span><EMAIL></span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Siguiente</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Anterior</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_2816
msgid "<span class=\"o_footer_copyright_name me-2\">Copyright ©Electronics</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Derechos reservados "
"©Electronics</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • director ejectuvido de"
" MiEmpresa</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • director ejecutivo de "
"MiEmpresa</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • director ejecutivo de "
"MiEmpresa</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Attachment</span>"
msgstr "<span class=\"s_website_form_label_content\">Archivo adjunto</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Description</span>"
msgstr "<span class=\"s_website_form_label_content\">Descripción</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de teléfono</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Product</span>"
msgstr "<span class=\"s_website_form_label_content\">Producto</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Serial Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de serie</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Asunto\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Asunto</span>\n"
"                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su empresa\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su correo electrónico\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su correo electrónico</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su nombre\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su nombre</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su pregunta</span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<strong style=\"font-weight: 500;\">Flow 4: Field Service Installation - "
"AC</strong>"
msgstr ""
"<strong style=\"font-weight: 500;\">Flujo 4: servicio externo de instalación"
" - unidad de aire acondicionado</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"<strong>\n"
"                                        <font class=\"text-o-color-4\">EXCLUSIVE FESTIVE OFFERS</font>\n"
"                                    </strong>"
msgstr ""
"<strong>\n"
"                                        <font class=\"text-o-color-4\">OFERTAS FESTIVAS EXCLUSIVAS</font>\n"
"                                    </strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 1. Purchase </strong>"
msgstr "<strong>Flujo 1: compra</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 2. Sale with serial number</strong>"
msgstr "<strong>Flujo 2: venta con un número de serie</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 3. Delivery</strong>"
msgstr "<strong>Flujo 3: envío</strong>"

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_10
msgid "About Us"
msgstr "Sobre nosotros"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "About us"
msgstr "Sobre nosotros"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adapte estas tres columnas para que se ajusten a sus necesidades de diseño. "
"Para duplicar, eliminar o mover columnas, seleccione la columna y use los "
"iconos superiores para realizar su acción."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
#: model_terms:ir.ui.view,arch_db:electronic_store.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Add details about your intervention..."
msgstr "Agregue detalles sobre su intervención..."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Add the products to the cart"
msgstr "Agregue productos al carrito"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "After completion of the work, update the remarks"
msgstr "Después de completar el trabajo, actualice los comentarios"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1043_product_template
#: model:project.project,name:electronic_store.project_project_3
msgid "Air Conditioner Installation"
msgstr "Instalación de aires acondicionados"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_10
msgid "Air Conditioners"
msgstr "Aires acondicionados"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1070_product_template
msgid "BOSS E111 Portable 125 Watt Hand Blender (Grey)"
msgstr "Batidora de mano portátil BOSS E111 125 Watts (Gris)"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_15
msgid "Blender"
msgstr "Licuadora"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1031_product_template
msgid "Bosch Compact Washer"
msgstr "Lavadora compacta Bosch"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1035_product_template
msgid "Breville Quick Touch Crisp Microwave"
msgstr "Microondas Breville Quick Touch Crisp"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Bruce Porter"
msgstr "Bruce Porter"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Business Flow"
msgstr "Flujo empresarial"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "CA&amp; 92277"
msgstr "CA&amp; 92277"

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_1
msgid "Cash"
msgstr "Efectivo"

#. module: electronic_store
#: model:account.journal,name:electronic_store.cash
msgid "Cash (Electronic Store)"
msgstr "Efectivo (tienda de electrónica)"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Check with My Tasks"
msgstr "Revisar con Mis tareas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click on the first delivery order"
msgstr "Haga clic en la primera orden de entrega"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click the \"validate\" barcode"
msgstr "Haga clic en el código de barras \"válido\""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Comments"
msgstr "Comentarios"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Confirm the RFQ"
msgstr "Confirme la solicitud de cotización"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "Contact us"
msgstr "Contáctenos"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"Contact us about anything related to our company or services.\n"
"                                            <br/>\n"
"                                            We'll do our best to get back to you as soon as possible."
msgstr ""
"Contáctenos para hablar acerca de cualquier cosa relacionada con nuestra empresa o servicios.\n"
"                                            <br/>\n"
"                                            Le responderemos tan pronto como sea posible."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create a quotation with:"
msgstr "Cree una cotización con:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create the purchase order"
msgstr "Cree la orden de compra"

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_2
msgid "Customer Account"
msgstr "Cuenta de cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customer can register their complaint for the existing purchased products "
"with serial numbers from the website:"
msgstr ""
"El cliente puede registrar su queja sobre los productos comprados existentes"
" con el número de serie y desde el sitio web:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customers are either visiting the store to discover new products, either "
"exploring the webshop looking for great offers and discounts / cheapest "
"costs,."
msgstr ""
"Los clientes están ya sea visitando la tienda para descubrir nuevos "
"productos, o explorando la tienda en línea para obtener ofertas y descuentos"
" y los costos más baratos."

#. module: electronic_store
#: model:account.analytic.plan,name:electronic_store.account_analytic_plan_1
msgid "Default"
msgstr "Predeterminado"

#. module: electronic_store
#: model:ir.model,name:electronic_store.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "Hoja de trabajo predeterminada"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Elimine la imagen anterior o reemplácela por otra que ilustre su mensaje. "
"Haga clic en la imagen para cambiar su <em>esquina redondeada</em>."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Document - Guidelines of Installation"
msgstr "Documentación - guías sobre la instalación"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1040_product_template
msgid "Dyson Purifier Cool (White/Silver) - TP07"
msgstr "Dyson Purifier Cool (Blanco/Plata) - TP07"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "ELECTRONICS STORE"
msgstr "Tienda de electrónicos"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Electronic Store"
msgstr "Tienda de electrónicos"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Enter the product <font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner</font>"
msgstr ""
"Ingrese el producto <font style=\"color: rgb(156, 0, 255);\">Aire "
"acondicionado LG DualCool Inverter tipo ventana </font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Every day, the logistic manager reserves products that should be delivered today (reservation is set to manual for this location, so that he has a control of what he wants to send today). If a delivery order should be delivered\n"
"            today but products are not available, he informs the customer using the chatter on the delivery order."
msgstr ""
"Cada día, el gerente de logística reserva productos que se deben entregar hoy (la reservación está configurada como manual para esta ubicación para que el gerente tenga el control sobre lo que quiere enviar hoy). Si se dene entregar una orden de entrega\n"
"hoy pero los productos no están disponibles, le informará a cliente por medio del chatter de la orden de envío."

#. module: electronic_store
#: model:ir.actions.server,name:electronic_store.ir_act_server_1
msgid "Execute Code"
msgstr "Ejecutar código"

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_2
msgid "Field Service"
msgstr "Servicio externo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Field service engineer checking daily activity with mobile app:"
msgstr ""
"Ingeniero de servicios externos que revisa la actividad diaria con la "
"aplicación móvil:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Field service engineer will visit the client place and start working on "
"installation."
msgstr ""
"El ingeniero del servicio externo visitará la dirección del cliente y "
"comenzará a trabajar en la instalación."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Fill the form:"
msgstr "Llene el formulario:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 1. Purchase"
msgstr "Flujo 1: compra"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 2. Sale with serial number"
msgstr "Flujo 2: venta con un número de serie"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 3. Delivery"
msgstr "Flujo 3: entrega"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 4: Field Service Installation - AC"
msgstr ""
"Flujo 4: servicio externo de instalación - unidad de aire acondicionado"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 5: Website Complaint"
msgstr "Flujo 5: sitio web de quejas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 6: POS Shopping"
msgstr "Flujo 6: compra en el PdV"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"For a better living, Lifestyle is a very important aspect. Well, now you can"
" enjoy the best and healthy lifestyle with Home Appliances. This Festive, "
"celebrate the festive tradition with smart innovation. Enjoy exciting offers"
" on Home Entertainment and Home Appliances..<br/>"
msgstr ""
"Algunos aspectos son muy importantes para mejorar su calidad de vida y "
"nuestros electrodomésticos harán que lleve un mejor y más saludable estilo "
"de vida. En esta temporada festiva, celebre la tradición con innovación "
"inteligente y disfrute de ofertas emocionantes en entretenimiento para el "
"hogar y electrodomésticos.<br/>"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1038_product_template
msgid "Frigidaire Portable Air Conditioner"
msgstr "Aire acondicionado portátil Frigidaire"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1025_product_template
msgid "Frigidaire Top-Freezer Refrigerator"
msgstr "Refrigerador Frigidaire con congelador en la parte superior"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the POS Shop:"
msgstr "Desde la tienda del PdV:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the website, click on the menu Helpdesk"
msgstr "Desde el sitio web, haga clic en el menú Soporte al cliente"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1036_product_template
msgid "GE Profile Over-the-Range Microwave"
msgstr "Microondas GE Profile Over-the-Range"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1047_product_template
msgid "Gift Card"
msgstr "Tarjeta de regalo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to mobile odoo app \"Air Conditioner Installation\""
msgstr "Vaya a la aplicación móvil de Odoo."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to project \"Air Conditioner Installation\""
msgstr "Abra el proyecto \"instalación de aire acondicionado\""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_9
msgid "Home Appliance"
msgstr "Electrodoméstico"

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_1
msgid "Home Appliances"
msgstr "Electrodomésticos"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1039_product_template
msgid "Honeywell QuietSet Tower Fan"
msgstr "Ventilador de torre Honeywell QuietSet"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"In the office, the service manager assigns tasks to installers. To do so"
msgstr ""
"En la oficina, el gerente de servicios le asigna tareas a las personas "
"encargadas de la instalación. Para eso"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_installation_date
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Installation Date"
msgstr "Fecha de la instalación"

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_1
msgid "Internal"
msgstr "Interno"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "KB ELECTRONICS"
msgstr "KB ELECTRONICS"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid ""
"KB Electronics is a family-owned home appliance store with a global "
"presence. Since 1978, KB Electronics is known for their knowledgeable staff "
"and large selection of home appliances. Throughout the years, they have "
"provided exceptional customer service both before and after the sale, as "
"well as offering professional custom installation services. We service what "
"we sell, and our knowledgeable and friendly staff can help you select home "
"appliances at any budget. We look forward to seeing you at KB Electronics!"
msgstr ""
"KB Electronics es una tienda familiar de electrodomésticos que cuenta con "
"presencia en todo el mundo. Desde 1978, en KB Electronics destacamos gracias"
" a nuestro personal capacitado y gran variedad de electrodomésticos para el "
"hogar. A lo largo de los años, hemos proporcionado un servicio al cliente "
"excepcional antes y después de la venta. Además de ofrecer servicios "
"profesionales de instalación personalizada, también ofrecemos servicio para "
"todos los productos que vendemos. El personal de nuestras tiendas es amable "
"y cuenta con el conocimiento adecuado para ayudarle a seleccionar "
"electrodomésticos dentro de cualquier presupuesto. ¡Esperamos verle en KB "
"Electronics!"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "KB Electronics<br/>"
msgstr "KB Electronics<br/>"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_13
msgid "Kitchen Appliance"
msgstr "Aparato de cocina"

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_2
msgid "Kitchen Appliances"
msgstr "Aparatos de cocina"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1026_product_template
msgid "KitchenAid Counter-Depth French Door Refrigerator"
msgstr "Refrigerador KitchenAid de puerta tipo francesa"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1037_product_template
msgid "LG Dual Inverter Window Air Conditioner"
msgstr "Aire acondicionado LG DualCool Inverter tipo ventana "

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1022_product_template
msgid "LG French Door Refrigerator"
msgstr "Refrigerador LG de puerta tipo francesa"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1033_product_template
msgid "LG NeoChef Countertop Microwave"
msgstr "Microondas LG NeoChef"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1028_product_template
msgid "LG Top-Load Washer with TurboWash"
msgstr "Lavadora de carga superior LG con TurboWash"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Let's buy some items that are tracked by serial numbers."
msgstr "Compremos algunos artículos que se rastreen con números de serie."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1027_product_template
msgid "Maytag Front-Load Washer"
msgstr "Lavadora de carga superior Maytag"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_16
msgid "Microwaves"
msgstr "Microondas "

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_makemodel
#: model_terms:ir.ui.view,arch_db:electronic_store.product_template_form_view
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Model Number"
msgstr "Número de modelo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Move the task \"<font style=\"color: rgb(156, 0, 255);\">In "
"Progress</font>\" and then \"<font style=\"color: rgb(156, 0, "
"255);\">Done</font>\" when it's finished and sign the service report."
msgstr ""
"Mueva la tarea a \"<font style=\"color: rgb(156, 0, 255);\">En "
"progreso</font>\" y después \"<font style=\"color: rgb(156, 0, "
"255);\">Hecho</font>\" cuando se haya terminado y hayan firmado el reporte "
"del servicio."

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_name_record
msgid "Name"
msgstr "Nombre"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Next"
msgstr "Siguiente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Odoo will generate 1 task for the Air Conditioner installation."
msgstr "Odoo generará 1 tarea para la instalación de aire acondicionado."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Once the order is confirmed:"
msgstr "Una vez que se confirme la orden:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Our References"
msgstr "Nuestras referencias"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1032_product_template
msgid "Panasonic Genius Sensor Microwave Oven"
msgstr "Microondas Panasonic con Genius Sensor"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Plan to visit customer location"
msgstr "Planee una visita a la dirección del cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Previous"
msgstr "Anterior"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Product wise guidelines and steps given for the Service Engineer"
msgstr ""
"Guías y pasos sobre el producto que se le da al Ingeniero del servicio"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Products"
msgstr "Productos"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_products_id_worksheet_template
msgid "Products on Tasks"
msgstr "Productos en tareas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Provide the serial number <font style=\"color: rgb(156, 0, "
"255);\">804KCPY43321</font>"
msgstr ""
"De el número de serie <font style=\"color: rgb(156, 0, "
"255);\">804KCPY43321</font>."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1042_product_template
#: model:project.project,name:electronic_store.project_project_4
msgid "Refrigerator Installation"
msgstr "Instalación de refrigeradores"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_11
msgid "Refrigerators"
msgstr "Refrigeradores"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_comments_remarks_record
msgid "Remarks"
msgstr "Comentarios"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1029_product_template
msgid "Samsung Front-Load Washer with Steam"
msgstr "Lavadora de carga superior Samsung con Hygiene Steam"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1023_product_template
msgid "Samsung Side-by-Side Refrigerator"
msgstr "Refrigerador Samsung Side by Side"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Scan product <strong><font style=\"color: rgb(255, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""
"Escanee el producto <strong><font style=\"color: rgb(255, 0, 255);\">Aire "
"acondicionado LG DualCool Inverter tipo ventana</font></strong>."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Scan the serial number"
msgstr "Escanee el número de serie."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select any of the payment methods and proceed to the payment."
msgstr ""
"Seleccione cualquier método de pago y después siga con el proceso de pago."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Select product <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""
"Seleccione el producto <strong><font style=\"color: rgb(156, 0, 255);\">Aire"
" acondicionado LG DualCool Inverter tipo ventana</font></strong>."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select the customer"
msgstr "Seleccione el cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineer"
msgstr "Técnico de servicio"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_service_engineeres
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineers"
msgstr "Técnicos de servicio"

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_11
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Services"
msgstr "Servicios"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit"
msgstr "Enviar"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit a Ticket"
msgstr "Enviar un ticket"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Submit the ticket."
msgstr "Envíe el ticket."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Switch tasks to Gantt view"
msgstr "Cambie la vista de las tareas a la vista Gantt"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1053_product_template
msgid "TV LED"
msgstr "TV LED"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_project_task_record
msgid "Task"
msgstr "Tarea"

#. module: electronic_store
#: model:project.project,label_tasks:electronic_store.project_project_3
#: model:project.project,label_tasks:electronic_store.project_project_4
#: model:project.project,label_tasks:electronic_store.project_project_5
msgid "Tasks"
msgstr "Tareas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "The delivery order will be created for the LG Dual Inverter"
msgstr "Se creará la orden de entrega para el LG DualCool Inverter"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"The salesperson sets the \"expected delivery date\" based on customer "
"availability"
msgstr ""
"La persona encargada de ventas indica la \"Fecha de entrega esperada\" según"
" la disponibilidad del cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Theia Hayward"
msgstr "Theia Hayward"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Then, the worker processes all delivery orders that are ready, using the "
"barcode interface. From delivery orders:"
msgstr ""
"Después, el trabajador procesará todas las órdenes de entrega que estén "
"listas usando la interfaz del código de barras. Desde las órdenes de "
"entrega:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"They generally require the functionalities of several apps to manage their "
"business workflow."
msgstr ""
"Generalmente requieren de las funciones de varias aplicaciones para "
"gestionar su flujo de trabajo."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"This Business setup for Electronics store where they can sell their "
"electronics items like Home Appliances, Kitchen Appliances, etc..."
msgstr ""
"Esta es una configuración empresarial para una tienda de electrónicos donde "
"todo lo que venden son artículos como electrodomésticos, línea blanca, etc."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Para agregar una cuarta columna, reduzca el tamaño de estas tres columnas "
"con el icono correspondiente de cada bloque y luego duplique una para crear "
"una nueva como copia."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "To speed up the creation of the quotation, use a quotation template."
msgstr ""
"Para acelerar el proceso de creación de una cotización, utilice la plantilla"
" de cotización."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1034_product_template
msgid "Toshiba EM131A5C-SS Microwave Oven"
msgstr "Microondas Toshiba EM131A5C-SS "

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_type_of_installation
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Type of Installation"
msgstr "Tipo de instalación"

#. module: electronic_store
#: model:base.automation,name:electronic_store.base_automation_1
msgid "Update warranty date on serial number"
msgstr "Actualizar la fecha de garantía en el número de serie"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"Upgrade Your Gear: Black Friday's Tech Delights Await!. Bring home smart "
"products with exclusive Festive Offers"
msgstr ""
"¡Conozca las novedades tecnológicas este Buen Fin! Nuestros productos "
"inteligentes están en descuento."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use Ship Later option before the checkout for shipping later"
msgstr ""
"Use la opción \"Enviar después\" antes de pagar para realizar el envío en "
"otro momento"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use the magnifier 🔎 icon to select tasks to assign"
msgstr "Use el icono de lupa 🔎 para seleccionar las tareas que debe asignar."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Validate the Receipt"
msgstr "Valide el proyecto"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_field_to_set_warranty_month
msgid "Warranty (months)"
msgstr "Garantía (meses)"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_date_helpdesk_ti_x_warranty_date
#: model:ir.model.fields,field_description:electronic_store.new_date_lot_serial_x_warranty_date
msgid "Warranty Date"
msgstr "Fecha de la garantía"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_12
msgid "Washing Machines"
msgstr "Lavadoras"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1050_product_template
#: model:project.project,name:electronic_store.project_project_5
msgid "Washing Machines Installation"
msgstr "Instalación de lavadoras"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "We are in good company."
msgstr "Está en las mejores manos."

#. module: electronic_store
#: model_terms:web_tour.tour,rainbow_man_message:electronic_store.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Le damos la bienvenida! Disfrute del sitio."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"When selling electronics, most items are tracked with serial numbers. To "
"deliver the installation service, we use the project management app, with "
"one task per installation to do."
msgstr ""
"En el sector de venta de electrónicos la mayoría de los artículos tendrán "
"números de serie para su rastreo. Para realizar el servicio de instalación "
"usamos la aplicación para gestión de proyectos y creamos una tarea por "
"instalación."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1024_product_template
msgid "Whirlpool Bottom-Freezer Refrigerator"
msgstr "Refrigerador Whirlpool con congelador en la parte inferior"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1030_product_template
msgid "Whirlpool High-Efficiency Top-Load Washer"
msgstr "Lavadora de alta eficiencia Whirlpool de carga superior"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1041_product_template
msgid "Whynter ARC-14S Dual Hose Portable Air Conditioner"
msgstr "Aire acondicionado portátil Whynter ARC-14S con doble manguera"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Wilson Holt"
msgstr "Wilson Holt"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Worksheet will help for the installation report"
msgstr "La hoja de trabajo le ayudará para el reporte de instalación"

#. module: electronic_store
#: model:ir.actions.act_window,name:electronic_store.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Hojas de trabajo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Escriba aquí una cita de uno de sus clientes. Las citas son una excelente "
"manera de hacer que la gente confíe en sus productos y servicios."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"payment term: <strong><font style=\"color: rgb(156, 0, 255);\">30 % Advance "
"Rest on Installment</font></strong>"
msgstr ""
"término de pago: <strong><font style=\"color: rgb(156, 0, 255);\">adelanto "
"del 30% y el resto al momento de la instalación</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">Air Conditioner "
"Installation</font></strong>"
msgstr ""
"producto: <strong><font style=\"color: rgb(156, 0, 255);\">instalación de "
"aire acondicionado</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner </font></strong>"
msgstr ""
"producto: <strong><font style=\"color: rgb(156, 0, 255);\">Aire "
"acondicionado LG DualCool Inverter tipo ventana </font></strong>"
