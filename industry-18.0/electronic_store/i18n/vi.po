# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* electronic_store
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:34+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1048_product_template
msgid "1 Year Extended Warranty"
msgstr "Bảo hành mở rộng 1 năm"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1049_product_template
msgid "3 Year Extended Warranty"
msgstr "Bảo hành mở rộng 3 năm"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "73455 Twentynine Palms Highway,"
msgstr "73455 Twentynine Palms Highway,"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<font style=\"color: var(--color) !important;\">Assign serial numbers</font>"
msgstr "<font style=\"color: var(--color) !important;\">Gán số sê-ri</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<font style=\"color: var(--color) !important;\">Go to the reception</font>"
msgstr ""
"<font style=\"color: var(--color) !important;\">Đi đến phiếu nhập kho</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\" data-bs-original-title=\"\" aria-describedby=\"tooltip846286\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\" data-bs-original-title=\"\" aria-describedby=\"tooltip846286\"/>\n"
"                                                <span><EMAIL></span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Tiếp</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Trước</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_2816
msgid "<span class=\"o_footer_copyright_name me-2\">Copyright ©Electronics</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Bản quyền thuộc "
"©Electronics</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Nam DOE</b> • CEO of MyCompany</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO của "
"MyCompany</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Long DOE</b> • CEO of "
"MyCompany</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Attachment</span>"
msgstr "<span class=\"s_website_form_label_content\">Tệp đính kèm</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Description</span>"
msgstr "<span class=\"s_website_form_label_content\">Mô tả</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Số điện thoại</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Product</span>"
msgstr "<span class=\"s_website_form_label_content\">Sản phẩm</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Serial Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Số sê-ri</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tiêu đề\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tiêu đề</span>\n"
"                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Công ty\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Email\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tên\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tên</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Câu hỏi</span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<strong style=\"font-weight: 500;\">Flow 4: Field Service Installation - "
"AC</strong>"
msgstr ""
"<strong style=\"font-weight: 500;\">Chu trình 4: Lắp đặt tận nơi - "
"AC</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"<strong>\n"
"                                        <font class=\"text-o-color-4\">EXCLUSIVE FESTIVE OFFERS</font>\n"
"                                    </strong>"
msgstr ""
"<strong>\n"
"                                        <font class=\"text-o-color-4\">ƯU ĐÃI ĐỘC QUYỀN MÙA LỄ HỘI</font>\n"
"                                    </strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 1. Purchase </strong>"
msgstr "<strong>Chu trình 1: Mua hàng</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 2. Sale with serial number</strong>"
msgstr "<strong>Chu trình 2: Bán hàng với số sê-ri</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 3. Delivery</strong>"
msgstr "<strong>Chu trình 3: Giao hàng</strong>"

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_10
msgid "About Us"
msgstr "Giới thiệu"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "About us"
msgstr "Về chúng tôi"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Điều chỉnh ba cột này để phù hợp với nhu cầu thiết kế của bạn. Để sao chép, "
"xóa hoặc di chuyển cột, hãy chọn cột và sử dụng các biểu tượng trên cùng để "
"thực hiện tác vụ của bạn."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
#: model_terms:ir.ui.view,arch_db:electronic_store.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Add details about your intervention..."
msgstr "Thêm chi tiết về cuộc xử lý của bạn..."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Add the products to the cart"
msgstr "Thêm sản phẩm vào giỏ hàng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "After completion of the work, update the remarks"
msgstr "Sau khi hoàn thành công việc, hãy cập nhật các nhận xét"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1043_product_template
#: model:project.project,name:electronic_store.project_project_3
msgid "Air Conditioner Installation"
msgstr "Lắp đặt điều hoà"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_10
msgid "Air Conditioners"
msgstr "Điều hòa không khí"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1070_product_template
msgid "BOSS E111 Portable 125 Watt Hand Blender (Grey)"
msgstr "Máy xay sinh tố cầm tay BOSS E111 công suất 125W (Xám)"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_15
msgid "Blender"
msgstr "Máy xay sinh tố"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1031_product_template
msgid "Bosch Compact Washer"
msgstr "Máy giặt nhỏ gọn Bosch"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1035_product_template
msgid "Breville Quick Touch Crisp Microwave"
msgstr "Lò vi sóng Breville Quick Touch Crisp"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Bruce Porter"
msgstr "Bruce Porter"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Business Flow"
msgstr "Chu trình kinh doanh"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "CA&amp; 92277"
msgstr "CA&amp; 92277"

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_1
msgid "Cash"
msgstr "Tiền mặt"

#. module: electronic_store
#: model:account.journal,name:electronic_store.cash
msgid "Cash (Electronic Store)"
msgstr "Tiền mặt (Cửa Hàng Đồ Điện Tử)"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Check with My Tasks"
msgstr "Kiểm tra Nhiệm vụ của tôi"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click on the first delivery order"
msgstr "Nhấp vào phiếu xuất kho đầu tiên"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click the \"validate\" barcode"
msgstr "Nhấp \"xác thực\" mã vạch"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Comments"
msgstr "Bình luận"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Confirm the RFQ"
msgstr "Xác nhận YCBG"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "Contact us"
msgstr "Liên hệ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"Contact us about anything related to our company or services.\n"
"                                            <br/>\n"
"                                            We'll do our best to get back to you as soon as possible."
msgstr ""
"Hãy liên hệ khi bạn gặp bất cứ vấn đề nào liên quan đến công ty hoặc dịch vụ của chúng tôi.<br/>\n"
"                                    Chúng tôi sẽ cố gắng phản hồi trong thời gian sớm nhất."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create a quotation with:"
msgstr "Tạo báo giá với:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create the purchase order"
msgstr "Tạo đơn mua hàng"

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_2
msgid "Customer Account"
msgstr "Tài khoản khách hàng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customer can register their complaint for the existing purchased products "
"with serial numbers from the website:"
msgstr ""
"Khách hàng có thể tạo khiếu nại đối với các sản phẩm đã mua bằng số sê-ri từ"
" trang web:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customers are either visiting the store to discover new products, either "
"exploring the webshop looking for great offers and discounts / cheapest "
"costs,."
msgstr ""
"Khách hàng có thể ghé thăm cửa hàng để khám phá sản phẩm mới hoặc khám phá "
"cửa hàng online để tìm kiếm các ưu đãi và chiết khấu hấp dẫn/chi phí rẻ "
"nhất."

#. module: electronic_store
#: model:account.analytic.plan,name:electronic_store.account_analytic_plan_1
msgid "Default"
msgstr "Mặc định"

#. module: electronic_store
#: model:ir.model,name:electronic_store.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "Bảng công tác mặc định"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Xóa hình trên hoặc thay thế bằng hình ảnh minh họa thông điệp của bạn. Nhấp "
"vào ảnh để thay đổi kiểu <em> góc tròn </em> của nó."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Document - Guidelines of Installation"
msgstr "Tài liệu - Hướng dẫn lắp đặt"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1040_product_template
msgid "Dyson Purifier Cool (White/Silver) - TP07"
msgstr "Máy lọc không khí Dyson Cool (Trắng/Bạc) - TP07"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "ELECTRONICS STORE"
msgstr "CỬA HÀNG ĐỒ ĐIỆN TỬ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Electronic Store"
msgstr "Cửa Hàng Đồ Điện Tử"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Enter the product <font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner</font>"
msgstr ""
"Nhập sản phẩm <font style=\"color: rgb(156, 0, 255);\">Điều hoà LG Dual "
"Inverter</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Every day, the logistic manager reserves products that should be delivered today (reservation is set to manual for this location, so that he has a control of what he wants to send today). If a delivery order should be delivered\n"
"            today but products are not available, he informs the customer using the chatter on the delivery order."
msgstr ""
"Mỗi ngày, quản lý logistics sẽ dự trữ các sản phẩm cần giao trong ngày hôm nay (dự trữ được thiết lập thủ công cho địa điểm này, để anh ta có thể kiểm soát những gì anh ta muốn gửi trong ngày hôm nay). Nếu một phiếu xuất kho cần được tiến hành trong\n"
"ngày hôm nay nhưng sản phẩm không có sẵn, anh ta sẽ thông báo cho khách hàng bằng cách sử dụng tin nhắn trên phiếu xuất kho."

#. module: electronic_store
#: model:ir.actions.server,name:electronic_store.ir_act_server_1
msgid "Execute Code"
msgstr "Thực thi mã"

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_2
msgid "Field Service"
msgstr "Dịch vụ hiện trường"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Field service engineer checking daily activity with mobile app:"
msgstr ""
"Kỹ sư dịch vụ hiện trường kiểm tra hoạt động hàng ngày bằng ứng dụng di "
"động:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Field service engineer will visit the client place and start working on "
"installation."
msgstr ""
"Kỹ sư dịch vụ hiện trường sẽ đến tận nơi của khách hàng và bắt đầu lắp đặt."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Fill the form:"
msgstr "Điền biểu mẫu:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 1. Purchase"
msgstr "Chu trình 1: Mua hàng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 2. Sale with serial number"
msgstr "Chu trình 2: Bán hàng với số sê-ri"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 3. Delivery"
msgstr "Chu trình 3: Giao hàng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 4: Field Service Installation - AC"
msgstr "Chu trình 4: Lắp đặt tận nơi - AC"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 5: Website Complaint"
msgstr "Chu trình 5: Khiếu nại trên trang web"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 6: POS Shopping"
msgstr "Chu trình 6: Mua sắm qua POS"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"For a better living, Lifestyle is a very important aspect. Well, now you can"
" enjoy the best and healthy lifestyle with Home Appliances. This Festive, "
"celebrate the festive tradition with smart innovation. Enjoy exciting offers"
" on Home Entertainment and Home Appliances..<br/>"
msgstr ""
"Để có cuộc sống tốt hơn, Lối sống là một khía cạnh rất quan trọng. Vâng, bây"
" giờ bạn có thể tận hưởng lối sống lành mạnh và tốt nhất với Thiết bị gia "
"dụng. Mùa lễ hội này, hãy kỷ niệm truyền thống lễ hội với sự đổi mới thông "
"minh. Tận hưởng các ưu đãi hấp dẫn về Giải trí gia đình và Thiết bị gia "
"dụng.<br/>"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1038_product_template
msgid "Frigidaire Portable Air Conditioner"
msgstr "Điều hòa di động Frigidaire"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1025_product_template
msgid "Frigidaire Top-Freezer Refrigerator"
msgstr "Tủ lạnh ngăn đá trên Frigidaire"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the POS Shop:"
msgstr "Từ cửa hàng POS:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the website, click on the menu Helpdesk"
msgstr "Từ trang web, nhấp vào menu Hỗ trợ"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1036_product_template
msgid "GE Profile Over-the-Range Microwave"
msgstr "Lò vi sóng Over-the-Range GE"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1047_product_template
msgid "Gift Card"
msgstr "Thẻ quà tặng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to mobile odoo app \"Air Conditioner Installation\""
msgstr "Đi đến ứng dụng di động Odoo \"Lắp đặt điều hòa\""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to project \"Air Conditioner Installation\""
msgstr "Đi đến dự án \"Lắp đặt điều hòa\""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_9
msgid "Home Appliance"
msgstr "Thiết bị gia dụng"

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_1
msgid "Home Appliances"
msgstr "Thiết bị gia dụng"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1039_product_template
msgid "Honeywell QuietSet Tower Fan"
msgstr "Quạt tháp Honeywell QuietSet"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"In the office, the service manager assigns tasks to installers. To do so"
msgstr ""
"Quản lý dịch vụ phân công nhiệm vụ cho nhân viên lắp đặt. Để làm như vậy"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_installation_date
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Installation Date"
msgstr "Ngày lắp đặt"

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_1
msgid "Internal"
msgstr "Nội bộ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "KB ELECTRONICS"
msgstr "KB ELECTRONICS"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid ""
"KB Electronics is a family-owned home appliance store with a global "
"presence. Since 1978, KB Electronics is known for their knowledgeable staff "
"and large selection of home appliances. Throughout the years, they have "
"provided exceptional customer service both before and after the sale, as "
"well as offering professional custom installation services. We service what "
"we sell, and our knowledgeable and friendly staff can help you select home "
"appliances at any budget. We look forward to seeing you at KB Electronics!"
msgstr ""
"KB Electronics là một cửa hàng đồ gia dụng thuộc loại hình kinh doanh gia "
"đình và đã có mặt trên toàn cầu. Từ năm 1978, KB Electronics nổi tiếng với "
"đội ngũ nhân viên thông thái và nhiều loại đồ gia dụng. Trong suốt nhiều "
"năm, KB Electronics đã cung cấp dịch vụ khách hàng đặc biệt trước và sau khi"
" bán hàng, cũng như cung cấp dịch vụ lắp đặt chuyên nghiệp theo yêu cầu. "
"Chúng tôi bảo dưỡng mọi thứ chúng tôi bán, trong khi đội ngũ nhân viên thông"
" minh và thân thiện của chúng tôi có thể giúp bạn lựa chọn đồ gia dụng với "
"bất kỳ ngân sách nào. Chúng tôi mong muốn được gặp bạn tại KB Electronics!"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "KB Electronics<br/>"
msgstr "KB Electronics<br/>"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_13
msgid "Kitchen Appliance"
msgstr "Thiết bị nhà bếp"

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_2
msgid "Kitchen Appliances"
msgstr "Thiết bị nhà bếp"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1026_product_template
msgid "KitchenAid Counter-Depth French Door Refrigerator"
msgstr "Tủ lạnh cửa Pháp KitchenAid Counter-Depth"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1037_product_template
msgid "LG Dual Inverter Window Air Conditioner"
msgstr "Điều hoà LG Dual Inverter"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1022_product_template
msgid "LG French Door Refrigerator"
msgstr "Tủ lạnh cửa Pháp LG"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1033_product_template
msgid "LG NeoChef Countertop Microwave"
msgstr "Lò vi sóng để bàn LG NeoChef"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1028_product_template
msgid "LG Top-Load Washer with TurboWash"
msgstr "Máy giặt cửa trên LG công nghệ TurboWash"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Let's buy some items that are tracked by serial numbers."
msgstr "Hãy mua một số mặt hàng được theo dõi bằng số sê-ri."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1027_product_template
msgid "Maytag Front-Load Washer"
msgstr "Máy giặt cửa trước Maytag"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_16
msgid "Microwaves"
msgstr "Lò vi sóng"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_makemodel
#: model_terms:ir.ui.view,arch_db:electronic_store.product_template_form_view
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Model Number"
msgstr "Số model"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Move the task \"<font style=\"color: rgb(156, 0, 255);\">In "
"Progress</font>\" and then \"<font style=\"color: rgb(156, 0, "
"255);\">Done</font>\" when it's finished and sign the service report."
msgstr ""
"Di chuyển nhiệm vụ sang \"<font style=\"color: rgb(156, 0, 255);\">Đang thực"
" hiện</font>\" rồi \"<font style=\"color: rgb(156, 0, 255);\">Hoàn "
"tất</font>\" sau khi hoàn thành và ký vào báo cáo dịch vụ."

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_name_record
msgid "Name"
msgstr "Tên"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Next"
msgstr "Tiếp theo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Odoo will generate 1 task for the Air Conditioner installation."
msgstr "Odoo sẽ tạo 1 nhiệm vụ cho Lắp đặt điều hòa."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Once the order is confirmed:"
msgstr "Sau khi đơn hàng được xác nhận:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Our References"
msgstr "Đối tác của chúng tôi"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1032_product_template
msgid "Panasonic Genius Sensor Microwave Oven"
msgstr "Lò vi sóng cảm biến thông minh Panasonic"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Plan to visit customer location"
msgstr "Lập kế hoạch đến địa điểm của khách hàng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Previous"
msgstr "Trước đó"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Product wise guidelines and steps given for the Service Engineer"
msgstr "Hướng dẫn và các bước lắp đặt sản phẩm được đưa ra cho Kỹ sư dịch vụ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Products"
msgstr "Sản phẩm"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_products_id_worksheet_template
msgid "Products on Tasks"
msgstr "Sản phẩm trong nhiệm vụ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Provide the serial number <font style=\"color: rgb(156, 0, "
"255);\">804KCPY43321</font>"
msgstr ""
"Cung cấp số sê-ri <font style=\"color: rgb(156, 0, "
"255);\">804KCPY43321</font>"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1042_product_template
#: model:project.project,name:electronic_store.project_project_4
msgid "Refrigerator Installation"
msgstr "Lắp đặt tủ lạnh"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_11
msgid "Refrigerators"
msgstr "Tủ lạnh"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_comments_remarks_record
msgid "Remarks"
msgstr "Nhận xét"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1029_product_template
msgid "Samsung Front-Load Washer with Steam"
msgstr "Máy giặt cửa trước Samsung có chức năng giặt hơi nước"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1023_product_template
msgid "Samsung Side-by-Side Refrigerator"
msgstr "Tủ lạnh Samsung Side-by-Side"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Scan product <strong><font style=\"color: rgb(255, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""
"Quét sản phẩm <strong><font style=\"color: rgb(255, 0, 255);\">Điều hoà LG "
"Dual Inverter</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Scan the serial number"
msgstr "Quét số sê-ri"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select any of the payment methods and proceed to the payment."
msgstr "Chọn bất kỳ phương thức thanh toán nào và tiến hành thanh toán."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Select product <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""
"Chọn sản phẩm <strong><font style=\"color: rgb(156, 0, 255);\">Điều hoà LG "
"Dual Inverter</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select the customer"
msgstr "Chọn khách hàng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineer"
msgstr "Kỹ sư dịch vụ"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_service_engineeres
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineers"
msgstr "Kỹ sư dịch vụ"

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_11
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Services"
msgstr "Dịch vụ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit"
msgstr "Gửi"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit a Ticket"
msgstr "Gửi phiếu hỗ trợ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Submit the ticket."
msgstr "Gửi phiếu hỗ trợ."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Switch tasks to Gantt view"
msgstr "Chuyển nhiệm vụ sang chế độ xem Gantt"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1053_product_template
msgid "TV LED"
msgstr "TV LED"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_project_task_record
msgid "Task"
msgstr "Nhiệm vụ"

#. module: electronic_store
#: model:project.project,label_tasks:electronic_store.project_project_3
#: model:project.project,label_tasks:electronic_store.project_project_4
#: model:project.project,label_tasks:electronic_store.project_project_5
msgid "Tasks"
msgstr "Nhiệm vụ"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "The delivery order will be created for the LG Dual Inverter"
msgstr "Phiếu xuất kho sẽ được tạo cho LG Dual Inverter"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"The salesperson sets the \"expected delivery date\" based on customer "
"availability"
msgstr ""
"Chuyên viên sales đặt \"ngày giao hàng dự kiến\" dựa vào thời gian của khách"
" hàng"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Theia Hayward"
msgstr "Theia Hayward"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Then, the worker processes all delivery orders that are ready, using the "
"barcode interface. From delivery orders:"
msgstr ""
"Sau đó, nhân viên sẽ xử lý tất cả các phiếu xuất kho đã sẵn sàng bằng giao "
"diện mã vạch. Từ phiếu xuất kho:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"They generally require the functionalities of several apps to manage their "
"business workflow."
msgstr ""
"Họ thường cần đến các chức năng của một số ứng dụng để quản lý chu trình "
"kinh doanh của mình."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"This Business setup for Electronics store where they can sell their "
"electronics items like Home Appliances, Kitchen Appliances, etc..."
msgstr ""
"Thiết lập ngành kinh doanh này dành cho cửa hàng đồ điện tử, nơi người ta "
"thường bán các mặt hàng điện tử như thiết bị gia dụng, thiết bị nhà bếp,..."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Để thêm cột thứ tư, hãy giảm kích thước của ba cột này bằng cách sử dụng "
"biểu tượng bên phải của mỗi khối. Sau đó, sao chép một trong các cột để tạo "
"một cột mới dưới dạng bản sao."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "To speed up the creation of the quotation, use a quotation template."
msgstr "Để tạo báo giá nhanh hơn, hãy sử dụng mẫu báo giá."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1034_product_template
msgid "Toshiba EM131A5C-SS Microwave Oven"
msgstr "Lò vi sóng Toshiba EM131A5C-SS"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_type_of_installation
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Type of Installation"
msgstr "Loại hình lắp đặt"

#. module: electronic_store
#: model:base.automation,name:electronic_store.base_automation_1
msgid "Update warranty date on serial number"
msgstr "Cập nhật ngày bảo hành trên số sê-ri"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"Upgrade Your Gear: Black Friday's Tech Delights Await!. Bring home smart "
"products with exclusive Festive Offers"
msgstr ""
"Nâng cấp thiết bị của bạn: Đại tiệc công nghệ Black Friday đang chờ đón! "
"Mang về nhà những sản phẩm thông minh với Ưu đãi độc quyền mùa lễ hội"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use Ship Later option before the checkout for shipping later"
msgstr "Sử dụng tùy chọn Giao hàng sau trước khi thanh toán để giao hàng sau"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use the magnifier 🔎 icon to select tasks to assign"
msgstr "Sử dụng biểu tượng kính lúp 🔎 để chọn nhiệm vụ cần phân công"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Validate the Receipt"
msgstr "Xác thực phiếu nhập kho"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_field_to_set_warranty_month
msgid "Warranty (months)"
msgstr "Bảo hành (tháng)"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_date_helpdesk_ti_x_warranty_date
#: model:ir.model.fields,field_description:electronic_store.new_date_lot_serial_x_warranty_date
msgid "Warranty Date"
msgstr "Ngày bảo hành"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_12
msgid "Washing Machines"
msgstr "Máy giặt"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1050_product_template
#: model:project.project,name:electronic_store.project_project_5
msgid "Washing Machines Installation"
msgstr "Lắp đặt máy giặt"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "We are in good company."
msgstr "Đây là một đơn vị uy tín."

#. module: electronic_store
#: model_terms:web_tour.tour,rainbow_man_message:electronic_store.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Chào mừng bạn! Chúc bạn một chuyến khám phá bổ ích!"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"When selling electronics, most items are tracked with serial numbers. To "
"deliver the installation service, we use the project management app, with "
"one task per installation to do."
msgstr ""
"Khi bán đồ điện tử, hầu hết các mặt hàng đều được theo dõi bằng số sê-ri. Để"
" cung cấp dịch vụ lắp đặt, chúng tôi sử dụng ứng dụng quản lý dự án, với một"
" nhiệm vụ cho mỗi lần lắp đặt."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1024_product_template
msgid "Whirlpool Bottom-Freezer Refrigerator"
msgstr "Tủ lạnh ngăn đá dưới Whirlpool"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1030_product_template
msgid "Whirlpool High-Efficiency Top-Load Washer"
msgstr "Máy giặt cửa trên hiệu suất cao Whirlpool"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1041_product_template
msgid "Whynter ARC-14S Dual Hose Portable Air Conditioner"
msgstr "Điều hòa di động Whynter ARC-14S Dual Hose"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Wilson Holt"
msgstr "Wilson Holt"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Worksheet will help for the installation report"
msgstr "Bảng công tác sẽ giúp ích cho công tác báo cáo lắp đặt"

#. module: electronic_store
#: model:ir.actions.act_window,name:electronic_store.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Bảng công tác"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Trích dẫn nhận xét của khách hàng tại đây. Dùng trích dẫn là một cách tuyệt "
"vời để xây dựng niềm tin vào sản phẩm hoặc dịch vụ của bạn."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"payment term: <strong><font style=\"color: rgb(156, 0, 255);\">30 % Advance "
"Rest on Installment</font></strong>"
msgstr ""
"điều khoản thanh toán: <strong><font style=\"color: rgb(156, 0, 255);\">Trả "
"trước 30% còn lại trả góp</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">Air Conditioner "
"Installation</font></strong>"
msgstr ""
"sản phẩm: <strong><font style=\"color: rgb(156, 0, 255);\">Lắp đặt điều "
"hoà</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner </font></strong>"
msgstr ""
"sản phẩm: <strong><font style=\"color: rgb(156, 0, 255);\">Điều hoà LG Dual "
"Inverter</font></strong>"
