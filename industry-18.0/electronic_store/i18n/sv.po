# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* electronic_store
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Lasse L, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:34+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1048_product_template
msgid "1 Year Extended Warranty"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1049_product_template
msgid "3 Year Extended Warranty"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "73455 Twentynine Palms Highway,"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<font style=\"color: var(--color) !important;\">Assign serial numbers</font>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<font style=\"color: var(--color) !important;\">Go to the reception</font>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\" data-bs-original-title=\"\" aria-describedby=\"tooltip846286\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Nästa </span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Förgående</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_2816
msgid "<span class=\"o_footer_copyright_name me-2\">Copyright ©Electronics</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> - VD för "
"MyCompany</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> - VD för "
"MyCompany</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> - VD för "
"MyCompany</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Attachment</span>"
msgstr "<span class=\"s_website_form_label_content\">Bifogat fil</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Description</span>"
msgstr "<span class=\"s_website_form_label_content\">Beskrivning</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Telefon</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Product</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Serial Number</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<strong style=\"font-weight: 500;\">Flow 4: Field Service Installation - "
"AC</strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"<strong>\n"
"                                        <font class=\"text-o-color-4\">EXCLUSIVE FESTIVE OFFERS</font>\n"
"                                    </strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 1. Purchase </strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 2. Sale with serial number</strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 3. Delivery</strong>"
msgstr ""

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_10
msgid "About Us"
msgstr "Om oss"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "About us"
msgstr "Om oss"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Anpassa dessa tre kolumner så att de passar dina designbehov. För att "
"duplicera, ta bort eller flytta kolumner väljer du kolumnen och använder de "
"översta ikonerna för att utföra åtgärden."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
#: model_terms:ir.ui.view,arch_db:electronic_store.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Add details about your intervention..."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Add the products to the cart"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "After completion of the work, update the remarks"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1043_product_template
#: model:project.project,name:electronic_store.project_project_3
msgid "Air Conditioner Installation"
msgstr ""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_10
msgid "Air Conditioners"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1070_product_template
msgid "BOSS E111 Portable 125 Watt Hand Blender (Grey)"
msgstr ""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_15
msgid "Blender"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1031_product_template
msgid "Bosch Compact Washer"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1035_product_template
msgid "Breville Quick Touch Crisp Microwave"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Bruce Porter"
msgstr "Bruce Porter"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Business Flow"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "CA&amp; 92277"
msgstr ""

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_1
msgid "Cash"
msgstr "Kontanter"

#. module: electronic_store
#: model:account.journal,name:electronic_store.cash
msgid "Cash (Electronic Store)"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Check with My Tasks"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click on the first delivery order"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click the \"validate\" barcode"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Comments"
msgstr "Kommentarer"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Confirm the RFQ"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "Contact us"
msgstr "Kontakta oss"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"Contact us about anything related to our company or services.\n"
"                                            <br/>\n"
"                                            We'll do our best to get back to you as soon as possible."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create a quotation with:"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create the purchase order"
msgstr ""

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_2
msgid "Customer Account"
msgstr "Kundkonto"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customer can register their complaint for the existing purchased products "
"with serial numbers from the website:"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customers are either visiting the store to discover new products, either "
"exploring the webshop looking for great offers and discounts / cheapest "
"costs,."
msgstr ""

#. module: electronic_store
#: model:account.analytic.plan,name:electronic_store.account_analytic_plan_1
msgid "Default"
msgstr "Standard"

#. module: electronic_store
#: model:ir.model,name:electronic_store.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Ta bort bilden ovan eller ersätt den med en bild som illustrerar ditt "
"meddelande. Klicka på bilden för att ändra stil på de <em>rundade "
"hörnen</em>."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Document - Guidelines of Installation"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1040_product_template
msgid "Dyson Purifier Cool (White/Silver) - TP07"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "ELECTRONICS STORE"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Electronic Store"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Enter the product <font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner</font>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Every day, the logistic manager reserves products that should be delivered today (reservation is set to manual for this location, so that he has a control of what he wants to send today). If a delivery order should be delivered\n"
"            today but products are not available, he informs the customer using the chatter on the delivery order."
msgstr ""

#. module: electronic_store
#: model:ir.actions.server,name:electronic_store.ir_act_server_1
msgid "Execute Code"
msgstr "Utför kod"

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_2
msgid "Field Service"
msgstr "Fälttjänst"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Field service engineer checking daily activity with mobile app:"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Field service engineer will visit the client place and start working on "
"installation."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Fill the form:"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 1. Purchase"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 2. Sale with serial number"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 3. Delivery"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 4: Field Service Installation - AC"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 5: Website Complaint"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 6: POS Shopping"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"For a better living, Lifestyle is a very important aspect. Well, now you can"
" enjoy the best and healthy lifestyle with Home Appliances. This Festive, "
"celebrate the festive tradition with smart innovation. Enjoy exciting offers"
" on Home Entertainment and Home Appliances..<br/>"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1038_product_template
msgid "Frigidaire Portable Air Conditioner"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1025_product_template
msgid "Frigidaire Top-Freezer Refrigerator"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the POS Shop:"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the website, click on the menu Helpdesk"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1036_product_template
msgid "GE Profile Over-the-Range Microwave"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1047_product_template
msgid "Gift Card"
msgstr "Presentkort"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to mobile odoo app \"Air Conditioner Installation\""
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to project \"Air Conditioner Installation\""
msgstr ""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_9
msgid "Home Appliance"
msgstr ""

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_1
msgid "Home Appliances"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1039_product_template
msgid "Honeywell QuietSet Tower Fan"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"In the office, the service manager assigns tasks to installers. To do so"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_installation_date
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Installation Date"
msgstr ""

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_1
msgid "Internal"
msgstr "Intern"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "KB ELECTRONICS"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid ""
"KB Electronics is a family-owned home appliance store with a global "
"presence. Since 1978, KB Electronics is known for their knowledgeable staff "
"and large selection of home appliances. Throughout the years, they have "
"provided exceptional customer service both before and after the sale, as "
"well as offering professional custom installation services. We service what "
"we sell, and our knowledgeable and friendly staff can help you select home "
"appliances at any budget. We look forward to seeing you at KB Electronics!"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "KB Electronics<br/>"
msgstr ""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_13
msgid "Kitchen Appliance"
msgstr ""

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_2
msgid "Kitchen Appliances"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1026_product_template
msgid "KitchenAid Counter-Depth French Door Refrigerator"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1037_product_template
msgid "LG Dual Inverter Window Air Conditioner"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1022_product_template
msgid "LG French Door Refrigerator"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1033_product_template
msgid "LG NeoChef Countertop Microwave"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1028_product_template
msgid "LG Top-Load Washer with TurboWash"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Let's buy some items that are tracked by serial numbers."
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1027_product_template
msgid "Maytag Front-Load Washer"
msgstr ""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_16
msgid "Microwaves"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_makemodel
#: model_terms:ir.ui.view,arch_db:electronic_store.product_template_form_view
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Model Number"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Move the task \"<font style=\"color: rgb(156, 0, 255);\">In "
"Progress</font>\" and then \"<font style=\"color: rgb(156, 0, "
"255);\">Done</font>\" when it's finished and sign the service report."
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_name_record
msgid "Name"
msgstr "Namn"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Next"
msgstr "Nästa"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Odoo will generate 1 task for the Air Conditioner installation."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Once the order is confirmed:"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Our References"
msgstr "Våra referenser"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1032_product_template
msgid "Panasonic Genius Sensor Microwave Oven"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Plan to visit customer location"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Previous"
msgstr "Föregående"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Product wise guidelines and steps given for the Service Engineer"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Products"
msgstr "Produkter"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_products_id_worksheet_template
msgid "Products on Tasks"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Provide the serial number <font style=\"color: rgb(156, 0, "
"255);\">804KCPY43321</font>"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1042_product_template
#: model:project.project,name:electronic_store.project_project_4
msgid "Refrigerator Installation"
msgstr ""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_11
msgid "Refrigerators"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_comments_remarks_record
msgid "Remarks"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1029_product_template
msgid "Samsung Front-Load Washer with Steam"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1023_product_template
msgid "Samsung Side-by-Side Refrigerator"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Scan product <strong><font style=\"color: rgb(255, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Scan the serial number"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select any of the payment methods and proceed to the payment."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Select product <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select the customer"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineer"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_service_engineeres
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineers"
msgstr ""

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_11
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Services"
msgstr "Tjänster"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit"
msgstr "Skicka"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit a Ticket"
msgstr "Skicka in ett ärende"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Submit the ticket."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Switch tasks to Gantt view"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1053_product_template
msgid "TV LED"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_project_task_record
msgid "Task"
msgstr "Aktivitet"

#. module: electronic_store
#: model:project.project,label_tasks:electronic_store.project_project_3
#: model:project.project,label_tasks:electronic_store.project_project_4
#: model:project.project,label_tasks:electronic_store.project_project_5
msgid "Tasks"
msgstr "Uppgifter"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "The delivery order will be created for the LG Dual Inverter"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"The salesperson sets the \"expected delivery date\" based on customer "
"availability"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Theia Hayward"
msgstr "Theia Hayward"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Then, the worker processes all delivery orders that are ready, using the "
"barcode interface. From delivery orders:"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"They generally require the functionalities of several apps to manage their "
"business workflow."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"This Business setup for Electronics store where they can sell their "
"electronics items like Home Appliances, Kitchen Appliances, etc..."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"För att lägga till en fjärde kolumn minskar du storleken på dessa tre "
"kolumner med hjälp av den högra ikonen i varje block. Duplicera sedan en av "
"kolumnerna för att skapa en ny som en kopia."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "To speed up the creation of the quotation, use a quotation template."
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1034_product_template
msgid "Toshiba EM131A5C-SS Microwave Oven"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_type_of_installation
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Type of Installation"
msgstr ""

#. module: electronic_store
#: model:base.automation,name:electronic_store.base_automation_1
msgid "Update warranty date on serial number"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"Upgrade Your Gear: Black Friday's Tech Delights Await!. Bring home smart "
"products with exclusive Festive Offers"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use Ship Later option before the checkout for shipping later"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use the magnifier 🔎 icon to select tasks to assign"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Validate the Receipt"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_field_to_set_warranty_month
msgid "Warranty (months)"
msgstr ""

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_date_helpdesk_ti_x_warranty_date
#: model:ir.model.fields,field_description:electronic_store.new_date_lot_serial_x_warranty_date
msgid "Warranty Date"
msgstr ""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_12
msgid "Washing Machines"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1050_product_template
#: model:project.project,name:electronic_store.project_project_5
msgid "Washing Machines Installation"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "We are in good company."
msgstr "Vi är i gott sällskap."

#. module: electronic_store
#: model_terms:web_tour.tour,rainbow_man_message:electronic_store.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"When selling electronics, most items are tracked with serial numbers. To "
"deliver the installation service, we use the project management app, with "
"one task per installation to do."
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1024_product_template
msgid "Whirlpool Bottom-Freezer Refrigerator"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1030_product_template
msgid "Whirlpool High-Efficiency Top-Load Washer"
msgstr ""

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1041_product_template
msgid "Whynter ARC-14S Dual Hose Portable Air Conditioner"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Wilson Holt"
msgstr "Wilson Holt"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Worksheet will help for the installation report"
msgstr ""

#. module: electronic_store
#: model:ir.actions.act_window,name:electronic_store.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Arbetsblad"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Här kan du skriva ett citat från en av dina kunder. Citat är ett bra sätt "
"att skapa förtroende för dina produkter eller tjänster."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"payment term: <strong><font style=\"color: rgb(156, 0, 255);\">30 % Advance "
"Rest on Installment</font></strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">Air Conditioner "
"Installation</font></strong>"
msgstr ""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner </font></strong>"
msgstr ""
