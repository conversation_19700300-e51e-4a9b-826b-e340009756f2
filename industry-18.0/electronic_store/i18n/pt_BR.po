# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* electronic_store
# 
# Translators:
# a75f12d3d37ea5bf159c4b3e85eb30e7_0fa6927, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:34+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1048_product_template
msgid "1 Year Extended Warranty"
msgstr "1 ano de garantia estendida"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1049_product_template
msgid "3 Year Extended Warranty"
msgstr "3 anos de garantia estendida"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "73455 Twentynine Palms Highway,"
msgstr "73455 Twentynine Palms Highway,"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<font style=\"color: var(--color) !important;\">Assign serial numbers</font>"
msgstr ""
"<font style=\"color: var(--color) !important;\">Atribuir números de "
"série</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<font style=\"color: var(--color) !important;\">Go to the reception</font>"
msgstr ""
"<font style=\"color: var(--color) !important;\">Ir para o recebimento</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\" data-bs-original-title=\"\" aria-describedby=\"tooltip846286\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\" data-bs-original-title=\"\" aria-describedby=\"tooltip846286\"/>\n"
"                                                <span><EMAIL></span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Próximo</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Anterior</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_2816
msgid "<span class=\"o_footer_copyright_name me-2\">Copyright ©Electronics</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©Electronics</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Cláudia OLIVEIRA</b> • CEO da "
"empresa</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>Jane DOE</b> • CEO da Empresa</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author\"><b>John DOE</b> • CEO da Empresa</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Attachment</span>"
msgstr "<span class=\"s_website_form_label_content\">Anexo</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Description</span>"
msgstr "<span class=\"s_website_form_label_content\">Descrição</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de telefone</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Product</span>"
msgstr "<span class=\"s_website_form_label_content\">Produto</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "<span class=\"s_website_form_label_content\">Serial Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de série</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Assunto\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Assunto</span>\n"
"                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Sua empresa\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Seu e-mail\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Seu E-mail</span>"
"                                                    <span "
"class=\"s_website_form_mark\"> *</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Seu nome\n"
"                                                                    </span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Seu nome</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Sua pergunta</span>\n"
"                                                                    <span class=\"s_website_form_mark\">*</span>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"<strong style=\"font-weight: 500;\">Flow 4: Field Service Installation - "
"AC</strong>"
msgstr ""
"<strong style=\"font-weight: 500;\">Fluxo 4: Instalação - Serviço de Campo -"
" AC</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"<strong>\n"
"                                        <font class=\"text-o-color-4\">EXCLUSIVE FESTIVE OFFERS</font>\n"
"                                    </strong>"
msgstr ""
"<strong>\n"
"                                        <font class=\"text-o-color-4\">OFERTAS FESTIVAS EXCLUSIVAS</font>\n"
"                                    </strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 1. Purchase </strong>"
msgstr "<strong>Fluxo 1. Compras </strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 2. Sale with serial number</strong>"
msgstr "<strong>Fluxo 2: Venda com número de série</strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "<strong>Flow 3. Delivery</strong>"
msgstr "<strong>Fluxo 3. Entrega</strong>"

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_10
msgid "About Us"
msgstr "Sobre nós"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "About us"
msgstr "Sobre nós"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adapte essas três colunas para atender às suas necessidades de projeto. Para"
" duplicar, excluir ou mover colunas, selecione a coluna e use os ícones "
"superiores para executar sua ação."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
#: model_terms:ir.ui.view,arch_db:electronic_store.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Add details about your intervention..."
msgstr "Adicionar detalhes sobre a intervenção…"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Add the products to the cart"
msgstr "Adicione os produtos ao carrinho"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "After completion of the work, update the remarks"
msgstr "Após a conclusão do trabalho, atualize as observações"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1043_product_template
#: model:project.project,name:electronic_store.project_project_3
msgid "Air Conditioner Installation"
msgstr "Instalação de ar-condicionado"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_10
msgid "Air Conditioners"
msgstr "Ar-condicionado"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1070_product_template
msgid "BOSS E111 Portable 125 Watt Hand Blender (Grey)"
msgstr " Liquidificador manual portátil BOSS E111 de 125 watts (cinza)"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_15
msgid "Blender"
msgstr "Liquidificador"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1031_product_template
msgid "Bosch Compact Washer"
msgstr "Lavadora compacta Bosch"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1035_product_template
msgid "Breville Quick Touch Crisp Microwave"
msgstr "Micro-ondas Breville Quick Touch Crisp"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Bruce Porter"
msgstr "Bruce Porter"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Business Flow"
msgstr "Fluxo de negócios"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "CA&amp; 92277"
msgstr "CA&amp; 92277"

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_1
msgid "Cash"
msgstr "Dinheiro"

#. module: electronic_store
#: model:account.journal,name:electronic_store.cash
msgid "Cash (Electronic Store)"
msgstr "Dinheiro (loja de eletrônicos)"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Check with My Tasks"
msgstr "Verificar com Minhas Tarefas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click on the first delivery order"
msgstr "Clique no primeiro pedido de entrega"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Click the \"validate\" barcode"
msgstr "Clique em \"validar\" código de barras"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Comments"
msgstr "Comentários"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Confirm the RFQ"
msgstr "Confirmar a SDC"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "Contact us"
msgstr "Entre em contato"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid ""
"Contact us about anything related to our company or services.\n"
"                                            <br/>\n"
"                                            We'll do our best to get back to you as soon as possible."
msgstr ""
"Entre em contato conosco sobre qualquer assunto relacionado à nossa empresa ou aos nossos serviços.\n"
"<br/>\n"
"Faremos o possível para entrar em contato com você o mais rápido possível."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create a quotation with:"
msgstr "Crie uma cotação com:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Create the purchase order"
msgstr "Crie o pedido de compra"

#. module: electronic_store
#: model:pos.payment.method,name:electronic_store.pos_payment_method_2
msgid "Customer Account"
msgstr "Conta do cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customer can register their complaint for the existing purchased products "
"with serial numbers from the website:"
msgstr ""
"O cliente pode registrar reclamações sobre produtos com números de série "
"comprados no site:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Customers are either visiting the store to discover new products, either "
"exploring the webshop looking for great offers and discounts / cheapest "
"costs,."
msgstr ""
"Os clientes ou visitam a loja para conhecer novos produtos, ou explorar a "
"loja on-line em busca de ótimas ofertas e descontos/custos mais baratos."

#. module: electronic_store
#: model:account.analytic.plan,name:electronic_store.account_analytic_plan_1
msgid "Default"
msgstr "Padrão"

#. module: electronic_store
#: model:ir.model,name:electronic_store.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "Planilha de trabalho padrão"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr ""
"Delete a imagem acima ou substitua-a com uma  imagem que ilustra sua "
"mensagem. Clique na imagem para alterar seus estilo de <em>canto "
"arredondado</em> ."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Document - Guidelines of Installation"
msgstr "Documento - Diretrizes de instalação"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1040_product_template
msgid "Dyson Purifier Cool (White/Silver) - TP07"
msgstr "Dyson Purifier Cool (Branco/Prata) - TP07"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "ELECTRONICS STORE"
msgstr "LOJA DE ELETRÔNICOS"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Electronic Store"
msgstr "Loja de eletrônicos"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Enter the product <font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner</font>"
msgstr ""
"Digite o produto <font style=\"color: rgb(156, 0, 255);\">Ar-condicionado de"
" janela LG Dual Inverter</font>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Every day, the logistic manager reserves products that should be delivered today (reservation is set to manual for this location, so that he has a control of what he wants to send today). If a delivery order should be delivered\n"
"            today but products are not available, he informs the customer using the chatter on the delivery order."
msgstr ""
"Todos os dias, o gerente de logística reserva os produtos que devem ser entregues nauqele dia (a reserva é definida como manual nesse local, para que ele tenha controle do que quer enviar). Se um pedido deve ser entregue hoje\n"
" mas os produtos não estão disponíveis, ele informa o cliente usando o chatter do peiddo de entrega."

#. module: electronic_store
#: model:ir.actions.server,name:electronic_store.ir_act_server_1
msgid "Execute Code"
msgstr "Executar código"

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_2
msgid "Field Service"
msgstr "Serviço de campo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Field service engineer checking daily activity with mobile app:"
msgstr ""
"Engenheiro de serviço de campo verificando a atividade diária com o "
"aplicativo móvel:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Field service engineer will visit the client place and start working on "
"installation."
msgstr ""
"O engenheiro de serviço de campo visitará o local do cliente e começará a "
"trabalhar na instalação."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Fill the form:"
msgstr "Preencha o formulário:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 1. Purchase"
msgstr "Fluxo 1. Compras"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 2. Sale with serial number"
msgstr "Fluxo 2: Venda com número de série"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 3. Delivery"
msgstr "Fluxo 3. Entrega"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 4: Field Service Installation - AC"
msgstr "Fluxo 4: Instalação - Serviço de Campo - AC"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 5: Website Complaint"
msgstr "Fluxo 5: Reclamação no site"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Flow 6: POS Shopping"
msgstr "Fluxo 6: Compras no PDV"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"For a better living, Lifestyle is a very important aspect. Well, now you can"
" enjoy the best and healthy lifestyle with Home Appliances. This Festive, "
"celebrate the festive tradition with smart innovation. Enjoy exciting offers"
" on Home Entertainment and Home Appliances..<br/>"
msgstr ""
"Para uma vida melhor, o estilo de vida é um aspecto muito importante. Bem, "
"agora você pode desfrutar do melhor e mais saudável estilo de vida com seus "
"eletrodomésticos. Nestas festividades, comemore a tradição com inovações "
"inteligentes. Aproveite as ofertas incríveis de entretenimento doméstico e "
"eletrodomésticos.<br/>"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1038_product_template
msgid "Frigidaire Portable Air Conditioner"
msgstr "Ar-condicionado portátil Frigidaire"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1025_product_template
msgid "Frigidaire Top-Freezer Refrigerator"
msgstr "Refrigerador Top-Freezer Frigidaire"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the POS Shop:"
msgstr "Da loja do PDV:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "From the website, click on the menu Helpdesk"
msgstr "No site, clique no menu Central de Ajuda"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1036_product_template
msgid "GE Profile Over-the-Range Microwave"
msgstr "Micro-ondas GE Profile Over-the-Range"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1047_product_template
msgid "Gift Card"
msgstr "Cartão de presente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to mobile odoo app \"Air Conditioner Installation\""
msgstr "Acesse o aplicativo móvel do Odoo \"Instalação do ar-condicionado\""

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Go to project \"Air Conditioner Installation\""
msgstr "Vá para o projeto \"Instalação de ar-condicionado\""

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_9
msgid "Home Appliance"
msgstr "Eletrodoméstico"

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_1
msgid "Home Appliances"
msgstr "Eletrodomésticos"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1039_product_template
msgid "Honeywell QuietSet Tower Fan"
msgstr "Ventilador vertical QuietSet Honeywell"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"In the office, the service manager assigns tasks to installers. To do so"
msgstr ""
"No escritório, o gerente de serviços atribui tarefas aos instaladores. Para "
"isso"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_installation_date
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Installation Date"
msgstr "Data de instalação"

#. module: electronic_store
#: model:account.analytic.account,name:electronic_store.account_analytic_account_1
msgid "Internal"
msgstr "Interno"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
msgid "KB ELECTRONICS"
msgstr "KB ELECTRONICS"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid ""
"KB Electronics is a family-owned home appliance store with a global "
"presence. Since 1978, KB Electronics is known for their knowledgeable staff "
"and large selection of home appliances. Throughout the years, they have "
"provided exceptional customer service both before and after the sale, as "
"well as offering professional custom installation services. We service what "
"we sell, and our knowledgeable and friendly staff can help you select home "
"appliances at any budget. We look forward to seeing you at KB Electronics!"
msgstr ""
"A KB Electronics é uma loja familiar de eletrodomésticos com presença "
"global. Desde 1978, a KB Electronics é conhecida por sua equipe experiente e"
" pela grande variedade de eletrodomésticos. Ao longo dos anos, a empresa tem"
" prestado um atendimento excepcional ao cliente antes e depois da venda, "
"além de oferecer serviços profissionais de instalação personalizada. "
"Prestamos assistência técnica ao que vendemos, e nossa equipe experiente e "
"simpática pode ajudá-lo a selecionar eletrodomésticos para qualquer "
"orçamento. Estamos ansiosos para vê-lo na KB Electronics!"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.website_page_about_us_view
msgid "KB Electronics<br/>"
msgstr "KB Electronics<br/>"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_13
msgid "Kitchen Appliance"
msgstr "Eletrodoméstico de cozinha"

#. module: electronic_store
#: model:pos.category,name:electronic_store.pos_category_2
msgid "Kitchen Appliances"
msgstr "Eletrodomésticos de cozinha"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1026_product_template
msgid "KitchenAid Counter-Depth French Door Refrigerator"
msgstr "Refrigerador porta-dupla com profundidade de balcão KitchenAid"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1037_product_template
msgid "LG Dual Inverter Window Air Conditioner"
msgstr "Ar-condicionado de janela LG Dual Inverter"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1022_product_template
msgid "LG French Door Refrigerator"
msgstr "Refrigerador LG de porta-dupla"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1033_product_template
msgid "LG NeoChef Countertop Microwave"
msgstr "Micro-ondas de bancada LG NeoChef"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1028_product_template
msgid "LG Top-Load Washer with TurboWash"
msgstr "Lavadora LG Top-Load com TurboWash"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Let's buy some items that are tracked by serial numbers."
msgstr "Vamos comprar alguns itens que são rastreados por números de série."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1027_product_template
msgid "Maytag Front-Load Washer"
msgstr "Lavadora de roupas frontal Maytag"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_16
msgid "Microwaves"
msgstr "Micro-ondas"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_makemodel
#: model_terms:ir.ui.view,arch_db:electronic_store.product_template_form_view
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Model Number"
msgstr "Número do modelo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Move the task \"<font style=\"color: rgb(156, 0, 255);\">In "
"Progress</font>\" and then \"<font style=\"color: rgb(156, 0, "
"255);\">Done</font>\" when it's finished and sign the service report."
msgstr ""
"Mova a tarefa para \"<font style=\"color: rgb(156, 0, 255);\">Em "
"andamento</font>\" e, quando estiver pronta, mova para \"<font "
"style=\"color: rgb(156, 0, 255);\">Concluído</font>\" e assine o relatório "
"de serviço."

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_name_record
msgid "Name"
msgstr "Nome"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Next"
msgstr "Próximo"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Odoo will generate 1 task for the Air Conditioner installation."
msgstr "O Odoo gerará uma tarefa para a instalação do ar-condicionado."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Once the order is confirmed:"
msgstr "Depois que o pedido for confirmado:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Our References"
msgstr "Nossas referências"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1032_product_template
msgid "Panasonic Genius Sensor Microwave Oven"
msgstr "Forno de micro-ondas Panasonic Genius Sensor"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Plan to visit customer location"
msgstr "Planeje visitar o local do cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Previous"
msgstr "Anterior"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Product wise guidelines and steps given for the Service Engineer"
msgstr ""
"Diretrizes e etapas relacionadas ao produto fornecidas ao engenheiro de "
"serviço"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Products"
msgstr "Produtos"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_products_id_worksheet_template
msgid "Products on Tasks"
msgstr "Produtos nas tarefas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Provide the serial number <font style=\"color: rgb(156, 0, "
"255);\">804KCPY43321</font>"
msgstr ""
"Forneça o número de série <font style=\"color: rgb(156, 0, "
"255);\">804KCPY43321</font>"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1042_product_template
#: model:project.project,name:electronic_store.project_project_4
msgid "Refrigerator Installation"
msgstr "Instalação do refrigerador"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_11
msgid "Refrigerators"
msgstr "Refrigeradores"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_comments_remarks_record
msgid "Remarks"
msgstr "Observações"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1029_product_template
msgid "Samsung Front-Load Washer with Steam"
msgstr "Lavadora de roupas frontal Samsung com vapor"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1023_product_template
msgid "Samsung Side-by-Side Refrigerator"
msgstr "Refrigerador Side-by-Side Samsung"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Scan product <strong><font style=\"color: rgb(255, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""
"Digitalize produto <strong><font style=\"color: rgb(255, 0, 255);\">Ar-"
"condicionado de janela LG Dual Inverter</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Scan the serial number"
msgstr "Digitalize o número de série"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select any of the payment methods and proceed to the payment."
msgstr ""
"Selecione qualquer um dos métodos de pagamento e prossiga com o pagamento."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Select product <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual "
"Inverter Window Air Conditioner</font></strong>"
msgstr ""
"Selecione o produto <strong><font style=\"color: rgb(156, 0, 255);\">Ar-"
"condicionado de janela LG Dual Inverter</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Select the customer"
msgstr "Selecione o cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineer"
msgstr "Engenheiro de serviços"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_service_engineeres
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Service Engineers"
msgstr "Engenheiros de serviço"

#. module: electronic_store
#: model:website.menu,name:electronic_store.website_menu_11
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Services"
msgstr "Serviços"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.contactus
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit"
msgstr "Enviar"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.ir_ui_view_3302
msgid "Submit a Ticket"
msgstr "Enviar um chamado"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Submit the ticket."
msgstr "Envie o chamado."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Switch tasks to Gantt view"
msgstr "Alternar tarefas para a visualização de Gantt"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1053_product_template
msgid "TV LED"
msgstr "TV LED"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.x_project_task_record
msgid "Task"
msgstr "Tarefa"

#. module: electronic_store
#: model:project.project,label_tasks:electronic_store.project_project_3
#: model:project.project,label_tasks:electronic_store.project_project_4
#: model:project.project,label_tasks:electronic_store.project_project_5
msgid "Tasks"
msgstr "Tarefas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "The delivery order will be created for the LG Dual Inverter"
msgstr "O pedido de entrega do LG Dual Inverter será criado"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"The salesperson sets the \"expected delivery date\" based on customer "
"availability"
msgstr ""
"O vendedor define a \"data de entrega prevista\" com base na disponibilidade"
" do cliente"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Theia Hayward"
msgstr "Theia Hayward"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"Then, the worker processes all delivery orders that are ready, using the "
"barcode interface. From delivery orders:"
msgstr ""
"Em seguida, o funcionário processa todos os pedidos de entrega que estão "
"prontos, usando a interface de código de barras. A partir dos pedidos de "
"entrega:"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"They generally require the functionalities of several apps to manage their "
"business workflow."
msgstr ""
"Em geral, são necessárias as funcionalidades de vários aplicativos para "
"gerenciar o fluxo de trabalho de seus negócios."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"This Business setup for Electronics store where they can sell their "
"electronics items like Home Appliances, Kitchen Appliances, etc..."
msgstr ""
"Esta configuração serve para a loja de eletrônicos, que vendem itens "
"eletrônicos como eletrodomésticos, aparelhos de cozinha, etc..."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Para adicionar uma quarta coluna, reduza o tamanho dessas três colunas "
"usando o ícone à direita de cada bloco. Em seguida, duplique uma das colunas"
" para criar uma nova como cópia."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "To speed up the creation of the quotation, use a quotation template."
msgstr "Para agilizar a criação da cotação, use um modelo de cotação."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1034_product_template
msgid "Toshiba EM131A5C-SS Microwave Oven"
msgstr "Forno micro-ondas Toshiba EM131A5C-SS"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.field_type_of_installation
#: model_terms:ir.ui.view,arch_db:electronic_store.report_custom_x_project_task_worksheet_template_1
msgid "Type of Installation"
msgstr "Tipo de instalação"

#. module: electronic_store
#: model:base.automation,name:electronic_store.base_automation_1
msgid "Update warranty date on serial number"
msgstr "Atualização data de garantia no número de série"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.homepage
msgid ""
"Upgrade Your Gear: Black Friday's Tech Delights Await!. Bring home smart "
"products with exclusive Festive Offers"
msgstr ""
"Atualize seus equipamentos: As delícias tecnológicas da Black Friday estão "
"lhe esperando! Leve para casa produtos inteligentes com ofertas "
"comemorativas exclusivas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use Ship Later option before the checkout for shipping later"
msgstr ""
"Use a opção Enviar mais tarde antes do checkout para postergar o envio"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Use the magnifier 🔎 icon to select tasks to assign"
msgstr "Use o ícone da lupa 🔎 para selecionar as tarefas a serem atribuídas"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Validate the Receipt"
msgstr "Validar o recebimento"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_field_to_set_warranty_month
msgid "Warranty (months)"
msgstr "Garantia (meses)"

#. module: electronic_store
#: model:ir.model.fields,field_description:electronic_store.new_date_helpdesk_ti_x_warranty_date
#: model:ir.model.fields,field_description:electronic_store.new_date_lot_serial_x_warranty_date
msgid "Warranty Date"
msgstr "Data de garantia"

#. module: electronic_store
#: model:product.public.category,name:electronic_store.product_public_category_12
msgid "Washing Machines"
msgstr "Máquinas de lavar roupa"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1050_product_template
#: model:project.project,name:electronic_store.project_project_5
msgid "Washing Machines Installation"
msgstr "Instalação de máquinas de lavar"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "We are in good company."
msgstr "Estamos em boa companhia."

#. module: electronic_store
#: model_terms:web_tour.tour,rainbow_man_message:electronic_store.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Welcome! Happy exploring."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"When selling electronics, most items are tracked with serial numbers. To "
"deliver the installation service, we use the project management app, with "
"one task per installation to do."
msgstr ""
"Ao vender eletrônicos, a maioria dos itens é rastreada por números de série."
" Para prestar o serviço de instalação, usamos o aplicativo de gerenciamento "
"de projetos, com uma tarefa por instalação a ser realizada."

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1024_product_template
msgid "Whirlpool Bottom-Freezer Refrigerator"
msgstr "Refrigerador com freezer inferior Whirlpool"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1030_product_template
msgid "Whirlpool High-Efficiency Top-Load Washer"
msgstr "Lavadora de alta eficiência Whirlpool Top-Load"

#. module: electronic_store
#: model:product.template,name:electronic_store.product_product_1041_product_template
msgid "Whynter ARC-14S Dual Hose Portable Air Conditioner"
msgstr "Ar-condicionado portátil de mangueira dupla Whynter ARC-14S"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid "Wilson Holt"
msgstr "Wilson Holt"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid "Worksheet will help for the installation report"
msgstr "A planilha ajudará no relatório de instalação"

#. module: electronic_store
#: model:ir.actions.act_window,name:electronic_store.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Planilhas de trabalho"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.services
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr ""
"Escreva aqui uma referência de um de seus clientes. As referências são uma "
"ótima maneira de criar confiança em seus produtos ou serviços."

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"payment term: <strong><font style=\"color: rgb(156, 0, 255);\">30 % Advance "
"Rest on Installment</font></strong>"
msgstr ""
"condição de pagamento: <strong><font style=\"color: rgb(156, 0, 255);\">30% "
"adiantado e restante em prestações</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">Air Conditioner "
"Installation</font></strong>"
msgstr ""
"produto: <strong><font style=\"color: rgb(156, 0, 255);\">Instalação de ar-"
"condicionado</font></strong>"

#. module: electronic_store
#: model_terms:ir.ui.view,arch_db:electronic_store.welcome_article_body
msgid ""
"product: <strong><font style=\"color: rgb(156, 0, 255);\">LG Dual Inverter "
"Window Air Conditioner </font></strong>"
msgstr ""
"produto: <strong><font style=\"color: rgb(156, 0, 255);\">Ar-condicionado de"
" janela LG Dual Inverter</font></strong>"
