<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <data noupdate="1">
        <record id="hr_leave_1" model="hr.leave">
            <field name="request_date_from" eval="DateTime.today() + relativedelta(days=10)"/>
            <field name="request_date_to" eval="DateTime.today() + relativedelta(days=14)"/>
            <field name="first_approver_id" ref="hr.employee_admin"/>
            <field name="holiday_status_id" ref="hr_holidays.holiday_status_sl"/>
            <field name="department_id" ref="hr.dep_administration"/>
            <field name="employee_id" ref="hr.employee_admin"/>
        </record>
    </data>

    <function model="hr.leave" name="action_validate">
        <value eval="[ref('hr_leave_1')]"/>
    </function>
</odoo>
