<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="crm_lead_1" model="crm.lead">
        <field name="name">Certification Inquiry for Residential Charging Station</field>
        <field name="partner_id" ref="res_partner_8" />
        <field name="team_id" ref="sales_team.team_sales_department" />
        <field name="street">77 Santa Barbara Rd</field>
        <field name="city">Pleasant Hill</field>
        <field name="stage_id" ref="crm.stage_lead4" />
        <field name="zip">94523</field>
        <field name="phone">(*************</field>
        <field name="contact_name"><PERSON></field>
        <field name="probability">100.0</field>
        <field name="expected_revenue">600.0</field>
        <field name="automated_probability">100.0</field>
        <field name="date_closed" eval="datetime.now()" />
        <field name="won_status">won</field>
        <field name="medium_id" ref="utm.utm_medium_website" />
        <field name="iap_enrich_done" eval="True" />
        <field name="tag_ids" eval="[ref('crm_tag_2')]" />
        <field name="description">&lt;p&gt;I've recently had an EcoCharge EC100 charging station installed in my garage. I'm interested in getting the charging station certified to ensure its safety and compliance with standards.&lt;/p&gt;</field>
    </record>
    <record id="crm_lead_2" model="crm.lead">
        <field name="name">Gas Installation Certification</field>
        <field name="partner_id" ref="res_partner_13" />
        <field name="partner_name">Sarah Thompson</field>
        <field name="team_id" ref="sales_team.team_sales_department" />
        <field name="stage_id" ref="crm.stage_lead1" />
        <field name="probability">91.67</field>
        <field name="expected_revenue">500.0</field>
        <field name="automated_probability">91.67</field>
        <field name="won_status">pending</field>
        <field name="iap_enrich_done" eval="True" />
        <field name="tag_ids" eval="[ref('crm_tag_3'),ref('crm_tag_4')]" />
        <field name="description">&lt;p &gt;&lt;span style="color: rgb(209, 213, 219);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(68, 70, 84)"&gt; &lt;/span&gt;&lt;span style="color: inherit; font-style: normal; font-weight: 400; background-color: inherit;"&gt;I recently had a new gas installation in my kitchen and would like to have it certified for safety and compliance. Please provide details about the certification process and associated fees.&lt;/span&gt;&lt;br&gt;&lt;/p&gt;</field>
    </record>
    <record id="crm_lead_3" model="crm.lead">
        <field name="name">Maria Rodriguez's opportunity</field>
        <field name="partner_id" ref="res_partner_14" />
        <field name="partner_name">Maria Rodriguez</field>
        <field name="priority">1</field>
        <field name="team_id" ref="sales_team.team_sales_department" />
        <field name="stage_id" ref="crm.stage_lead3" />
        <field name="probability">50.0</field>
        <field name="expected_revenue">1000.0</field>
        <field name="automated_probability">50.0</field>
        <field name="won_status">pending</field>
        <field name="iap_enrich_done" eval="True" />
        <field name="tag_ids" eval="[ref('crm_tag_3'), ref('crm_tag_4'), ref('crm_tag_5')]" />
        <field name="description">&lt;p &gt;&lt;span style="color: inherit; font-style: normal; font-weight: 400; background-color: inherit;"&gt;I'm planning to renovate my home, which includes upgrading electrical wiring and installing new gas connections. I'd like to ensure all installations are certified to meet safety standards.&lt;/span&gt;&lt;br&gt;&lt;/p&gt;</field>
    </record>
    <record id="crm_lead_4" model="crm.lead">
        <field name="name">Innovate Great Solutions's opportunity</field>
        <field name="partner_id" ref="res_partner_15" />
        <field name="partner_name">Innovate Great Solutions</field>
        <field name="team_id" ref="sales_team.team_sales_department" />
        <field name="stage_id" ref="crm.stage_lead4" />
        <field name="phone">(*************</field>
        <field name="probability">100.0</field>
        <field name="expected_revenue">800.0</field>
        <field name="automated_probability">100.0</field>
        <field name="won_status">won</field>
        <field name="tag_ids" eval="[ref('crm_tag_2'),ref('crm_tag_4')]" />
        <field name="description">&lt;p &gt;&lt;span style="color: inherit; font-style: normal; font-weight: 400; background-color: inherit;"&gt;Our office has recently installed the ChargeTech CT300 charging station for our employees and visitors. We're interested in certifying its safety and compliance with standards.&lt;/span&gt;&lt;br&gt;&lt;/p&gt;</field>
    </record>
    <record id="crm_lead_5" model="crm.lead">
        <field name="name">Charging Station Certification Inquiry for My Home</field>
        <field name="partner_id" ref="res_partner_18" />
        <field name="team_id" ref="sales_team.team_sales_department" />
        <field name="street">789 Oak Lane</field>
        <field name="city">Suburbia</field>
        <field name="stage_id" ref="crm.stage_lead4" />
        <field name="zip">12345</field>
        <field name="phone">(*************</field>
        <field name="contact_name">Emily Davis</field>
        <field name="probability">100.0</field>
        <field name="automated_probability">100.0</field>
        <field name="date_closed" eval="datetime.now()" />
        <field name="won_status">won</field>
        <field name="medium_id" ref="utm.utm_medium_website" />
        <field name="description">&lt;p&gt;I recently installed the EcoCharge EC100 charging station in my garage, and I'm interested in getting it certified for safety and compliance. It's my first time owning an electric vehicle, and I want to ensure everything is safe and up to standard.&lt;/p&gt;</field>
    </record>
</odoo>
