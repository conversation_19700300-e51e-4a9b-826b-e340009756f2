<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="x_control_charging_station_2" model="x_control_charging_station">
        <field name="x_comments">&lt;p&gt;The electrical installation complies with the requirements of the law concerning low voltage and very low voltage electrical installations. The next inspection visit must be carried out no later than within 25 years.&lt;/p&gt;&lt;p&gt;If applicable, appropriate measures have been taken by YourCompany to seal the input terminals of the residual current device located at the origin of the electrical installation and the single-line diagrams and position plans have been reviewed again by YourCompany.&lt;br&gt;&lt;/p&gt;</field>
        <field name="x_installation_description">&lt;p&gt;&lt;span class="oe-tabs" style="width: 40px;"&gt;	&lt;/span&gt;​&lt;span class="oe-tabs" style="width: 40px;"&gt;	&lt;/span&gt;​&lt;br&gt;&lt;/p&gt;</field>
        <field name="x_grounded_socket">Loop</field>
        <field name="x_control_location" ref="res_partner_8"/>
        <field name="x_control_type">Commissioning</field>
        <field name="x_nameplate_capacity_kw">30mA</field>
        <field name="x_owner_address" ref="res_partner_8"/>
        <field name="x_meter_number">12345678</field>
        <field name="x_control_date" eval="DateTime.today()" />
        <field name="x_power_supply">Distribution network operator</field>
        <field name="x_operating_voltage">3X400 + N</field>
        <field name="x_circuit_breaker_bandwidth_test" eval="True"/>
        <field name="x_serial_number">123456</field>
        <field name="x_protection_against_indirect_contacts" eval="True"/>
        <field name="x_protection_against_overintensity" eval="True"/>
        <field name="x_ground_resistance_">5</field>
        <field name="x_next_control_before" eval="datetime.now()" />
        <field name="x_ean">123456789123456789</field>
        <field name="x_visiting_agent_signature" type="base64" file="certification_organism/static/src/binary/x_control_charging_station/2-x_visiting_agent_signature"/>
        <field name="x_protection_a">40</field>
        <field name="x_distribution_network_provider">EnerGrid Networks</field>
        <field name="x_email_address"><EMAIL></field>
        <field name="x_fixed_material" eval="True"/>
        <field name="x_state_control" eval="True"/>
        <field name="x_differential_protection">Type A</field>
        <field name="x_protection_against_direct_contacts" eval="True"/>
        <field name="x_project_task_id" model="project.task" eval="obj().env.ref('certification_organism.sale_order_1').tasks_ids.id" />
        <field name="x_isolation">12</field>
        <field name="x_max_protection">63</field>
        <field name="x_installation_date" eval="DateTime.today()" />
        <field name="x_contractor" ref="res_partner_10"/>
        <field name="x_model">VoltCharge Box</field>
        <field name="x_diagrams" eval="True"/>
        <field name="x_in" eval="True"/>
        <field name="x_grounding_diagram">TT</field>
        <field name="x_pictures" type="base64" file="certification_organism/static/src/binary/x_control_charging_station/2-x_pictures_1"/>
        <field name="x_manufacturer" ref="res_partner_11"/>
        <field name="x_conclusion">&lt;p&gt;&lt;font style="background-color: rgb(214, 239, 214);"&gt;&lt;span style="font-size: 24px;"&gt;Certified&lt;/span&gt;&lt;/font&gt;&lt;/p&gt;</field>
    </record>
    <record id="x_control_charging_station_3" model="x_control_charging_station">
        <field name="x_comments">&lt;p&gt;The electrical installation complies with the requirements of the law concerning low voltage and very low voltage electrical installations. The next inspection visit must be carried out no later than within 25 years.&lt;/p&gt;&lt;p&gt;If applicable, appropriate measures have been taken by YourCompany to seal the input terminals of the residual current device located at the origin of the electrical installation and the single-line diagrams and position plans have been reviewed again by YourCompany.&lt;br&gt;&lt;/p&gt;</field>
        <field name="x_grounded_socket">Loop</field>
        <field name="x_control_location" ref="res_partner_18"/>
        <field name="x_control_type">Commissioning</field>
        <field name="x_nameplate_capacity_kw">30mA</field>
        <field name="x_owner_address" ref="res_partner_18"/>
        <field name="x_meter_number">87456321</field>
        <field name="x_control_date" eval="DateTime.today()" />
        <field name="x_visiting_agent" eval="[(6, 0, [ref('base.user_admin')])]"/>
        <field name="x_power_supply">Distribution network operator</field>
        <field name="x_operating_voltage">3X400 + N</field>
        <field name="x_circuit_breaker_bandwidth_test" eval="True"/>
        <field name="x_serial_number">123456</field>
        <field name="x_protection_against_indirect_contacts" eval="True"/>
        <field name="x_protection_against_overintensity" eval="True"/>
        <field name="x_ground_resistance_">5</field>
        <field name="x_next_control_before" eval="datetime.now()" />
        <field name="x_ean">123456789123456789</field>
        <field name="x_visiting_agent_signature" type="base64" file="certification_organism/static/src/binary/x_control_charging_station/3-x_visiting_agent_signature"/>
        <field name="x_protection_a">40</field>
        <field name="x_distribution_network_provider">EnerGrid Networks</field>
        <field name="x_email_address"><EMAIL></field>
        <field name="x_fixed_material" eval="True"/>
        <field name="x_state_control" eval="True"/>
        <field name="x_differential_protection">Type A</field>
        <field name="x_protection_against_direct_contacts" eval="True"/>
        <field name="x_project_task_id" model="project.task" eval="obj().env.ref('certification_organism.sale_order_4').tasks_ids.id" />
        <field name="x_isolation">12</field>
        <field name="x_max_protection">63</field>
        <field name="x_installation_date" eval="DateTime.today()" />
        <field name="x_contractor" ref="res_partner_10"/>
        <field name="x_model">VoltCharge Box</field>
        <field name="x_diagrams" eval="True"/>
        <field name="x_in" eval="True"/>
        <field name="x_grounding_diagram">TT</field>
        <field name="x_pictures" type="base64" file="certification_organism/static/src/binary/x_control_charging_station/3-x_pictures_1"/>
        <field name="x_manufacturer" ref="res_partner_11"/>
        <field name="x_conclusion">&lt;p&gt;&lt;font style="background-color: rgb(214, 239, 214);"&gt;&lt;span style="font-size: 24px;"&gt;Certified&lt;/span&gt;&lt;/font&gt;&lt;br&gt;&lt;/p&gt;</field>
    </record>
</odoo>
