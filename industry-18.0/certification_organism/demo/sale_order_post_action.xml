<?xml version='1.0' encoding='UTF-8'?>
<odoo>
	<!-- Confirm action -->
    <function model="sale.order" name="action_confirm">
        <value eval="[ref('sale_order_1'), ref('sale_order_2'), ref('sale_order_3'), ref('sale_order_4')]"/>
    </function>

    <!-- Create Invoice -->
    <record id="sale_advance_payment_inv_1" model="sale.advance.payment.inv">
        <field name="advance_payment_method" >delivered</field>
        <field name="sale_order_ids" eval="[Command.set([ref('sale_order_1')])]"/>
    </record>

    <function model="sale.advance.payment.inv" name="create_invoices"
            eval="[ref('sale_advance_payment_inv_1')]"/>

    <record id="sale_advance_payment_inv_2" model="sale.advance.payment.inv">
        <field name="advance_payment_method" >delivered</field>
        <field name="sale_order_ids" eval="[Command.set([ref('sale_order_4')])]"/>
    </record>

    <function model="sale.advance.payment.inv" name="create_invoices"
            eval="[ref('sale_advance_payment_inv_2')]"/>

    <!-- Update Task -->
    <!-- sale order sale_order_1 -->
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_1').tasks_ids.ids"/>
        <value eval="{'user_ids': [ref('base.user_admin')], 'planned_date_begin': datetime.now() - relativedelta(days=1), 'date_deadline': datetime.now(), 'stage_id': ref('planning_project_stage_1'), 'tag_ids': [ref('project_tags_1')]}"/>
    </function>
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_1').tasks_ids.ids"/>
        <value eval="{'stage_id': ref('planning_project_stage_2')}"/>
    </function>
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_1').tasks_ids.ids"/>
        <value eval="{'stage_id': ref('planning_project_stage_3'), 'state': '1_done'}"/>
    </function>

    <!-- sale order sale_order_2 -->
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_2').tasks_ids.ids"/>
        <value eval="{'user_ids': [ref('base.user_admin')], 'planned_date_begin': datetime.now() - relativedelta(days=1), 'date_deadline': datetime.now(), 'stage_id': ref('planning_project_stage_1'), 'tag_ids': [ref('project_tags_1')]}"/>
    </function>

    <!-- sale order sale_order_3 -->
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_3').tasks_ids.ids"/>
        <value eval="{'user_ids': [ref('base.user_admin')], 'planned_date_begin': datetime.now() - relativedelta(days=1), 'date_deadline': datetime.now(), 'stage_id': ref('planning_project_stage_1'), 'tag_ids': [ref('project_tags_1'), ref('project_tags_2')]}"/>
    </function>

    <!-- sale order sale_order_4 -->
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_4').tasks_ids.ids"/>
        <value eval="{'user_ids': [ref('base.user_admin')], 'planned_date_begin': datetime.now() - relativedelta(days=1), 'date_deadline': datetime.now(), 'stage_id': ref('planning_project_stage_1')}"/>
    </function>
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_4').tasks_ids.ids"/>
        <value eval="{'stage_id': ref('planning_project_stage_2')}"/>
    </function>
    <function model="project.task" name="write">
        <value model="sale.order" eval="obj().env.ref('certification_organism.sale_order_4').tasks_ids.ids"/>
        <value eval="{'stage_id': ref('planning_project_stage_3'), 'state': '1_done'}"/>
    </function>

    <!-- Rating of project task -->

    <record id="rating_task_1" model="rating.rating">
        <field name="res_model_id" ref="project.model_project_task"/>
        <field name="res_id" model="sale.order.line" eval="obj().env.ref('certification_organism.sale_order_line_1').task_id.id"/>
        <field name="partner_id" ref="res_partner_8"/>
        <field name="rated_partner_id" ref="base.partner_admin"/>
    </record>
    <function model="project.task" name="rating_apply"
            eval="([obj().env.ref('certification_organism.sale_order_line_1').task_id.id], 5, None, obj().env.ref('certification_organism.rating_task_1'), 'Highly impressed with VoltCharge Innovations service! Emily and the team were both skilled and personable. The inspection was thorough, and I now have complete peace of mind about my charging stations safety. Kudos for a fantastic job!')"/>

</odoo>
