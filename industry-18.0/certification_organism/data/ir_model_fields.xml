<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="filename_for_x_studi_e4a6f7a0-7955-48a2-ab1c-a295727bd488" model="ir.model.fields">
        <field name="name">x_annexes_filename</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Filename for x_binary_field_465_1h8jpmjfh</field>
    </record>
    <record id="new_checkbox_install_07b49038-6246-41fa-a8ec-119909ba7be3" model="ir.model.fields">
        <field name="name">x_protection_against_indirect_contacts</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">Protection Against Indirect Contacts</field>
    </record>
    <record id="new_checkbox_install_1a7f02cf-8f6a-4d98-9d7b-b81d70f5b91d" model="ir.model.fields">
        <field name="name">x_state_control</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">State Control</field>
    </record>
    <record id="new_checkbox_install_1da09a59-1725-4ec7-a6a9-3fd88f1c69a0" model="ir.model.fields">
        <field name="name">x_protection_against_direct_contacts</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">Protection Against Direct Contacts</field>
    </record>
    <record id="new_checkbox_install_38c7f1b2-1c72-4cad-a3bd-0089bf65f4e1" model="ir.model.fields">
        <field name="name">x_circuit_breaker_bandwidth_test</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">Circuit Breaker Bandwidth Test</field>
    </record>
    <record id="new_checkbox_install_756f305b-2979-4d4d-baf3-a1076dfb5a68" model="ir.model.fields">
        <field name="name">x_in</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">&#916;In</field>
    </record>
    <record id="new_checkbox_install_8ffbf2f2-43b1-4eb7-93dd-a8ddff7b1ec4" model="ir.model.fields">
        <field name="name">x_protection_against_overintensity</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">Protection Against Overintensity</field>
    </record>
    <record id="new_checkbox_install_9210cacd-48ce-4e3e-b4e7-efc01c3fc7bd" model="ir.model.fields">
        <field name="name">x_fixed_material</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">Fixed Material</field>
    </record>
    <record id="new_checkbox_install_a0b739f2-4cb1-4502-8b37-8897a8aeffad" model="ir.model.fields">
        <field name="name">x_diagrams</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">boolean</field>
        <field name="field_description">Diagrams</field>
    </record>
    <record id="new_date_installatio_879fcac2-2f44-4d3f-b789-b1f61e97edef" model="ir.model.fields">
        <field name="name">x_next_control_before</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">date</field>
        <field name="field_description">Next Control Before</field>
    </record>
    <record id="new_date_installatio_bea4dd93-6b43-412f-8b05-a0364fa90293" model="ir.model.fields">
        <field name="name">x_control_date</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">date</field>
        <field name="field_description">Control Date</field>
    </record>
    <record id="new_date_installatio_efbdb6ce-50de-48e4-8992-1f5e72e65380" model="ir.model.fields">
        <field name="name">x_installation_date</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">date</field>
        <field name="field_description">Installation Date</field>
    </record>
    <record id="new_datetime_install_2bbd9d49-f78f-4318-bf29-2f0ff5f8ddb1" model="ir.model.fields">
        <field name="name">x_report_date</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">datetime</field>
        <field name="field_description">Report Date</field>
    </record>
    <record id="new_file_installatio_2cc1cd0a-1f0c-473e-93ff-cfbf112ce880" model="ir.model.fields">
        <field name="name">x_annexes</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">binary</field>
        <field name="field_description">Annexes</field>
    </record>
    <record id="new_html_installatio_59802a97-4667-42d4-8658-f04a466fb512" model="ir.model.fields">
        <field name="name">x_comments</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">html</field>
        <field name="field_description">Comments</field>
    </record>
    <record id="new_html_installatio_b8a358e7-77a4-4e10-8e7c-91584f9401a0" model="ir.model.fields">
        <field name="name">x_installation_description</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">html</field>
        <field name="field_description">Installation Description</field>
    </record>
    <record id="new_html_installatio_bce7d605-17da-4b46-9be2-f5d0c8e10bfd" model="ir.model.fields">
        <field name="name">x_conclusion</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">html</field>
        <field name="field_description">Conclusion</field>
    </record>
    <record id="new_image_installati_7bb7ef76-751f-4749-8832-1da0ff83bec0" model="ir.model.fields">
        <field name="name">x_pictures</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">binary</field>
        <field name="field_description">Pictures</field>
    </record>
    <record id="new_integer_installa_38923213-00f8-4249-9e45-a08efb7f9976" model="ir.model.fields">
        <field name="name">x_serial_number</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">integer</field>
        <field name="field_description">Serial Number</field>
    </record>
    <record id="new_integer_installa_5f0f247a-a0c8-4fcc-b2e5-5a60d2904967" model="ir.model.fields">
        <field name="name">x_meter_number</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">integer</field>
        <field name="field_description">Meter Number</field>
    </record>
    <record id="new_integer_installa_6185d132-f0ca-45ce-811e-7d9f7e816414" model="ir.model.fields">
        <field name="name">x_protection_a</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">integer</field>
        <field name="field_description">Protection (A)</field>
    </record>
    <record id="new_integer_installa_c533a573-29b4-4d52-9721-11aa04014346" model="ir.model.fields">
        <field name="name">x_isolation</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">integer</field>
        <field name="field_description">Isolation</field>
    </record>
    <record id="new_integer_installa_e3180eac-14c7-407d-8954-87d9f5bce7c6" model="ir.model.fields">
        <field name="name">x_ground_resistance_</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">integer</field>
        <field name="field_description">Ground Resistance (&#937;)</field>
    </record>
    <record id="new_integer_installa_e8935d3c-93ab-4b0c-bd54-6ffe77c00882" model="ir.model.fields">
        <field name="name">x_max_protection</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">integer</field>
        <field name="field_description">Max. Protection</field>
    </record>
    <record id="new_many2one_install_414eae17-7a9f-4a2f-858c-36218ca2a64c" model="ir.model.fields">
        <field name="name">x_manufacturer</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">many2one</field>
        <field name="relation">res.partner</field>
        <field name="field_description">Manufacturer</field>
    </record>
    <record id="new_many2one_install_63f73c9f-d07b-45e8-bb81-0078b7fb97be" model="ir.model.fields">
        <field name="name">x_contractor</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">many2one</field>
        <field name="relation">res.partner</field>
        <field name="field_description">Contractor</field>
    </record>
    <record id="new_many2one_install_a963bdf3-1118-458a-b056-85422bafe0ec" model="ir.model.fields">
        <field name="name">x_installer</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">many2one</field>
        <field name="relation">res.partner</field>
        <field name="field_description">Installer</field>
    </record>
    <record id="ir_model_fields_10245_2c1e3810" model="ir.model.fields">
        <field name="name">x_project_task_id</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">many2one</field>
        <field name="relation">project.task</field>
        <field name="required" eval="True" />
        <field name="on_delete">restrict</field>
        <field name="field_description">Task</field>
    </record>
    <record id="new_related_field_in_5a396d3d-7953-4770-a4b7-a69459e388f1" model="ir.model.fields">
        <field name="name">x_control_location</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">many2one</field>
        <field name="relation">res.partner</field>
        <field name="readonly" eval="True" />
        <field name="related">x_project_task_id.sale_order_id.partner_shipping_id</field>
        <field name="field_description">Control Location</field>
    </record>
    <record id="new_related_field_in_7ac1d42b-d321-4e8e-9aa8-f6d19f01a153" model="ir.model.fields">
        <field name="name">x_email_address</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="readonly" eval="True" />
        <field name="related">x_contractor.email</field>
        <field name="field_description">Email Address</field>
    </record>

    <record id="new_related_field_in_d424d6c6-250b-4e2b-b02c-909d624d9e36" model="ir.model.fields">
        <field name="name">x_owner_address</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">many2one</field>
        <field name="relation">res.partner</field>
        <field name="readonly" eval="True" />
        <field name="related">x_project_task_id.sale_order_id.partner_invoice_id</field>
        <field name="field_description">Owner Address</field>
    </record>
    <record id="new_related_field_in_eb7af591-0632-4fc9-8555-3e0bda3b9939" model="ir.model.fields">
        <field name="name">x_visiting_agent</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">many2many</field>
        <field name="relation">res.users</field>
        <field name="readonly" eval="True" />
        <field name="related">x_project_task_id.user_ids</field>
        <field name="store" eval="False" />
        <field name="field_description">Visiting Agent</field>
    </record>
    <record id="new_selection_instal_4d4b88fd-9b8d-43c4-ac2b-baf736781d26" model="ir.model.fields">
        <field name="name">x_control_type</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">selection</field>
        <field name="selection">[('Commissioning', 'Commissioning'), ('Maintenance', 'Maintenance')]</field>
        <field name="field_description">Control Type</field>
    </record>
    <record id="new_signature_instal_d7e23d6e-e60a-4561-9db6-d85fef213749" model="ir.model.fields">
        <field name="name">x_visiting_agent_signature</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">binary</field>
        <field name="field_description">Visiting Agent Signature</field>
    </record>
    <record id="new_text_installatio_0cd78554-7c1d-46a5-be12-2b31668b7957" model="ir.model.fields">
        <field name="name">x_premises_type</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Premises Type</field>
    </record>
    <record id="new_text_installatio_1d268a1f-8476-496a-84b0-42153d64d396" model="ir.model.fields">
        <field name="name">x_distribution_network_provider</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Distribution Network Provider</field>
    </record>
    <record id="new_text_installatio_4fcfeac5-d596-4291-a9b9-ad0e519d1671" model="ir.model.fields">
        <field name="name">x_grounded_socket</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Grounded Socket</field>
    </record>
    <record id="new_text_installatio_7be3783d-b74a-4eda-8671-130e1c312bbf" model="ir.model.fields">
        <field name="name">x_nameplate_capacity_kw</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Nameplate Capacity (kW)</field>
    </record>
    <record id="new_text_installatio_7c3fe422-e483-4f9c-b9b8-ca9daca8ae17" model="ir.model.fields">
        <field name="name">x_operating_voltage</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Operating Voltage</field>
    </record>
    <record id="new_text_installatio_8a2bcf26-8bef-4a2f-9062-b8b95417a3fe" model="ir.model.fields">
        <field name="name">x_ean</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">EAN</field>
    </record>
    <record id="new_text_installatio_909d67ad-c9bf-4dbe-a309-25678cac2316" model="ir.model.fields">
        <field name="name">x_grounding_diagram</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Grounding Diagram</field>
    </record>
    <record id="new_text_installatio_adb62158-d7bf-403e-b7bf-76fe3faa4354" model="ir.model.fields">
        <field name="name">x_model</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Model</field>
    </record>
    <record id="new_text_installatio_b4dca44c-3589-48b4-8b44-63468cedcc86" model="ir.model.fields">
        <field name="name">x_protection_6ma_dc</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Protection 6mA DC</field>
    </record>
    <record id="new_text_installatio_ead54bd7-5601-45b5-9360-47cd843cc177" model="ir.model.fields">
        <field name="name">x_power_supply</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Power Supply</field>
    </record>
    <record id="new_text_installatio_fa307386-1f6a-4b86-bd5b-94d072589284" model="ir.model.fields">
        <field name="name">x_differential_protection</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="field_description">Differential Protection</field>
    </record>
    <record id="ir_model_fields_10247_6da24e2f" model="ir.model.fields">
        <field name="name">x_name</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1" />
        <field name="ttype">char</field>
        <field name="related">x_project_task_id.name</field>
        <field name="field_description">Name</field>
    </record>
</odoo>
