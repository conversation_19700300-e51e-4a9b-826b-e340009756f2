<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record model="res.config.settings" id="res_config_account_setting">
        <field name="group_analytic_accounting" eval="1"/>
        <field name="group_project_rating" eval="1"/>
        <field name="group_project_stages" eval="True"/>
    </record>

    <function model="res.config.settings" name="execute">
        <value eval="[ref('res_config_account_setting')]"/>
    </function>

</odoo>
