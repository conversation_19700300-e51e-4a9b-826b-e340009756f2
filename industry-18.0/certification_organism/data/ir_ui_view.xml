<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="x_control_charging_station_ir_ui_view_1" model="ir.ui.view">
        <field name="name">template_view_Installation_Control</field>
        <field name="type">form</field>
        <field name="model">x_control_charging_station</field>
        <field name="active" eval="True"/>
        <field name="arch" type="xml">
            <form create="false" duplicate="false">
                <sheet>
                    <h1 invisible="context.get('default_x_project_task_id')">
                        <field name="x_project_task_id"/>
                    </h1>
                    <group>
                        <field name="id" string="Report Number"/>
                        <field name="x_control_date" string="Control Date" required="1"/>
                        <field name="create_date" string="Report Date"/>
                        <field name="x_next_control_before" string="Next Control Before" required="1"/>
                        <field name="x_visiting_agent" widget="many2many_tags" string="Visiting Agent"/>
                        <field name="x_control_type" string="Control Type" required="1"/>
                        <field name="x_conclusion" string="Conclusion"/>
                        <field name="x_owner_address" widget="res_partner_many2one" context="{&quot;show_address&quot;: 1}"/>
                        <field name="x_installer" string="Installer"/>
                        <field name="x_contractor" string="Contractor"/>
                        <field name="x_email_address" string="Email Address"/>
                        <field name="x_power_supply" string="Power Supply"/>
                        <field name="x_distribution_network_provider" string="Distribution Network Provider"/>
                        <field name="x_meter_number" string="Meter Number" options="{&quot;enable_formatting&quot;:false}"/>
                        <field name="x_grounding_diagram" string="Grounding Diagram"/>
                        <field name="x_grounded_socket" string="Owner Address"/>
                        <field name="x_operating_voltage" string="Operating Voltage"/>
                        <field name="x_max_protection" string="Max. Protection"/>
                        <field name="x_ean"/>
                        <field name="x_installation_description" string="Installation Description"/>
                        <field name="x_installation_date" string="Installation Date"/>
                        <field name="x_manufacturer" string="Manufacturer"/>
                        <field name="x_model" string="EAN"/>
                        <field name="x_nameplate_capacity_kw" string="Nameplate Capacity (kW)"/>
                        <field name="x_differential_protection" string="Differential Protection"/>
                        <field name="x_protection_a" string="Protection (A)"/>
                        <field name="x_protection_6ma_dc" string="Protection 6mA DC"/>
                        <field name="x_serial_number" string="Serial Number"/>
                        <field name="x_ground_resistance_" string="Ground Resistance (Ω)"/>
                        <field name="x_isolation"/>
                        <field name="x_circuit_breaker_bandwidth_test" string="Circuit Breaker Bandwidth Test"/>
                        <field name="x_in" string="ΔIn"/>
                        <field name="x_state_control" string="State Control" options="{&quot;enable_formatting&quot;:false}"/>
                        <field name="x_fixed_material" string="Fixed Material"/>
                        <field name="x_protection_against_direct_contacts" string="Protection Against Direct Contacts"/>
                        <field name="x_protection_against_indirect_contacts" string="Protection Against Indirect Contacts"/>
                        <field name="x_protection_against_overintensity" string="Protection Against Overintensity"/>
                        <field name="x_diagrams" string="Diagrams" options="{&quot;enable_formatting&quot;:false}"/>
                        <field name="x_comments" string="Comments"/>
                        <field widget="image" name="x_pictures"/>
                        <field widget="file" filename="x_annexes_filename" name="x_annexes" string="Annexes"/>
                        <field widget="signature" name="x_visiting_agent_signature" string="Visiting Agent Signature"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="x_control_charging_station_ir_ui_view_2" model="ir.ui.view">
        <field name="name">tree_view_Installation_Control</field>
        <field name="type">list</field>
        <field name="model">x_control_charging_station</field>
        <field name="active" eval="True"/>
        <field name="arch" type="xml">
            <list>
                <field name="create_date"/>
                <field name="x_name"/>
            </list>
        </field>
    </record>
    <record id="x_control_charging_station_ir_ui_view_3" model="ir.ui.view">
        <field name="name">search_view_Installation_Control</field>
        <field name="type">search</field>
        <field name="model">x_control_charging_station</field>
        <field name="active" eval="True"/>
        <field name="arch" type="xml">
            <search>
                <field name="x_name"/>
                <filter string="Created on" date="create_date" name="create_date"/>
                <filter name="group_by_month" string="Created on" context="{'group_by': 'create_date:month'}"/>
            </search>
        </field>
    </record>
</odoo>
