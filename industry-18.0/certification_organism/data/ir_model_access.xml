<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="ir_model_access_914" model="ir.model.access">
        <field name="name">x_control_charging_station_manager_access</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1"/>
        <field name="group_id" ref="project.group_project_manager"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
    <record id="ir_model_access_915" model="ir.model.access">
        <field name="name">x_control_charging_station_user_access</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1"/>
        <field name="group_id" ref="project.group_project_user"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>
</odoo>
