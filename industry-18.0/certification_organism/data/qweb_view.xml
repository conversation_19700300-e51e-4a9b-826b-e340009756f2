<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="report_custom_x_control_charging_station" model="ir.ui.view">
        <field name="name">x_control_charging_station</field>
        <field name="type">qweb</field>
        <field name="active" eval="True"/>
        <field name="arch" type="xml">
            <t t-name="x_control_charging_station">
                <div>
                    <div>
                        <div>
                            <div class="row mb-3" style="page-break-inside: avoid">
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Report Number</div>
                                    <div string="Report Number" modifiers="{&quot;readonly&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.id"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Control
                            Date</div>
                                    <div string="Control Date" modifiers="{&quot;required&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_control_date"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Created on
                        </div>
                                    <div string="Report Date" modifiers="{&quot;readonly&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.create_date"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Next
                            Control Before</div>
                                    <div string="Next Control Before" modifiers="{&quot;required&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_next_control_before"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Control
                            Location</div>
                                    <div string="Control Location" modifiers="{&quot;readonly&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_control_location"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Visiting
                            Agent</div>
                                    <div widget="many2many_tags" string="Visiting Agent" modifiers="{&quot;readonly&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_visiting_agent"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Control
                            Type</div>
                                    <div string="Control Type" modifiers="{&quot;required&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_control_type"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Conclusion
                        </div>
                                    <div string="Conclusion" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_conclusion"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Owner
                            Address</div>
                                    <div context="{'show_address': 1}" widget="res_partner_many2one" modifiers="{&quot;readonly&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_owner_address"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Installer
                        </div>
                                    <div string="Installer" can_create="true" can_write="true" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_installer"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Contractor
                        </div>
                                    <div string="Contractor" on_change="1" can_create="true" can_write="true" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_contractor"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Email
                            Address</div>
                                    <div string="Email Address" modifiers="{&quot;readonly&quot;: true}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_email_address"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Power
                            Supply</div>
                                    <div string="Power Supply" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_power_supply"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">
                            Distribution Network Provider</div>
                                    <div string="Distribution Network Provider" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_distribution_network_provider"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Meter
                            Number</div>
                                    <div string="Meter Number" options="{&quot;enable_formatting&quot;:false}" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_meter_number"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Grounding
                            Diagram</div>
                                    <div string="Grounding Diagram" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_grounding_diagram"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Grounded
                            Socket</div>
                                    <div string="Owner Address" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_grounded_socket"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Operating
                            Voltage</div>
                                    <div string="Operating Voltage" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_operating_voltage"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Max.
                            Protection</div>
                                    <div string="Max. Protection" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_max_protection"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">EAN</div>
                                    <div t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_ean"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">
                            Installation Description</div>
                                    <div string="Installation Description" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_installation_description"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">
                            Installation Date</div>
                                    <div string="Installation Date" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_installation_date"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">
                            Manufacturer</div>
                                    <div string="Manufacturer" can_create="true" can_write="true" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_manufacturer"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Model
                        </div>
                                    <div string="EAN" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_model"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Nameplate
                            Capacity (kW)</div>
                                    <div string="Nameplate Capacity (kW)" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_nameplate_capacity_kw"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">
                            Differential Protection</div>
                                    <div string="Differential Protection" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_differential_protection"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Protection
                            (A)</div>
                                    <div string="Protection (A)" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_protection_a"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Protection
                            6mA DC</div>
                                    <div string="Protection 6mA DC" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_protection_6ma_dc"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Serial
                            Number</div>
                                    <div string="Serial Number" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_serial_number"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Ground
                            Resistance (Ω)</div>
                                    <div string="Ground Resistance (Ω)" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_ground_resistance_"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Isolation
                        </div>
                                    <div t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_isolation"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Circuit
                            Breaker Bandwidth Test</div>
                                    <i string="Circuit Breaker Bandwidth Test" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_circuit_breaker_bandwidth_test else 'fa-square-o')"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">ΔIn</div>
                                    <i string="ΔIn" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_in else 'fa-square-o')"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">State
                            Control</div>
                                    <i string="State Control" options="{&quot;enable_formatting&quot;:false}" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_state_control else 'fa-square-o')"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Fixed
                            Material</div>
                                    <i string="Fixed Material" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_fixed_material else 'fa-square-o')"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Protection
                            Against Direct Contacts</div>
                                    <i string="Protection Against Direct Contacts" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_protection_against_direct_contacts else 'fa-square-o')"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Protection
                            Against Indirect Contacts</div>
                                    <i string="Protection Against Indirect Contacts" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_protection_against_indirect_contacts else 'fa-square-o')"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Protection
                            Against Overintensity</div>
                                    <i string="Protection Against Overintensity" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_protection_against_overintensity else 'fa-square-o')"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Diagrams
                        </div>
                                    <i string="Diagrams" options="{&quot;enable_formatting&quot;:false}" t-att-class="'col-lg-7 col-12 fa ' + ('fa-check-square' if worksheet.x_diagrams else 'fa-square-o')"/>
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Comments
                        </div>
                                    <div string="Comments" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_comments"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Pictures
                        </div>
                                    <div widget="image" t-options-widget="'image'" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_pictures"/>
                                </div>
                                <div class="row mb-3" style="page-break-inside: avoid">
                                    <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Visiting
                            Agent Signature</div>
                                    <img string="Visiting Agent Signature" style="width: 250px;" t-att-src="image_data_uri(worksheet.x_visiting_agent_signature)" t-if="worksheet.x_visiting_agent_signature"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </field>
    </record>
</odoo>
