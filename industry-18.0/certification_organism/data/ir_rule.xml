<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="ir_rule_297" model="ir.rule">
        <field name="name">x_control_charging_station_own</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1"/>
        <field name="groups" eval="[(6, 0, [ref('project.group_project_user')])]"/>
        <field name="domain_force"><![CDATA[[('create_uid', '=', user.id)]]]></field>
    </record>
    <record id="ir_rule_298" model="ir.rule">
        <field name="name">x_control_charging_station_all</field>
        <field name="model_id" ref="x_control_charging_station_ir_model_1"/>
        <field name="groups" eval="[(6, 0, [ref('project.group_project_manager'), ref('industry_fsm.group_fsm_user', raise_if_not_found=False)])]"/>
        <field name="domain_force"><![CDATA[[(1, '=', 1)]]]></field>
    </record>
</odoo>
