<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="planning_project_stage_0" model="project.task.type">
        <field name="name">New</field>
        <field name="sequence">1</field>
    </record>

    <record id="planning_project_stage_1" model="project.task.type">
        <field name="name">Planned</field>
        <field name="sequence">5</field>
    </record>

    <record id="planning_project_stage_2" model="project.task.type">
        <field name="name">In Progress</field>
        <field name="sequence">10</field>
    </record>

    <record id="planning_project_stage_3" model="project.task.type">
        <field name="name">Done</field>
        <field name="sequence">20</field>
        <field name="fold" eval="True"/>
    </record>

    <record id="planning_project_stage_4" model="project.task.type">
        <field name="name">Cancelled</field>
        <field name="sequence">25</field>
        <field name="fold" eval="True"/>
    </record>
    <record id="certification_project" model="project.project">
        <field name="name">Certifications</field>
        <field name="is_fsm" eval="True"/>
        <field name="stage_id" ref="project.project_project_stage_0"/>
        <field name="favorite_user_ids" eval="[(6, 0, [])]"/>
        <field name="company_id" ref="base.main_company"/>
        <field name="allow_billable" eval="True"/>
        <field name="allow_timesheets" eval="False"/>
        <field name="account_id" ref="account_analytic_account_2"/>
        <field name="allow_worksheets" eval="True"/>
        <field name="worksheet_template_id" ref="worksheet_template_2"/>
        <field name="type_ids" eval="[(6, 0, [ref('planning_project_stage_0'), ref('planning_project_stage_1'), ref('planning_project_stage_2'), ref('planning_project_stage_3'), ref('planning_project_stage_4')])]"/>
    </record>
</odoo>
