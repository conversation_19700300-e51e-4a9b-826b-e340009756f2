# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* certification_organism
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:25+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "1) Get opportunities from your website"
msgstr "1) Obtenga oportunidades desde su sitio web"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "2) Transform your opportunities into sales deals 💪"
msgstr "2) Transforme oportunidades en ventas 💪"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "3) Confirm your Sales Order"
msgstr "3) Confirme su orden de venta"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "4) Time to plan your Field Service task"
msgstr "4) Planee su tarea de servicio externo"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "5) Time to get this certification done 🚀"
msgstr "5) Haga esta certificación 🚀"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "6) Send the certification to the customer ✉️"
msgstr "6) Envíe esta certificación al cliente ✉️"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "7) Time to get your money 💰"
msgstr "7) Reciba su dinero 💰"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "8) Analyze your performance &amp;amp; track your profitability"
msgstr "8) Analice su rendimiento y tenga en cuenta su rentabilidad"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "9) Ready to go one step further?"
msgstr "9) ¿Está listo para conocer más?"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">City</span>"
msgstr "<span class=\"s_website_form_label_content\">Ciudad</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de teléfono</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Street</span>"
msgstr "<span class=\"s_website_form_label_content\">Calle</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Asunto</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Su empresa</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su correo electrónico</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su nombre</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su pregunta</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Zip</span>"
msgstr "<span class=\"s_website_form_label_content\">Código postal</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "<strong>Expand your business</strong> with the following apps:"
msgstr "<strong>Expanda su negocio</strong> con las siguientes aplicaciones:"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Accounting"
msgstr "Contabilidad"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"After seeing how wonderful your website is, potential leads can contact you "
"through a <strong>website form</strong> for more information about your "
"services."
msgstr ""
"Después de ver lo increíble que es su sitio web, es probable que leads "
"potenciales se quieran poner en contacto con usted por medio de un "
"<strong>formulario de sitio web</strong> para obtener más información sobre "
"sus servicios."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Alright, let's fill in this <strong>worksheet</strong>!"
msgstr "¡Llenemos esta <strong>hoja de trabajo</strong>!"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Analyze the <strong>profitability</strong> of your services with "
"<strong>project updates</strong>. (Install the 'Accounting' app and enable "
"the 'Analytic Accounting' feature.)"
msgstr ""
"Analice la <strong>rentabilidad</strong> de sus servicios con "
"<strong>actualizaciones del proyecto</strong>. Instale la aplicación "
"Contabilidad y active la función \"Contabilidad analítica\"."

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_file_installatio_2cc1cd0a-1f0c-473e-93ff-cfbf112ce880
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Annexes"
msgstr "Anexos"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"At last, <strong>send a message</strong> to your customer informing them of "
"the scheduled date of their intervention from the chatter of your task."
msgstr ""
"Al menos <strong>envíe un mensaje</strong> a su cliente en el que le informe"
" la fecha programada para la intervención desde el chatter de su tarea."

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_6
msgid "B2B"
msgstr "B2B"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_4
#: model:project.tags,name:certification_organism.project_tags_2
msgid "B2C"
msgstr "B2C"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_20
msgid "Canceled"
msgstr "Cancelada"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_4
msgid "Cancelled"
msgstr "Cancelado"

#. module: certification_organism
#: model:account.analytic.account,name:certification_organism.account_analytic_account_2
#: model:project.project,name:certification_organism.certification_project
msgid "Certifications"
msgstr "Certificaciones"

#. module: certification_organism
#: model:product.template,name:certification_organism.product_product_3_product_template
msgid "Charging Station Certification"
msgstr "Certificación de estación de carga"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Circuit\n"
"                            Breaker Bandwidth Test"
msgstr ""
"Prueba\n"
"                            del ancho de banda del disyuntor"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_38c7f1b2-1c72-4cad-a3bd-0089bf65f4e1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Circuit Breaker Bandwidth Test"
msgstr "Prueba del ancho de banda del disyuntor"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Click on the ➕ icon to create new tasks, or the 🔍 icon to <strong>schedule "
"existing tasks</strong>."
msgstr ""
"Haga clic en el icono ➕ para crear tareas nuevas, o en el icono 🔍 para "
"<strong>programar tareas existentes</strong>."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Click on the<strong> 'New Quotation' </strong>button from your CRM "
"opportunity. <strong>Send it to your customer by email</strong> once it's "
"ready."
msgstr ""
"Haga clic en el botón <strong>\"Nueva cotización\"</strong> desde su "
"oportunidad CRM. <strong>Envíela a su cliente por correo</strong> una vez "
"que esté listo."

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_59802a97-4667-42d4-8658-f04a466fb512
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Comments"
msgstr "Comentarios"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_bce7d605-17da-4b46-9be2-f5d0c8e10bfd
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Conclusion"
msgstr "Conclusión"

#. module: certification_organism
#: model:website.menu,name:certification_organism.website_menu_6
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "Contact us"
msgstr "Contáctenos"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"Contáctenos para hablar acerca de cualquier cosa relacionada con nuestra empresa o servicios.<br/>\n"
"                                                                        Le responderemos tan pronto como sea posible."

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_63f73c9f-d07b-45e8-bb81-0078b7fb97be
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Contractor"
msgstr "Contratista"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Date"
msgstr ""
"Fecha\n"
"                            de control"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Location"
msgstr ""
"Ubicación\n"
"                            de control"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Type"
msgstr ""
"Tipo\n"
"                            de control"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_bea4dd93-6b43-412f-8b05-a0364fa90293
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Control Date"
msgstr "Fecha de control"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_5a396d3d-7953-4770-a4b7-a69459e388f1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Control Location"
msgstr "Ubicación de control"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_selection_instal_4d4b88fd-9b8d-43c4-ac2b-baf736781d26
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Control Type"
msgstr "Tipo de control"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Create as many templates as you need from the 'Configuration &amp;gt; "
"Worksheet Templates' menu. Here, we would recommend creating one template "
"<strong>per type of certification</strong>."
msgstr ""
"Cree tantas plantillas como necesite desde el menú 'Configuración &amp;gt; "
"Plantillas de la hoja de trabajo'. Aquí le recomendamos crear una plantilla "
"<strong>por tipo de certificación</strong>."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_3
msgid "Created on"
msgstr "Creado el"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Curious to get <strong>customer feedback</strong> on your services? Enable "
"the 'Customer Ratings' feature in the settings of Project."
msgstr ""
"¿Le interesa recibir <strong>retroalimentación de los clientes</strong> "
"acerca de sus servicios? Active la función \"Calificación de clientes\" en "
"los ajustes de Proyecto."

#. module: certification_organism
#: model:account.analytic.plan,name:certification_organism.account_analytic_plan_1
msgid "Default"
msgstr "Predeterminado"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_a0b739f2-4cb1-4502-8b37-8897a8aeffad
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Diagrams"
msgstr "Diagramas"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_fa307386-1f6a-4b86-bd5b-94d072589284
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Differential Protection"
msgstr "Protección diferencial"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_1d268a1f-8476-496a-84b0-42153d64d396
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Distribution Network Provider"
msgstr "Proveedor de red de distribución"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_3
#: model:project.task.type,name:certification_organism.project_task_type_19
msgid "Done"
msgstr "Listo"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_8a2bcf26-8bef-4a2f-9062-b8b95417a3fe
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "EAN"
msgstr "EAN"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Email\n"
"                            Address"
msgstr ""
"Dirección\n"
"                            de correo electrónico"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_7ac1d42b-d321-4e8e-9aa8-f6d19f01a153
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Email Address"
msgstr "Dirección de correo electrónico"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.filename_for_x_studi_e4a6f7a0-7955-48a2-ab1c-a295727bd488
msgid "Filename for x_binary_field_465_1h8jpmjfh"
msgstr "Nombre de archivo para x_binary_field_465_1h8jpmjfh"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Fixed\n"
"                            Material"
msgstr ""
"Material\n"
"                            fijo"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_9210cacd-48ce-4e3e-b4e7-efc01c3fc7bd
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Fixed Material"
msgstr "Material fijo"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"From the <strong>map view</strong>, your agents see where they need to head "
"for their interventions of the day. This visual aid will also help you plan "
"tasks so that each agent covers a specific area."
msgstr ""
"Desde la <strong>vista de mapa</strong>, sus agentes podrán ver a dónde "
"tienen que ir para realizar sus intervenciones del día. Este apoyo visual "
"también le ayudará a planear tareas para que cada agente pueda cubrir un "
"área en específico."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Get an <strong>overview of the ratings</strong> you are receiving from the "
"'Field Service &amp;gt; Reporting &amp;gt; Customer Ratings' menu."
msgstr ""
"Obtenga un <strong>resumen de las calificaciones</strong> que recibe desde "
"el menú \"Servicio externo &amp;gt; Reportes &amp;gt; Calificaciones de "
"clientes\"."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Get reporting on the various fields you are tracking during your "
"interventions. Identify recurring sources of issues and detect warning signs"
" of future potential issues."
msgstr ""
"Obtenga reportes de los diferentes campos que está monitoreando durante sus "
"intervenciones. Identifique cuáles son las causas comunes de problemas y "
"detecte señales de advertencia de problemas potenciales."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go back to the Quotation you had previously created, and hit that "
"'<strong>Confirm</strong>' button. Your Quotation is now a <strong>Sales "
"Order.</strong>"
msgstr ""
"Regrese a la cotización que había creado antes y haga clic en el botón "
"'<strong>confirmar</strong>'. Su cotización ahora es una <strong>orden de "
"ventas.</strong>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to 'Configuration &amp;gt; Worksheet Templates' and click on "
"'<strong>Analysis</strong>' from your '<strong>Worksheet Template</strong>' "
"form view."
msgstr ""
"Vaya a 'Configuración &amp;gt; Plantillas de hojas de trabajo' y haga clic "
"en \"<strong>Análisis</strong>\" desde su vista de formulario "
"\"<strong>Plantilla de hoja de trabajo</strong>\"."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to 'Dashboards &amp;gt; Projects' to get an overview of your tasks. <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\">Use predefined </span>\n"
"                    <span style=\"color: inherit; font-style: normal; background-color: inherit;\"><strong>dashboards</strong></span>\n"
"                    <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\"> or build your own with the advanced reporting engine. Share filters with the team.</span>"
msgstr ""
"Vaya a 'Tableros &amp;gt; Proyectos' para obtener un resumen de sus tareas. <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\">Use tableros</span>\n"
"<span style=\"color: inherit; font-style: normal; background-color: inherit;\"><strong>predefinidos</strong></span>\n"
"<span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\">o construya su  propio tablero con un motor de reportes avanzados y comparta los filtros con el equipo.</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to Reporting &amp;gt; <strong>Tasks Analysis</strong> to get statistics "
"on your tasks and to analyze the performance of your projects."
msgstr ""
"Vaya a Reportes &amp;gt; <strong>Análisis de tareas</strong> para obtener "
"estadísticas sobre sus tareas y para analizar el rendimiento de sus "
"proyectos."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to the 'Configuration &amp;gt; Stages' menu. Then define a '<strong>Rating Email Template</strong>' on your 'Done' stage (or any other one you'd like). This will automatically send a <strong>rating request</strong> to the\n"
"                    customer once the task reaches this stage."
msgstr ""
"Vaya al menú 'Configuración &amp;gt; Etapas'. Defina una \"<strong>Plantilla de correo de calificación</strong>\" en su etapa \"Hecho\" (o en cualquier otra etapa que quiera). Esto hará que se envíe de forma automática una <strong>solicitud de calificación</strong> al\n"
"cliente una vez que se llegue a esta etapa."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Ground\n"
"                            Resistance (Ω)"
msgstr ""
"Resistencia\n"
"                            de puesta a tierra (Ω)"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_e3180eac-14c7-407d-8954-87d9f5bce7c6
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Ground Resistance (Ω)"
msgstr "Resistencia de puesta a tierra (Ω)"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Grounded\n"
"                            Socket"
msgstr ""
"Enchufe\n"
"                            con toma de tierra"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_4fcfeac5-d596-4291-a9b9-ad0e519d1671
msgid "Grounded Socket"
msgstr "Enchufe con toma de tierra"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Grounding\n"
"                            Diagram"
msgstr ""
"Esquema\n"
"                            de conexión a tierra"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_909d67ad-c9bf-4dbe-a309-25678cac2316
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Grounding Diagram"
msgstr "Esquema de conexión a tierra"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Guide to the Certification Auditors Industry"
msgstr "Guía para el sector de certificación de auditores"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Here, we will be <strong>invoicing</strong> our customer after our intervention is done (see the 'Invoicing Policy' defined as 'Based on Delivered Quantity (Manual)'. However, you could also bill your customer right away by\n"
"                    configuring the 'Invoicing Policy' as 'Fixed Price/Prepaid'."
msgstr ""
"Aquí emitiremos una <strong>factura</strong> de cliente una vez que hayamos terminado nuestra intervención (vea la 'política de facturación' definida como \"Basado en cantidad entregada (manual)\". Sin embargo, también puede facturar a su cliente de inmediato si\n"
"configura la \"política de facturción\" como \"Precio fijo o de prepago\". "

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Human Resources"
msgstr "Recursos Humanos"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_2
msgid "In Progress"
msgstr "En progreso"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"In this article, you will discover how to best use Odoo to fit your needs. "
"We will go from the first customer contact all the way to invoicing them "
"your services. Let's go 🚀"
msgstr ""
"En este artículo descubrirá la mejor manera de usar Odoo para cubrir sus "
"necesidades. Veremos desde el primer contacto con el cliente hasta que emita"
" una factura por sus servicios. Manos a la obra. 🚀"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_14
msgid "Inbox"
msgstr "Bandeja de entrada"

#. module: certification_organism
#: model:ir.model,name:certification_organism.x_control_charging_station_ir_model_1
msgid "Installation Control of Residential Charging Stations"
msgstr "Control de la instalación de estaciones de carga residenciales"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_efbdb6ce-50de-48e4-8992-1f5e72e65380
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installation Date"
msgstr "Fecha de la instalación"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_b8a358e7-77a4-4e10-8e7c-91584f9401a0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installation Description"
msgstr "Descripción de la instalación"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_a963bdf3-1118-458a-b056-85422bafe0ec
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installer"
msgstr "Instalador"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_c533a573-29b4-4d52-9721-11aa04014346
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Isolation"
msgstr "Aislamiento"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_18
msgid "Later"
msgstr "Más tarde"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Let's <strong>create and confirm the invoice</strong> 🙌"
msgstr "<strong>Creemos y confirmemos la factura</strong> 🙌"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Let's move to the 'Planning by User' menu in Field Service. From the "
"<strong>Gantt view</strong>, you see at a glance the agents that are "
"<strong>available</strong> (notice that Fiona is off this week 🏖️)."
msgstr ""
"Vayamos al menú \"Planeación por usuario\" en Servicio externo. En la "
"<strong>vista Gantt</strong> podrá ver de inmediato los trabajadores que "
"están <strong>disponibles</strong> (note que Fiona tiene tiempo personal "
"esta semana 🏖️)."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Luckily, our customer was amazed by our offer and accepted it. We can thus "
"<strong>mark our CRM opportunity as won</strong> 🥳"
msgstr ""
"Por suerte, a nuestro cliente le encantó nuestra oferta y la está aceptando,"
" por lo tengo, podemos <strong>marcar nuestra oportunidad CRM como "
"ganada</strong>. 🥳"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_414eae17-7a9f-4a2f-858c-36218ca2a64c
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Manufacturer"
msgstr "Fabricante"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Marketing"
msgstr "Marketing"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Max.\n"
"                            Protection"
msgstr ""
"Protección\n"
"                            máx."

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_e8935d3c-93ab-4b0c-bd54-6ffe77c00882
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Max. Protection"
msgstr "Protección máx."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Meter\n"
"                            Number"
msgstr ""
"Número\n"
"                            de medidor"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_5f0f247a-a0c8-4fcc-b2e5-5a60d2904967
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Meter Number"
msgstr "Número de identificación"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_adb62158-d7bf-403e-b7bf-76fe3faa4354
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Model"
msgstr "Modelo"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "My Company"
msgstr "Mi Empresa"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.ir_model_fields_10247_6da24e2f
msgid "Name"
msgstr "Nombre"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Nameplate\n"
"                            Capacity (kW)"
msgstr ""
"Capacidad\n"
"                            nominal (kW)"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_7be3783d-b74a-4eda-8671-130e1c312bbf
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Nameplate Capacity (kW)"
msgstr "Capacidad nominal (kW)"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_0
msgid "New"
msgstr "Nuevo"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Next\n"
"                            Control Before"
msgstr ""
"Siguiente control\n"
"                            antes del"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_879fcac2-2f44-4d3f-b789-b1f61e97edef
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Next Control Before"
msgstr "Siguiente control antes del"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Notice how the <strong>service</strong> we are offering has been configured:"
msgstr ""
"Fíjese en cómo configuramos el <strong>servicio</strong> que estamos "
"ofreciendo:"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Now that the certification is complete and sent to the customer, we can "
"<strong>mark the task as done</strong>."
msgstr ""
"Ahora que la certificación está completa y se envió al cliente, podemos "
"<strong>marcar la tarea como hecha</strong>."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Now, <strong>register their payment</strong>. (Note: your customer can also "
"directly pay from their portal 💡)"
msgstr ""
"Ahora <strong>debemos registrar su pago</strong>. (Nota: el cliente también "
"puede pagar directamente desde el portal 💡)."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Once you're done filling in the worksheet, simply click on 'Send Report' to "
"<strong>send your customer the certification</strong>."
msgstr ""
"Una vez que termine de llenar la hoja de trabajo haga clic en \"Enviar "
"reporte\" para <strong>enviarle la certificación al cliente</strong>."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Operating\n"
"                            Voltage"
msgstr ""
"Tensión\n"
"                            de funcionamiento"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_7c3fe422-e483-4f9c-b9b8-ca9daca8ae17
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Operating Voltage"
msgstr "Tensión de funcionamiento"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Owner\n"
"                            Address"
msgstr ""
"Dirección\n"
"                            del propietario"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_d424d6c6-250b-4e2b-b02c-909d624d9e36
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Owner Address"
msgstr "Dirección del propietario"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_image_installati_7bb7ef76-751f-4749-8832-1da0ff83bec0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Pictures"
msgstr "Fotografías"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_1
msgid "Planned"
msgstr "Planeado"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Power\n"
"                            Supply"
msgstr ""
"Fuente\n"
"                            de alimentación"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_ead54bd7-5601-45b5-9360-47cd843cc177
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Power Supply"
msgstr "Fuente de alimentación"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_0cd78554-7c1d-46a5-be12-2b31668b7957
msgid "Premises Type"
msgstr "Tipo de instalaciones"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            (A)"
msgstr ""
"Protección\n"
"                            (A)"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            6mA DC"
msgstr ""
"Protección\n"
"                            6mA DC"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Direct Contacts"
msgstr ""
"Protección\n"
"                            contra los contactos directos"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Indirect Contacts"
msgstr ""
"Protección\n"
"                            contra los contactos indirectos"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Overintensity"
msgstr ""
"Protección\n"
"                            contra sobreintensidades"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_6185d132-f0ca-45ce-811e-7d9f7e816414
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection (A)"
msgstr "Protección (A)"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_b4dca44c-3589-48b4-8b44-63468cedcc86
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection 6mA DC"
msgstr "Protección 6mA DC"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_1da09a59-1725-4ec7-a6a9-3fd88f1c69a0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Direct Contacts"
msgstr "Protección contra los contactos directos"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_07b49038-6246-41fa-a8ec-119909ba7be3
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Indirect Contacts"
msgstr "Protección contra los contactos indirectos"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_8ffbf2f2-43b1-4eb7-93dd-a8ddff7b1ec4
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Overintensity"
msgstr "Protección contra sobreintensidades"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_datetime_install_2bbd9d49-f78f-4318-bf29-2f0ff5f8ddb1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Report Date"
msgstr "Fecha del reporte"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Report Number"
msgstr "Número de reporte"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Send your customer the <strong>invoice</strong>. Note that it will also be "
"available in their <strong>portal</strong>."
msgstr ""
"Envie la <strong>factura</strong> al cliente. Note que la factura también "
"estará disponible en su <strong>portal</strong>."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Serial\n"
"                            Number"
msgstr ""
"Número\n"
"                            de serie"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_38923213-00f8-4249-9e45-a08efb7f9976
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Serial Number"
msgstr "Nº de serie"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"State\n"
"                            Control"
msgstr ""
"Control\n"
"                           del estado"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_1a7f02cf-8f6a-4d98-9d7b-b81d70f5b91d
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "State Control"
msgstr "Control del estado"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "Submit"
msgstr "Enviar"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.ir_model_fields_10245_2c1e3810
msgid "Task"
msgstr "Tarea"

#. module: certification_organism
#: model:project.project,label_tasks:certification_organism.certification_project
msgid "Tasks"
msgstr "Tareas"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Thanks to Studio, you can entirely <strong>customize the report</strong> to "
"fit your needs. Add as many fields of any type as you'd like."
msgstr ""
"Gracias a Studio, puede <strong>personalizar el reporte</strong> por "
"complete para que cumpla con sus necesidades. Agregue tantos tipos de campo "
"como quiera."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Thanks to the '<strong>Worksheet Template</strong>' we selected for the "
"product and on the task, our form has all of the necessary fields for this "
"kind of certification."
msgstr ""
"Gracias a la \"<strong>plantilla de hoja de trabajo</strong>\" que "
"seleccionamos para el producto y en la tarea, nuestro formulario tiene todos"
" los campos necesarios para este tipo de certificación."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"The certification will remain available to your customer at any time in their <strong>portal</strong>, where they will be able to download and print the report if needed. Click on the 'C<strong>ustomer Preview</strong>' button to\n"
"                    get a sneak peek of what it'll look like for your customer."
msgstr ""
"La certificación seguirá disponible para su cliente en cualquier momento en su <strong>portal</strong>, donde podrán descargar e imprimir el reporte si lo necesitan. Haga clic en el botón \"<strong>Vista previa del cliente</strong>\" para\n"
"poder ver cómo se verá para el cliente."

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_17
msgid "This Month"
msgstr "Este mes"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_16
msgid "This Week"
msgstr "Esta semana"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"This task will directly get the right certification form for this type of "
"intervention thanks to the '<strong>Worksheet Template</strong>' selected on"
" the product."
msgstr ""
"Esta tarea directamente tendrá el formulario de certificación correcto para "
"este tipo de intervención gracias a la \"<strong>plantilla de hoja de "
"trabajo</strong>\" que seleccionamos en el producto."

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_15
msgid "Today"
msgstr "Hoy"

#. module: certification_organism
#: model:website.menu,name:certification_organism.website_menu_4
msgid "Top Menu for Website 1"
msgstr "Menú superior para el sitio web 1"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Visiting\n"
"                            Agent"
msgstr ""
"Agente\n"
"                            encargado de la visita"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Visiting\n"
"                            Agent Signature"
msgstr ""
"Firma del agente\n"
"                            encargado de la visita"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_eb7af591-0632-4fc9-8555-3e0bda3b9939
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Visiting Agent"
msgstr "Agente encargado de la visita"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_signature_instal_d7e23d6e-e60a-4561-9db6-d85fef213749
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Visiting Agent Signature"
msgstr "Firma del agente encargado de la visita"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"We're now at the customer's place for their certification. Open your task "
"and click on the '<strong>Worksheet</strong>' button."
msgstr ""
"Ahora estamos en la dirección que indicó el cliente para su certificación. "
"Abra su tarea y haga clic en el botón \"<strong>Hoja de trabajo</strong>\"."

#. module: certification_organism
#: model_terms:web_tour.tour,rainbow_man_message:certification_organism.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Le damos la bienvenida! Disfrute del sitio."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"When the Sales Order is confirmed, a <strong>task is automatically "
"generated</strong> in our 'Certifications' project."
msgstr ""
"Cuando se confirma la orden de ventas, <strong>se generará una tarea de "
"forma automática</strong> en nuestro proyecto \"Certificaciones\"."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"When the form is submitted, an <strong>opportunity</strong> is automatically"
" generated in your CRM.<br/>"
msgstr ""
"Cuando se envía el formulario también se genera de forma automática una "
"<strong>oportunidad</strong> en su CRM."

#. module: certification_organism
#: model:ir.actions.act_window,name:certification_organism.x_control_charging_station_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Hojas de trabajo"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_2
#: model:project.tags,name:certification_organism.project_tags_1
msgid "charging station"
msgstr "estación de carga"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_5
msgid "electrical installation"
msgstr "instalación eléctrica"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_3
msgid "gas installation"
msgstr "instalación de gas"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_756f305b-2979-4d4d-baf3-a1076dfb5a68
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "ΔIn"
msgstr "ΔIn"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"💡 This process can be <strong>automated</strong> by setting an 'Email "
"Template' on the 'Planned' stage of your project (or any other stage you'd "
"like). A pre-made email template is available to you."
msgstr ""
"💡 Este proceso puede <strong>automatizarse</strong> si configura una "
"\"Plantilla de correo electrónico\" en la etapa \"Planeado\" de su proyecto "
"(o en cualquier otra etapa que quiera). Tiene disponible una plantilla de "
"correo electrónico predeterminada."
