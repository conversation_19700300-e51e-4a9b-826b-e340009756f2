# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* certification_organism
# 
# Translators:
# jabiri7, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# erii<PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:25+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Quim - eccit <<EMAIL>>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "1) Get opportunities from your website"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "2) Transform your opportunities into sales deals 💪"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "3) Confirm your Sales Order"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "4) Time to plan your Field Service task"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "5) Time to get this certification done 🚀"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "6) Send the certification to the customer ✉️"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "7) Time to get your money 💰"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "8) Analyze your performance &amp;amp; track your profitability"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "9) Ready to go one step further?"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">City</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de telèfon</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Street</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">La vostra empresa</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Zip</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "<strong>Expand your business</strong> with the following apps:"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Accounting"
msgstr "Comptabilitat"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"After seeing how wonderful your website is, potential leads can contact you "
"through a <strong>website form</strong> for more information about your "
"services."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Alright, let's fill in this <strong>worksheet</strong>!"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Analyze the <strong>profitability</strong> of your services with "
"<strong>project updates</strong>. (Install the 'Accounting' app and enable "
"the 'Analytic Accounting' feature.)"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_file_installatio_2cc1cd0a-1f0c-473e-93ff-cfbf112ce880
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Annexes"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"At last, <strong>send a message</strong> to your customer informing them of "
"the scheduled date of their intervention from the chatter of your task."
msgstr ""

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_6
msgid "B2B"
msgstr "B2B"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_4
#: model:project.tags,name:certification_organism.project_tags_2
msgid "B2C"
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_20
msgid "Canceled"
msgstr "Cancel·lat"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_4
msgid "Cancelled"
msgstr "Cancel·lat"

#. module: certification_organism
#: model:account.analytic.account,name:certification_organism.account_analytic_account_2
#: model:project.project,name:certification_organism.certification_project
msgid "Certifications"
msgstr "Certificacions"

#. module: certification_organism
#: model:product.template,name:certification_organism.product_product_3_product_template
msgid "Charging Station Certification"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Circuit\n"
"                            Breaker Bandwidth Test"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_38c7f1b2-1c72-4cad-a3bd-0089bf65f4e1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Circuit Breaker Bandwidth Test"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Click on the ➕ icon to create new tasks, or the 🔍 icon to <strong>schedule "
"existing tasks</strong>."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Click on the<strong> 'New Quotation' </strong>button from your CRM "
"opportunity. <strong>Send it to your customer by email</strong> once it's "
"ready."
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_59802a97-4667-42d4-8658-f04a466fb512
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Comments"
msgstr "Comentaris"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_bce7d605-17da-4b46-9be2-f5d0c8e10bfd
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Conclusion"
msgstr "Conclusió"

#. module: certification_organism
#: model:website.menu,name:certification_organism.website_menu_6
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "Contact us"
msgstr "Contacta amb nosaltres"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_63f73c9f-d07b-45e8-bb81-0078b7fb97be
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Contractor"
msgstr "Contractista"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Date"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Location"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Type"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_bea4dd93-6b43-412f-8b05-a0364fa90293
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Control Date"
msgstr "Data de control"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_5a396d3d-7953-4770-a4b7-a69459e388f1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Control Location"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_selection_instal_4d4b88fd-9b8d-43c4-ac2b-baf736781d26
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Control Type"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Create as many templates as you need from the 'Configuration &amp;gt; "
"Worksheet Templates' menu. Here, we would recommend creating one template "
"<strong>per type of certification</strong>."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_3
msgid "Created on"
msgstr "Creat el"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Curious to get <strong>customer feedback</strong> on your services? Enable "
"the 'Customer Ratings' feature in the settings of Project."
msgstr ""

#. module: certification_organism
#: model:account.analytic.plan,name:certification_organism.account_analytic_plan_1
msgid "Default"
msgstr "Per defecte"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_a0b739f2-4cb1-4502-8b37-8897a8aeffad
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Diagrams"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_fa307386-1f6a-4b86-bd5b-94d072589284
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Differential Protection"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_1d268a1f-8476-496a-84b0-42153d64d396
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Distribution Network Provider"
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_3
#: model:project.task.type,name:certification_organism.project_task_type_19
msgid "Done"
msgstr "Fet"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_8a2bcf26-8bef-4a2f-9062-b8b95417a3fe
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "EAN"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Email\n"
"                            Address"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_7ac1d42b-d321-4e8e-9aa8-f6d19f01a153
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Email Address"
msgstr "Adreça de correu electrònic"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.filename_for_x_studi_e4a6f7a0-7955-48a2-ab1c-a295727bd488
msgid "Filename for x_binary_field_465_1h8jpmjfh"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Fixed\n"
"                            Material"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_9210cacd-48ce-4e3e-b4e7-efc01c3fc7bd
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Fixed Material"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"From the <strong>map view</strong>, your agents see where they need to head "
"for their interventions of the day. This visual aid will also help you plan "
"tasks so that each agent covers a specific area."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Get an <strong>overview of the ratings</strong> you are receiving from the "
"'Field Service &amp;gt; Reporting &amp;gt; Customer Ratings' menu."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Get reporting on the various fields you are tracking during your "
"interventions. Identify recurring sources of issues and detect warning signs"
" of future potential issues."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go back to the Quotation you had previously created, and hit that "
"'<strong>Confirm</strong>' button. Your Quotation is now a <strong>Sales "
"Order.</strong>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to 'Configuration &amp;gt; Worksheet Templates' and click on "
"'<strong>Analysis</strong>' from your '<strong>Worksheet Template</strong>' "
"form view."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to 'Dashboards &amp;gt; Projects' to get an overview of your tasks. <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\">Use predefined </span>\n"
"                    <span style=\"color: inherit; font-style: normal; background-color: inherit;\"><strong>dashboards</strong></span>\n"
"                    <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\"> or build your own with the advanced reporting engine. Share filters with the team.</span>"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to Reporting &amp;gt; <strong>Tasks Analysis</strong> to get statistics "
"on your tasks and to analyze the performance of your projects."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to the 'Configuration &amp;gt; Stages' menu. Then define a '<strong>Rating Email Template</strong>' on your 'Done' stage (or any other one you'd like). This will automatically send a <strong>rating request</strong> to the\n"
"                    customer once the task reaches this stage."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Ground\n"
"                            Resistance (Ω)"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_e3180eac-14c7-407d-8954-87d9f5bce7c6
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Ground Resistance (Ω)"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Grounded\n"
"                            Socket"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_4fcfeac5-d596-4291-a9b9-ad0e519d1671
msgid "Grounded Socket"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Grounding\n"
"                            Diagram"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_909d67ad-c9bf-4dbe-a309-25678cac2316
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Grounding Diagram"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Guide to the Certification Auditors Industry"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Here, we will be <strong>invoicing</strong> our customer after our intervention is done (see the 'Invoicing Policy' defined as 'Based on Delivered Quantity (Manual)'. However, you could also bill your customer right away by\n"
"                    configuring the 'Invoicing Policy' as 'Fixed Price/Prepaid'."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Human Resources"
msgstr "Recursos humans"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_2
msgid "In Progress"
msgstr "En curs"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"In this article, you will discover how to best use Odoo to fit your needs. "
"We will go from the first customer contact all the way to invoicing them "
"your services. Let's go 🚀"
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_14
msgid "Inbox"
msgstr "Bústia d'entrada"

#. module: certification_organism
#: model:ir.model,name:certification_organism.x_control_charging_station_ir_model_1
msgid "Installation Control of Residential Charging Stations"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_efbdb6ce-50de-48e4-8992-1f5e72e65380
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installation Date"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_b8a358e7-77a4-4e10-8e7c-91584f9401a0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installation Description"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_a963bdf3-1118-458a-b056-85422bafe0ec
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installer"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_c533a573-29b4-4d52-9721-11aa04014346
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Isolation"
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_18
msgid "Later"
msgstr "Més tard"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Let's <strong>create and confirm the invoice</strong> 🙌"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Let's move to the 'Planning by User' menu in Field Service. From the "
"<strong>Gantt view</strong>, you see at a glance the agents that are "
"<strong>available</strong> (notice that Fiona is off this week 🏖️)."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Luckily, our customer was amazed by our offer and accepted it. We can thus "
"<strong>mark our CRM opportunity as won</strong> 🥳"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_414eae17-7a9f-4a2f-858c-36218ca2a64c
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Manufacturer"
msgstr "Fabricant"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Marketing"
msgstr "Màrqueting"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Max.\n"
"                            Protection"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_e8935d3c-93ab-4b0c-bd54-6ffe77c00882
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Max. Protection"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Meter\n"
"                            Number"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_5f0f247a-a0c8-4fcc-b2e5-5a60d2904967
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Meter Number"
msgstr "Número del vúmetre"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_adb62158-d7bf-403e-b7bf-76fe3faa4354
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Model"
msgstr "Model"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "My Company"
msgstr "La meva empresa"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.ir_model_fields_10247_6da24e2f
msgid "Name"
msgstr "Nom"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Nameplate\n"
"                            Capacity (kW)"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_7be3783d-b74a-4eda-8671-130e1c312bbf
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Nameplate Capacity (kW)"
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_0
msgid "New"
msgstr "Nou"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Next\n"
"                            Control Before"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_879fcac2-2f44-4d3f-b789-b1f61e97edef
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Next Control Before"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Notice how the <strong>service</strong> we are offering has been configured:"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Now that the certification is complete and sent to the customer, we can "
"<strong>mark the task as done</strong>."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Now, <strong>register their payment</strong>. (Note: your customer can also "
"directly pay from their portal 💡)"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Once you're done filling in the worksheet, simply click on 'Send Report' to "
"<strong>send your customer the certification</strong>."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Operating\n"
"                            Voltage"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_7c3fe422-e483-4f9c-b9b8-ca9daca8ae17
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Operating Voltage"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Owner\n"
"                            Address"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_d424d6c6-250b-4e2b-b02c-909d624d9e36
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Owner Address"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_image_installati_7bb7ef76-751f-4749-8832-1da0ff83bec0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Pictures"
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_1
msgid "Planned"
msgstr "Planificat"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Power\n"
"                            Supply"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_ead54bd7-5601-45b5-9360-47cd843cc177
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Power Supply"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_0cd78554-7c1d-46a5-be12-2b31668b7957
msgid "Premises Type"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            (A)"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            6mA DC"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Direct Contacts"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Indirect Contacts"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Overintensity"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_6185d132-f0ca-45ce-811e-7d9f7e816414
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection (A)"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_b4dca44c-3589-48b4-8b44-63468cedcc86
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection 6mA DC"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_1da09a59-1725-4ec7-a6a9-3fd88f1c69a0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Direct Contacts"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_07b49038-6246-41fa-a8ec-119909ba7be3
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Indirect Contacts"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_8ffbf2f2-43b1-4eb7-93dd-a8ddff7b1ec4
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Overintensity"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_datetime_install_2bbd9d49-f78f-4318-bf29-2f0ff5f8ddb1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Report Date"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Report Number"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Send your customer the <strong>invoice</strong>. Note that it will also be "
"available in their <strong>portal</strong>."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Serial\n"
"                            Number"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_38923213-00f8-4249-9e45-a08efb7f9976
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Serial Number"
msgstr "Núm. de sèrie"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"State\n"
"                            Control"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_1a7f02cf-8f6a-4d98-9d7b-b81d70f5b91d
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "State Control"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "Submit"
msgstr "Publicar"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.ir_model_fields_10245_2c1e3810
msgid "Task"
msgstr "Tasca"

#. module: certification_organism
#: model:project.project,label_tasks:certification_organism.certification_project
msgid "Tasks"
msgstr "Tasques"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Thanks to Studio, you can entirely <strong>customize the report</strong> to "
"fit your needs. Add as many fields of any type as you'd like."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Thanks to the '<strong>Worksheet Template</strong>' we selected for the "
"product and on the task, our form has all of the necessary fields for this "
"kind of certification."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"The certification will remain available to your customer at any time in their <strong>portal</strong>, where they will be able to download and print the report if needed. Click on the 'C<strong>ustomer Preview</strong>' button to\n"
"                    get a sneak peek of what it'll look like for your customer."
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_17
msgid "This Month"
msgstr "Aquest mes"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_16
msgid "This Week"
msgstr "Aquesta setmana"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"This task will directly get the right certification form for this type of "
"intervention thanks to the '<strong>Worksheet Template</strong>' selected on"
" the product."
msgstr ""

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_15
msgid "Today"
msgstr "Avui"

#. module: certification_organism
#: model:website.menu,name:certification_organism.website_menu_4
msgid "Top Menu for Website 1"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Visiting\n"
"                            Agent"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Visiting\n"
"                            Agent Signature"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_eb7af591-0632-4fc9-8555-3e0bda3b9939
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Visiting Agent"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_signature_instal_d7e23d6e-e60a-4561-9db6-d85fef213749
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Visiting Agent Signature"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"We're now at the customer's place for their certification. Open your task "
"and click on the '<strong>Worksheet</strong>' button."
msgstr ""

#. module: certification_organism
#: model_terms:web_tour.tour,rainbow_man_message:certification_organism.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"When the Sales Order is confirmed, a <strong>task is automatically "
"generated</strong> in our 'Certifications' project."
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"When the form is submitted, an <strong>opportunity</strong> is automatically"
" generated in your CRM.<br/>"
msgstr ""

#. module: certification_organism
#: model:ir.actions.act_window,name:certification_organism.x_control_charging_station_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Fulls de treball"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_2
#: model:project.tags,name:certification_organism.project_tags_1
msgid "charging station"
msgstr ""

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_5
msgid "electrical installation"
msgstr ""

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_3
msgid "gas installation"
msgstr ""

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_756f305b-2979-4d4d-baf3-a1076dfb5a68
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "ΔIn"
msgstr ""

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"💡 This process can be <strong>automated</strong> by setting an 'Email "
"Template' on the 'Planned' stage of your project (or any other stage you'd "
"like). A pre-made email template is available to you."
msgstr ""
