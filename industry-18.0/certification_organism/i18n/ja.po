# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* certification_organism
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:25+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "1) Get opportunities from your website"
msgstr "1) ウェブサイトから案件を入手"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "2) Transform your opportunities into sales deals 💪"
msgstr "2) 案件を販売取引に変える 💪"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "3) Confirm your Sales Order"
msgstr "3) 販売オーダを確定"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "4) Time to plan your Field Service task"
msgstr "4) フィールドサービスタスクを計画"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "5) Time to get this certification done 🚀"
msgstr "5) 認定を完了 🚀"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "6) Send the certification to the customer ✉️"
msgstr "6) 顧客に認定書を送信 ✉️"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "7) Time to get your money 💰"
msgstr "7) 料金を受取る 💰"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "8) Analyze your performance &amp;amp; track your profitability"
msgstr "8) パフォーマンスを分析し、収益性を追跡"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "9) Ready to go one step further?"
msgstr "9) さらに一歩進みますか?"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">City</span>"
msgstr "<span class=\"s_website_form_label_content\">都市</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">電話番号</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Street</span>"
msgstr "<span class=\"s_website_form_label_content\">町名番地</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">タイトル</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">会社</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Eメール</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">名前</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">質問</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "<span class=\"s_website_form_label_content\">Zip</span>"
msgstr "<span class=\"s_website_form_label_content\">郵便番号</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "<strong>Expand your business</strong> with the following apps:"
msgstr "アプリを活用して<strong>ビジネスを拡大</strong>しましょう:"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Accounting"
msgstr "会計"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"After seeing how wonderful your website is, potential leads can contact you "
"through a <strong>website form</strong> for more information about your "
"services."
msgstr ""
"ウェブサイトを確認した見込客は、 <strong>ウェブサイトフォーム</strong>から、サービスの詳細情報を問い合わせることができます。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Alright, let's fill in this <strong>worksheet</strong>!"
msgstr "それでは、この <strong>ワークシート</strong>を入力しましょう!"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Analyze the <strong>profitability</strong> of your services with "
"<strong>project updates</strong>. (Install the 'Accounting' app and enable "
"the 'Analytic Accounting' feature.)"
msgstr ""
"サービスの<strong>収益性</strong>を分析するために<strong>プロジェクト更新</strong>を使用しましょう。 ('会計' "
"アプリをインストールし、'分析会計' 機能を有効にして下さい。)"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_file_installatio_2cc1cd0a-1f0c-473e-93ff-cfbf112ce880
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Annexes"
msgstr "付録"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"At last, <strong>send a message</strong> to your customer informing them of "
"the scheduled date of their intervention from the chatter of your task."
msgstr "最後に、タスクのチャターから調査予定日を通知する <strong>メッセージを送信</strong>します。"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_6
msgid "B2B"
msgstr "B2B"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_4
#: model:project.tags,name:certification_organism.project_tags_2
msgid "B2C"
msgstr "B2C"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_20
msgid "Canceled"
msgstr "取消済"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_4
msgid "Cancelled"
msgstr "取消済"

#. module: certification_organism
#: model:account.analytic.account,name:certification_organism.account_analytic_account_2
#: model:project.project,name:certification_organism.certification_project
msgid "Certifications"
msgstr "検定"

#. module: certification_organism
#: model:product.template,name:certification_organism.product_product_3_product_template
msgid "Charging Station Certification"
msgstr "充電ステーション認証"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Circuit\n"
"                            Breaker Bandwidth Test"
msgstr ""
"サーキット\n"
"                            ブレーカー帯域幅テスト"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_38c7f1b2-1c72-4cad-a3bd-0089bf65f4e1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Circuit Breaker Bandwidth Test"
msgstr "サーキットブレーカー帯域幅テスト"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Click on the ➕ icon to create new tasks, or the 🔍 icon to <strong>schedule "
"existing tasks</strong>."
msgstr "➕ アイコンをクリックして新規タスクを作成するか🔍 アイコンで <strong>既存のタスクをスケジュール</strong>して下さい。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Click on the<strong> 'New Quotation' </strong>button from your CRM "
"opportunity. <strong>Send it to your customer by email</strong> once it's "
"ready."
msgstr ""
"CRM案件から<strong> '新規見積' </strong>ボタンをクリックします。用意ができたら、 "
"<strong>Eメールで顧客に送信</strong> して下さい。"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_59802a97-4667-42d4-8658-f04a466fb512
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Comments"
msgstr "コメント"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_bce7d605-17da-4b46-9be2-f5d0c8e10bfd
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Conclusion"
msgstr "結論"

#. module: certification_organism
#: model:website.menu,name:certification_organism.website_menu_6
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "Contact us"
msgstr "お問い合わせ"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"当社またはサービスに関してお気軽にお問合せ下さい。<br/>\n"
"                                                                        早急にお返事させて頂きます。"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_63f73c9f-d07b-45e8-bb81-0078b7fb97be
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Contractor"
msgstr "契約社員"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Date"
msgstr ""
"検査\n"
"                            日"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Location"
msgstr ""
"検査\n"
"                            ロケーション"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Control\n"
"                            Type"
msgstr ""
"検査\n"
"                            タイプ"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_bea4dd93-6b43-412f-8b05-a0364fa90293
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Control Date"
msgstr "管理日"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_5a396d3d-7953-4770-a4b7-a69459e388f1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Control Location"
msgstr "検査ロケーション"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_selection_instal_4d4b88fd-9b8d-43c4-ac2b-baf736781d26
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Control Type"
msgstr "検査タイプ"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Create as many templates as you need from the 'Configuration &amp;gt; "
"Worksheet Templates' menu. Here, we would recommend creating one template "
"<strong>per type of certification</strong>."
msgstr ""
"'設定 &amp;gt; ワークシートテンプレート' メニューからテンプレートを必要なだけ作成しましょう。ここでは、 "
"<strong>認定の種類ごと</strong>に1つのテンプレートの作成をお勧めします。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_3
msgid "Created on"
msgstr "作成日"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Curious to get <strong>customer feedback</strong> on your services? Enable "
"the 'Customer Ratings' feature in the settings of Project."
msgstr ""
"サービスについて<strong>顧客によるフィードバック</strong>を希望しますか? プロジェクト設定で'顧客評価' 機能を有効にして下さい。"

#. module: certification_organism
#: model:account.analytic.plan,name:certification_organism.account_analytic_plan_1
msgid "Default"
msgstr "デフォルト"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_a0b739f2-4cb1-4502-8b37-8897a8aeffad
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Diagrams"
msgstr "図"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_fa307386-1f6a-4b86-bd5b-94d072589284
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Differential Protection"
msgstr "差動プロテクション"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_1d268a1f-8476-496a-84b0-42153d64d396
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Distribution Network Provider"
msgstr "流通ネットワークプロバイダー"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_3
#: model:project.task.type,name:certification_organism.project_task_type_19
msgid "Done"
msgstr "完了"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_8a2bcf26-8bef-4a2f-9062-b8b95417a3fe
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "EAN"
msgstr "EAN"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Email\n"
"                            Address"
msgstr ""
"Eメール\n"
"                            アドレス"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_7ac1d42b-d321-4e8e-9aa8-f6d19f01a153
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Email Address"
msgstr "Eメールアドレス"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.filename_for_x_studi_e4a6f7a0-7955-48a2-ab1c-a295727bd488
msgid "Filename for x_binary_field_465_1h8jpmjfh"
msgstr "Filename for x_binary_field_465_1h8jpmjfh"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Fixed\n"
"                            Material"
msgstr ""
"固定\n"
"                            マテリアル"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_9210cacd-48ce-4e3e-b4e7-efc01c3fc7bd
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Fixed Material"
msgstr "固定マテリアル"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"From the <strong>map view</strong>, your agents see where they need to head "
"for their interventions of the day. This visual aid will also help you plan "
"tasks so that each agent covers a specific area."
msgstr ""
"<strong>マップビュー</strong>から、エージェントは、その日の調査のために向かうべき場所を確認できます。この視覚的なサポートは、各エージェントが特定のエリアを担当するようにタスクを計画する際にも役立ちます。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Get an <strong>overview of the ratings</strong> you are receiving from the "
"'Field Service &amp;gt; Reporting &amp;gt; Customer Ratings' menu."
msgstr ""
" 'フィールドサービス &amp;gt; レポーティング &amp;gt; 顧客評価' "
"メニューから<strong>評価概要</strong>を入手しましょう。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Get reporting on the various fields you are tracking during your "
"interventions. Identify recurring sources of issues and detect warning signs"
" of future potential issues."
msgstr ""
"調査中に追跡しているさまざまな分野に関するレポートを取得します。繰返し発生する問題の原因を特定し、将来的に発生する可能性のある問題の兆候を検出します。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go back to the Quotation you had previously created, and hit that "
"'<strong>Confirm</strong>' button. Your Quotation is now a <strong>Sales "
"Order.</strong>"
msgstr ""
"前回作成した見積に戻り '<strong>確認</strong>' ボタンをクリックします。見積が "
"<strong>販売オーダ</strong>になりました。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to 'Configuration &amp;gt; Worksheet Templates' and click on "
"'<strong>Analysis</strong>' from your '<strong>Worksheet Template</strong>' "
"form view."
msgstr ""
"'設定 &amp;gt; ワークシートテンプレート' に行き '<strong>分析</strong>' "
"をクリックします('<strong>ワークテンプレート</strong>' フォームビューより)。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to 'Dashboards &amp;gt; Projects' to get an overview of your tasks. <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\">Use predefined </span>\n"
"                    <span style=\"color: inherit; font-style: normal; background-color: inherit;\"><strong>dashboards</strong></span>\n"
"                    <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\"> or build your own with the advanced reporting engine. Share filters with the team.</span>"
msgstr ""
"'ダッシュボード &amp;gt; プロジェクト' に行きタスクの概要を確認しましょう。 <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\">事前定義された</span>\n"
"                    <span style=\"color: inherit; font-style: normal; background-color: inherit;\"><strong>ダッシュボード</strong></span>を使用\n"
"                    <span style=\"color: inherit; font-style: normal; font-weight: 400; background-color: inherit;\"> または高度なレポーティングエンジンを使用して自分で作成して下さい。チームとファイルを共有しましょう。</span>"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to Reporting &amp;gt; <strong>Tasks Analysis</strong> to get statistics "
"on your tasks and to analyze the performance of your projects."
msgstr ""
"レポーティング &amp;gt; <strong>タスク分析</strong> に行き、タスクの統計を取得し、プロジェクトのパフォーマンスを分析します。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Go to the 'Configuration &amp;gt; Stages' menu. Then define a '<strong>Rating Email Template</strong>' on your 'Done' stage (or any other one you'd like). This will automatically send a <strong>rating request</strong> to the\n"
"                    customer once the task reaches this stage."
msgstr ""
"'設定 &amp;gt; ステージ' メニューに移動します。そして '完了' ステージ (または他に希望するもの)で '<strong>評価Eメールテンプレート</strong>' を定義します。これによりタスクがこのステージに到達すると、<strong>評価リクエスト</strong> が顧客に\n"
"                    自動的に送信されます。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Ground\n"
"                            Resistance (Ω)"
msgstr ""
"アース\n"
"                            抵抗 (Ω)"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_e3180eac-14c7-407d-8954-87d9f5bce7c6
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Ground Resistance (Ω)"
msgstr "アース抵抗 (Ω)"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Grounded\n"
"                            Socket"
msgstr ""
"アース\n"
"                            ソケット"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_4fcfeac5-d596-4291-a9b9-ad0e519d1671
msgid "Grounded Socket"
msgstr "アースソケット"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Grounding\n"
"                            Diagram"
msgstr ""
"アース\n"
"                            線図"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_909d67ad-c9bf-4dbe-a309-25678cac2316
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Grounding Diagram"
msgstr "アース線図"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Guide to the Certification Auditors Industry"
msgstr "認証監査団体のインダストリーガイド"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Here, we will be <strong>invoicing</strong> our customer after our intervention is done (see the 'Invoicing Policy' defined as 'Based on Delivered Quantity (Manual)'. However, you could also bill your customer right away by\n"
"                    configuring the 'Invoicing Policy' as 'Fixed Price/Prepaid'."
msgstr ""
"ここで、調査完了後に顧客に<strong>請求</strong> します ('請求方針' が '配送済み数量 (手動)に基づく'に定義されていることを確認して下さい)。しかし、すぐに請求したい場合は\n"
"                    '請求方針' を'固定価格/前払'に設定すれば可能です。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Human Resources"
msgstr "人事"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_2
msgid "In Progress"
msgstr "進行中"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"In this article, you will discover how to best use Odoo to fit your needs. "
"We will go from the first customer contact all the way to invoicing them "
"your services. Let's go 🚀"
msgstr ""
"ここでは、あなたのニーズに最適なOdooの使用方法をご紹介します。顧客への最初の連絡から、サービスの請求まで全てを網羅しています。それでは始めましょう。"
" 🚀"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_14
msgid "Inbox"
msgstr "受信箱"

#. module: certification_organism
#: model:ir.model,name:certification_organism.x_control_charging_station_ir_model_1
msgid "Installation Control of Residential Charging Stations"
msgstr "住宅用充電ステーションの設置管理"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_efbdb6ce-50de-48e4-8992-1f5e72e65380
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installation Date"
msgstr "設置日"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_html_installatio_b8a358e7-77a4-4e10-8e7c-91584f9401a0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installation Description"
msgstr "設置説明"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_a963bdf3-1118-458a-b056-85422bafe0ec
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Installer"
msgstr "インストーラー"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_c533a573-29b4-4d52-9721-11aa04014346
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Isolation"
msgstr "アイソレーション"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_18
msgid "Later"
msgstr "以降"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Let's <strong>create and confirm the invoice</strong> 🙌"
msgstr "<strong>請求書を作成・確認</strong>しましょう 🙌"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Let's move to the 'Planning by User' menu in Field Service. From the "
"<strong>Gantt view</strong>, you see at a glance the agents that are "
"<strong>available</strong> (notice that Fiona is off this week 🏖️)."
msgstr ""
"フィールドサービスの'ユーザによる計画' メニューに移動しましょう。<strong>ガントビュー</strong>から、一目でどのエージェントが "
"<strong>利用可能</strong>かが分かります (今はFionaが休暇中です 🏖️)."

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Luckily, our customer was amazed by our offer and accepted it. We can thus "
"<strong>mark our CRM opportunity as won</strong> 🥳"
msgstr "幸いにも、顧客は提案にを気入り、承諾しました。 <strong>CRM 案件を成約としてマーク</strong>できます 🥳"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_many2one_install_414eae17-7a9f-4a2f-858c-36218ca2a64c
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Manufacturer"
msgstr "製造業"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid "Marketing"
msgstr "マーケティング"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Max.\n"
"                            Protection"
msgstr ""
"最大\n"
"                            プロテクション"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_e8935d3c-93ab-4b0c-bd54-6ffe77c00882
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Max. Protection"
msgstr "最大プロテクション"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Meter\n"
"                            Number"
msgstr ""
"メータ\n"
"                            番号"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_5f0f247a-a0c8-4fcc-b2e5-5a60d2904967
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Meter Number"
msgstr "メーター数"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_adb62158-d7bf-403e-b7bf-76fe3faa4354
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Model"
msgstr "モデル"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "My Company"
msgstr "My Company"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.ir_model_fields_10247_6da24e2f
msgid "Name"
msgstr "名称"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Nameplate\n"
"                            Capacity (kW)"
msgstr ""
"定格\n"
"                            容量 (kW)"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_7be3783d-b74a-4eda-8671-130e1c312bbf
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Nameplate Capacity (kW)"
msgstr "定格容量 (kW)"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_0
msgid "New"
msgstr "新規"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Next\n"
"                            Control Before"
msgstr ""
"次回\n"
"                            検査日期限:"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_date_installatio_879fcac2-2f44-4d3f-b789-b1f61e97edef
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Next Control Before"
msgstr "次回検査日期限:"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Notice how the <strong>service</strong> we are offering has been configured:"
msgstr "提供中の <strong>サービス</strong> がどのように設定されているか確認して下さい:"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Now that the certification is complete and sent to the customer, we can "
"<strong>mark the task as done</strong>."
msgstr "認証が完了し、顧客に送信されたので、 <strong>タスクを完了済としてマーク</strong>できます。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Now, <strong>register their payment</strong>. (Note: your customer can also "
"directly pay from their portal 💡)"
msgstr "では、<strong>支払を登録</strong>しましょう。 (メモ: 顧客はポータルから直接支払うこともできます 💡)"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Once you're done filling in the worksheet, simply click on 'Send Report' to "
"<strong>send your customer the certification</strong>."
msgstr "ワークシートに記入し終わったら 'レポートを送信' をクリックして <strong>顧客に認定証を送信</strong>して下さい。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Operating\n"
"                            Voltage"
msgstr ""
"使用\n"
"                            電圧"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_7c3fe422-e483-4f9c-b9b8-ca9daca8ae17
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Operating Voltage"
msgstr "使用電圧"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Owner\n"
"                            Address"
msgstr ""
"所有者\n"
"                            住所"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_d424d6c6-250b-4e2b-b02c-909d624d9e36
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Owner Address"
msgstr "所有者住所"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_image_installati_7bb7ef76-751f-4749-8832-1da0ff83bec0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid "Pictures"
msgstr "写真"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.planning_project_stage_1
msgid "Planned"
msgstr "予定済"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Power\n"
"                            Supply"
msgstr ""
"電源\n"
"                            供給"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_ead54bd7-5601-45b5-9360-47cd843cc177
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Power Supply"
msgstr "電源供給"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_0cd78554-7c1d-46a5-be12-2b31668b7957
msgid "Premises Type"
msgstr "施設タイプ"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            (A)"
msgstr ""
"プロテクション\n"
"                            (A)"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            6mA DC"
msgstr ""
"プロテクション\n"
"                            6mA DC"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Direct Contacts"
msgstr ""
"プロテクション\n"
"                            対接触"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Indirect Contacts"
msgstr ""
"プロテクション\n"
"                            対非接触"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Protection\n"
"                            Against Overintensity"
msgstr ""
"プロテクション\n"
"                            対過剰強度"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_6185d132-f0ca-45ce-811e-7d9f7e816414
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection (A)"
msgstr "プロテクション (A)"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_text_installatio_b4dca44c-3589-48b4-8b44-63468cedcc86
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection 6mA DC"
msgstr "プロテクション 6mA DC"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_1da09a59-1725-4ec7-a6a9-3fd88f1c69a0
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Direct Contacts"
msgstr "プロテクション対接触"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_07b49038-6246-41fa-a8ec-119909ba7be3
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Indirect Contacts"
msgstr "プロテクション対非接触"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_8ffbf2f2-43b1-4eb7-93dd-a8ddff7b1ec4
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Protection Against Overintensity"
msgstr "プロテクション対過剰強度"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_datetime_install_2bbd9d49-f78f-4318-bf29-2f0ff5f8ddb1
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Report Date"
msgstr "報告日"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Report Number"
msgstr "レポート番号"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Send your customer the <strong>invoice</strong>. Note that it will also be "
"available in their <strong>portal</strong>."
msgstr "顧客に <strong>請求書</strong>を送ります。<strong>ポータル</strong>でも請求書を入手することができます。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Serial\n"
"                            Number"
msgstr ""
"シリアル\n"
"                            番号"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_integer_installa_38923213-00f8-4249-9e45-a08efb7f9976
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Serial Number"
msgstr "シリアル番号"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"State\n"
"                            Control"
msgstr ""
"ステータス\n"
"                            コントロール"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_1a7f02cf-8f6a-4d98-9d7b-b81d70f5b91d
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "State Control"
msgstr "ステータスコントロール"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.contactus
msgid "Submit"
msgstr "提出"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.ir_model_fields_10245_2c1e3810
msgid "Task"
msgstr "タスク"

#. module: certification_organism
#: model:project.project,label_tasks:certification_organism.certification_project
msgid "Tasks"
msgstr "タスク"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Thanks to Studio, you can entirely <strong>customize the report</strong> to "
"fit your needs. Add as many fields of any type as you'd like."
msgstr ""
"スタジオを利用して、必要に応じて "
"<strong>レポートをカスタマイズ</strong>することができます。使用したいタイプのフィールドを必要なだけ追加しましょう。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"Thanks to the '<strong>Worksheet Template</strong>' we selected for the "
"product and on the task, our form has all of the necessary fields for this "
"kind of certification."
msgstr ""
"プロダクト用、およびタスク上で選択した '<strong>ワークシートテンプレート</strong>' "
"により、フォームにはこのような認定に必要な全てのフィールドが盛り込まれています。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"The certification will remain available to your customer at any time in their <strong>portal</strong>, where they will be able to download and print the report if needed. Click on the 'C<strong>ustomer Preview</strong>' button to\n"
"                    get a sneak peek of what it'll look like for your customer."
msgstr ""
"顧客は必要な時にはいつでも <strong>ポータル</strong>から認定書を入手できます。 '<strong>顧客プレビュー</strong>' ボタンをクリックして\n"
"                    顧客にどのように見えるかを確認しましょう。"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_17
msgid "This Month"
msgstr "今月"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_16
msgid "This Week"
msgstr "今週"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"This task will directly get the right certification form for this type of "
"intervention thanks to the '<strong>Worksheet Template</strong>' selected on"
" the product."
msgstr ""
"このタスクは、プロダクト上で選択済の '<strong>ワークシートテンプレート</strong>' "
"により、このタイプの調査に適正な認定フォームを直接指定します。"

#. module: certification_organism
#: model:project.task.type,name:certification_organism.project_task_type_15
msgid "Today"
msgstr "今日"

#. module: certification_organism
#: model:website.menu,name:certification_organism.website_menu_4
msgid "Top Menu for Website 1"
msgstr "トップメニュー ウェブサイト 1"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Visiting\n"
"                            Agent"
msgstr ""
"訪問\n"
"                            検査員"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
msgid ""
"Visiting\n"
"                            Agent Signature"
msgstr ""
"訪問\n"
"                            検査員署名"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_related_field_in_eb7af591-0632-4fc9-8555-3e0bda3b9939
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Visiting Agent"
msgstr "訪問検査員"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_signature_instal_d7e23d6e-e60a-4561-9db6-d85fef213749
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "Visiting Agent Signature"
msgstr "訪問検査員署名"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"We're now at the customer's place for their certification. Open your task "
"and click on the '<strong>Worksheet</strong>' button."
msgstr ""
"現在、顧客の認証のために顧客のところに来ています。 タスクを開き、'<strong>ワークシート</strong>'ボタンをクリックします。"

#. module: certification_organism
#: model_terms:web_tour.tour,rainbow_man_message:certification_organism.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ようこそ！"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"When the Sales Order is confirmed, a <strong>task is automatically "
"generated</strong> in our 'Certifications' project."
msgstr "販売オーダが確認されたら、'認定' プロジェクト内で<strong>タスクが自動的に生成</strong>されます。"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"When the form is submitted, an <strong>opportunity</strong> is automatically"
" generated in your CRM.<br/>"
msgstr "フォームが提出されると、<strong>案件</strong> が自動的にCRMに作成されます。<br/>"

#. module: certification_organism
#: model:ir.actions.act_window,name:certification_organism.x_control_charging_station_ir_actions_act_window_1
msgid "Worksheets"
msgstr "ワークシート"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_2
#: model:project.tags,name:certification_organism.project_tags_1
msgid "charging station"
msgstr "チャージステーション"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_5
msgid "electrical installation"
msgstr "電気設置"

#. module: certification_organism
#: model:crm.tag,name:certification_organism.crm_tag_3
msgid "gas installation"
msgstr "ガス設置"

#. module: certification_organism
#: model:ir.model.fields,field_description:certification_organism.new_checkbox_install_756f305b-2979-4d4d-baf3-a1076dfb5a68
#: model_terms:ir.ui.view,arch_db:certification_organism.report_custom_x_control_charging_station
#: model_terms:ir.ui.view,arch_db:certification_organism.x_control_charging_station_ir_ui_view_1
msgid "ΔIn"
msgstr "ΔIn"

#. module: certification_organism
#: model_terms:ir.ui.view,arch_db:certification_organism.welcome_article_body
msgid ""
"💡 This process can be <strong>automated</strong> by setting an 'Email "
"Template' on the 'Planned' stage of your project (or any other stage you'd "
"like). A pre-made email template is available to you."
msgstr ""
"💡 'Eメールテンプレート' をプロジェクトの '計画済' ステージ (または希望するその他のステージ)に設定することでこのプロジェクトは "
"<strong>自動化</strong> されます。事前作成されたEメールテンプレートを使用することができます。"
