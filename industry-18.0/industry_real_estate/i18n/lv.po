# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_real_estate
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Will Sensors, 2024
# ieva<PERSON><PERSON><PERSON> <ievai.putnin<PERSON>@gmail.com>, 2024
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:45+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "<b>ABOUT US</b>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Modern Real Estate&amp;nbsp;—</b>\n"
"                                    </font>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Tālruņa numurs</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Property</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your long term or mid "
"long term rental properties</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your properties, create "
"and manage rental contracts, and streamline your entire <strong>rental "
"process.</strong>  Efficient property management.</span>"
msgstr ""

#. module: industry_real_estate
#: model:website.menu,name:industry_real_estate.website_menu_10
msgid "About Us"
msgstr "Par mums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "About us"
msgstr "Par mums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Add Images"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_address
msgid "Address"
msgstr "Adrese"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Aline Turner, CTO"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Also define the date of the next invoice. Note that this date will be taken as the template date for all the next invoices. For example, if you define a next date of invoice of 04/30 with a monthly recurring period, the invoices\n"
"            will automatically be created each 30th of the month."
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_21
msgid "Apartment 26"
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_20
msgid "Apartment 27"
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_22
msgid "Apartment 28"
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_26
msgid "Apartment 29"
msgstr ""

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_availability
#: model:ir.ui.menu,name:industry_real_estate.menu_availability
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_gantt_view
msgid "Availability"
msgstr "Pieejamība"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_11
#: model:ir.model.fields,field_description:industry_real_estate.field_property_building_1
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_related_building_id
msgid "Building"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Business Flows"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Check Out for availability"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.view_crm_lead_form_quick_create_inherit
msgid "Choose a property..."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on \"New\" to create a new rental contract."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Availability menu. All your rental contracts appear on that "
"screen in a neat little Gantt view."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Properties menu. Now, click on \"New\" to create a new "
"property. Make sure you fill in all the required information."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on the Rental Contract menu."
msgstr ""

#. module: industry_real_estate
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_root
msgid "Configuration"
msgstr "Konfigurācija"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact Us"
msgstr "Sazināties ar mums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact us"
msgstr "Sazināties"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Create Invoice"
msgstr "Izveidot rēķinu"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_create_invoice_meters
msgid "Create Invoice for Meter Readings"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_currency
msgid "Currency"
msgstr "Valūta"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_date
msgid "Date"
msgstr "Datums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Describe your property here"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_name
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_reading_description
#: model:ir.model.fields,field_description:industry_real_estate.field_property_description
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Description"
msgstr "Apraksts"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Discover more"
msgstr "Uzziniet vairāk"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Documents"
msgstr "Dokumenti"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_46_product_template
msgid "Electricity "
msgstr ""

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_server_set_usage_meter_reading
msgid "Execute Code"
msgstr "Izpildīt kodu"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_9
msgid "Ferme Saint-Jean"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "First, head over to the <strong>Properties</strong> app."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"From here, you can also easily create a new rental contract with ease. "
"Simply click on the \"New\" button and enter all the relevant details."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Gallery"
msgstr ""

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_48_product_template
msgid "Gas"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_guarant_partner_id
msgid "Guarant"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Habitat Model"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Habitats with a minimum footprint on the planet and a maximum positive "
"impact on the local community."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Help us protect and preserve for future generations"
msgstr ""

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_12
msgid "Immeuble Bellevue"
msgstr ""

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_8
msgid "Informations"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_invoice_id
msgid "Invoice"
msgstr "Rēķins"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_invoice_status
msgid "Invoice Status"
msgstr "Rēķina Statuss"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Invoices"
msgstr "Rēķini"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Iris Joe, CFO"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_property
msgid "Is Property"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Join us and make the planet a better place."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Leads get created in the CRM by filling the \"Contact us\" form of the "
"website or manually by the real estate agent."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Learn how to use organic gardening methods to grow the freshest food in your"
" fruit and vegetable garden."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Learn more"
msgstr "Lasīt vairāk"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage Leads"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage properties"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage rental contracts"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_meter_id
msgid "Meter"
msgstr ""

#. module: industry_real_estate
#: model:ir.model,name:industry_real_estate.model_meter_reading
msgid "Meter Reading"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_meter_reading_ids
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_meter_reading_ids
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Meter Readings"
msgstr ""

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_configuration_meters
#: model:ir.model,name:industry_real_estate.model_meters
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_meters
msgid "Meters"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Mich Stark, COO"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "My Company"
msgstr "Mans uzņēmums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Now, in the sub-level form, select the appropriate products."
msgstr ""

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_10
msgid "Office"
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_23
msgid "Office M"
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_24
msgid "Office S"
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_25
msgid "Office T"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"One last thing to note: if you don't have any rental contracts for a "
"property yet, you won't see this property in the Gantt view. <br/>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Organic Garden"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Our mission is to provide you with the most modern properties."
msgstr ""

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_10
msgid "Park Station"
msgstr ""

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_43_product_template
msgid "Plumbing"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_price
msgid "Price"
msgstr "Cena"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_products
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_products
msgid "Products"
msgstr "Preces"

#. module: industry_real_estate
#: model:account.analytic.plan,name:industry_real_estate.analytic_plan_properties
#: model:ir.actions.act_window,name:industry_real_estate.action_properties
#: model:ir.actions.server,name:industry_real_estate.action_properties_server
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_properties
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_root
#: model:ir.ui.menu,name:industry_real_estate.menu_root
#: model:website.menu,name:industry_real_estate.website_menu_properties
msgid "Properties"
msgstr "Īpašības"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_account_analytic_account_id
#: model:ir.model.fields,field_description:industry_real_estate.field_crm_lead_property_id
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_account_analytic_account
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Property"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_doc_ids
msgid "Property Documents"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_image
msgid "Property Image"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_image_ids
msgid "Property Images"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_type
msgid "Property Type"
msgstr ""

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_44_product_template
msgid "Provision for fees"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_published
msgid "Published"
msgstr "Publicēts"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_quantity
msgid "Quantity"
msgstr "Daudzums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Read more"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Real Estate rental agency"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Recycling water"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Recycling water for reuse applications instead of using freshwater supplies "
"can be a water-saving measure."
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_rental_contract_id
msgid "Rental Contract"
msgstr ""

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_rental_contracts
#: model:ir.ui.menu,name:industry_real_estate.menu_rental_contracts
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Rental Contracts"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_rental_start_date
msgid "Rental Start Date"
msgstr ""

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_42_product_template
msgid "Rental fee"
msgstr ""

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_49_product_template
msgid "Repair jobs"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_sale_order
msgid "Sale Order"
msgstr "Pārdošanas pasūtījums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search"
msgstr "Meklēt"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search..."
msgstr "Meklēt..."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Section Subtitle"
msgstr ""

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_45_product_template
msgid "Security deposit "
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: industry_real_estate
#: model:base.automation,name:industry_real_estate.automation_set_usage_meter_reading
msgid "Set Usage in Meter Readings"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Shaping our future"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the customer to whom you want to rent the property and then select "
"the property you want to rent."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the rental start date and define the rental period. Whether you want"
" it to be monthly, bimonthly, quarterly, or whatever suits your needs, you "
"can customize it. Don't forget to specify the end date of the contract."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Start with the customer – find out what they want and give it to them."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "Submit"
msgstr "Iesniegt"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_form_view
msgid "Tenant"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"This view shows the availability of each of your resource based on the "
"rental contracts."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"To create a product, head back to the properties menu and click on \"Products\". Click on \"New\" and give your product a name. Check the \"Recurring\" box and define the product type as \"Service\". In the \"Time based pricing\" tab, define\n"
"            the rental price of the property per period."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Tony Fred, CEO"
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_27
msgid "Uccle Observatoire Duplex"
msgstr ""

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_usage
msgid "Usage"
msgstr "Pielietojums"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Vendor Bills"
msgstr "Piegādātāju pavadzīmes"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_9
msgid "Visit"
msgstr ""

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_47_product_template
msgid "Water"
msgstr "Ūdens"

#. module: industry_real_estate
#: model_terms:web_tour.tour,rainbow_man_message:industry_real_estate.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Esiet sveicināti! Veiksmīgu izpēti."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also have a dedicated tab to fill at the time of customer's entry in the"
" property so that you can log and keep track of meter readings."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also need to create products for your properties. You have 2 options: "
"either create one product for all your properties and manually change the "
"price when creating the rental contract, or define one product per property."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "e.g. Apartment Oxford Street"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "found)"
msgstr ""
