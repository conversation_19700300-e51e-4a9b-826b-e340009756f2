# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_real_estate
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:45+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "<b>ABOUT US</b>"
msgstr "<b>CHI SIAMO</b>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Modern Real Estate&amp;nbsp;—</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Agenzia immobiliare moderna&amp;nbsp;—</b>\n"
"                                    </font>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Numero di telefono</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Property</span>"
msgstr "<span class=\"s_website_form_label_content\">Proprietà</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Oggetto</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Azienda</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">E-mail</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Nome</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Domanda</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your long term or mid "
"long term rental properties</span>"
msgstr ""
"<span class=\"text-o-color-5 bg-o-color-4\">Gestire le proprietà in affitto "
"a lungo o a medio lungo termine</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your properties, create "
"and manage rental contracts, and streamline your entire <strong>rental "
"process.</strong>  Efficient property management.</span>"
msgstr ""
"<span class=\"text-o-color-5 bg-o-color-4\">Gestisci le proprietà, crea e "
"gestisci contratti di affitto e ottimizza l'intero "
"<strong>processo.</strong> Gestione delle proprietà efficace.</span>"

#. module: industry_real_estate
#: model:website.menu,name:industry_real_estate.website_menu_10
msgid "About Us"
msgstr "Chi siamo"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "About us"
msgstr "Chi siamo"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Add Images"
msgstr "Aggiungi immagini"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_address
msgid "Address"
msgstr "Indirizzo"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Aline Turner, CTO"
msgstr "Elena Bianchi - Direttore tecnologico"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Elena è una persona iconica, di quelle che possono vantarsi di amare il "
"proprio lavoro. Supervisiona oltre 100 sviluppatori interni e si occupa "
"della comunità, che conta migliaia di sviluppatori."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Also define the date of the next invoice. Note that this date will be taken as the template date for all the next invoices. For example, if you define a next date of invoice of 04/30 with a monthly recurring period, the invoices\n"
"            will automatically be created each 30th of the month."
msgstr ""
"Definisci la data della prossima fattura. Nota che la data verrà utilizzata come data modello per le prossime fatture. Ad esempio, se indichi come prossima data di fatturazione il 30/04 con un periodo di ricorrenza mensile, le fatture \n"
"            verranno create automaticamente ogni 30 del mese."

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_21
msgid "Apartment 26"
msgstr "Appartamento 26"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_20
msgid "Apartment 27"
msgstr "Appartamento 27"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_22
msgid "Apartment 28"
msgstr "Appartamento 28"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_26
msgid "Apartment 29"
msgstr "Appartamento 29"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_availability
#: model:ir.ui.menu,name:industry_real_estate.menu_availability
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_gantt_view
msgid "Availability"
msgstr "Disponibilità"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_11
#: model:ir.model.fields,field_description:industry_real_estate.field_property_building_1
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_related_building_id
msgid "Building"
msgstr "Edificio"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Business Flows"
msgstr "Flussi aziendali"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr "Cambiare il mondo è possibile.<br/> L'abbiamo già fatto."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Check Out for availability"
msgstr "Verificare la disponibilità"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.view_crm_lead_form_quick_create_inherit
msgid "Choose a property..."
msgstr "Scegli una proprietà..."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on \"New\" to create a new rental contract."
msgstr "Fai clic su \"Nuovo\" per creare un nuovo contratto di affitto."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Availability menu. All your rental contracts appear on that "
"screen in a neat little Gantt view."
msgstr ""
"Fai clic sul menu Disponibilità. Tutti i contratti di affitto appariranno "
"sullo schermo con vista Gantt."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Properties menu. Now, click on \"New\" to create a new "
"property. Make sure you fill in all the required information."
msgstr ""
"Fai clic sul menu Proprietà. In seguito, fai clic su \"Nuova\" per creare "
"una nuova proprietà. Assicurati di aggiungere tutte le informazioni "
"richieste."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on the Rental Contract menu."
msgstr "Fai clic sul menu Contratto di affitto."

#. module: industry_real_estate
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_root
msgid "Configuration"
msgstr "Configurazione"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact Us"
msgstr "Contattaci"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact us"
msgstr "Contattaci"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"Contattaci per qualsiasi domanda che riguarda l'azienda o i servizi che offriamo.<br/>\n"
"                                                                        Faremo del nostro meglio per rispondere il prima possibile."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Create Invoice"
msgstr "Crea fattura"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_create_invoice_meters
msgid "Create Invoice for Meter Readings"
msgstr "Crea fattura per le letture del contatore"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_currency
msgid "Currency"
msgstr "Valuta"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_date
msgid "Date"
msgstr "Data"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Describe your property here"
msgstr "Descrivi qui la tua proprietà"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_name
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_reading_description
#: model:ir.model.fields,field_description:industry_real_estate.field_property_description
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Description"
msgstr "Descrizione"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Discover more"
msgstr "Scopri di più"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Documents"
msgstr "Documenti"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_46_product_template
msgid "Electricity "
msgstr "Elettricità"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_server_set_usage_meter_reading
msgid "Execute Code"
msgstr "Esegui codice"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_9
msgid "Ferme Saint-Jean"
msgstr "Casale Saint-Jean"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "First, head over to the <strong>Properties</strong> app."
msgstr "Per prima cosa, passa sull'app <strong>Proprietà</strong>."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"Fondatore e capo visionario, Tony è la forza trainante dell'azienda. Ama\n"
"                                                                tenersi occupato partecipando allo sviluppo del software,\n"
"                                                                del marketing e delle strategie di customer experience."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"From here, you can also easily create a new rental contract with ease. "
"Simply click on the \"New\" button and enter all the relevant details."
msgstr ""
"Da qui, puoi creare facilmente un nuovo contratto di affitto. Fai clic sul "
"pulsante \"Nuovo\" e inserisci tutti i dettagli rilevanti."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Gallery"
msgstr "Galleria"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_48_product_template
msgid "Gas"
msgstr "Gas"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_guarant_partner_id
msgid "Guarant"
msgstr "Garante"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Habitat Model"
msgstr "Modello di habitat"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Habitats with a minimum footprint on the planet and a maximum positive "
"impact on the local community."
msgstr ""
"Habitat con un'impronta minima sul pianeta e un impatto positivo massimo "
"sulla comunità locale."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Help us protect and preserve for future generations"
msgstr "Aiutaci a proteggere e preservare per le generazioni future"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_12
msgid "Immeuble Bellevue"
msgstr "Palazzo Bellavista"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_8
msgid "Informations"
msgstr "Informazioni"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_invoice_id
msgid "Invoice"
msgstr "Fattura"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_invoice_status
msgid "Invoice Status"
msgstr "Stato fattura"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Invoices"
msgstr "Fatture"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Iris Joe, CFO"
msgstr "Irene Verdi - Direttore finanziario"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Grazie alla sua esperienza internazionale, Irene aiuta a comprendere le "
"cifre e a migliorarle. È determinata ad avere successo mettendo le sue "
"capacità professionali al servizio dell'azienda per aiutarla a progredire. "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_property
msgid "Is Property"
msgstr "È una proprietà"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Join us and make the planet a better place."
msgstr "Unisciti a noi e rendi il pianeta un posto migliore."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Leads get created in the CRM by filling the \"Contact us\" form of the "
"website or manually by the real estate agent."
msgstr ""
"I lead vengono creati nel CRM compilando il modulo \"Contattaci\" dal sito "
"web o manualmente dall'agente immobiliare."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Learn how to use organic gardening methods to grow the freshest food in your"
" fruit and vegetable garden."
msgstr ""
"Impara a utilizzare i metodi del giardinaggio biologico per coltivare gli "
"alimenti più freschi nel tuo orto e frutteto."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Learn more"
msgstr "Scopri di più"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage Leads"
msgstr "Gestione dei lead"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage properties"
msgstr "Gestione delle proprietà"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage rental contracts"
msgstr "Gestione dei contratti di affitto"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_meter_id
msgid "Meter"
msgstr "Contatore"

#. module: industry_real_estate
#: model:ir.model,name:industry_real_estate.model_meter_reading
msgid "Meter Reading"
msgstr "Lettura contatore"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_meter_reading_ids
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_meter_reading_ids
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Meter Readings"
msgstr "Letture contatore"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_configuration_meters
#: model:ir.model,name:industry_real_estate.model_meters
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_meters
msgid "Meters"
msgstr "Contatori"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Mich Stark, COO"
msgstr "Filippo Neri - Direttore operativo"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Filippo ama affrontare le sfide. Grazie alla sua pluriennale esperienza come"
" direttore commerciale nell'industria del software, Filippo ha contributo al"
" successo attuale dell'azienda. È una tra le menti più brillanti."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "My Company"
msgstr "La mia azienda"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Now, in the sub-level form, select the appropriate products."
msgstr "Ora, nel modulo secondario, seleziona i prodotti appropriati."

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_10
msgid "Office"
msgstr "Ufficio"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_23
msgid "Office M"
msgstr "Ufficio M"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_24
msgid "Office S"
msgstr "Ufficio S"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_25
msgid "Office T"
msgstr "Ufficio T"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"One last thing to note: if you don't have any rental contracts for a "
"property yet, you won't see this property in the Gantt view. <br/>"
msgstr ""
"Un'ultima cosa da ricordare: se non hai nessun contratto ancora, non vedrai "
"la proprietà nella vista Gantt. <br/>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Organic Garden"
msgstr "Orto biologico"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Our mission is to provide you with the most modern properties."
msgstr "La nostra missione è di fornirti le proprietà più moderne."

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_10
msgid "Park Station"
msgstr "Park Station"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_43_product_template
msgid "Plumbing"
msgstr "Impianto idraulico"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_price
msgid "Price"
msgstr "Prezzo"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_products
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_products
msgid "Products"
msgstr "Prodotti"

#. module: industry_real_estate
#: model:account.analytic.plan,name:industry_real_estate.analytic_plan_properties
#: model:ir.actions.act_window,name:industry_real_estate.action_properties
#: model:ir.actions.server,name:industry_real_estate.action_properties_server
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_properties
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_root
#: model:ir.ui.menu,name:industry_real_estate.menu_root
#: model:website.menu,name:industry_real_estate.website_menu_properties
msgid "Properties"
msgstr "Proprietà"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_account_analytic_account_id
#: model:ir.model.fields,field_description:industry_real_estate.field_crm_lead_property_id
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_account_analytic_account
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Property"
msgstr "Proprietà"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_doc_ids
msgid "Property Documents"
msgstr "Documenti proprietà"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_image
msgid "Property Image"
msgstr "Immagine proprietà"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_image_ids
msgid "Property Images"
msgstr "Immagini proprietà"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_type
msgid "Property Type"
msgstr "Tipo di proprietà"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_44_product_template
msgid "Provision for fees"
msgstr "Pagamento servizi"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_published
msgid "Published"
msgstr "Pubblicato"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_quantity
msgid "Quantity"
msgstr "Quantità"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Read more"
msgstr "Leggi di più"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Real Estate rental agency"
msgstr "Agenzia immobiliare"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Recycling water"
msgstr "Riciclo dell'acqua"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Recycling water for reuse applications instead of using freshwater supplies "
"can be a water-saving measure."
msgstr ""
"Riciclare l'acqua per applicazioni di riutilizzo invece di utilizzare le "
"riserve di acqua dolce può essere una misura di risparmio idrico."

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_rental_contract_id
msgid "Rental Contract"
msgstr "Contratto d'affitto"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_rental_contracts
#: model:ir.ui.menu,name:industry_real_estate.menu_rental_contracts
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Rental Contracts"
msgstr "Contratti d'affitto"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_rental_start_date
msgid "Rental Start Date"
msgstr "Data inizio noleggio"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_42_product_template
msgid "Rental fee"
msgstr "Canone d'affitto"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_49_product_template
msgid "Repair jobs"
msgstr "Lavori di riparazione"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_sale_order
msgid "Sale Order"
msgstr "Ordine di vendita"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search"
msgstr "Ricerca"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search..."
msgstr "Ricerca..."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Section Subtitle"
msgstr "Sottotitolo sezione"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_45_product_template
msgid "Security deposit "
msgstr "Deposito cauzionale"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: industry_real_estate
#: model:base.automation,name:industry_real_estate.automation_set_usage_meter_reading
msgid "Set Usage in Meter Readings"
msgstr "Configura utilizzo letture contatore"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Shaping our future"
msgstr "Dare forma al futuro"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the customer to whom you want to rent the property and then select "
"the property you want to rent."
msgstr ""
"Indica il cliente a cui vuoi affittare la proprietà per poi selezionare la "
"proprietà corrispondente."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the rental start date and define the rental period. Whether you want"
" it to be monthly, bimonthly, quarterly, or whatever suits your needs, you "
"can customize it. Don't forget to specify the end date of the contract."
msgstr ""
"Specifica la data di inizio dell'affitto e definiscine il periodo. È "
"possibile personalizzarlo, che sia mensile, bimestrale, trimestrale o "
"qualsiasi altra cosa sia adatta alle proprie esigenze. Non dimenticare di "
"specificare la data di scadenza del contratto."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Parti dal cliente: scopri quello che vuole e fornisci la soluzione."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "Submit"
msgstr "Invia"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_form_view
msgid "Tenant"
msgstr "Proprietario"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"This view shows the availability of each of your resource based on the "
"rental contracts."
msgstr ""
"La vista mostra la disponibilità di ognuna delle risorse in base ai "
"contratti d'affitto."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"To create a product, head back to the properties menu and click on \"Products\". Click on \"New\" and give your product a name. Check the \"Recurring\" box and define the product type as \"Service\". In the \"Time based pricing\" tab, define\n"
"            the rental price of the property per period."
msgstr ""
"Per creare un prodotto, torna al menu delle proprietà e fai clic su \"Prodotti\". Fai clic su \"Nuovo\" e dai un nome al prodotto. Spunta la casella \"Ricorrente\" e seleziona \"Servizio\" per il tipo di prodotto. Nella scheda \"Prezzo basato sul tempo\" indica\n"
"            il prezzo dell'affitto della proprietà in base al periodo."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Tony Fred, CEO"
msgstr "Giuseppe Rossi, Amministratore delegato"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_27
msgid "Uccle Observatoire Duplex"
msgstr "Duplex osservatorio di Roma"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_usage
msgid "Usage"
msgstr "Utilizzo"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Vendor Bills"
msgstr "Fatture fornitore"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_9
msgid "Visit"
msgstr "Visita"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_47_product_template
msgid "Water"
msgstr "Acqua"

#. module: industry_real_estate
#: model_terms:web_tour.tour,rainbow_man_message:industry_real_estate.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Benvenuto! Buona visita."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"Con tutti i problemi che il pianeta deve affrontare oggi,<br/> le comunità "
"di persone che devono affrontarli crescono sempre più<br/> per evitare un "
"impatto negativo."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Scrivi uno o due paragrafi che descrivono il prodotto o i servizi. Per avere"
" successo, il contenuto deve essere utile ai lettori. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also have a dedicated tab to fill at the time of customer's entry in the"
" property so that you can log and keep track of meter readings."
msgstr ""
"Inoltre, è disponibile una scheda dedicata da compilare al momento "
"dell'ingresso del cliente nella proprietà, in modo da poter registrare e "
"tenere traccia delle letture dei contatori."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also need to create products for your properties. You have 2 options: "
"either create one product for all your properties and manually change the "
"price when creating the rental contract, or define one product per property."
msgstr ""
"È necessario creare prodotti per le proprietà. Hai due opzioni: creare un "
"prodotto per tutte le proprietà e modificare manualmente il prezzo durante "
"la creazione del contratto di affitto, oppure definire un prodotto per ogni "
"proprietà."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "e.g. Apartment Oxford Street"
msgstr "ad es. Appartamento Via Roma"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "found)"
msgstr "trovato)"
