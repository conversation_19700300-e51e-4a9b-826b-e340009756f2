# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_real_estate
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:45+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "<b>ABOUT US</b>"
msgstr "<b>من نحن</b> "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Modern Real Estate&amp;nbsp;—</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— العقارات الحديثة —</b>\n"
"                                    </font>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">رقم الهاتف</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Property</span>"
msgstr "<span class=\"s_website_form_label_content\">العقارات</span> "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">الموضوع</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">شركتك</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span> "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">بريدك الإلكتروني</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">اسمك</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span> "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">سؤالك</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your long term or mid "
"long term rental properties</span>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your properties, create "
"and manage rental contracts, and streamline your entire <strong>rental "
"process.</strong>  Efficient property management.</span>"
msgstr ""

#. module: industry_real_estate
#: model:website.menu,name:industry_real_estate.website_menu_10
msgid "About Us"
msgstr "من نحن "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "About us"
msgstr "من نحن "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Add Images"
msgstr "إضافة صور "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_address
msgid "Address"
msgstr "العنوان"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Aline Turner, CTO"
msgstr "Aline Turner، المدير التقني"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline هي أحد الأشخاص المميزين في الحياة الذين يحبون عملهم بشغف. تقوم بتدريب "
"أكثر من 100 مطور في العمل وتساعد آلاف المطورين المجتمعيين. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Also define the date of the next invoice. Note that this date will be taken as the template date for all the next invoices. For example, if you define a next date of invoice of 04/30 with a monthly recurring period, the invoices\n"
"            will automatically be created each 30th of the month."
msgstr ""

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_21
msgid "Apartment 26"
msgstr "شقة 26 "

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_20
msgid "Apartment 27"
msgstr "شقة 27 "

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_22
msgid "Apartment 28"
msgstr "شقة 28 "

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_26
msgid "Apartment 29"
msgstr "شقة 29 "

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_availability
#: model:ir.ui.menu,name:industry_real_estate.menu_availability
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_gantt_view
msgid "Availability"
msgstr "التوافر"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_11
#: model:ir.model.fields,field_description:industry_real_estate.field_property_building_1
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_related_building_id
msgid "Building"
msgstr "المبنى "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Business Flows"
msgstr "مراحل سير العمل "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr "من الممكن تغيير العالم.<br/> لقد قمنا بذلك من قبل. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Check Out for availability"
msgstr "تحقق من التوفر "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.view_crm_lead_form_quick_create_inherit
msgid "Choose a property..."
msgstr "اختر عقاراً... "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on \"New\" to create a new rental contract."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Availability menu. All your rental contracts appear on that "
"screen in a neat little Gantt view."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Properties menu. Now, click on \"New\" to create a new "
"property. Make sure you fill in all the required information."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on the Rental Contract menu."
msgstr "اضغط على قائمة عقد االإيجار. "

#. module: industry_real_estate
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_root
msgid "Configuration"
msgstr "التهيئة "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact Us"
msgstr "تواصل معنا"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact us"
msgstr "تواصل معنا "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"لا تتردد في التواصل معنا إذا كان لديك أي استفسار متعلق بشركتنا أو خدماتنا.<br/>\n"
"                                                                        سوف نبذل قصارى جهدنا للتواصل معك في أقرب وقت ممكن. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Create Invoice"
msgstr "إنشاء فاتورة"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_create_invoice_meters
msgid "Create Invoice for Meter Readings"
msgstr "إنشاء فاتورة لقراءات العداد "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_currency
msgid "Currency"
msgstr "العملة"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_date
msgid "Date"
msgstr "التاريخ"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Describe your property here"
msgstr "صف عقارك هنا "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_name
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_reading_description
#: model:ir.model.fields,field_description:industry_real_estate.field_property_description
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Description"
msgstr "الوصف"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Discover more"
msgstr "اكتشف المزيد "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Documents"
msgstr "المستندات"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_46_product_template
msgid "Electricity "
msgstr "الكهرباء "

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_server_set_usage_meter_reading
msgid "Execute Code"
msgstr "تنفيذ الكود "

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_9
msgid "Ferme Saint-Jean"
msgstr "Ferme Saint-Jean"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "First, head over to the <strong>Properties</strong> app."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"توني، المؤسس وصاحب الرؤية، هو القوة الدافعة وراء الشركة. يحب العمل\n"
"                                                                باستمرار عن طريق المشاركة في تطوير البرنامج\n"
"                                                                والتسويق واستراتيجيات تجربة العملاء. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"From here, you can also easily create a new rental contract with ease. "
"Simply click on the \"New\" button and enter all the relevant details."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Gallery"
msgstr "صالة عرض"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_48_product_template
msgid "Gas"
msgstr "الغاز "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_guarant_partner_id
msgid "Guarant"
msgstr "Guarant"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Habitat Model"
msgstr "نموذج البيئة "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Habitats with a minimum footprint on the planet and a maximum positive "
"impact on the local community."
msgstr ""
"البيئات التي لها أقل تأثير على الكوكب وأكبر تأثير الإيجابي على المجتمع "
"المحلي. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Help us protect and preserve for future generations"
msgstr "ساعدنا على حمايتها والحفاظ عليها للأجيال القادمة "

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_12
msgid "Immeuble Bellevue"
msgstr "Immeuble Bellevue"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_8
msgid "Informations"
msgstr "المعلومات "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_invoice_id
msgid "Invoice"
msgstr "الفاتورة"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_invoice_status
msgid "Invoice Status"
msgstr "حالة الفاتورة"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Invoices"
msgstr "فواتير العملاء "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Iris Joe, CFO"
msgstr "Iris Joe، المدير المالي"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"تُسخّر Iris خبرتها العالمية لمساعدتنا على فهم الأرقام وتحسينها، وهي مصممة "
"على أن تحفزنا على النجاح وإيصال ذكائها المهني لتصعد بالشركة إلى المستوى "
"التالي. "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_property
msgid "Is Property"
msgstr "عقار "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Join us and make the planet a better place."
msgstr "انضم إلينا واجعل كوكبنا مكاناً أفضل. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Leads get created in the CRM by filling the \"Contact us\" form of the "
"website or manually by the real estate agent."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Learn how to use organic gardening methods to grow the freshest food in your"
" fruit and vegetable garden."
msgstr ""
"تعلم كيفية استخدام أساليب البستنة العضوية لزراعة الأطعمة الطازجة في حديقة "
"الفاكهة والخضروات الخاصة بك. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Learn more"
msgstr "اعرف المزيد "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage Leads"
msgstr "إدارة العملاء المهتمين "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage properties"
msgstr "إضافة الخصائص "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage rental contracts"
msgstr "إدارة عقود التأجير "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_meter_id
msgid "Meter"
msgstr "العداد "

#. module: industry_real_estate
#: model:ir.model,name:industry_real_estate.model_meter_reading
msgid "Meter Reading"
msgstr "قراءة العداد "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_meter_reading_ids
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_meter_reading_ids
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Meter Readings"
msgstr "قراءات العداد "

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_configuration_meters
#: model:ir.model,name:industry_real_estate.model_meters
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_meters
msgid "Meters"
msgstr "العدادات "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Mich Stark, COO"
msgstr "Mich Stark، مدير العمليات"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"يحب Mich أن يواجه التحديات التي تعترض طريقه، وبالاستعانة بخبرته الطويلة "
"كمدير تجاري في مجال التكنولوجيا والبرمجيات، تمكن Mich من مساعدة الشركة على "
"الوصول إلى ما هي عليه الآن. إنه حقاً أحد العقول النيّرة. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "My Company"
msgstr "شركتي "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Now, in the sub-level form, select the appropriate products."
msgstr ""

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_10
msgid "Office"
msgstr "المكتب "

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_23
msgid "Office M"
msgstr "مكتب M "

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_24
msgid "Office S"
msgstr "مكتب S "

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_25
msgid "Office T"
msgstr "مكتب T "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"One last thing to note: if you don't have any rental contracts for a "
"property yet, you won't see this property in the Gantt view. <br/>"
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Organic Garden"
msgstr "حديقة عضوية "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Our mission is to provide you with the most modern properties."
msgstr "مهمتنا هي أن نقدم لك أكثر العقارات عصرية. "

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_10
msgid "Park Station"
msgstr "مواقف "

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_43_product_template
msgid "Plumbing"
msgstr "السباكة "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_price
msgid "Price"
msgstr "السعر"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_products
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_products
msgid "Products"
msgstr "المنتجات"

#. module: industry_real_estate
#: model:account.analytic.plan,name:industry_real_estate.analytic_plan_properties
#: model:ir.actions.act_window,name:industry_real_estate.action_properties
#: model:ir.actions.server,name:industry_real_estate.action_properties_server
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_properties
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_root
#: model:ir.ui.menu,name:industry_real_estate.menu_root
#: model:website.menu,name:industry_real_estate.website_menu_properties
msgid "Properties"
msgstr "الخصائص "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_account_analytic_account_id
#: model:ir.model.fields,field_description:industry_real_estate.field_crm_lead_property_id
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_account_analytic_account
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Property"
msgstr "العقار "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_doc_ids
msgid "Property Documents"
msgstr "مستندات العقار "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_image
msgid "Property Image"
msgstr "صورة العقار "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_image_ids
msgid "Property Images"
msgstr "صور العقار "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_type
msgid "Property Type"
msgstr "نوع العقار "

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_44_product_template
msgid "Provision for fees"
msgstr "أحكام الرسوم "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_published
msgid "Published"
msgstr "تم النشر "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_quantity
msgid "Quantity"
msgstr "الكمية"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Read more"
msgstr "قراءة المزيد"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Real Estate rental agency"
msgstr "وكالة تأجير العقارات "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Recycling water"
msgstr "إعادة تدوير الماء "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Recycling water for reuse applications instead of using freshwater supplies "
"can be a water-saving measure."
msgstr ""
"يمكن أن تكون إعادة تدوير المياه لتطبيقات إعادة الاستخدام عوضاً عن استخدام "
"إمدادات المياه العذبة إجراءً لتوفير المياه. "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_rental_contract_id
msgid "Rental Contract"
msgstr "عقد الإيجار "

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_rental_contracts
#: model:ir.ui.menu,name:industry_real_estate.menu_rental_contracts
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Rental Contracts"
msgstr "عقود الإيجار "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_rental_start_date
msgid "Rental Start Date"
msgstr "تاريخ بدء التأجير "

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_42_product_template
msgid "Rental fee"
msgstr "رسوم الإيجار "

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_49_product_template
msgid "Repair jobs"
msgstr "عمليات التصليح "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_sale_order
msgid "Sale Order"
msgstr "أمر البيع"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search"
msgstr "بحث"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search..."
msgstr "بحث..."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Section Subtitle"
msgstr "عنوان فرعي للقسم"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_45_product_template
msgid "Security deposit "
msgstr "مبلغ التأمين "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: industry_real_estate
#: model:base.automation,name:industry_real_estate.automation_set_usage_meter_reading
msgid "Set Usage in Meter Readings"
msgstr "تعيين الاستخدام في قراءات العداد "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Shaping our future"
msgstr "نسعى لتشكيل مستقبلنا "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the customer to whom you want to rent the property and then select "
"the property you want to rent."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the rental start date and define the rental period. Whether you want"
" it to be monthly, bimonthly, quarterly, or whatever suits your needs, you "
"can customize it. Don't forget to specify the end date of the contract."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Start with the customer – find out what they want and give it to them."
msgstr "ابدأ بالعميل - تعرّف على ما يريده العميل وامنحه إياه. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "Submit"
msgstr "إرسال"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_form_view
msgid "Tenant"
msgstr "المستأجر "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"This view shows the availability of each of your resource based on the "
"rental contracts."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"To create a product, head back to the properties menu and click on \"Products\". Click on \"New\" and give your product a name. Check the \"Recurring\" box and define the product type as \"Service\". In the \"Time based pricing\" tab, define\n"
"            the rental price of the property per period."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Tony Fred, CEO"
msgstr "Tony Fred، المدير التنفيذي "

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_27
msgid "Uccle Observatoire Duplex"
msgstr "Uccle Observatoire Duplex "

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_usage
msgid "Usage"
msgstr "الاستخدام "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Vendor Bills"
msgstr "فواتير المورد"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_9
msgid "Visit"
msgstr "زيارة "

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_47_product_template
msgid "Water"
msgstr "ماء"

#. module: industry_real_estate
#: model_terms:web_tour.tour,rainbow_man_message:industry_real_estate.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "أهلاً وسهلاً بك! نتمنى لك رحلة استكشافية سعيدة. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"مع كل المشاكل العالمية التي يواجهها كوكبنا اليوم،<br/> تتزايد مجتمعات "
"الأشخاص المعنيين بها<br/> لمنع التأثير السلبي. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"اكتب فقرة أو اثنتين تصف فيها منتجاتك أو خدماتك. حتى تكون ناجحاً، يجب أن يكون"
" محتواك ذا فائدة لقرائك. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also have a dedicated tab to fill at the time of customer's entry in the"
" property so that you can log and keep track of meter readings."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also need to create products for your properties. You have 2 options: "
"either create one product for all your properties and manually change the "
"price when creating the rental contract, or define one product per property."
msgstr ""

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "e.g. Apartment Oxford Street"
msgstr "مثال: شقة في شارع أكسفورد "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "found)"
msgstr "النتائج)"
