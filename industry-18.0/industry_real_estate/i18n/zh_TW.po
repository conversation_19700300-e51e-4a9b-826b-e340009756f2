# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_real_estate
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>doo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:45+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "<b>ABOUT US</b>"
msgstr "<b>關於我們</b>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Modern Real Estate&amp;nbsp;—</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— 現代化房地產 —</b>\n"
"                                    </font>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">電話號碼</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Property</span>"
msgstr "<span class=\"s_website_form_label_content\">物業</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">主題</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">你的公司/機構名稱</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">你的電郵</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">你的名字</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">你的查詢內容</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *（必填）</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your long term or mid "
"long term rental properties</span>"
msgstr "<span class=\"text-o-color-5 bg-o-color-4\">管理你的長期或中長期租賃物業</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your properties, create "
"and manage rental contracts, and streamline your entire <strong>rental "
"process.</strong>  Efficient property management.</span>"
msgstr ""
"<span class=\"text-o-color-5 bg-o-"
"color-4\">管理物業，建立及管理租賃合約，為你簡化<strong>整個租賃流程</strong>，盡享高效率物業管理。</span>"

#. module: industry_real_estate
#: model:website.menu,name:industry_real_estate.website_menu_10
msgid "About Us"
msgstr "關於我們"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "About us"
msgstr "關於我們"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Add Images"
msgstr "加入圖片"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_address
msgid "Address"
msgstr "地址"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Aline Turner, CTO"
msgstr "Aline Turner，技術總監"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr "Aline 是日常生活中可以自認熱愛自己所做事情的那種標誌人物。她指導 100 多名內部開發人員，並照顧數千名開發人員的社群。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Also define the date of the next invoice. Note that this date will be taken as the template date for all the next invoices. For example, if you define a next date of invoice of 04/30 with a monthly recurring period, the invoices\n"
"            will automatically be created each 30th of the month."
msgstr ""
"另外，也要定義下次發票的日期。請留意，此日期將用作所有後續發票的範本日期。舉例，若將下次發票日期定義為 4 月 30 日，而重複周期設為每月，\n"
"            其後的發票將在每月 30 日自動建立。"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_21
msgid "Apartment 26"
msgstr "單位 26"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_20
msgid "Apartment 27"
msgstr "單位 27"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_22
msgid "Apartment 28"
msgstr "單位 28"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_26
msgid "Apartment 29"
msgstr "單位 29"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_availability
#: model:ir.ui.menu,name:industry_real_estate.menu_availability
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_gantt_view
msgid "Availability"
msgstr "可用"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_11
#: model:ir.model.fields,field_description:industry_real_estate.field_property_building_1
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_related_building_id
msgid "Building"
msgstr "大廈"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Business Flows"
msgstr "業務流程"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr "改變世界是可能的。<br/>我們曾經做到過。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Check Out for availability"
msgstr "查看可用性"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.view_crm_lead_form_quick_create_inherit
msgid "Choose a property..."
msgstr "選擇物業⋯"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on \"New\" to create a new rental contract."
msgstr "按一下「新增」，以建立新的租賃合約。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Availability menu. All your rental contracts appear on that "
"screen in a neat little Gantt view."
msgstr "按一下「可用項目」選單，畫面會顯示所有租賃合約，全部以簡潔的甘特圖檢視畫面整理。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Properties menu. Now, click on \"New\" to create a new "
"property. Make sure you fill in all the required information."
msgstr "點選「物業」選單。現在，按一下「新增」以建立新物業。記得填寫所有必填資料。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on the Rental Contract menu."
msgstr "按一下租賃合約選單。"

#. module: industry_real_estate
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_root
msgid "Configuration"
msgstr "配置"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact Us"
msgstr "聯絡我們"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact us"
msgstr "聯絡我們"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"如欲查詢本公司或我們服務的任何資料，歡迎聯絡我們。<br/>\n"
"                                                                        我們會盡力盡早回覆。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Create Invoice"
msgstr "建立發票"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_create_invoice_meters
msgid "Create Invoice for Meter Readings"
msgstr "為抄錶讀數建立發票"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_currency
msgid "Currency"
msgstr "貨幣"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_date
msgid "Date"
msgstr "日期"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Describe your property here"
msgstr "請描述物業詳情"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_name
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_reading_description
#: model:ir.model.fields,field_description:industry_real_estate.field_property_description
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Description"
msgstr "說明"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Discover more"
msgstr "發現更多"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Documents"
msgstr "文件"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_46_product_template
msgid "Electricity "
msgstr "電力"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_server_set_usage_meter_reading
msgid "Execute Code"
msgstr "執行程式碼"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_9
msgid "Ferme Saint-Jean"
msgstr "阿里山區域"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "First, head over to the <strong>Properties</strong> app."
msgstr "首先，前往<strong>物業</strong>應用程式。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"作為創辦人兼首席遠見者，Tony 是公司背後的推動力。\n"
"                                                                他喜歡參與軟件開發、\n"
"                                                                推廣和客戶體驗策略，來保持忙碌。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"From here, you can also easily create a new rental contract with ease. "
"Simply click on the \"New\" button and enter all the relevant details."
msgstr "在此處，你亦可輕鬆建立新的租賃合約。只需按一下「新增」按鈕，然後輸入所有相關詳細資訊即可。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Gallery"
msgstr "相片集"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_48_product_template
msgid "Gas"
msgstr "燃氣"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_guarant_partner_id
msgid "Guarant"
msgstr "擔保人"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Habitat Model"
msgstr "生境模型"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Habitats with a minimum footprint on the planet and a maximum positive "
"impact on the local community."
msgstr "對地球影響最小、對當地社區的積極影響最大的棲息地."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Help us protect and preserve for future generations"
msgstr "幫助我們為子孫後代保護及保育"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_12
msgid "Immeuble Bellevue"
msgstr "公寓大廈"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_8
msgid "Informations"
msgstr "資訊"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_invoice_id
msgid "Invoice"
msgstr "發票"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_invoice_status
msgid "Invoice Status"
msgstr "發票狀態"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Invoices"
msgstr "發票"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Iris Joe, CFO"
msgstr "Iris Joe，財務長"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr "Iris 憑藉她的國際經驗，幫助我們輕鬆理解這些數字，並改善業績。她決心推動成功，並發揮自己的專業智慧，使公司更上一層樓。"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_property
msgid "Is Property"
msgstr "是物業"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Join us and make the planet a better place."
msgstr "加入我們，讓地球變得更美好。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Leads get created in the CRM by filling the \"Contact us\" form of the "
"website or manually by the real estate agent."
msgstr "網站訪客填寫「聯絡我們」表單時，系統會在客戶關係應用程式中，建立潛在客戶記錄，也可由物業代理手動建立。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Learn how to use organic gardening methods to grow the freshest food in your"
" fruit and vegetable garden."
msgstr "學習如何使用有機園藝方法，在蔬果園種出最新鮮的食物。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Learn more"
msgstr "了解更多"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage Leads"
msgstr "管理潛在客戶"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage properties"
msgstr "管理物業"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage rental contracts"
msgstr "管理租約"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_meter_id
msgid "Meter"
msgstr "讀數錶"

#. module: industry_real_estate
#: model:ir.model,name:industry_real_estate.model_meter_reading
msgid "Meter Reading"
msgstr "抄錶讀數"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_meter_reading_ids
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_meter_reading_ids
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Meter Readings"
msgstr "抄錶讀數"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_configuration_meters
#: model:ir.model,name:industry_real_estate.model_meters
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_meters
msgid "Meters"
msgstr "讀數錶"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Mich Stark, COO"
msgstr "Mich Stark，營運總監"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr "Mich 喜歡接受挑戰。 憑藉在軟件行業擔任商務總監的多年經驗，Mich 幫助公司取得了今天的成就。 Mich 是最聰明的人之一。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "My Company"
msgstr "我的公司"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Now, in the sub-level form, select the appropriate products."
msgstr "現在，於子級表單中，選擇適當的產品。"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_10
msgid "Office"
msgstr "辦公室"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_23
msgid "Office M"
msgstr "辦公室 M"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_24
msgid "Office S"
msgstr "辦公室 S"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_25
msgid "Office T"
msgstr "辦公室 T"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"One last thing to note: if you don't have any rental contracts for a "
"property yet, you won't see this property in the Gantt view. <br/>"
msgstr "最後要注意：如果物業仍未有任何租賃合約，甘特圖檢視畫面將不會顯示該物業。<br/>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Organic Garden"
msgstr "有機花園"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Our mission is to provide you with the most modern properties."
msgstr "我們的使命是為你提供最時尚的房產。"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_10
msgid "Park Station"
msgstr "公園車站"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_43_product_template
msgid "Plumbing"
msgstr "渠管"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_price
msgid "Price"
msgstr "價格"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_products
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_products
msgid "Products"
msgstr "產品"

#. module: industry_real_estate
#: model:account.analytic.plan,name:industry_real_estate.analytic_plan_properties
#: model:ir.actions.act_window,name:industry_real_estate.action_properties
#: model:ir.actions.server,name:industry_real_estate.action_properties_server
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_properties
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_root
#: model:ir.ui.menu,name:industry_real_estate.menu_root
#: model:website.menu,name:industry_real_estate.website_menu_properties
msgid "Properties"
msgstr "屬性"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_account_analytic_account_id
#: model:ir.model.fields,field_description:industry_real_estate.field_crm_lead_property_id
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_account_analytic_account
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Property"
msgstr "物業"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_doc_ids
msgid "Property Documents"
msgstr "物業文件"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_image
msgid "Property Image"
msgstr "物業圖片"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_image_ids
msgid "Property Images"
msgstr "物業圖片"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_type
msgid "Property Type"
msgstr "物業類型"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_44_product_template
msgid "Provision for fees"
msgstr "費用按金/準備金"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_published
msgid "Published"
msgstr "已發佈"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_quantity
msgid "Quantity"
msgstr "數量"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Read more"
msgstr "看更多"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Real Estate rental agency"
msgstr "房地產租賃代理公司"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Recycling water"
msgstr "循環用水"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Recycling water for reuse applications instead of using freshwater supplies "
"can be a water-saving measure."
msgstr "將水回收再利用，取代使用淡水供應，可以是一項節水措施。"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_rental_contract_id
msgid "Rental Contract"
msgstr "租賃合約"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_rental_contracts
#: model:ir.ui.menu,name:industry_real_estate.menu_rental_contracts
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Rental Contracts"
msgstr "租賃合約"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_rental_start_date
msgid "Rental Start Date"
msgstr "租賃開始日期"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_42_product_template
msgid "Rental fee"
msgstr "租金費用"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_49_product_template
msgid "Repair jobs"
msgstr "維修工作"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_sale_order
msgid "Sale Order"
msgstr "銷售訂單"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search"
msgstr "搜尋"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search..."
msgstr "搜尋⋯"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Section Subtitle"
msgstr "段落子標題"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_45_product_template
msgid "Security deposit "
msgstr "保證金/按金"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_sequence
msgid "Sequence"
msgstr "序列號"

#. module: industry_real_estate
#: model:base.automation,name:industry_real_estate.automation_set_usage_meter_reading
msgid "Set Usage in Meter Readings"
msgstr "設定抄錶讀數用量"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Shaping our future"
msgstr "塑造我們的未來"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the customer to whom you want to rent the property and then select "
"the property you want to rent."
msgstr "指定你想出租物業給哪個客戶，然後選擇要出租的物業。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the rental start date and define the rental period. Whether you want"
" it to be monthly, bimonthly, quarterly, or whatever suits your needs, you "
"can customize it. Don't forget to specify the end date of the contract."
msgstr "設定租賃開始日期，以及租賃期長度。無論是想以每月、每兩月、每季度計租，還是其他適合你需要的設定，都可以自訂。記得設定租賃合約的結束日期。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Start with the customer – find out what they want and give it to them."
msgstr "從客戶角度出發：找出他們想要甚麼，並盡量配合提供。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "Submit"
msgstr "提交"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_form_view
msgid "Tenant"
msgstr "租戶"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"This view shows the availability of each of your resource based on the "
"rental contracts."
msgstr "此檢視模式會根據租賃合約資料，顯示每項資源的可用性。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"To create a product, head back to the properties menu and click on \"Products\". Click on \"New\" and give your product a name. Check the \"Recurring\" box and define the product type as \"Service\". In the \"Time based pricing\" tab, define\n"
"            the rental price of the property per period."
msgstr ""
"要建立產品，請返回屬性選單，然後按一下「產品」。按一下「新增」，並為產品命名，之後勾選「重複」方格，並將產品類型設為「服務」。在「按工時定價」分頁中，\n"
"            你可定義不同期間的物業租賃價格。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Tony Fred, CEO"
msgstr "Tony Fred，執行長"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_27
msgid "Uccle Observatoire Duplex"
msgstr "台北 101 區域複式公寓"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_usage
msgid "Usage"
msgstr "用途"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Vendor Bills"
msgstr "供應商賬單"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_9
msgid "Visit"
msgstr "到訪"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_47_product_template
msgid "Water"
msgstr "水"

#. module: industry_real_estate
#: model_terms:web_tour.tour,rainbow_man_message:industry_real_estate.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "歡迎！請享受探索過程。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr "對於當今地球面對的所有全球性問題，<br/>關心這些問題的社群正在不斷壯大，<br/>以防止問題的負面影響。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr "寫一兩段文字，描述你的產品或服務。成功的內容必須對讀者有用。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also have a dedicated tab to fill at the time of customer's entry in the"
" property so that you can log and keep track of meter readings."
msgstr "客戶入伙時，你也可在專用的分頁填寫資料，方便你記錄及追蹤抄錶讀數。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also need to create products for your properties. You have 2 options: "
"either create one product for all your properties and manually change the "
"price when creating the rental contract, or define one product per property."
msgstr ""
"你亦需要為各項物業，建立「產品」記錄。可以有 2 個選擇：為所有物業建立單一項產品，然後在建立租賃合約時，手動更改價格；或者，為每項物業建立單獨的產品。"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "e.g. Apartment Oxford Street"
msgstr "例：忠孝東路公寓"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "found)"
msgstr "已找到）"
