# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_real_estate
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:45+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "<b>ABOUT US</b>"
msgstr "<b>GIỚI THIỆU</b>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Modern Real Estate&amp;nbsp;—</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Modern Real Estate&amp;nbsp;—</b>\n"
"                                    </font>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                                <span><EMAIL></span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                                <span class=\"o_force_ltr\">+****************</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Số điện thoại</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "<span class=\"s_website_form_label_content\">Property</span>"
msgstr "<span class=\"s_website_form_label_content\">Bất động sản</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tiêu đề</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Company</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Công ty</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tên</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Câu hỏi</span>\n"
"                                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your long term or mid "
"long term rental properties</span>"
msgstr ""
"<span class=\"text-o-color-5 bg-o-color-4\">Quản lý bất động sản cho thuê "
"dài hạn hoặc trung hạn của bạn</span>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"<span class=\"text-o-color-5 bg-o-color-4\">Manage your properties, create "
"and manage rental contracts, and streamline your entire <strong>rental "
"process.</strong>  Efficient property management.</span>"
msgstr ""
"<span class=\"text-o-color-5 bg-o-color-4\">Quản lý bất động sản của bạn, "
"tạo và quản lý hợp đồng cho thuê và hợp lý hóa toàn bộ <strong>quy trình cho"
" thuê</strong>. Quản lý bất động sản hiệu quả.</span>"

#. module: industry_real_estate
#: model:website.menu,name:industry_real_estate.website_menu_10
msgid "About Us"
msgstr "Giới thiệu"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "About us"
msgstr "Về chúng tôi"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Add Images"
msgstr "Thêm hình ảnh"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_address
msgid "Address"
msgstr "Địa chỉ"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline là một trong những hình mẫu yêu mến công việc tiêu biểu trong cuộc "
"sống. Cô ấy cố vấn cho hơn 100 lập trình viên nội bộ và hỗ trợ cộng đồng "
"hàng nghìn lập trình viên."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Also define the date of the next invoice. Note that this date will be taken as the template date for all the next invoices. For example, if you define a next date of invoice of 04/30 with a monthly recurring period, the invoices\n"
"            will automatically be created each 30th of the month."
msgstr ""
"Cũng xác định ngày của hóa đơn tiếp theo. Lưu ý rằng ngày này sẽ được coi là ngày mẫu cho tất cả hóa đơn tiếp theo. Ví dụ, nếu bạn xác định ngày hóa đơn tiếp theo là 04/30 với chu kỳ định kỳ hàng tháng, thì hóa đơn\n"
"sẽ tự động được tạo vào ngày 30 hàng tháng."

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_21
msgid "Apartment 26"
msgstr "Căn hộ 26"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_20
msgid "Apartment 27"
msgstr "Căn hộ 27"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_22
msgid "Apartment 28"
msgstr "Căn hộ 28"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_26
msgid "Apartment 29"
msgstr "Căn hộ 29"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_availability
#: model:ir.ui.menu,name:industry_real_estate.menu_availability
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_gantt_view
msgid "Availability"
msgstr "Tình trạng còn hàng"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_11
#: model:ir.model.fields,field_description:industry_real_estate.field_property_building_1
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_related_building_id
msgid "Building"
msgstr "Toà nhà"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Business Flows"
msgstr "Chu trình kinh doanh"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr ""
"Thay đổi thế giới là điều có thể,<br/> vì chúng tôi đã từng làm được điều "
"đó."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Check Out for availability"
msgstr "Kiểm tra tình trạng còn hàng"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.view_crm_lead_form_quick_create_inherit
msgid "Choose a property..."
msgstr "Chọn một bất động sản..."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on \"New\" to create a new rental contract."
msgstr "Nhấp vào \"Mới\" để tạo hợp đồng cho thuê mới."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Availability menu. All your rental contracts appear on that "
"screen in a neat little Gantt view."
msgstr ""
"Nhấp vào menu Trình trạng còn hàng. Tất cả hợp đồng cho thuê của bạn sẽ xuất"
" hiện trên màn hình đó theo chế độ xem Gantt nhỏ gọn."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Click on the Properties menu. Now, click on \"New\" to create a new "
"property. Make sure you fill in all the required information."
msgstr ""
"Nhấp vào menu Bất động sản. Bây giờ, nhấp vào \"Mới\" để tạo một bất động "
"sản mới. Hãy nhớ điền đầy đủ thông tin bắt buộc."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Click on the Rental Contract menu."
msgstr "Nhấp vào menu Cho thuê."

#. module: industry_real_estate
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_root
msgid "Configuration"
msgstr "Cấu hình"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact Us"
msgstr "Liên hệ với chúng tôi"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Contact us"
msgstr "Liên hệ"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                                                        We'll do our best to get back to you as soon as possible."
msgstr ""
"Hãy liên hệ khi bạn gặp bất cứ vấn đề nào liên quan đến công ty hoặc dịch vụ của chúng tôi.<br/>\n"
"                                    Chúng tôi sẽ cố gắng phản hồi trong thời gian sớm nhất."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Create Invoice"
msgstr "Tạo hoá đơn"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_create_invoice_meters
msgid "Create Invoice for Meter Readings"
msgstr "Tạo hoá đơn cho số công tơ"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_currency
msgid "Currency"
msgstr "Tiền tệ"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_date
msgid "Date"
msgstr "Ngày"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Describe your property here"
msgstr "Mô tả bất động sản của bạn tại đây"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_name
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_reading_description
#: model:ir.model.fields,field_description:industry_real_estate.field_property_description
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Description"
msgstr "Mô tả"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Discover more"
msgstr "Khám phá thêm"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Documents"
msgstr "Tài liệu"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_46_product_template
msgid "Electricity "
msgstr "Điện"

#. module: industry_real_estate
#: model:ir.actions.server,name:industry_real_estate.action_server_set_usage_meter_reading
msgid "Execute Code"
msgstr "Thực thi mã"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_9
msgid "Ferme Saint-Jean"
msgstr "Ferme Saint-Jean"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "First, head over to the <strong>Properties</strong> app."
msgstr "Đầu tiên, hãy vào ứng dụng <strong>Bất động sản</strong>."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"Là nhà sáng lập và có tầm nhìn xa trông rộng, Tony là động lực phát triển của công ty. Anh\n"
"                              thích tạo sự bận rộn cho bản thân bằng cách tham gia phát triển phần mềm,\n"
"                              marketing, và các chiến lược về trải nghiệm khách hàng. "

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"From here, you can also easily create a new rental contract with ease. "
"Simply click on the \"New\" button and enter all the relevant details."
msgstr ""
"Từ đây, bạn cũng có thể dễ dàng tạo hợp đồng cho thuê mới. Chỉ cần nhấp vào "
"nút \"Mới\" và nhập tất cả các thông tin liên quan."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Gallery"
msgstr "Thư viện"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_48_product_template
msgid "Gas"
msgstr "Khí đốt"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_guarant_partner_id
msgid "Guarant"
msgstr "Bên bảo lãnh"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Habitat Model"
msgstr "Habitat Model"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Habitats with a minimum footprint on the planet and a maximum positive "
"impact on the local community."
msgstr ""
"Habitats with a minimum footprint on the planet and a maximum positive "
"impact on the local community."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Help us protect and preserve for future generations"
msgstr "Giúp chúng tôi bảo vệ và gìn giữ cho các thế hệ tương lai"

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_12
msgid "Immeuble Bellevue"
msgstr "Immeuble Bellevue"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_8
msgid "Informations"
msgstr "Thông tin"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_invoice_id
msgid "Invoice"
msgstr "Hóa đơn"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_invoice_status
msgid "Invoice Status"
msgstr "Trạng thái hóa đơn"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Invoices"
msgstr "Hóa đơn"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Iris, với kinh nghiệm quốc tế của mình, giúp chúng tôi dễ dàng hiểu các con "
"số và cải thiện chúng. Cô quyết tâm thúc đẩy thành công và thể hiện sự nhạy "
"bén trong nghề nghiệp của mình để đưa công ty phát triển lên một tầm cao "
"mới."

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_property
msgid "Is Property"
msgstr "Là bất động sản"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Join us and make the planet a better place."
msgstr ""
"Hãy tham gia cùng chúng tôi và biến hành tinh này thành một nơi tốt đẹp hơn."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Leads get created in the CRM by filling the \"Contact us\" form of the "
"website or manually by the real estate agent."
msgstr ""
"Lead được tạo trong CRM bằng cách điền vào biểu mẫu \"Liên hệ với chúng "
"tôi\" trên trang web hoặc do chuyên viên sales điền thủ công."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Learn how to use organic gardening methods to grow the freshest food in your"
" fruit and vegetable garden."
msgstr ""
"Learn how to use organic gardening methods to grow the freshest food in your"
" fruit and vegetable garden."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Learn more"
msgstr "Tìm hiểu thêm"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage Leads"
msgstr "Quản lý lead"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage properties"
msgstr "Quản lý bất động sản"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Manage rental contracts"
msgstr "Quản lý hợp đồng cho thuê"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_meter_id
msgid "Meter"
msgstr "Công tơ"

#. module: industry_real_estate
#: model:ir.model,name:industry_real_estate.model_meter_reading
msgid "Meter Reading"
msgstr "Số công tơ"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_meter_reading_ids
#: model:ir.model.fields,field_description:industry_real_estate.field_sale_order_meter_reading_ids
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Meter Readings"
msgstr "Số công tơ"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_configuration_meters
#: model:ir.model,name:industry_real_estate.model_meters
#: model:ir.ui.menu,name:industry_real_estate.menu_configuration_meters
msgid "Meters"
msgstr "Công tơ"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich rất thích thử thách. Với kinh nghiệm nhiều năm làm Giám đốc Thương mại "
"trong ngành phần mềm, Mich đã giúp công ty có được vị trí như ngày hôm nay. "
"Mich là một trong số những người tài giỏi nhất."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "My Company"
msgstr "My Company"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Now, in the sub-level form, select the appropriate products."
msgstr "Bây giờ, trong biểu mẫu cấp độ phụ, hãy chọn sản phẩm phù hợp."

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_10
msgid "Office"
msgstr "Văn phòng"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_23
msgid "Office M"
msgstr "Văn phòng M"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_24
msgid "Office S"
msgstr "Văn phòng S"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_25
msgid "Office T"
msgstr "Văn phòng T"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"One last thing to note: if you don't have any rental contracts for a "
"property yet, you won't see this property in the Gantt view. <br/>"
msgstr ""
"Một điều cuối cùng cần lưu ý: nếu bạn chưa có hợp đồng cho thuê bất động sản"
" nào, bạn sẽ không thấy bất động sản này trong chế độ xem Gantt. <br/>"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Organic Garden"
msgstr "Organic Garden"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Our mission is to provide you with the most modern properties."
msgstr ""
"Sứ mệnh của chúng tôi là cung cấp cho bạn những bất động sản hiện đại nhất."

#. module: industry_real_estate
#: model:account.account.tag,name:industry_real_estate.account_account_tag_10
msgid "Park Station"
msgstr "Bãi đỗ xe"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_43_product_template
msgid "Plumbing"
msgstr "Hệ thống ống nước"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_price
msgid "Price"
msgstr "Giá"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_products
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_products
msgid "Products"
msgstr "Sản phẩm"

#. module: industry_real_estate
#: model:account.analytic.plan,name:industry_real_estate.analytic_plan_properties
#: model:ir.actions.act_window,name:industry_real_estate.action_properties
#: model:ir.actions.server,name:industry_real_estate.action_properties_server
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_properties
#: model:ir.ui.menu,name:industry_real_estate.menu_properties_root
#: model:ir.ui.menu,name:industry_real_estate.menu_root
#: model:website.menu,name:industry_real_estate.website_menu_properties
msgid "Properties"
msgstr "Thuộc tính"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_account_analytic_account_id
#: model:ir.model.fields,field_description:industry_real_estate.field_crm_lead_property_id
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_account_analytic_account
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Property"
msgstr "Bất động sản"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_doc_ids
msgid "Property Documents"
msgstr "Tài liệu bất động sản"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_image
msgid "Property Image"
msgstr "Hình ảnh bất động sản"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_attachment_image_ids
msgid "Property Images"
msgstr "Hình ảnh bất động sản"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_type
msgid "Property Type"
msgstr "Loại bất động sản"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_44_product_template
msgid "Provision for fees"
msgstr "Dự phòng cho chi phí"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_is_published
msgid "Published"
msgstr "Đã đăng"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_quantity
msgid "Quantity"
msgstr "Số lượng"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Read more"
msgstr "Xem thêm"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid "Real Estate rental agency"
msgstr "Văn Phòng Cho Thuê Bất Động Sản"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid "Recycling water"
msgstr "Recycling water"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
msgid ""
"Recycling water for reuse applications instead of using freshwater supplies "
"can be a water-saving measure."
msgstr ""
"Recycling water for reuse applications instead of using freshwater supplies "
"can be a water-saving measure."

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_property_rental_contract_id
msgid "Rental Contract"
msgstr "Hợp đồng cho thuê"

#. module: industry_real_estate
#: model:ir.actions.act_window,name:industry_real_estate.action_rental_contracts
#: model:ir.ui.menu,name:industry_real_estate.menu_rental_contracts
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Rental Contracts"
msgstr "Hợp đồng cho thuê"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_rental_start_date
msgid "Rental Start Date"
msgstr "Ngày bắt đầu cho thuê"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_42_product_template
msgid "Rental fee"
msgstr "Phí cho thuê"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_49_product_template
msgid "Repair jobs"
msgstr "Công tác sửa chữa"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_sale_order
msgid "Sale Order"
msgstr "Đơn bán hàng"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search"
msgstr "Tìm kiếm"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "Search..."
msgstr "Tìm kiếm..."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Section Subtitle"
msgstr "Mục phụ đề"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_45_product_template
msgid "Security deposit "
msgstr "Tiền đặt cọc bảo đảm"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meters_sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: industry_real_estate
#: model:base.automation,name:industry_real_estate.automation_set_usage_meter_reading
msgid "Set Usage in Meter Readings"
msgstr "Thiết lập sử dụng trong số công tơ"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Shaping our future"
msgstr "Định hình tương lai"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the customer to whom you want to rent the property and then select "
"the property you want to rent."
msgstr ""
"Chỉ định khách hàng mà bạn muốn cho thuê bất động sản và sau đó chọn bất "
"động sản bạn muốn cho thuê."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"Specify the rental start date and define the rental period. Whether you want"
" it to be monthly, bimonthly, quarterly, or whatever suits your needs, you "
"can customize it. Don't forget to specify the end date of the contract."
msgstr ""
"Chỉ định ngày bắt đầu thuê và xác định thời hạn thuê. Cho dù bạn muốn thuê "
"theo tháng, hai tháng một lần, quý hay bất kỳ thời hạn nào phù hợp với nhu "
"cầu của bạn, bạn đều có thể tùy chỉnh. Đừng quên chỉ định ngày kết thúc hợp "
"đồng."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Bắt đầu với khách hàng-tìm hiểu những gì họ muốn và cho họ điều đó."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.contactus
msgid "Submit"
msgstr "Gửi"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.rental_form_view
msgid "Tenant"
msgstr "Người thuê nhà"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"This view shows the availability of each of your resource based on the "
"rental contracts."
msgstr ""
"Chế độ xem này hiển thị tình trạng khả dụng của từng tài nguyên dựa trên hợp"
" đồng cho thuê."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"To create a product, head back to the properties menu and click on \"Products\". Click on \"New\" and give your product a name. Check the \"Recurring\" box and define the product type as \"Service\". In the \"Time based pricing\" tab, define\n"
"            the rental price of the property per period."
msgstr ""
"Để tạo sản phẩm, hãy quay lại menu thuộc tính và nhấp vào \"Sản phẩm\". Nhấp vào \"Mới\" và đặt tên cho sản phẩm của bạn. Đánh dấu vào ô \"Định kỳ\" và xác định loại sản phẩm là \"Dịch vụ\". Trong tab \"Giá theo thời gian\", hãy xác định\n"
"giá thuê của bất động sản theo giai đoạn."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: industry_real_estate
#: model:account.analytic.account,name:industry_real_estate.account_analytic_account_27
msgid "Uccle Observatoire Duplex"
msgstr "Uccle Observatoire Duplex"

#. module: industry_real_estate
#: model:ir.model.fields,field_description:industry_real_estate.field_meter_reading_usage
msgid "Usage"
msgstr "Cách dùng"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "Vendor Bills"
msgstr "Hoá đơn mua hàng"

#. module: industry_real_estate
#: model:crm.tag,name:industry_real_estate.crm_tag_9
msgid "Visit"
msgstr "Lượt xem"

#. module: industry_real_estate
#: model:product.template,name:industry_real_estate.product_product_47_product_template
msgid "Water"
msgstr "Nước "

#. module: industry_real_estate
#: model_terms:web_tour.tour,rainbow_man_message:industry_real_estate.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Chào mừng bạn! Chúc bạn một chuyến khám phá bổ ích!"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.homepage
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.ir_ui_view_1416
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Viết một hoặc hai đoạn văn mô tả sản phẩm hoặc dịch vụ của bạn. Để thành "
"công, nội dung của bạn cần phải hữu ích cho người đọc."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also have a dedicated tab to fill at the time of customer's entry in the"
" property so that you can log and keep track of meter readings."
msgstr ""
"Bạn cũng có một tab riêng để điền khi khách hàng chuyển vào bất động sản để "
"bạn có thể ghi lại và theo dõi số công tơ."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.welcome_article_body
msgid ""
"You also need to create products for your properties. You have 2 options: "
"either create one product for all your properties and manually change the "
"price when creating the rental contract, or define one product per property."
msgstr ""
"Bạn cũng cần tạo sản phẩm cho bất động sản của mình. Bạn có 2 lựa chọn: tạo "
"một sản phẩm cho tất cả bất động sản của mình và thay đổi giá thủ công khi "
"tạo hợp đồng cho thuê hoặc xác định một sản phẩm cho mỗi bất động sản."

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.property_form_view
msgid "e.g. Apartment Oxford Street"
msgstr "VD: Apartment Oxford Street"

#. module: industry_real_estate
#: model_terms:ir.ui.view,arch_db:industry_real_estate.website_listing_account_analytic_account
msgid "found)"
msgstr "được tìm thấy)"
