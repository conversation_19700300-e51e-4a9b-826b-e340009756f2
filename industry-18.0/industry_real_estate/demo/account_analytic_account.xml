<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="account_analytic_account_20" model="account.analytic.account">
        <field name="name">Apartment 27</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Appartment</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/20-x_property_image" />
        <field name="x_property_address">Rue de Neupr&#233; 108 8934 Osivies </field>
        <field name="x_is_published" eval="True" />
        <field name="x_property_building_id" eval="ref('x_buildings_3')"/>
    </record>
    <record id="account_analytic_account_21" model="account.analytic.account">
        <field name="name">Apartment 26</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Appartment</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/21-x_property_image" />
        <field name="x_property_address">Rue de Neupr&#233; 108 8934 Osivies </field>
        <field name="x_is_published" eval="True" />
        <field name="x_property_building_id" eval="ref('x_buildings_3')"/>
    </record>
    <record id="account_analytic_account_22" model="account.analytic.account">
        <field name="name">Apartment 28</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Appartment</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/22-x_property_image" />
        <field name="x_property_address">Rue de Neupr&#233; 108  8934 Osivies </field>
        <field name="x_is_published" eval="True" />
        <field name="x_property_building_id" eval="ref('x_buildings_3')"/>
    </record>
    <record id="account_analytic_account_23" model="account.analytic.account">
        <field name="name">Office M</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Office</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/23-x_property_image" />
        <field name="x_property_address">Rue de Bruxelles 119 1083 Louise </field>
        <field name="x_is_published" eval="True" />
        <field name="x_website_description">Welcome to the Ideal Workspace Hub, where innovation and convenience converge in the heart of the city. Our modern coworking space offers flexible workspaces, high-speed internet, and professional amenities, all in a central location. With 46 offices and 2 meeting rooms, you'll find the perfect spot to take your productivity to new heights.</field>
        <field name="x_property_building_id" eval="ref('x_buildings_1')"/>
    </record>
    <record id="account_analytic_account_24" model="account.analytic.account">
        <field name="name">Office S</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Office</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/24-x_property_image" />
        <field name="x_property_address">Rue de Bruxelles 119 1083 Louise </field>
        <field name="x_is_published" eval="True" />
        <field name="x_property_building_id" eval="ref('x_buildings_1')"/>
    </record>
    <record id="account_analytic_account_25" model="account.analytic.account">
        <field name="name">Office T</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Office</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/25-x_property_image" />
        <field name="x_property_address">Rue de Bruxelles 119 1083 Louise </field>
        <field name="x_is_published" eval="True" />
        <field name="x_property_building_id" eval="ref('x_buildings_1')"/>
    </record>
    <record id="account_analytic_account_26" model="account.analytic.account">
        <field name="name">Apartment 29</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Appartment</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/26-x_property_image" />
        <field name="x_property_address">Rue de Neupr&#233; 108 8934 Osivies </field>
        <field name="x_is_published" eval="True" />
        <field name="x_property_building_id" eval="ref('x_buildings_3')"/>
    </record>
    <record id="account_analytic_account_27" model="account.analytic.account">
        <field name="name">Uccle Observatoire Duplex</field>
        <field name="plan_id" ref="analytic_plan_properties" />
        <field name="x_property_type">Appartment</field>
        <field name="x_property_image" type="base64" file="industry_real_estate/static/src/binary/account_analytic_account/27-x_property_image" />
        <field name="x_property_address">Avenue du Chateau 123 1400 Uccle </field>
        <field name="x_is_published" eval="True" />
        <field name="x_property_building_id" eval="ref('x_buildings_2')"/>
    </record>
</odoo>
