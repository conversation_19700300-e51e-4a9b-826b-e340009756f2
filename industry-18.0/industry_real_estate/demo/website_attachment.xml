<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">

    <record id="configurator_1_library_image_02" model="ir.attachment">
        <field name="name">website.library_image_02</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/642-website.library_image_02"/>
        <field name="key">industry_real_estate.library_image_02</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_03" model="ir.attachment">
        <field name="name">website.library_image_03</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/635-website.library_image_03"/>
        <field name="key">industry_real_estate.library_image_03</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_05" model="ir.attachment">
        <field name="name">website.library_image_05</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/638-website.library_image_05"/>
        <field name="key">industry_real_estate.library_image_05</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_08" model="ir.attachment">
        <field name="name">website.library_image_08</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/641-website.library_image_08"/>
        <field name="key">industry_real_estate.library_image_08</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_10" model="ir.attachment">
        <field name="name">website.library_image_10</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/636-website.library_image_10"/>
        <field name="key">industry_real_estate.library_image_10</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_13" model="ir.attachment">
        <field name="name">website.library_image_13</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/637-website.library_image_13"/>
        <field name="key">industry_real_estate.library_image_13</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_14" model="ir.attachment">
        <field name="name">website.library_image_14</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/639-website.library_image_14"/>
        <field name="key">industry_real_estate.library_image_14</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_16" model="ir.attachment">
        <field name="name">website.library_image_16</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/640-website.library_image_16"/>
        <field name="key">industry_real_estate.library_image_16</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_banner_default_image" model="ir.attachment">
        <field name="name">website.s_banner_default_image</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/619-website.s_banner_default_image"/>
        <field name="key">industry_real_estate.s_banner_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_carousel_default_image_1" model="ir.attachment">
        <field name="name">website.s_carousel_default_image_1</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/616-website.s_carousel_default_image_1"/>
        <field name="key">industry_real_estate.s_carousel_default_image_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_carousel_default_image_2" model="ir.attachment">
        <field name="name">website.s_carousel_default_image_2</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/617-website.s_carousel_default_image_2"/>
        <field name="key">industry_real_estate.s_carousel_default_image_2</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_carousel_default_image_3" model="ir.attachment">
        <field name="name">website.s_carousel_default_image_3</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/618-website.s_carousel_default_image_3"/>
        <field name="key">industry_real_estate.s_carousel_default_image_3</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_cover_default_image" model="ir.attachment">
        <field name="name">website.s_cover_default_image</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/609-website.s_cover_default_image"/>
        <field name="key">industry_real_estate.s_cover_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_image_text_default_image" model="ir.attachment">
        <field name="name">website.s_image_text_default_image</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/629-website.s_image_text_default_image"/>
        <field name="key">industry_real_estate.s_image_text_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_masonry_block_default_image_1" model="ir.attachment">
        <field name="name">website.s_masonry_block_default_image_1</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/610-website.s_masonry_block_default_image_1"/>
        <field name="key">industry_real_estate.s_masonry_block_default_image_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_media_list_default_image_1" model="ir.attachment">
        <field name="name">website.s_media_list_default_image_1</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/611-website.s_media_list_default_image_1"/>
        <field name="key">industry_real_estate.s_media_list_default_image_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_media_list_default_image_2" model="ir.attachment">
        <field name="name">website.s_media_list_default_image_2</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/612-website.s_media_list_default_image_2"/>
        <field name="key">industry_real_estate.s_media_list_default_image_2</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_media_list_default_image_3" model="ir.attachment">
        <field name="name">website.s_media_list_default_image_3</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/613-website.s_media_list_default_image_3"/>
        <field name="key">industry_real_estate.s_media_list_default_image_3</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_parallax_default_image" model="ir.attachment">
        <field name="name">website.s_parallax_default_image</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/620-website.s_parallax_default_image"/>
        <field name="key">industry_real_estate.s_parallax_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_picture_default_image" model="ir.attachment">
        <field name="name">website.s_picture_default_image</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/628-website.s_picture_default_image"/>
        <field name="key">industry_real_estate.s_picture_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_catalog_default_image" model="ir.attachment">
        <field name="name">website.s_product_catalog_default_image</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/621-website.s_product_catalog_default_image"/>
        <field name="key">industry_real_estate.s_product_catalog_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_1" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_1</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/622-website.s_product_list_default_image_1"/>
        <field name="key">industry_real_estate.s_product_list_default_image_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_2" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_2</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/623-website.s_product_list_default_image_2"/>
        <field name="key">industry_real_estate.s_product_list_default_image_2</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_3" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_3</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/624-website.s_product_list_default_image_3"/>
        <field name="key">industry_real_estate.s_product_list_default_image_3</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_4" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_4</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/625-website.s_product_list_default_image_4"/>
        <field name="key">industry_real_estate.s_product_list_default_image_4</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_5" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_5</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/626-website.s_product_list_default_image_5"/>
        <field name="key">industry_real_estate.s_product_list_default_image_5</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_6" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_6</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/627-website.s_product_list_default_image_6"/>
        <field name="key">industry_real_estate.s_product_list_default_image_6</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_quotes_carousel_demo_image_0" model="ir.attachment">
        <field name="name">website.s_quotes_carousel_demo_image_0</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/634-website.s_quotes_carousel_demo_image_0"/>
        <field name="key">industry_real_estate.s_quotes_carousel_demo_image_0</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_quotes_carousel_demo_image_1" model="ir.attachment">
        <field name="name">website.s_quotes_carousel_demo_image_1</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/614-website.s_quotes_carousel_demo_image_1"/>
        <field name="key">industry_real_estate.s_quotes_carousel_demo_image_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_quotes_carousel_demo_image_2" model="ir.attachment">
        <field name="name">website.s_quotes_carousel_demo_image_2</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/615-website.s_quotes_carousel_demo_image_2"/>
        <field name="key">industry_real_estate.s_quotes_carousel_demo_image_2</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_text_image_default_image" model="ir.attachment">
        <field name="name">website.s_text_image_default_image</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/630-website.s_text_image_default_image"/>
        <field name="key">industry_real_estate.s_text_image_default_image</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_three_columns_default_image_1" model="ir.attachment">
        <field name="name">website.s_three_columns_default_image_1</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/631-website.s_three_columns_default_image_1"/>
        <field name="key">industry_real_estate.s_three_columns_default_image_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_three_columns_default_image_2" model="ir.attachment">
        <field name="name">website.s_three_columns_default_image_2</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/632-website.s_three_columns_default_image_2"/>
        <field name="key">industry_real_estate.s_three_columns_default_image_2</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_three_columns_default_image_3" model="ir.attachment">
        <field name="name">website.s_three_columns_default_image_3</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/633-website.s_three_columns_default_image_3"/>
        <field name="key">industry_real_estate.s_three_columns_default_image_3</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_601" model="ir.attachment">
        <field name="name">Team Member 1.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/601-team_team_member_1.jpg" />
        <field name="key">industry_real_estate.s_company_team_image_1</field>
        <field name="website_id" ref="website.default_website" />
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_602" model="ir.attachment">
        <field name="name">Team Member 2.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/602-team_team_member_2.jpg" />
        <field name="key">industry_real_estate.s_company_team_image_2</field>
        <field name="website_id" ref="website.default_website" />
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_603" model="ir.attachment">
        <field name="name">Team Member 3.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/603-team_team_member_3.jpg" />
        <field name="key">industry_real_estate.s_company_team_image_3</field>
        <field name="website_id" ref="website.default_website" />
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_604" model="ir.attachment">
        <field name="name">Team Member 4.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/604-team_team_member_4.jpg" />
        <field name="key">industry_real_estate.s_company_team_image_4</field>
        <field name="website_id" ref="website.default_website" />
        <field name="public" eval="True"/>
    </record>
</odoo>
