<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="res_partner_41" model="res.partner">
        <field name="name"><PERSON></field>
    </record>
    <record id="res_partner_42" model="res.partner">
        <field name="name"><PERSON></field>
    </record>
    <record id="res_partner_43" model="res.partner">
        <field name="name"><PERSON><PERSON></field>
    </record>
    <record id="res_partner_44" model="res.partner">
        <field name="name"><PERSON><PERSON></field>
    </record>
    <record id="res_partner_45" model="res.partner">
        <field name="name"><PERSON></field>
        <field name="email"><EMAIL></field>
        <field name="phone">******-555-5555</field>
    </record>
    <record id="res_partner_46" model="res.partner">
        <field name="name"><PERSON><PERSON></field>
    </record>
    <record id="res_partner_47" model="res.partner">
        <field name="name"><PERSON></field>
    </record>
    <record id="res_partner_48" model="res.partner">
        <field name="name">EPG Companies</field>
        <field name="is_company" eval="True" />
        <field name="email"><EMAIL></field>
        <field name="website">http://epgco.example.com</field>
        <field name="image_1920" type="base64" file="industry_real_estate/static/src/binary/res_partner/48-image_1920" />
    </record>
    <record id="res_partner_49" model="res.partner">
        <field name="name">Welight</field>
        <field name="is_company" eval="True" />
        <field name="email"><EMAIL></field>
        <field name="website">http://welight.example.com</field>
        <field name="image_1920" type="base64" file="industry_real_estate/static/src/binary/res_partner/49-image_1920" />
    </record>
    <record id="res_partner_50" model="res.partner">
        <field name="name">Mitchell Garmin</field>
        <field name="company_name">YourCompany</field>
        <field name="email"><EMAIL></field>
        <field name="phone">******-555-5555</field>
        <field name="street">215 Vine St</field>
        <field name="city">Scranton</field>
        <field name="state_id" ref="base.state_us_39" />
        <field name="country_id" ref="base.us" />
        <field name="zip">18503</field>
    </record>
    <record id="res_partner_51" model="res.partner">
        <field name="name">John Doe</field>
        <field name="is_company" eval="True" />
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_52" model="res.partner">
        <field name="name">GT French</field>
        <field name="is_company" eval="True" />
        <field name="email"><EMAIL></field>
        <field name="website">http://gtfrench.example.com</field>
        <field name="image_1920" type="base64" file="industry_real_estate/static/src/binary/res_partner/52-image_1920" />
    </record>
    <record id="res_partner_53" model="res.partner">
        <field name="name">Mr Plumber</field>
        <field name="is_company" eval="True" />
        <field name="website">http://mrplumber.example.com</field>
        <field name="image_1920" type="base64" file="industry_real_estate/static/src/binary/res_partner/53-image_1920" />
    </record>
    <record id="res_partner_54" model="res.partner">
        <field name="name">Total</field>
        <field name="is_company" eval="True" />
        <field name="website">http://total.example.com</field>
        <field name="image_1920" type="base64" file="industry_real_estate/static/src/binary/res_partner/54-image_1920" />
    </record>
    <record id="res_partner_55" model="res.partner">
        <field name="name">Cile</field>
        <field name="is_company" eval="True" />
    </record>
</odoo>
