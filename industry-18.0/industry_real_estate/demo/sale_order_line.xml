<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_line_49" model="sale.order.line">
        <field name="order_id" ref="sale_order_25"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_50" model="sale.order.line">
        <field name="order_id" ref="sale_order_26"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_51" model="sale.order.line">
        <field name="order_id" ref="sale_order_27"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_52" model="sale.order.line">
        <field name="order_id" ref="sale_order_28"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_53" model="sale.order.line">
        <field name="order_id" ref="sale_order_29"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_54" model="sale.order.line">
        <field name="order_id" ref="sale_order_30"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_55" model="sale.order.line">
        <field name="order_id" ref="sale_order_31"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_56" model="sale.order.line">
        <field name="order_id" ref="sale_order_32"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_57" model="sale.order.line">
        <field name="order_id" ref="sale_order_33"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_58" model="sale.order.line">
        <field name="order_id" ref="sale_order_34"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
    <record id="sale_order_line_59" model="sale.order.line">
        <field name="order_id" ref="sale_order_35"/>
        <field name="product_id" ref="product_product_42"/>
    </record>
</odoo>
