<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="ir_attachment_664" model="ir.attachment">
        <field name="name">home.png</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/664-home.png"/>
        <field name="res_id" ref="base.user_admin"/>
        <field name="res_model">res.users</field>
        <field name="website_id" ref="website.default_website"/>
    </record>
    <record id="ir_attachment_669" model="ir.attachment">
        <field name="name">ParkLine-apartment-in-Miami-FL.jpg.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/669-ParkLine-apartment-in-Miami-FL.jpg.jpg"/>
        <field name="website_id" ref="website.default_website"/>
    </record>
    <record id="ir_attachment_671" model="ir.attachment">
        <field name="name">ParkLine-apartment-in-Miami-FL.jpg.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/671-ParkLine-apartment-in-Miami-FL.jpg.jpg"/>
        <field name="website_id" ref="website.default_website"/>
    </record>
    <record id="ir_attachment_673" model="ir.attachment">
        <field name="name">ParkLine-apartment-in-Miami-FL.jpg.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/673-ParkLine-apartment-in-Miami-FL.jpg.jpg"/>
        <field name="website_id" ref="website.default_website"/>
    </record>
    <record id="ir_attachment_675" model="ir.attachment">
        <field name="name">ParkLine-apartment-in-Miami-FL.jpg.jpg</field>
        <field name="datas" type="base64" file="industry_real_estate/static/src/binary/ir_attachment/675-ParkLine-apartment-in-Miami-FL.jpg.jpg"/>
        <field name="website_id" ref="website.default_website"/>
    </record>
</odoo>
