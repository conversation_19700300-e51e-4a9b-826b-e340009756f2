<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="crm.crm_lead_action_pipeline" model="ir.actions.act_window">
        <field name="context">{'default_type': 'opportunity', 'search_default_assigned_to_me': 0}</field>
    </record>

    <record id="action_rental_contracts" model="ir.actions.act_window">
        <field name="name">Rental Contracts</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">kanban,list,form,calendar</field>
        <field name="domain">[('x_account_analytic_account_id', '!=', False)]</field>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('sale.view_sale_order_kanban')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('rental_form_view')}),
        ]"/>
    </record>

    <record id="action_availability" model="ir.actions.act_window">
        <field name="name">Availability</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">gantt,list,form</field>
        <field name="domain">[('x_account_analytic_account_id', '!=', False)]</field>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'gantt', 'view_id': ref('rental_gantt_view')}),
        ]"/>
    </record>

    <record id="action_properties" model="ir.actions.act_window">
        <field name="name">Properties</field>
        <field name="res_model">account.analytic.account</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('x_is_property', '=', True)]</field>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('property_kanban_view')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('property_form_view')}),
        ]"/>
    </record>

    <record id="action_configuration_meters" model="ir.actions.act_window">
        <field name="name">Meters</field>
        <field name="res_model">x_meters</field>
        <field name="view_mode">list</field>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'list', 'view_id': ref('meters_list_view')}),
        ]"/>
    </record>

    <record id="action_products" model="ir.actions.act_window">
        <field name="name">Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">kanban,list,form</field>
    </record>

    <record id="action_buildings" model="ir.actions.act_window">
        <field name="name">Buildings</field>
        <field name="res_model">x_buildings</field>
        <field name="view_mode">kanban,form</field>
    </record>
</odoo>
