<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="welcome_article_body">
        <h1>Real Estate rental agency <a>🏠🏢</a></h1>
        <p><span class="text-o-color-5 bg-o-color-4">Manage your long term or mid long term rental properties</span></p>
        <p>
            <span class="text-o-color-5 bg-o-color-4">Manage your properties, create and manage rental contracts, and streamline your entire <strong>rental process.</strong>  Efficient property management.</span>
        </p>
        <h2>Business Flows </h2>
        <h3>Manage Leads</h3>
        <p>Leads get created in the CRM by filling the "Contact us" form of the website or manually by the real estate agent.</p>
        <h3>Manage properties </h3>
        <p>First, head over to the <strong>Properties</strong> app. </p>
        <p>Click on the Properties menu. Now, click on "New" to create a new property. Make sure you fill in all the required information. </p>
        <p>You also need to create products for your properties. You have 2 options: either create one product for all your properties and manually change the price when creating the rental contract, or define one product per property.</p>
        <p>
            To create a product, head back to the properties menu and click on "Products". Click on "New" and give your product a name. Check the "Recurring" box and define the product type as "Service". In the "Time based pricing" tab, define
            the rental price of the property per period.
        </p>
        <h3>Manage rental contracts</h3>
        <p>Click on the Rental Contract menu.</p>
        <p>Click on "New" to create a new rental contract.</p>
        <p>Specify the customer to whom you want to rent the property and then select the property you want to rent.</p>
        <p>Specify the rental start date and define the rental period. Whether you want it to be monthly, bimonthly, quarterly, or whatever suits your needs, you can customize it. Don't forget to specify the end date of the contract.</p>
        <p>
            Also define the date of the next invoice. Note that this date will be taken as the template date for all the next invoices. For example, if you define a next date of invoice of 04/30 with a monthly recurring period, the invoices
            will automatically be created each 30th of the month.
        </p>
        <p>Now, in the sub-level form, select the appropriate products. </p>
        <p>You also have a dedicated tab to fill at the time of customer's entry in the property so that you can log and keep track of meter readings.</p>
        <h3>Check Out for availability</h3>
        <p>Click on the Availability menu. All your rental contracts appear on that screen in a neat little Gantt view.</p>
        <p>This view shows the availability of each of your resource based on the rental contracts.</p>
        <p>From here, you can also easily create a new rental contract with ease. Simply click on the "New" button and enter all the relevant details. </p>
        <p>One last thing to note: if you don't have any rental contracts for a property yet, you won't see this property in the Gantt view. <br /></p>
    </template>    

    <record id="welcome_article" model="knowledge.article">
        <field name="name">Real Estate rental agency 🏠🏢</field>
        <field name="sequence">1</field>
        <field name="category">workspace</field>
        <field name="is_locked" eval="True"/>
        <field name="is_article_visible_by_everyone" eval="True"/>
        <field name="internal_permission">write</field>
        <field name="cover_image_id" ref="knowledge_cover_3"/>
        <field name="cover_image_position">75.40000000000002</field>
        <field name="body">
            <![CDATA[]]>
        </field>
    </record>
</odoo>
