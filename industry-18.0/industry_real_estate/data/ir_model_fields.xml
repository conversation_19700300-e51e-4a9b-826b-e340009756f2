<?xml version='1.0' encoding='UTF-8'?>
<odoo>

    <!-- meters -->
    <record id="field_meters_sequence" model="ir.model.fields">
        <field name="name">x_sequence</field>
        <field name="field_description">Sequence</field>
        <field name="model_id" ref="model_meters"/>
        <field name="ttype">integer</field>
    </record>

    <record id="field_meters_name" model="ir.model.fields">
        <field name="name">x_name</field>
        <field name="field_description">Description</field>
        <field name="model_id" ref="model_meters"/>
        <field name="ttype">char</field>
    </record>

    <record id="field_meters_currency" model="ir.model.fields">
        <field name="name">x_currency_id</field>
        <field name="field_description">Currency</field>
        <field name="model_id" ref="model_meters"/>
        <field name="relation">res.currency</field>
        <field name="ttype">many2one</field>
    </record>

    <record id="field_meters_price" model="ir.model.fields">
        <field name="name">x_price</field>
        <field name="field_description">Price</field>
        <field name="model_id" ref="model_meters"/>
        <field name="ttype">monetary</field>
    </record>

    <!-- meter reading -->
    <record id="field_meter_reading_date" model="ir.model.fields">
        <field name="name">x_date</field>
        <field name="field_description">Date</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="ttype">date</field>
        <field name="required" eval="True"/>
    </record>

    <record id="field_meter_reading_quantity" model="ir.model.fields">
        <field name="name">x_quantity</field>
        <field name="field_description">Quantity</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="ttype">float</field>
    </record>

    <record id="field_meter_reading_usage" model="ir.model.fields">
        <field name="name">x_usage</field>
        <field name="field_description">Usage</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="ttype">float</field>
        <field name="readonly" eval="True"/>
    </record>

    <record id="field_meters_reading_description" model="ir.model.fields">
        <field name="name">x_description</field>
        <field name="field_description">Description</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="ttype">char</field>
    </record>

    <record id="field_meter_reading_sale_order" model="ir.model.fields">
        <field name="name">x_sale_order_id</field>
        <field name="field_description">Sale Order</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="domain">[('x_account_analytic_account_id', '!=', False)]</field>
        <field name="relation">sale.order</field>
        <field name="ttype">many2one</field>
    </record>

    <record id="field_meter_reading_account_analytic_account" model="ir.model.fields">
        <field name="name">x_account_analytic_account_id</field>
        <field name="field_description">Property</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="relation">account.analytic.account</field>
        <field name="ttype">many2one</field>
    </record>

    <record id="field_meter_reading_meter_id" model="ir.model.fields">
        <field name="name">x_meter_id</field>
        <field name="field_description">Meter</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="ttype">many2one</field>
        <field name="relation">x_meters</field>
    </record>

    <record id="field_meter_reading_invoice_id" model="ir.model.fields">
        <field name="name">x_invoice_id</field>
        <field name="field_description">Invoice</field>
        <field name="model_id" ref="model_meter_reading"/>
        <field name="relation">account.move</field>
        <field name="ttype">many2one</field>
    </record>

    <!-- Buildings -->
    <record id="name_buildings" model="ir.model.fields">
        <field name="name">x_name</field>
        <field name="ttype">char</field>
        <field name="copied" eval="True"/>
        <field name="field_description">Name</field>
        <field name="model_id" ref="model_buildings"/>
        <field name="required" eval="True"/>
    </record>
    <record id="street" model="ir.model.fields">
        <field name="name">x_street</field>
        <field name="ttype">char</field>
        <field name="copied" eval="True"/>
        <field name="field_description">Street</field>
        <field name="model_id" ref="model_buildings"/>
    </record>
    <record id="street2" model="ir.model.fields">
        <field name="name">x_street2</field>
        <field name="ttype">char</field>
        <field name="copied" eval="True"/>
        <field name="field_description">Street2</field>
        <field name="model_id" ref="model_buildings"/>
    </record>
    <record id="city" model="ir.model.fields">
        <field name="name">x_city</field>
        <field name="ttype">char</field>
        <field name="copied" eval="True"/>
        <field name="field_description">City</field>
        <field name="model_id" ref="model_buildings"/>
    </record>
    <record id="zip" model="ir.model.fields">
        <field name="name">x_zip</field>
        <field name="ttype">char</field>
        <field name="copied" eval="True"/>
        <field name="field_description">ZIP</field>
        <field name="model_id" ref="model_buildings"/>
    </record>
    <record id="country" model="ir.model.fields">
        <field name="name">x_country</field>
        <field name="ttype">many2one</field>
        <field name="copied" eval="True"/>
        <field name="field_description">Country</field>
        <field name="model_id" ref="model_buildings"/>
        <field name="relation">res.country</field>
    </record>
    <record id="country_state" model="ir.model.fields">
        <field name="name">x_state</field>
        <field name="ttype">many2one</field>
        <field name="copied" eval="True"/>
        <field name="field_description">State</field>
        <field name="model_id" ref="model_buildings"/>
        <field name="relation">res.country.state</field>
    </record>

    <!-- Analytic account -->
    <record id="field_property_image" model="ir.model.fields">
        <field name="name">x_property_image</field>
        <field name="field_description">Property Image</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="ttype">binary</field>
    </record>

    <record id="field_property_is_property" model="ir.model.fields">
        <field name="name">x_is_property</field>
        <field name="field_description">Is Property</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="ttype">boolean</field>
        <field name="readonly" eval="True"/>
        <field name="depends">plan_id</field>
        <field name="compute">
<![CDATA[
property_plan = self.env.ref('industry_real_estate.analytic_plan_properties')
for property in self:
    property['x_is_property'] = property.plan_id == property_plan
]]>
</field>
    </record>

    <record id="field_property_is_published" model="ir.model.fields">
        <field name="name">x_is_published</field>
        <field name="model_id" ref="analytic.model_account_analytic_account" />
        <field name="field_description">Published</field>
        <field name="ttype">boolean</field>
        <field name="index" eval="True"/>
    </record>

    <record id="field_property_building" model="ir.model.fields">
        <field name="name">x_property_building_id</field>
        <field name="field_description">Building</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="relation">x_buildings</field>
        <field name="ttype">many2one</field>
    </record>

    <record id="field_property_address" model="ir.model.fields">
        <field name="name">x_property_address</field>
        <field name="field_description">Address</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="ttype">text</field>
    </record>

    <record id="field_property_type" model="ir.model.fields">
        <field name="name">x_property_type</field>
        <field name="field_description">Property Type</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="ttype">selection</field>
        <field name="selection">[('Appartment', 'Apartment'), ('House', 'House'), ('Studio', 'Studio'), ('Office', 'Office'), ('Commercial space', 'Commercial space'), ('Warehouse', 'Warehouse'), ('Land', 'Land'), ('Garage', 'Garage'), ('Room', 'Room')]</field>
    </record>

    <record id="field_property_description" model="ir.model.fields">
        <field name="name">x_website_description</field>
        <field name="field_description">Description</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="ttype">text</field>
    </record>

    <record id="field_property_meter_reading_ids" model="ir.model.fields">
        <field name="name">x_property_meter_reading_ids</field>
        <field name="field_description">Meter Readings</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="ttype">one2many</field>
        <field name="relation">x_meter_reading</field>
        <field name="relation_field">x_account_analytic_account_id</field>
    </record>

    <record id="field_property_attachment_image_ids" model="ir.model.fields">
        <field name="name">x_property_attachment_image_ids</field>
        <field name="field_description">Property Images</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="relation">ir.attachment</field>
        <field name="relation_table">x_real_estate_property_attachment_image_rel</field>
        <field name="ttype">many2many</field>
    </record>

    <record id="field_property_attachment_doc_ids" model="ir.model.fields">
        <field name="name">x_property_attachment_doc_ids</field>
        <field name="field_description">Property Documents</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="relation">ir.attachment</field>
        <field name="relation_table">x_real_estate_property_attachment_doc_rel</field>
        <field name="ttype">many2many</field>
    </record>

    <record id="field_property_rental_contract_id" model="ir.model.fields">
        <field name="name">x_rental_contract_id</field>
        <field name="field_description">Rental Contract</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="relation">sale.order</field>
        <field name="ttype">many2one</field>
        <field name="readonly" eval="True"/>
        <field name="store" eval="False"/>
        <field name="compute">
<![CDATA[
for record in self:
    sos = self.env['sale.order'].search([('x_account_analytic_account_id', '=', record.id), ('state', '=', 'sale'), ('x_rental_start_date', '<=', datetime.date.today())], order='x_rental_start_date desc')
    record['x_rental_contract_id'] = sos[0] if sos else None
]]></field>
    </record>

    <record id="field_property_invoice_status" model="ir.model.fields">
        <field name="name">x_invoice_status</field>
        <field name="field_description">Invoice Status</field>
        <field name="model_id" ref="analytic.model_account_analytic_account"/>
        <field name="ttype">selection</field>
        <field name="selection">[('no', 'Nothing to invoice'), ('invoiced', 'Already invoiced'), ('to_invoice', 'To invoice')]</field>
        <field name="readonly" eval="True"/>
        <field name="depends">x_property_meter_reading_ids.x_usage,x_property_meter_reading_ids.x_invoice_id</field>
        <field name="compute">
<![CDATA[
for record in self:
    meter_readings = record.x_property_meter_reading_ids.filtered(lambda l: l.x_usage > 0)
    if not meter_readings:
        record['x_invoice_status'] = 'no'
    elif not meter_readings.filtered(lambda l: not l.x_invoice_id):
        record['x_invoice_status'] = 'invoiced'
    else:
        record['x_invoice_status'] = 'to_invoice'
]]></field>
    </record>

    <!-- crm lead -->
    <record id="field_crm_lead_property_id" model="ir.model.fields">
        <field name="field_description">Property</field>
        <field name="model_id" ref="crm.model_crm_lead"/>
        <field name="name">x_property_id</field>
        <field name="relation">account.analytic.account</field>
        <field name="ttype">many2one</field>
        <field name="website_form_blacklisted" eval="False"/>
    </record>

    <!-- Sale order -->
    <record id="field_rental_start_date" model="ir.model.fields">
        <field name="name">x_rental_start_date</field>
        <field name="field_description">Rental Start Date</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="ttype">date</field>
    </record>

    <record id="field_account_analytic_account_id" model="ir.model.fields">
        <field name="name">x_account_analytic_account_id</field>
        <field name="field_description">Property</field>
        <field name="domain">[('x_is_property', '=', True)]</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="ttype">many2one</field>
        <field name="relation">account.analytic.account</field>
        <field name="group_expand" eval="True"/>
    </record>

    <record id="field_guarant_partner_id" model="ir.model.fields">
        <field name="name">x_guarant_partner_id</field>
        <field name="field_description">Guarant</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="ttype">many2one</field>
        <field name="relation">res.partner</field>
    </record>

    <record id="field_sale_order_meter_reading_ids" model="ir.model.fields">
        <field name="name">x_so_meter_reading_ids</field>
        <field name="field_description">Meter Readings</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="ttype">one2many</field>
        <field name="relation">x_meter_reading</field>
        <field name="relation_field">x_sale_order_id</field>
    </record>

    <record id="field_sale_order_related_building_ids" model="ir.model.fields">
        <field name="name">x_related_buildings_ids</field>
        <field name="field_description">Building</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="ttype">many2one</field>
        <field name="readonly" eval="True"/>
        <field name="related">x_account_analytic_account_id.x_property_building_id</field>
        <field name="relation">x_buildings</field>
    </record>
</odoo>
