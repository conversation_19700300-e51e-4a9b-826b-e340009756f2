<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_template_4" model="sale.order.template">
        <field name="name">Standard apartment renting</field>
        <field name="require_payment" eval="True"/>
        <field name="require_signature" eval="True"/>
        <field name="plan_id" ref="sale_subscription.subscription_plan_month"/>
        <field name="sale_order_template_line_ids">
            <record id="sale_order_template_line_4" model="sale.order.template.line">
                <field name="product_id" ref="product_product_42"/>
            </record>
            <record id="sale_order_template_line_5" model="sale.order.template.line">
                <field name="product_id" ref="product_product_44"/>
            </record>
        </field>
    </record>
    <record id="sale_order_template_5" model="sale.order.template">
        <field name="name">Standard office renting</field>
        <field name="require_payment" eval="True"/>
        <field name="require_signature" eval="True"/>
        <field name="plan_id" ref="sale_subscription.subscription_plan_month"/>
        <field name="sale_order_template_line_ids">
            <record id="sale_order_template_line_6" model="sale.order.template.line">
                <field name="product_id" ref="product_product_42"/>
            </record>
            <record id="sale_order_template_line_7" model="sale.order.template.line">
                <field name="product_id" ref="product_product_44"/>
            </record>
        </field>
    </record>
</odoo>
