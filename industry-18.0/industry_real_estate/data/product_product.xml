<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_42" model="product.product">
        <field name="service_type">manual</field>
        <field name="purchase_ok" eval="False"/>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Rental fee</field>
        <field name="invoice_policy">order</field>
        <field name="type">service</field>
        <field name="recurring_invoice" eval="True"/>
    </record>
    <record id="product_product_43" model="product.product">
        <field name="sale_ok" eval="False"/>
        <field name="service_type">manual</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Plumbing</field>
        <field name="invoice_policy">order</field>
    </record>
    <record id="product_product_44" model="product.product">
        <field name="service_type">manual</field>
        <field name="purchase_ok" eval="False"/>
        <field name="list_price">50.0</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Provision for fees</field>
        <field name="invoice_policy">order</field>
        <field name="recurring_invoice" eval="True"/>
    </record>
    <record id="product_product_45" model="product.product">
        <field name="service_type">manual</field>
        <field name="list_price">2000.0</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Security deposit </field>
        <field name="invoice_policy">order</field>
    </record>
    <record id="product_product_46" model="product.product">
        <field name="sale_ok" eval="False"/>
        <field name="service_type">manual</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Electricity </field>
        <field name="invoice_policy">order</field>
    </record>
    <record id="product_product_47" model="product.product">
        <field name="service_type">manual</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Water</field>
        <field name="invoice_policy">order</field>
    </record>
    <record id="product_product_48" model="product.product">
        <field name="sale_ok" eval="False"/>
        <field name="service_type">manual</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Gas</field>
        <field name="invoice_policy">order</field>
    </record>
    <record id="product_product_49" model="product.product">
        <field name="sale_ok" eval="False"/>
        <field name="service_type">manual</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="name">Repair jobs</field>
        <field name="invoice_policy">order</field>
    </record>
</odoo>
