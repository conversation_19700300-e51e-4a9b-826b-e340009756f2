<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="website_image_02" model="ir.attachment">
        <field name="name">website.library_image_03</field>
        <field name="key">beverage_distributor.library_image_03</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="beverage_distributor/static/src/binary/ir_attachment/website.s_three_columns_default_image_1.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_03" model="ir.attachment">
        <field name="name">website.library_image_05</field>
        <field name="key">beverage_distributor.library_image_05</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="beverage_distributor/static/src/binary/ir_attachment/website.s_three_columns_default_image_2.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_04" model="ir.attachment">
        <field name="name">website.library_image_08</field>
        <field name="key">beverage_distributor.library_image_08</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="beverage_distributor/static/src/binary/ir_attachment/website.s_three_columns_default_image_3.jpg" />
        <field name="public" eval="True"/>
    </record>
</odoo>
