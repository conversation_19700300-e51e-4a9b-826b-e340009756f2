<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="website_menu_11" model="website.menu">
        <field name="name">Services</field>
        <field name="url">/our-services</field>
        <field name="sequence">30</field>
        <field name="parent_id" model="website.menu" eval="obj().search([
                ('website_id', '=', ref('website.default_website')),
                ('url', '=', '/default-main-menu'),
        ]).id"/>
        <field name="page_id" ref="website_page_5"/>
        <field name="website_id" ref="website.default_website"/>
    </record>
</odoo>
