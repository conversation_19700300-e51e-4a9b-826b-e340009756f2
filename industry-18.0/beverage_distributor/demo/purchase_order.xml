<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="purchase_order_4" model="purchase.order">
        <field name="partner_id" ref="res_partner_39"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="purchase_order_5" model="purchase.order">
        <field name="partner_id" ref="res_partner_40"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="purchase_order_6" model="purchase.order">
        <field name="partner_id" ref="res_partner_40"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="purchase_order_7" model="purchase.order">
        <field name="partner_id" ref="res_partner_38"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
</odoo>
