<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_21" model="sale.order">
        <field name="partner_id" ref="res_partner_22"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_22" model="sale.order">
        <field name="partner_id" ref="res_partner_22"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_23" model="sale.order">
        <field name="partner_id" ref="res_partner_27"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_24" model="sale.order">
        <field name="partner_id" ref="res_partner_20"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_25" model="sale.order">
        <field name="partner_id" ref="res_partner_27"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_26" model="sale.order">
        <field name="partner_id" ref="res_partner_25"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_27" model="sale.order">
        <field name="partner_id" ref="res_partner_27"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_28" model="sale.order">
        <field name="partner_id" ref="res_partner_21"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_29" model="sale.order">
        <field name="partner_id" ref="res_partner_20"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="sale_order_30" model="sale.order">
        <field name="partner_id" ref="res_partner_22"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
</odoo>
