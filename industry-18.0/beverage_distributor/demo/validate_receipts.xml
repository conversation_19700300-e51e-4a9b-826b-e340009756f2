<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function name="button_validate" model="stock.picking">
        <value model="stock.picking" eval="(
            obj().env.ref('beverage_distributor.purchase_order_4') +
            obj().env.ref('beverage_distributor.purchase_order_5') +
            obj().env.ref('beverage_distributor.purchase_order_6') +
            obj().env.ref('beverage_distributor.purchase_order_7')
        ).picking_ids.ids"/>
    </function>
</odoo>
