<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_51" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_60" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_57" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_54" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_59" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_7" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_6" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_62" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_61" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_64" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_63" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_66" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_65" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_68" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_67" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_70" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_69" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
    <record id="product_template_71" model="product.template">
        <field name="is_published" eval="True"/>
    </record>
</odoo>
