<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function name="make_scss_customization" model="web_editor.assets" context="{'website_id': ref('website.default_website')}">
        <value eval="'/website/static/src/scss/options/colors/user_color_palette.scss'"/>
        <value eval="{'o-color-1': '#5f4b8b','o-color-2': '#e69a8d','o-color-3': '#f2f0f5','o-color-4': '#FFFFFF','o-color-5': '#2e1d14','menu': 1}"/>
    </function>
    <function name="write" model="ir.ui.view">
        <value model="ir.ui.view" eval="obj().env['website'].with_context(website_id=obj().env.ref('website.default_website').id).viewref('website.homepage').id"/>
        <value model="ir.ui.view" eval="{'arch': obj().env.ref('beverage_distributor.homepage').arch}"/>
    </function>
</odoo>
