<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function name="button_validate" model="stock.picking">
        <value model="stock.picking" eval="(
            obj().env.ref('beverage_distributor.sale_order_21') +
            obj().env.ref('beverage_distributor.sale_order_22') +
            obj().env.ref('beverage_distributor.sale_order_23') +
            obj().env.ref('beverage_distributor.sale_order_24') +
            obj().env.ref('beverage_distributor.sale_order_25') +
            obj().env.ref('beverage_distributor.sale_order_26') +
            obj().env.ref('beverage_distributor.sale_order_27') +
            obj().env.ref('beverage_distributor.sale_order_28') +
            obj().env.ref('beverage_distributor.sale_order_29') +
            obj().env.ref('beverage_distributor.sale_order_30')
        ).picking_ids.ids"/>
    </function>
</odoo>
