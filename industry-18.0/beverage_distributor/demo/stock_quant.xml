<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_quant_1" model="stock.quant">
        <field name="product_id" ref="product_product_37"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_2" model="stock.quant">
        <field name="product_id" ref="product_product_7"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_3" model="stock.quant">
        <field name="product_id" ref="product_product_39"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_4" model="stock.quant">
        <field name="product_id" ref="product_product_41"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_5" model="stock.quant">
        <field name="product_id" ref="product_product_43"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_6" model="stock.quant">
        <field name="product_id" ref="product_product_45"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_7" model="stock.quant">
        <field name="product_id" ref="product_product_47"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_8" model="stock.quant">
        <field name="product_id" ref="product_product_36"/>
        <field name="inventory_quantity">4</field>
    </record>
    <record id="product_quant_9" model="stock.quant">
        <field name="product_id" ref="product_product_6"/>
        <field name="inventory_quantity">0</field>
    </record>
    <record id="product_quant_10" model="stock.quant">
        <field name="product_id" search="[('product_tmpl_id', '=', ref('product_template_56'))]"/>
        <field name="inventory_quantity">0</field>
    </record>
    <function name="action_apply_inventory" model="stock.quant" eval="[[
        ref('product_quant_1'),
        ref('product_quant_2'),
        ref('product_quant_3'),
        ref('product_quant_4'),
        ref('product_quant_5'),
        ref('product_quant_6'),
        ref('product_quant_7'),
        ref('product_quant_8'),
        ref('product_quant_9'),
        ref('product_quant_10'),
    ]]"/>
</odoo>
