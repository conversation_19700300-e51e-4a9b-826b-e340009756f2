# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* beverage_distributor
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 10:35+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Enjoy our B2B loyalty program<br/>"
msgstr "&amp;nbsp;Disfrute de nuestro programa de fidelización<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;For order beyond 300$<br/>"
msgstr "&amp;nbsp;Para pedidos mayores a 3000 €<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Get delivered next week.<br/>"
msgstr "&amp;nbsp;Reciba la próxima semana.<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Relies on amazing partners<br/>"
msgstr "&amp;nbsp;Trabajamos con socios increíbles<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Visit us to get a free degustation for B2B partners !<br/>"
msgstr "&amp;nbsp;Visítenos para obtener una degustación gratuita<br/>"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_17
msgid "+16"
msgstr "+16"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_18
msgid "+18"
msgstr "+18"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Thanks to our new wine specialist, we are now proud to propose more than 100 different wines in our stock.</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">Gracias a nuestro nuevo especialista en vinos, ahora nos enorgullece contar con más de 100 vinos distintos en nuestro stock.</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Welcome our wine assortment</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">Le damos la bienvenida a nuestra selección de vinos</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<font class=\"text-o-color-5\">​</font>"
msgstr "<font class=\"text-o-color-5\">​</font>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Siguiente</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Siguiente</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Anterior</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Anterior</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Beverages "
"distributor</strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong>Odoo para distribuidores de "
"bebidas</strong></span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<span class=\"h1-fs\">Basics</span>"
msgstr "<span class=\"h1-fs\">Básicos</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">10%</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">10 %</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Tips for Success</span></strong>"
msgstr "<strong><span class=\"h2-fs\">Consejos para el éxito</span></strong>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"A truly exquisite drinking experience. I highly recommend the Margarita, out"
" of this world."
msgstr ""
"Una experiencia de bebidas exquisitas. Recomiendo la margarita, es "
"increíble."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"A wide selection of beverages with local products. We’re constantly looking "
"to propose new drinks and flavors. While respecting seasons' products and "
"nature."
msgstr ""
"Contamos con una amplia selección de bebidas locales. Nuestra meta es "
"innovar con nuestras bebidas y sabores sin olvidarnos de los productos de "
"temporada y la naturaleza."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Access customer-specific pricing"
msgstr "Acceder a precios exclusivos para clientes."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adapte estas tres columnas para que se ajusten a sus necesidades de diseño. "
"Para duplicar, eliminar o mover columnas, seleccione la columna y use los "
"iconos superiores para realizar su acción."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_15
msgid "Age Limit"
msgstr "Límite de edad"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_5
msgid "Amber"
msgstr "Ámber"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Apply DEP XX taxes to these consigns."
msgstr "Aplique los impuestos DEP XX a estos envíos."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"At the Bistro, you can expect a wide selection of beverages to accompany "
"your meal. Come and visit us for a Sunday brunch, refreshing juices full of "
"flavor, or delicious cocktails that satisfy your thirst. And each day, "
"you’ll discover our Today’s Special."
msgstr ""
"En el bistró siempre encontrará una amplia selección de bebidas para "
"acompañar su comida. Visítenos para el brunch de los domingos, le esperan "
"zumos refrescantes llenos de sabor y deliciosos cócteles. Todos los días "
"contamos con un especial del día."

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.auto_production
msgid "Auto Production"
msgstr "Producción automática"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_aut_production
msgid "Auto-production"
msgstr "Producción automática"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.bom_automation
msgid "Automated BoM"
msgstr "LdM automatizada"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_2
msgid "B2B"
msgstr "B2B"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Barcode Scanning 📱"
msgstr "Escaneo de códigos de barras 📱"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Basics"
msgstr "Básico"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_10
msgid "Beer type"
msgstr "Tipo de cerveza"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_2
#: model:pos.category,name:beverage_distributor.pos_category_6
#: model:product.public.category,name:beverage_distributor.product_public_category_4
msgid "Beers"
msgstr "Cervezas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Beverage Selection"
msgstr "Selección de bebidas"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_2
msgid "Blond"
msgstr "Rubia"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_16
msgid "Brand"
msgstr "Marca"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_3
msgid "Brown"
msgstr "Café"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_24
msgid "Brussels beer project"
msgstr "Proyecto de cervezas de Bruselas"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_22
msgid "Bubbles"
msgstr "Vino espumoso"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_67
msgid "Cava Brut Il Lusio - Josep Masachs 6x75cl"
msgstr "Cava Brut Il Lusio - Josep Masachs 6 x 75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_68
msgid "Cava Brut Il Lusio - Josep Masachs 75cl"
msgstr "Cava Brut Il Lusio - Josep Masachs 75 cl"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_21
msgid "Chaudfontaine"
msgstr "Chaudfontaine"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_14
msgid "Coca-cola"
msgstr "Coca-Cola"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_51
msgid "Coca-cola 24x33cl"
msgstr "Coca-cola 24x33 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Collect deposit when you deliver your customer. It is easy. When validating your delivery, you can directly set a \"Return\" and precisely count the number of each type of deposit your customer gave you. It will automatically be\n"
"                        reflected on his invoice or issue a credit note."
msgstr ""
"Cobre el depósito cuando haga la entrega a su cliente, es muy fácil. Al validar la entrega puede configurar una “Devolución” y contar con precisión el número de cada tipo de depósito que le haya entregado su cliente. Este se reflejará automáticamente\n"
"                        en su factura o emitirá una factura rectificativa."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_quantity_by_deposit_product
msgid "Contains"
msgstr "Contiene"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Cosy and friendly, good atmosphere and super drinks. Especially the homemade"
" lemonade."
msgstr ""
"Ambiente íntimo y agradable, bebidas increíbles (sobre todo la limonada "
"casera)."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create and track sales orders"
msgstr "Crear y llevar seguimiento de los pedidos de venta"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create deposit products for each different type of consigns"
msgstr "Cree productos de depósito para cada tipo distinto de envío."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create purchase orders"
msgstr "Crear pedidos de compra"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Customer Relationship Management (CRM) 🤝"
msgstr "Gestión de relaciones con el cliente (CRM) 🤝"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_69
msgid "Cuvée Grillo - Villa Carumé 6x75cl"
msgstr "Cuvée Grillo - Villa Carumé 6x75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_70
msgid "Cuvée Grillo - Villa Carumé 75cl"
msgstr "Cuvée Grillo - Villa Carumé 75 cl"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_01_purchase
#: model:account.tax,name:beverage_distributor.account_tax_01_sale
msgid "DEP 0.1"
msgstr "DEP 0,1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_21_sale
msgid "DEP 2.1"
msgstr "DEP 2,1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_24_purchase
#: model:account.tax,name:beverage_distributor.account_tax_24_sale
msgid "DEP 2.4"
msgstr "DEP 2,4"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_45_purchase
#: model:account.tax,name:beverage_distributor.account_tax_45_sale
msgid "DEP 4.5"
msgstr "DEP 4,5"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_1
msgid "Default"
msgstr "Por defecto"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.make_deposit_storable_delivery_invoice
msgid "Default fields for deposits"
msgstr "Campos por defecto para depósitos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Delete the above image or replace it with a picture that showcases our "
"products. Click on the picture to change its rounded corner style."
msgstr ""
"Elimine la imagen anterior o reemplácela con una que muestre nuestros "
"productos. Haga clic en la imagen para cambiar su esquina redondeada."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.deposit_tax_group
#: model:pos.category,name:beverage_distributor.pos_category_5
msgid "Deposit"
msgstr "Depósito"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_9
msgid "Deposit 0.1"
msgstr "Depósito 0,1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_56
msgid "Deposit 2.1"
msgstr "Depósito 2,1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_53
msgid "Deposit 2.4"
msgstr "Depósito 2,4"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_55
msgid "Deposit 4.5"
msgstr "Depósito 4,5"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Deposit Management 🍻"
msgstr "Gestión de depósitos 🍻"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_deposit_product_1
msgid "Deposit product"
msgstr "Producto de depósito"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Deposit product must be set in Deposit category"
msgstr ""
"El producto de depósito debe estar configurado en la categoría de depósito"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Do You Want to Go Further?"
msgstr "¿Quiere llegar más lejos?"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_21676_sale
msgid "EXC 2.1676"
msgstr "EXC 2.1676"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_coca_sale
msgid "EXC COCA"
msgstr "EXC COCA"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_empty_deposit
msgid "Empty deposit product"
msgstr "Producto de depósito vacío"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Encourage your B2B clients to use the online ordering system for improved "
"efficiency"
msgstr ""
"Invite a sus clientes a utilizar el sistema de pedidos en línea para mejorar"
" la eficiencia"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Every time you need to sell a single Mobius Blanche - 33cl, if you don't "
"have enough in stock, it will split your main product in 24 units and take "
"into account an extra empty bin that will be left at the end."
msgstr ""
"Cada que necesite vender solo un Mobius Blanche - 33 cl, si no tiene "
"suficiente en el stock, su producto principal se dividirá en 24 unidades y "
"se tomará en cuenta un contenedor vacío adicional que quedará al final."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.excises_tax_group
msgid "Excises"
msgstr "Impuestos sobre el consumo"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.action_make_deposit_storable_delivery_invoice
#: model:ir.actions.server,name:beverage_distributor.bom_server_action
#: model:ir.actions.server,name:beverage_distributor.update_sales_taxes_server_action
msgid "Execute Code"
msgstr "Ejecutar código"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.update_state
msgid "Execute Existing Actions"
msgstr "Ejecutar acciones existentes"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Fast Delivery"
msgstr "Entrega rápida"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Follow up with potential clients"
msgstr "Hacer un seguimiento de clientes potenciales"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For B2C sales, the POS app enables you to:"
msgstr "Para ventas B2C, la aplicación TPV le permite:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For example:"
msgstr "Por ejemplo:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Free shipping&amp;nbsp;<br/>"
msgstr "Envío gratuito&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Get rewards&amp;nbsp;<br/>"
msgstr "Obtener recompensas&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Handle returns and consignment easily"
msgstr "Gestionar las devoluciones y envíos con facilidad"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_14
msgid "IPA"
msgstr "IPA"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to automatically split products sold in a specific packaging, "
"precise a unit sale product in the so-called field and the number of units "
"contained in the main product."
msgstr ""
"Si desea dividir automáticamente los productos vendidos en un paquete "
"específico, primero especifique un producto de venta por unidades en el "
"campo correspondiente y el número de unidades contenidas en el producto "
"principal."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to easily discover every features of this package, try "
"downloading the Demo Data."
msgstr ""
"Descargue los datos de demostración si desea descubrir todas las funciones "
"de este paquete con facilidad. "

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Implement the Quality app to ensure consistent product quality"
msgstr ""
"Implemente la aplicación Calidad para garantizar que la calidad de sus "
"productos sea consistente."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Implement the Surveys app to gather customer feedback and improve your "
"service"
msgstr ""
"Implemente la aplicación Encuesta para recopilar los comentarios de sus "
"clientes y mejorar su servicio."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Inventory Management 📦"
msgstr "Gestión de inventario 📦"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Iris SMITH • CEO of Beverage Co."
msgstr "Iris SMITH • Directora ejecutiva de Beverage Co."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.x_is_a_deposit
msgid "Is a deposit"
msgstr "Es un depósito"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_23
msgid "Jack Daniels"
msgstr "Jack Daniels"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_71
msgid "Jack Daniels 70cl"
msgstr "Jack Daniels 70 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Jane SMITH • CEO of Beverage Co."
msgstr "Jane SMITH • Directora ejecutiva de Beverage Co."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "John SMITH • CEO of Beverage Co."
msgstr "John SMITH • Director ejecutivo de Beverage Co."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Join the Party."
msgstr "Únase a la fiesta."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Just be careful, once demo data are uploaded, it cannot be easily deleted. "
"But you can restart a fresh database on Odoo.com/trial"
msgstr ""
"Tenga cuidado, una vez que haya subido los datos de prueba no podrá "
"eliminarlos con facilidad, pero podrá crear una nueva base de datos en "
"odoo.com/es/trial"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Keep your inventory updated in real-time"
msgstr "Mantener su inventario actualizado en tiempo real"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Leave empty if this product is the smallest unit"
msgstr "Déjelo vacío si este producto es la unidad más pequeña"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Leverage the reporting tools across all apps to gain insights into your "
"business performance"
msgstr ""
"Utilice las herramientas de informes en todas las aplicaciones para obtener "
"información relacionada con el rendimiento de su compañía."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_13
msgid "Local Brand"
msgstr "Marca local"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_63
msgid "Lou Daro - Château de Gragnos 6x75cl"
msgstr "Lou Daro - Château de Gragnos 6x75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_64
msgid "Lou Daro - Château de Gragnos 75cl"
msgstr "Lou Daro - Château de Gragnos 75 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage consignment for drinks sold in crates"
msgstr "Gestione el envío de las bebidas que vende por caja."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage supplier relationships"
msgstr "Gestionar las relaciones con los proveedores"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your deposit more precisely and efficiently than ever before."
msgstr "Gestione su depósito con mayor precisión y eficiencia que nunca."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your product catalog"
msgstr "Gestionar su catálogo de productos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your sales pipeline"
msgstr "Gestionar su flujo de ventas"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_12
msgid "Mobius"
msgstr "Mobius"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 24x33cl is your main product"
msgstr "Mobius Blanche - 24 x 33 cl es su producto principal"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 33cl is the unit sale product"
msgstr "Mobius Blanche - 33 cl es el producto de venta unitario"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_6
msgid "Mobius Blanche 24x33cl"
msgstr "Mobius Blanche 24x33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_7
msgid "Mobius Blanche 33cl"
msgstr "Mobius Blanche 33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_59
msgid "Mobius IPA 24x33cl "
msgstr "Mobius IPA 24x33 cl "

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_60
msgid "Mobius IPA 33cl"
msgstr "Mobius IPA 33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_61
msgid "Mobius Triple 24x33cl"
msgstr "Mobius Triple 24x33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_62
msgid "Mobius Triple 33cl"
msgstr "Mobius Triple 33 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Next"
msgstr "Siguiente"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_16
msgid "No"
msgstr "No"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Odoo offers additional possibilities to enhance your drink distribution "
"business:"
msgstr ""
"Odoo ofrece más posibilidades para mejorar su empresa de distribución de "
"bebidas:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Order online&amp;nbsp;<br/>"
msgstr "Haga su pedido en línea&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Our Partners"
msgstr "Nuestros socios"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"Our professional yet convivial service will make your "
"supply&amp;nbsp;experience unforgettable."
msgstr ""
"Nuestro servicio profesional y amistoso hará que su experiencia de "
"suministro sea inolvidable."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_bom
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_rr
msgid "Parent product"
msgstr "Producto padre"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Perform efficient stock takes"
msgstr "Elabore recuentos de inventario eficientes."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Place orders online 24/7"
msgstr "Realizar pedidos en línea 24/7."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Point of Sale (POS) 💳"
msgstr "TPV 💳"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Previous"
msgstr "Anterior"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process incoming and outgoing shipments"
msgstr "Procese envíos entrantes y salientes."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process quick sales at your distribution center"
msgstr "Procesar ventas rápidas en su centro de distribución"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Purchase Management 🛒"
msgstr "Gestión de compras 🛒"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Quality Products"
msgstr "Productos de calidad"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Reach us"
msgstr "Contacto"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_10
msgid "Red"
msgstr "Rojo"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Regularly update your website with new products and promotions"
msgstr ""
"Actualice su sitio web frecuentemente con nuevos productos y promociones."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_11
msgid "Rose"
msgstr "Rosa"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_65
msgid "Rosé Cosmos - Château de Gragnos 6x75cl"
msgstr "Rosé Cosmos - Château de Gragnos 6x75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_66
msgid "Rosé Cosmos - Château de Gragnos 75cl"
msgstr "Rosé Cosmos - Château de Gragnos 75 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Sales Management 🍾"
msgstr "Gestión de ventas 🍾"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Scan products for quick data entry"
msgstr "Escanee los productos para una rápida introducción de datos."

#. module: beverage_distributor
#: model:website.menu,name:beverage_distributor.website_menu_11
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Services"
msgstr "Servicios"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Set a deposit product on your product to apply these taxes. This will ensure"
" that you invoice correctly the price of consigns."
msgstr ""
"Configure un producto de depósito en su producto para aplicar estos "
"impuestos. Así se asegurará de facturar correctamente el precio de los "
"envíos."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up pricing strategies for different customer segments"
msgstr ""
"Configurar distintas estrategias de precios para sus segmentos de mercado"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up reordering rules for popular products"
msgstr "Configure reglas de reabastecimiento para los productos populares"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Shop our products"
msgstr "Compre nuestros productos"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_8
msgid "Soda"
msgstr "Con gas"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_11
msgid "Soda type"
msgstr "Con/sin gas"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_1
#: model:pos.category,name:beverage_distributor.pos_category_7
#: model:product.public.category,name:beverage_distributor.product_public_category_1
msgid "Sodas"
msgstr "Refrescos con gas"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_13
msgid "Spa"
msgstr "Spa"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_54
msgid "Spa Still 24x25cl"
msgstr "Spa Still 24x25 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_57
msgid "Spa Still 25cl"
msgstr "Spa Still 25 cl"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_3
#: model:product.public.category,name:beverage_distributor.product_public_category_2
msgid "Spirits"
msgstr "Licores"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_4
msgid "Stout"
msgstr "Cerveza fuerte"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Streamline picking and packing processes"
msgstr "Agilice los procesos de picking y de empaquetado."

#. module: beverage_distributor
#: model:delivery.carrier,name:beverage_distributor.delivery_carrier_1
#: model:product.template,name:beverage_distributor.product_product_delivery_product_template
msgid "Take Away"
msgstr "Para llevar"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Barcode app enhances your inventory operations:"
msgstr "La aplicación Código de barras mejora sus operaciones de inventario:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The CRM app helps you:"
msgstr "La aplicación CRM le ayuda a:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Inventory app is the core of your distribution business:"
msgstr ""
"La aplicación Inventario es esencial para sus actividades de distribución:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Purchase app allows you to:"
msgstr "La aplicación Compra le permite:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The inventory of all products will be updated accordingly."
msgstr "Se actualizará el inventario de todos los productos."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"These features can be added to your current subscription. Feel free to "
"explore and expand your Odoo experience!"
msgstr ""
"Puede añadir estas funciones a su suscripción actual. ¡Siéntase libre de "
"explorar y ampliar su experiencia con Odoo!"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This deposit management system is a specific features of this package. "
"Proceed carefully if you need to adapt anything."
msgstr ""
"El sistema de gestión de depósitos es una función específica de este "
"paquete. Continúe con cuidado en caso de que necesite adaptar algo."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "This place is perfect for groups or a casual date night."
msgstr ""
"Este es el lugar perfecto para cenar con tus amigos o para una cita casual. "

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This will automatically create a Bill of Material and process a Manufacture "
"Order each time you need to split your product and reflect this operation in"
" your stock perfectly."
msgstr ""
"Esto creará automáticamente una lista de materiales y procesará una orden de"
" fabricación cada que necesite dividir su producto y reflejará perfectamente"
" esta operación en su stock."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Para añadir una cuarta columna, reduzca el tamaño de estas tres columnas "
"usando el icono adecuado de cada bloque. Entonces, duplique una de las "
"columnas para crear una nueva como copia."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track incoming shipments"
msgstr "Rastrear los productos entrantes"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track leads and opportunities"
msgstr "Llevar un seguimiento de sus leads y oportunidades"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track stock levels across multiple warehouses"
msgstr "Lleve seguimiento del nivel de stock en varios almacenes."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Train your team on efficient use of the barcode system in warehouse "
"operations"
msgstr ""
"Forme a su equipo en el uso eficaz del sistema de códigos de barras en las "
"operaciones de almacén."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_unit_sale_product
msgid "Unit sale product"
msgstr "Producto de venta por unidades"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_9
msgid "Unit sales"
msgstr "Ventas unitarias"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.update_sales_taxes
msgid "Update Taxes"
msgstr "Actualizar impuestos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use barcode scanning for all inventory movements to ensure accuracy"
msgstr ""
"Utilice la función de escaneo de códigos de barras en todos los movimientos "
"de inventario para garantizar su precisión."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Use the Marketing Automation app to nurture leads and promote new products"
msgstr ""
"Use la aplicación Automatización de marketing para enriquecer leads y "
"promover nuevos productos."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use the Sales app to:"
msgstr "Use la aplicación Ventas para:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Utilize the Fleet app to manage your delivery vehicles efficiently"
msgstr ""
"Utilice la aplicación Flota para gestionar eficazmente sus vehículos de "
"reparto"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "View their order history and reorder easily"
msgstr "Ver su historial de pedidos y volver a realizarlos fácilmente."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_7
msgid "Water"
msgstr "Agua"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Welcome to your new Odoo Drink Distributors package! This guide will help "
"you navigate the key features and get your distribution business running "
"smoothly, with a focus on inventory management, efficient operations, and "
"online sales."
msgstr ""
"Le damos la bienvenida a su nuevo paquete de Odoo para distribuidores de "
"bebidas. Esta guía le ayudará a navegar por las funciones clave y hará que "
"su empresa de distribución funcione sin problemas mientras se enfoca en la "
"gestión de inventario, operaciones eficientes y ventas en línea."

#. module: beverage_distributor
#: model_terms:web_tour.tour,rainbow_man_message:beverage_distributor.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Bienvenido! Disfrute del sitio."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_1
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_9
msgid "White"
msgstr "Blanco"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_12
msgid "Wine type"
msgstr "Tipo de vino"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_4
#: model:pos.category,name:beverage_distributor.pos_category_8
#: model:product.public.category,name:beverage_distributor.product_public_category_3
msgid "Wines"
msgstr "Vinos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Would you like to discuss your Odoo setup with us or explore more features?"
msgstr ""
"¿Le gustaría que le ayudemos con su configuración de Odoo o desea explorar "
"más funciones?"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_15
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_25
msgid "Yes"
msgstr "Sí"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"You can also collect deposit directly on your Point of Sale. Select the "
"correct product and encode a negative quantity."
msgstr ""
"También puede cobrar el depósito en su TPV, solo seleccione el producto "
"correcto y escriba la cantidad negativa."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "You can still do it by upgrading your package in Apps."
msgstr "Todavía puede hacerlo si actualiza su paquete en Aplicaciones."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Your B2B eCommerce portal allows clients to:"
msgstr "Su portal de comercio electrónico B2B permite a los clientes:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[EMPTY 33 BIN] Regular 24x33 Bin is the empty deposit product (2.1$ value)"
msgstr ""
"[DEPÓSITO VACÍO 33] El depósito regular de 24 x 33 es el producto de "
"depósito vacío (con un valor de 2,10 €)."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[FULL 33 BIN] Regular 24x33 Bin is the complete deposit product (4.5$ value)"
msgstr ""
"[DEPÓSITO LLENO 33] El depósito regular de 24 x 33 es el producto de "
"depósito completo (con un valor de 4,50 €)."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "deliveries each week"
msgstr "entregas semanales"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "eCommerce Website 🌐"
msgstr "Sitio web de comercio electrónico 🌐"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "ex. Delta I.P.A. - 33cl"
msgstr "p. ej. Delta I.P.A. - 33 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "for B2B partners"
msgstr "para contactos B2B"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "happy customers"
msgstr "clientes felices"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "labeled bottles on rack"
msgstr "botellas etiquetadas en la estantería"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "references"
msgstr "referencias"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Barcode"
msgstr "🎓 Código de barras"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Inventory"
msgstr "🎓 Inventario"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Point of Sale"
msgstr "🎓 Punto de venta"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Purchase"
msgstr "🎓 Compra"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 Ventas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 Sitio web"
