# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* beverage_distributor
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 10:35+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: Mait<PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Enjoy our B2B loyalty program<br/>"
msgstr "Aproveite nosso programa de fidelidade B2B <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;For order beyond 300$<br/>"
msgstr "Para pedidos acima de US$ 300<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Get delivered next week.<br/>"
msgstr "Receba na próxima semana.<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Relies on amazing partners<br/>"
msgstr "Conta com parceiros incríveis<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Visit us to get a free degustation for B2B partners !<br/>"
msgstr "Visite-nos para ganhar uma degustação grátis para parceiros B2B!<br/>"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_17
msgid "+16"
msgstr "+16"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_18
msgid "+18"
msgstr "+18"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Thanks to our new wine specialist, we are now proud to propose more than 100 different wines in our stock.</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">Graças ao nosso novo especialista em vinhos, agora temos o orgulho de oferecer mais de 100 vinhos diferentes em nosso estoque.</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Welcome our wine assortment</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">Dê as boas-vindas à nossa variedade de vinhos</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<font class=\"text-o-color-5\">​</font>"
msgstr "<font class=\"text-o-color-5\">​</font>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Próximo</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Próximo</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Anterior</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Anterior</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Beverages "
"distributor</strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong>Odoo para Distribuidoras de "
"Bebidas</strong></span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<span class=\"h1-fs\">Basics</span>"
msgstr "<span class=\"h1-fs\">Noções Básicas</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">10%</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">10%</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Tips for Success</span></strong>"
msgstr "<strong><span class=\"h2-fs\">Dicas para o sucesso</span></strong>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"A truly exquisite drinking experience. I highly recommend the Margarita, out"
" of this world."
msgstr ""
"Uma experiência com bebidas realmente requintada. Recomendo muito a "
"Margarita, é de outro mundo."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"A wide selection of beverages with local products. We’re constantly looking "
"to propose new drinks and flavors. While respecting seasons' products and "
"nature."
msgstr ""
"Uma ampla seleção de bebidas com produtos locais. Estamos sempre buscando "
"trazer novas bebidas e sabores e respeitando os produtos da estação e a "
"natureza."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Access customer-specific pricing"
msgstr "Acessar preços específicos do cliente"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Adapte essas três colunas para atender às suas necessidades de projeto. Para"
" duplicar, excluir ou mover colunas, selecione a coluna e use os ícones "
"superiores para executar sua ação."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_15
msgid "Age Limit"
msgstr "Limite de idade"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_5
msgid "Amber"
msgstr "Âmbar"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Apply DEP XX taxes to these consigns."
msgstr "Aplique os impostos DEP XX a essas remessas."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"At the Bistro, you can expect a wide selection of beverages to accompany "
"your meal. Come and visit us for a Sunday brunch, refreshing juices full of "
"flavor, or delicious cocktails that satisfy your thirst. And each day, "
"you’ll discover our Today’s Special."
msgstr ""
"No Bistrô, você encontra uma ampla seleção de bebidas para acompanhar sua "
"refeição. Venha nos visitar para um brunch de domingo, sucos refrescantes "
"cheios de sabor ou coquetéis deliciosos que satisfazem sua sede. E todos os "
"dias, você descobrirá o nosso Especial do dia."

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.auto_production
msgid "Auto Production"
msgstr "Produção própria"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_aut_production
msgid "Auto-production"
msgstr "Produção própria"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.bom_automation
msgid "Automated BoM"
msgstr "LM automatizada"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_2
msgid "B2B"
msgstr "B2B"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Barcode Scanning 📱"
msgstr "Leitura de código de barras 📱"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Basics"
msgstr "Elementos básicos"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_10
msgid "Beer type"
msgstr "Tipo de cerveja"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_2
#: model:pos.category,name:beverage_distributor.pos_category_6
#: model:product.public.category,name:beverage_distributor.product_public_category_4
msgid "Beers"
msgstr "Cervejas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Beverage Selection"
msgstr "Seleção de bebidas"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_2
msgid "Blond"
msgstr "Blond"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_16
msgid "Brand"
msgstr "Marca"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_3
msgid "Brown"
msgstr "Marrom"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_24
msgid "Brussels beer project"
msgstr "Projeto de cerveja em Bruxelas"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_22
msgid "Bubbles"
msgstr "Bolhas"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_67
msgid "Cava Brut Il Lusio - Josep Masachs 6x75cl"
msgstr "Cava Brut Il Lusio - Josep Masachs 6x750ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_68
msgid "Cava Brut Il Lusio - Josep Masachs 75cl"
msgstr "Cava Brut Il Lusio - Josep Masachs 750ml"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_21
msgid "Chaudfontaine"
msgstr "Chaudfontaine"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_14
msgid "Coca-cola"
msgstr "Coca-cola"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_51
msgid "Coca-cola 24x33cl"
msgstr "Coca-cola 24x330ml"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Collect deposit when you deliver your customer. It is easy. When validating your delivery, you can directly set a \"Return\" and precisely count the number of each type of deposit your customer gave you. It will automatically be\n"
"                        reflected on his invoice or issue a credit note."
msgstr ""
"Receba o depósito quando entregar ao seu cliente. É fácil. Ao validar sua entrega, você pode definir uma \"Devolução\" diretamente e contar com precisão o número de cada tipo de depósito que seu cliente fez. Isso será automaticamente\n"
"refletido em sua fatura ou emitirá uma nota de crédito."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_quantity_by_deposit_product
msgid "Contains"
msgstr "Contém"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Cosy and friendly, good atmosphere and super drinks. Especially the homemade"
" lemonade."
msgstr ""
"Acolhedor e simpático, bom ambiente e ótimos drinques, especialmente a "
"limonada caseira."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create and track sales orders"
msgstr "Criar e rastrear pedidos de vendas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create deposit products for each different type of consigns"
msgstr "Criar produtos de depósito para cada tipo diferente de consignação"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create purchase orders"
msgstr "Criar pedidos de compra"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Customer Relationship Management (CRM) 🤝"
msgstr "Gestão de relacionamento com o cliente (CRM) 🤝"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_69
msgid "Cuvée Grillo - Villa Carumé 6x75cl"
msgstr "Cuvée Grillo - Villa Carumé 6x750ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_70
msgid "Cuvée Grillo - Villa Carumé 75cl"
msgstr "Cuvée Grillo - Villa Carumé 750ml"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_01_purchase
#: model:account.tax,name:beverage_distributor.account_tax_01_sale
msgid "DEP 0.1"
msgstr "DEP 0.1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_21_sale
msgid "DEP 2.1"
msgstr "DEP 2.1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_24_purchase
#: model:account.tax,name:beverage_distributor.account_tax_24_sale
msgid "DEP 2.4"
msgstr "DEP 2.4"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_45_purchase
#: model:account.tax,name:beverage_distributor.account_tax_45_sale
msgid "DEP 4.5"
msgstr "DEP 4.5"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_1
msgid "Default"
msgstr "Padrão"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.make_deposit_storable_delivery_invoice
msgid "Default fields for deposits"
msgstr "Campos padrão para depósitos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Delete the above image or replace it with a picture that showcases our "
"products. Click on the picture to change its rounded corner style."
msgstr ""
"Exclua a imagem acima ou substitua-a por uma imagem que mostre nossos "
"produtos. Clique na imagem para alterar o estilo do canto arredondado."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.deposit_tax_group
#: model:pos.category,name:beverage_distributor.pos_category_5
msgid "Deposit"
msgstr "Depósito"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_9
msgid "Deposit 0.1"
msgstr "Depósito 0.1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_56
msgid "Deposit 2.1"
msgstr "Depósito 2.1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_53
msgid "Deposit 2.4"
msgstr "Depósito 2.4"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_55
msgid "Deposit 4.5"
msgstr "Depósito 4.5"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Deposit Management 🍻"
msgstr "Gerenciamento de depósitos 🍻"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_deposit_product_1
msgid "Deposit product"
msgstr "Produto de depósito"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Deposit product must be set in Deposit category"
msgstr "O produto de depósito deve ser definido na categoria Depósito"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Do You Want to Go Further?"
msgstr "Você quer ir mais além?"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_21676_sale
msgid "EXC 2.1676"
msgstr "EXC 2.1676"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_coca_sale
msgid "EXC COCA"
msgstr "EXC COCA"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_empty_deposit
msgid "Empty deposit product"
msgstr "Esvaziar produto de depósito"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Encourage your B2B clients to use the online ordering system for improved "
"efficiency"
msgstr ""
"Incentive seus clientes B2B a usar o sistema de pedidos on-line para "
"aumentar a eficiência"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Every time you need to sell a single Mobius Blanche - 33cl, if you don't "
"have enough in stock, it will split your main product in 24 units and take "
"into account an extra empty bin that will be left at the end."
msgstr ""
"Toda vez que você precisar vender um único Mobius Blanche - 330ml, se não "
"tiver o suficiente em estoque, seu produto principal será dividido em 24 "
"unidades, levando em conta uma caixa vazia extra que será deixada no final."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.excises_tax_group
msgid "Excises"
msgstr "Tributos"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.action_make_deposit_storable_delivery_invoice
#: model:ir.actions.server,name:beverage_distributor.bom_server_action
#: model:ir.actions.server,name:beverage_distributor.update_sales_taxes_server_action
msgid "Execute Code"
msgstr "Executar código"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.update_state
msgid "Execute Existing Actions"
msgstr "Executar ações existentes"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Fast Delivery"
msgstr "Entrega rápida"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Follow up with potential clients"
msgstr "Acompanhamento de clientes potenciais"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For B2C sales, the POS app enables you to:"
msgstr "Em vendas B2C, o aplicativo PDV permite:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For example:"
msgstr "Por exemplo:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Free shipping&amp;nbsp;<br/>"
msgstr "Frete grátis&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Get rewards&amp;nbsp;<br/>"
msgstr "Receber recompensas&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Handle returns and consignment easily"
msgstr "Lidar facilmente com devoluções e consignações"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_14
msgid "IPA"
msgstr "IPA"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to automatically split products sold in a specific packaging, "
"precise a unit sale product in the so-called field and the number of units "
"contained in the main product."
msgstr ""
"Se quiser dividir automaticamente os produtos vendidos em uma embalagem "
"específica, especifique um produto de venda unitária no campo denominado e o"
" número de unidades contidas no produto principal."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to easily discover every features of this package, try "
"downloading the Demo Data."
msgstr ""
"Se quiser facilitar ainda mais sua descoberta de todos os recursos desse "
"pacote, experimente fazer o download dos dados de demonstração."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Implement the Quality app to ensure consistent product quality"
msgstr ""
"Implemente o aplicativo Qualidade para assegurar a consistência da qualidade"
" de produtos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Implement the Surveys app to gather customer feedback and improve your "
"service"
msgstr ""
"Implemente o aplicativo Pesquisas para obter feedback dos clientes e "
"melhorar seu serviço"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Inventory Management 📦"
msgstr "Gerenciamento de inventário 📦"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Iris SMITH • CEO of Beverage Co."
msgstr "Iris SMITH - CEO da Beverage Co."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.x_is_a_deposit
msgid "Is a deposit"
msgstr "É depósito"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_23
msgid "Jack Daniels"
msgstr "Jack Daniels"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_71
msgid "Jack Daniels 70cl"
msgstr "Jack Daniels 700ml"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Jane SMITH • CEO of Beverage Co."
msgstr "Jane SMITH - CEO da Beverage Co."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "John SMITH • CEO of Beverage Co."
msgstr "John SMITH - CEO da Beverage Co."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Join the Party."
msgstr "Participe da festa."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Just be careful, once demo data are uploaded, it cannot be easily deleted. "
"But you can restart a fresh database on Odoo.com/trial"
msgstr ""
"Mas tome cuidado: uma vez que os dados de demonstração são carregados, não é"
" possível excluí-los facilmente. Mas você pode reiniciar uma nova base de "
"dados em Odoo.com/trial"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Keep your inventory updated in real-time"
msgstr "Mantenha seu inventário atualizado em tempo real"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Leave empty if this product is the smallest unit"
msgstr "Deixe em branco se esse produto for a menor unidade"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Leverage the reporting tools across all apps to gain insights into your "
"business performance"
msgstr ""
"Aproveite as ferramentas de relatório em todos os aplicativos para obter "
"insights sobre o desempenho de seus negócios"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_13
msgid "Local Brand"
msgstr "Marca local"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_63
msgid "Lou Daro - Château de Gragnos 6x75cl"
msgstr "Lou Daro - Château de Gragnos 6x750ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_64
msgid "Lou Daro - Château de Gragnos 75cl"
msgstr "Lou Daro - Château de Gragnos 750ml"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage consignment for drinks sold in crates"
msgstr "Gerenciar consignação de bebidas vendidas em caixas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage supplier relationships"
msgstr "Gerenciar relacionamentos com fornecedores"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your deposit more precisely and efficiently than ever before."
msgstr "Gerencie seu depósito com mais precisão e eficiência do que nunca."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your product catalog"
msgstr "Gerencie seu catálogo de produtos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your sales pipeline"
msgstr "Gerencie seu funil de vendas"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_12
msgid "Mobius"
msgstr "Mobius"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 24x33cl is your main product"
msgstr "Mobius Blanche - 24x330ml é o principal produto"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 33cl is the unit sale product"
msgstr "Mobius Blanche - 330ml é o produto de venda unitária"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_6
msgid "Mobius Blanche 24x33cl"
msgstr "Mobius Blanche 24x330ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_7
msgid "Mobius Blanche 33cl"
msgstr "Mobius Blanche 330ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_59
msgid "Mobius IPA 24x33cl "
msgstr "Mobius IPA 24x330ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_60
msgid "Mobius IPA 33cl"
msgstr "Mobius IPA 330ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_61
msgid "Mobius Triple 24x33cl"
msgstr "Mobius Triple 24x330ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_62
msgid "Mobius Triple 33cl"
msgstr "Mobius Triple 330ml"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Next"
msgstr "Próximo"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_16
msgid "No"
msgstr "Não"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Odoo offers additional possibilities to enhance your drink distribution "
"business:"
msgstr ""
"O Odoo oferece possibilidades adicionais para aprimorar seu negócio de "
"distribuição de bebidas:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Order online&amp;nbsp;<br/>"
msgstr "Peça on-line<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Our Partners"
msgstr "Nossos parceiros"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"Our professional yet convivial service will make your "
"supply&amp;nbsp;experience unforgettable."
msgstr ""
"Nosso serviço profissional, mas amigável, tonará sua experiência de "
"fornecimento inesquecível."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_bom
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_rr
msgid "Parent product"
msgstr "Produto principal"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Perform efficient stock takes"
msgstr "Realize tomadas de estoque eficientes"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Place orders online 24/7"
msgstr "Faça pedidos on-line 24/7"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Point of Sale (POS) 💳"
msgstr "Ponto de Venda (PDV) 💳"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Previous"
msgstr "Anterior"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process incoming and outgoing shipments"
msgstr "Processar remessas enviadas e recebidas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process quick sales at your distribution center"
msgstr "Processar vendas rápidas em seu centro de distribuição"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Purchase Management 🛒"
msgstr "Gerenciamento de compras 🛒"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Quality Products"
msgstr "Produtos de qualidade"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Reach us"
msgstr "Entre em contato"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_10
msgid "Red"
msgstr "Vermelho"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Regularly update your website with new products and promotions"
msgstr "Atualize regularmente seu site com novos produtos e promoções"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_11
msgid "Rose"
msgstr "Rose"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_65
msgid "Rosé Cosmos - Château de Gragnos 6x75cl"
msgstr "Rosé Cosmos - Château de Gragnos 6x750ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_66
msgid "Rosé Cosmos - Château de Gragnos 75cl"
msgstr "Rosé Cosmos - Château de Gragnos 750ml"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Sales Management 🍾"
msgstr "Gerenciamento de vendas 🍾"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Scan products for quick data entry"
msgstr "Escaneie produtos para entrada rápida de dados"

#. module: beverage_distributor
#: model:website.menu,name:beverage_distributor.website_menu_11
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Services"
msgstr "Serviços"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Set a deposit product on your product to apply these taxes. This will ensure"
" that you invoice correctly the price of consigns."
msgstr ""
"Defina o produto como produto de depósito para aplicar esses impostos. Isso "
"garantirá que o preço das remessas seja faturado corretamente."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up pricing strategies for different customer segments"
msgstr ""
"Estabeleça estratégias de preços para diferentes segmentos de clientes"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up reordering rules for popular products"
msgstr "Configurar regras de reposição para produtos populares"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Shop our products"
msgstr "Compre nossos produtos"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_8
msgid "Soda"
msgstr "Refrigerante"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_11
msgid "Soda type"
msgstr "Tipo de refrigerante"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_1
#: model:pos.category,name:beverage_distributor.pos_category_7
#: model:product.public.category,name:beverage_distributor.product_public_category_1
msgid "Sodas"
msgstr "Refrigerantes"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_13
msgid "Spa"
msgstr "Spa"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_54
msgid "Spa Still 24x25cl"
msgstr "Spa Still 24x250ml"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_57
msgid "Spa Still 25cl"
msgstr "Spa Still 250ml"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_3
#: model:product.public.category,name:beverage_distributor.product_public_category_2
msgid "Spirits"
msgstr "Destilados"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_4
msgid "Stout"
msgstr "Cerveja preta"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Streamline picking and packing processes"
msgstr "Simplificar processos de separação e embalagem"

#. module: beverage_distributor
#: model:delivery.carrier,name:beverage_distributor.delivery_carrier_1
#: model:product.template,name:beverage_distributor.product_product_delivery_product_template
msgid "Take Away"
msgstr "Retirada"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Barcode app enhances your inventory operations:"
msgstr "O aplicativo Código de Barras aprimora suas operações de inventário:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The CRM app helps you:"
msgstr "O aplicativo CRM ajuda a:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Inventory app is the core of your distribution business:"
msgstr "O aplicativo Inventário é o núcleo de seu negócio de distribuição:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Purchase app allows you to:"
msgstr "O aplicativo Purchase permite que você:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The inventory of all products will be updated accordingly."
msgstr "O inventário de todos os produtos será atualizado de acordo."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"These features can be added to your current subscription. Feel free to "
"explore and expand your Odoo experience!"
msgstr ""
"Esses recursos podem ser adicionados à sua assinatura atual. Sinta-se à "
"vontade para explorar e expandir sua experiência com o Odoo!"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This deposit management system is a specific features of this package. "
"Proceed carefully if you need to adapt anything."
msgstr ""
"Esse sistema de gerenciamento de depósitos é um recurso específico deste "
"pacote. Tenha cuidado se for necessário adaptar alguma coisa."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "This place is perfect for groups or a casual date night."
msgstr "O lugar é perfeito para grupos ou para um encontro casual."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This will automatically create a Bill of Material and process a Manufacture "
"Order each time you need to split your product and reflect this operation in"
" your stock perfectly."
msgstr ""
"Isso criará automaticamente uma lista de materiais e processará uma ordem de"
" produção sempre que você precisar dividir o produto, refletindo essa "
"operação perfeitamente em seu estoque."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Para adicionar uma quarta coluna, reduza o tamanho dessas três colunas "
"usando o ícone à direita de cada bloco. Em seguida, duplique uma das colunas"
" para criar uma nova como cópia."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track incoming shipments"
msgstr "Rastrear remessas recebidas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track leads and opportunities"
msgstr "Rastrear leads e oportunidades"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track stock levels across multiple warehouses"
msgstr "Acompanhe os níveis de estoque em vários depósitos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Train your team on efficient use of the barcode system in warehouse "
"operations"
msgstr ""
"Treine sua equipe sobre o uso eficiente do sistema de código de barras nas "
"operações do depósito"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_unit_sale_product
msgid "Unit sale product"
msgstr "Produto de venda unitária"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_9
msgid "Unit sales"
msgstr "Venda unitária"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.update_sales_taxes
msgid "Update Taxes"
msgstr "Atualizar impostos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use barcode scanning for all inventory movements to ensure accuracy"
msgstr ""
"Use a leitura de código de barras em todas as movimentações de estoque para "
"garantir precisão"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Use the Marketing Automation app to nurture leads and promote new products"
msgstr ""
"Use o aplicativo de Automação de Marketing para estimular leads e promover "
"novos produtos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use the Sales app to:"
msgstr "Use o aplicativo Vendas para:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Utilize the Fleet app to manage your delivery vehicles efficiently"
msgstr ""
"Utilize o aplicativo Frotas para efeciência no gerenciamento dos veículos de"
" entrega"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "View their order history and reorder easily"
msgstr "Visualize o histórico de pedidos e faça novos pedidos facilmente"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_7
msgid "Water"
msgstr "Água"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Welcome to your new Odoo Drink Distributors package! This guide will help "
"you navigate the key features and get your distribution business running "
"smoothly, with a focus on inventory management, efficient operations, and "
"online sales."
msgstr ""
"Bem-vindo ao seu novo pacote Odoo Distribuidoras de Bebidas! Este guia o "
"ajudará a navegar pelos principais recursos e a fazer com que seu negócio de"
" distribuição funcione sem problemas, com enfoque para o gerenciamento de "
"estoque, operações eficientes e vendas on-line."

#. module: beverage_distributor
#: model_terms:web_tour.tour,rainbow_man_message:beverage_distributor.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Bem-vindo! Boa exploração."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_1
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_9
msgid "White"
msgstr "Branco"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_12
msgid "Wine type"
msgstr "Tipo de vinho"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_4
#: model:pos.category,name:beverage_distributor.pos_category_8
#: model:product.public.category,name:beverage_distributor.product_public_category_3
msgid "Wines"
msgstr "Vinho"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Would you like to discuss your Odoo setup with us or explore more features?"
msgstr ""
"Gostaria de discutir sua configuração do Odoo conosco ou explorar mais "
"recursos?"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_15
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_25
msgid "Yes"
msgstr "Sim"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"You can also collect deposit directly on your Point of Sale. Select the "
"correct product and encode a negative quantity."
msgstr ""
"Você também pode cobrar o depósito diretamente em seu Ponto de Venda. "
"Selecione o produto correto e codifique uma quantidade negativa."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "You can still do it by upgrading your package in Apps."
msgstr "Você ainda pode fazer isso atualizando seu pacote em Aplicativos."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Your B2B eCommerce portal allows clients to:"
msgstr "Seu portal de e-Commerce B2B permite que os clientes:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[EMPTY 33 BIN] Regular 24x33 Bin is the empty deposit product (2.1$ value)"
msgstr ""
"[EMPTY 33 BIN] A lixeira regular 24x330 é o produto de depósito vazio (valor"
" de US$ 2,1)"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[FULL 33 BIN] Regular 24x33 Bin is the complete deposit product (4.5$ value)"
msgstr ""
"[FULL 33 BIN] A lixeira regular 24x330 é o produto de depósito completo "
"(valor de US$ 4,5)"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "deliveries each week"
msgstr "entregas semanais"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "eCommerce Website 🌐"
msgstr "Site de e-Commerce 🌐"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "ex. Delta I.P.A. - 33cl"
msgstr "ex.: Delta I.P.A. - 330ml"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "for B2B partners"
msgstr "para parceiros B2B"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "happy customers"
msgstr "clientes satisfeitos"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "labeled bottles on rack"
msgstr "garrafas rotuladas na prateleira"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "references"
msgstr "referências"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Barcode"
msgstr "🎓 Código de Barras"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Inventory"
msgstr "🎓 Inventário"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Point of Sale"
msgstr "🎓 Ponto de Venda"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Purchase"
msgstr "🎓 Compras"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 Vendas"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 Site"
