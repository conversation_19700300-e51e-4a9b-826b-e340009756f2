# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* beverage_distributor
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 10:35+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Enjoy our B2B loyalty program<br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;For order beyond 300$<br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Get delivered next week.<br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Relies on amazing partners<br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Visit us to get a free degustation for B2B partners !<br/>"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_17
msgid "+16"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_18
msgid "+18"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Thanks to our new wine specialist, we are now proud to propose more than 100 different wines in our stock.</font>\n"
"                                    <br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Welcome our wine assortment</font>\n"
"                                    <br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<font class=\"text-o-color-5\">​</font>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Next</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Beverages "
"distributor</strong></span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<span class=\"h1-fs\">Basics</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">10%</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Tips for Success</span></strong>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"A truly exquisite drinking experience. I highly recommend the Margarita, out"
" of this world."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"A wide selection of beverages with local products. We’re constantly looking "
"to propose new drinks and flavors. While respecting seasons' products and "
"nature."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Access customer-specific pricing"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Te tri stolpce prilagodite svojim potrebam po oblikovanju. Če želite "
"podvojiti, izbrisati ali premakniti stolpce, izberite stolpec in uporabite "
"zgornje ikone za izvedbo dejanja."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_15
msgid "Age Limit"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_5
msgid "Amber"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Apply DEP XX taxes to these consigns."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"At the Bistro, you can expect a wide selection of beverages to accompany "
"your meal. Come and visit us for a Sunday brunch, refreshing juices full of "
"flavor, or delicious cocktails that satisfy your thirst. And each day, "
"you’ll discover our Today’s Special."
msgstr ""

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.auto_production
msgid "Auto Production"
msgstr ""

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_aut_production
msgid "Auto-production"
msgstr ""

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.bom_automation
msgid "Automated BoM"
msgstr ""

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_2
msgid "B2B"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Barcode Scanning 📱"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Basics"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_10
msgid "Beer type"
msgstr ""

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_2
#: model:pos.category,name:beverage_distributor.pos_category_6
#: model:product.public.category,name:beverage_distributor.product_public_category_4
msgid "Beers"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Beverage Selection"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_2
msgid "Blond"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_16
msgid "Brand"
msgstr "Znamka"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_3
msgid "Brown"
msgstr "Rjava"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_24
msgid "Brussels beer project"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_22
msgid "Bubbles"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_67
msgid "Cava Brut Il Lusio - Josep Masachs 6x75cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_68
msgid "Cava Brut Il Lusio - Josep Masachs 75cl"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_21
msgid "Chaudfontaine"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_14
msgid "Coca-cola"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_51
msgid "Coca-cola 24x33cl"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Collect deposit when you deliver your customer. It is easy. When validating your delivery, you can directly set a \"Return\" and precisely count the number of each type of deposit your customer gave you. It will automatically be\n"
"                        reflected on his invoice or issue a credit note."
msgstr ""

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_quantity_by_deposit_product
msgid "Contains"
msgstr "Vsebuje"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Cosy and friendly, good atmosphere and super drinks. Especially the homemade"
" lemonade."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create and track sales orders"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create deposit products for each different type of consigns"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create purchase orders"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Customer Relationship Management (CRM) 🤝"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_69
msgid "Cuvée Grillo - Villa Carumé 6x75cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_70
msgid "Cuvée Grillo - Villa Carumé 75cl"
msgstr ""

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_01_purchase
#: model:account.tax,name:beverage_distributor.account_tax_01_sale
msgid "DEP 0.1"
msgstr ""

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_21_sale
msgid "DEP 2.1"
msgstr ""

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_24_purchase
#: model:account.tax,name:beverage_distributor.account_tax_24_sale
msgid "DEP 2.4"
msgstr ""

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_45_purchase
#: model:account.tax,name:beverage_distributor.account_tax_45_sale
msgid "DEP 4.5"
msgstr ""

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_1
msgid "Default"
msgstr "Privzeto"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.make_deposit_storable_delivery_invoice
msgid "Default fields for deposits"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Delete the above image or replace it with a picture that showcases our "
"products. Click on the picture to change its rounded corner style."
msgstr ""

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.deposit_tax_group
#: model:pos.category,name:beverage_distributor.pos_category_5
msgid "Deposit"
msgstr "Depozit"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_9
msgid "Deposit 0.1"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_56
msgid "Deposit 2.1"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_53
msgid "Deposit 2.4"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_55
msgid "Deposit 4.5"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Deposit Management 🍻"
msgstr ""

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_deposit_product_1
msgid "Deposit product"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Deposit product must be set in Deposit category"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Do You Want to Go Further?"
msgstr ""

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_21676_sale
msgid "EXC 2.1676"
msgstr ""

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_coca_sale
msgid "EXC COCA"
msgstr ""

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_empty_deposit
msgid "Empty deposit product"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Encourage your B2B clients to use the online ordering system for improved "
"efficiency"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Every time you need to sell a single Mobius Blanche - 33cl, if you don't "
"have enough in stock, it will split your main product in 24 units and take "
"into account an extra empty bin that will be left at the end."
msgstr ""

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.excises_tax_group
msgid "Excises"
msgstr ""

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.action_make_deposit_storable_delivery_invoice
#: model:ir.actions.server,name:beverage_distributor.bom_server_action
#: model:ir.actions.server,name:beverage_distributor.update_sales_taxes_server_action
msgid "Execute Code"
msgstr ""

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.update_state
msgid "Execute Existing Actions"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Fast Delivery"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Follow up with potential clients"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For B2C sales, the POS app enables you to:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For example:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Free shipping&amp;nbsp;<br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Get rewards&amp;nbsp;<br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Handle returns and consignment easily"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_14
msgid "IPA"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to automatically split products sold in a specific packaging, "
"precise a unit sale product in the so-called field and the number of units "
"contained in the main product."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to easily discover every features of this package, try "
"downloading the Demo Data."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Implement the Quality app to ensure consistent product quality"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Implement the Surveys app to gather customer feedback and improve your "
"service"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Inventory Management 📦"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Iris SMITH • CEO of Beverage Co."
msgstr ""

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.x_is_a_deposit
msgid "Is a deposit"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_23
msgid "Jack Daniels"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_71
msgid "Jack Daniels 70cl"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Jane SMITH • CEO of Beverage Co."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "John SMITH • CEO of Beverage Co."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Join the Party."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Just be careful, once demo data are uploaded, it cannot be easily deleted. "
"But you can restart a fresh database on Odoo.com/trial"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Keep your inventory updated in real-time"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Leave empty if this product is the smallest unit"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Leverage the reporting tools across all apps to gain insights into your "
"business performance"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_13
msgid "Local Brand"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_63
msgid "Lou Daro - Château de Gragnos 6x75cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_64
msgid "Lou Daro - Château de Gragnos 75cl"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage consignment for drinks sold in crates"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage supplier relationships"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your deposit more precisely and efficiently than ever before."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your product catalog"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your sales pipeline"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_12
msgid "Mobius"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 24x33cl is your main product"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 33cl is the unit sale product"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_6
msgid "Mobius Blanche 24x33cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_7
msgid "Mobius Blanche 33cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_59
msgid "Mobius IPA 24x33cl "
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_60
msgid "Mobius IPA 33cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_61
msgid "Mobius Triple 24x33cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_62
msgid "Mobius Triple 33cl"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Next"
msgstr "Naprej"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_16
msgid "No"
msgstr "Ne"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Odoo offers additional possibilities to enhance your drink distribution "
"business:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Order online&amp;nbsp;<br/>"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Our Partners"
msgstr "Naši partnerji"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"Our professional yet convivial service will make your "
"supply&amp;nbsp;experience unforgettable."
msgstr ""

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_bom
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_rr
msgid "Parent product"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Perform efficient stock takes"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Place orders online 24/7"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Point of Sale (POS) 💳"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Previous"
msgstr "Nazaj"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process incoming and outgoing shipments"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process quick sales at your distribution center"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Purchase Management 🛒"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Quality Products"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Reach us"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_10
msgid "Red"
msgstr "Rdeča"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Regularly update your website with new products and promotions"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_11
msgid "Rose"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_65
msgid "Rosé Cosmos - Château de Gragnos 6x75cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_66
msgid "Rosé Cosmos - Château de Gragnos 75cl"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Sales Management 🍾"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Scan products for quick data entry"
msgstr ""

#. module: beverage_distributor
#: model:website.menu,name:beverage_distributor.website_menu_11
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Services"
msgstr "Storitve"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Set a deposit product on your product to apply these taxes. This will ensure"
" that you invoice correctly the price of consigns."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up pricing strategies for different customer segments"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up reordering rules for popular products"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Shop our products"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_8
msgid "Soda"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_11
msgid "Soda type"
msgstr ""

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_1
#: model:pos.category,name:beverage_distributor.pos_category_7
#: model:product.public.category,name:beverage_distributor.product_public_category_1
msgid "Sodas"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_13
msgid "Spa"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_54
msgid "Spa Still 24x25cl"
msgstr ""

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_57
msgid "Spa Still 25cl"
msgstr ""

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_3
#: model:product.public.category,name:beverage_distributor.product_public_category_2
msgid "Spirits"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_4
msgid "Stout"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Streamline picking and packing processes"
msgstr ""

#. module: beverage_distributor
#: model:delivery.carrier,name:beverage_distributor.delivery_carrier_1
#: model:product.template,name:beverage_distributor.product_product_delivery_product_template
msgid "Take Away"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Barcode app enhances your inventory operations:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The CRM app helps you:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Inventory app is the core of your distribution business:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Purchase app allows you to:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The inventory of all products will be updated accordingly."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"These features can be added to your current subscription. Feel free to "
"explore and expand your Odoo experience!"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This deposit management system is a specific features of this package. "
"Proceed carefully if you need to adapt anything."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "This place is perfect for groups or a casual date night."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This will automatically create a Bill of Material and process a Manufacture "
"Order each time you need to split your product and reflect this operation in"
" your stock perfectly."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Če želite dodati četrti stolpec, zmanjšajte velikost teh treh stolpcev z "
"desno ikono vsakega bloka. Nato podvojite enega od stolpcev in ustvarite "
"novega kot kopijo."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track incoming shipments"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track leads and opportunities"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track stock levels across multiple warehouses"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Train your team on efficient use of the barcode system in warehouse "
"operations"
msgstr ""

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_unit_sale_product
msgid "Unit sale product"
msgstr ""

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_9
msgid "Unit sales"
msgstr ""

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.update_sales_taxes
msgid "Update Taxes"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use barcode scanning for all inventory movements to ensure accuracy"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Use the Marketing Automation app to nurture leads and promote new products"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use the Sales app to:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Utilize the Fleet app to manage your delivery vehicles efficiently"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "View their order history and reorder easily"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_7
msgid "Water"
msgstr "Voda"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Welcome to your new Odoo Drink Distributors package! This guide will help "
"you navigate the key features and get your distribution business running "
"smoothly, with a focus on inventory management, efficient operations, and "
"online sales."
msgstr ""

#. module: beverage_distributor
#: model_terms:web_tour.tour,rainbow_man_message:beverage_distributor.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_1
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_9
msgid "White"
msgstr "Bela"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_12
msgid "Wine type"
msgstr ""

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_4
#: model:pos.category,name:beverage_distributor.pos_category_8
#: model:product.public.category,name:beverage_distributor.product_public_category_3
msgid "Wines"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Would you like to discuss your Odoo setup with us or explore more features?"
msgstr ""

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_15
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_25
msgid "Yes"
msgstr "Da"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"You can also collect deposit directly on your Point of Sale. Select the "
"correct product and encode a negative quantity."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "You can still do it by upgrading your package in Apps."
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Your B2B eCommerce portal allows clients to:"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[EMPTY 33 BIN] Regular 24x33 Bin is the empty deposit product (2.1$ value)"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[FULL 33 BIN] Regular 24x33 Bin is the complete deposit product (4.5$ value)"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "deliveries each week"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "eCommerce Website 🌐"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "ex. Delta I.P.A. - 33cl"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "for B2B partners"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "happy customers"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "labeled bottles on rack"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "references"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Barcode"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 CRM"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Inventory"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Point of Sale"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Purchase"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Sales"
msgstr ""

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Website"
msgstr ""
