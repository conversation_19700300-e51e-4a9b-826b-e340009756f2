# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* beverage_distributor
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 10:35+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Enjoy our B2B loyalty program<br/>"
msgstr "&amp;nbsp;B2B 고객 로열티 프로그램을 즐겨보세요<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;For order beyond 300$<br/>"
msgstr "&amp;nbsp;300$ 이상 주문 시<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Get delivered next week.<br/>"
msgstr "&amp;nbsp;다음 주에 배송됩니다.<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Relies on amazing partners<br/>"
msgstr "&amp;nbsp;훌륭한 파트너와 함께<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Visit us to get a free degustation for B2B partners !<br/>"
msgstr "&amp;nbsp;B2B 파트너에게 제공되는 무료 시음 세션에 참여하세요!<br/>"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_17
msgid "+16"
msgstr "+16"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_18
msgid "+18"
msgstr "+18"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Thanks to our new wine specialist, we are now proud to propose more than 100 different wines in our stock.</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">새로운 와인 전문가 덕분에 이제 100가지가 넘는 다양한 와인을 선보이게 되었습니다.</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Welcome our wine assortment</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">와인 컬렉션에 오신 것을 환영합니다.</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<font class=\"text-o-color-5\">​</font>"
msgstr "<font class=\"text-o-color-5\">​</font>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">다음</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">다음</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">이전</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">이전</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Beverages "
"distributor</strong></span>"
msgstr "<span class=\"display-4-fs\"><strong>음료 유통업체를 위한 Odoo</strong></span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<span class=\"h1-fs\">Basics</span>"
msgstr "<span class=\"h1-fs\">베이직</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">10%</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">10%</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Tips for Success</span></strong>"
msgstr "<strong><span class=\"h2-fs\">성공을 위한 팁</span></strong>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"A truly exquisite drinking experience. I highly recommend the Margarita, out"
" of this world."
msgstr "특별한 칵테일 경험. 이 세상에서 가장 맛있는 마가리타를 추천합니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"A wide selection of beverages with local products. We’re constantly looking "
"to propose new drinks and flavors. While respecting seasons' products and "
"nature."
msgstr ""
"지역 특산물을 사용한 다양한 음료입니다. 제철 농산물과 자연을 존중하면서 새로운 음료와 맛을 지속적으로 선보이기 위해 노력하고 있습니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Access customer-specific pricing"
msgstr "고객별 요금제에 액세스"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"이 세 개의 열을 설계 요구에 맞게 조정합니다. 열을 복제, 삭제 또는 이동하려면 열을 선택하고 상단 아이콘을 사용하여 작업을 "
"수행합니다."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_15
msgid "Age Limit"
msgstr "연령 제한"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_5
msgid "Amber"
msgstr "Amber"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Apply DEP XX taxes to these consigns."
msgstr "이 화물에 DEP XX 세금을 적용합니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"At the Bistro, you can expect a wide selection of beverages to accompany "
"your meal. Come and visit us for a Sunday brunch, refreshing juices full of "
"flavor, or delicious cocktails that satisfy your thirst. And each day, "
"you’ll discover our Today’s Special."
msgstr ""
"비스트로에서는 식사와 함께 즐길 수 있는 다양한 음료가 준비되어 있습니다. 즐거운 일요일 브런치를 즐기거나 풍미가 가득한 상큼한 주스를 "
"맛보거나 갈증을 해소해 주는 칵테일을 즐겨 보세요. 또한 매일 제공되는 오늘의 스페셜도 놓치지 마세요!"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.auto_production
msgid "Auto Production"
msgstr "자동 생산"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_aut_production
msgid "Auto-production"
msgstr "자동 생산"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.bom_automation
msgid "Automated BoM"
msgstr "자동화된 BoM"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_2
msgid "B2B"
msgstr "B2B"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Barcode Scanning 📱"
msgstr "바코드 스캐닝 📱"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Basics"
msgstr "베이직"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_10
msgid "Beer type"
msgstr "맥주 종류"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_2
#: model:pos.category,name:beverage_distributor.pos_category_6
#: model:product.public.category,name:beverage_distributor.product_public_category_4
msgid "Beers"
msgstr "맥주"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Beverage Selection"
msgstr "음료 선택"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_2
msgid "Blond"
msgstr "블론드"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_16
msgid "Brand"
msgstr "상표"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_3
msgid "Brown"
msgstr "갈색"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_24
msgid "Brussels beer project"
msgstr "브뤼셀 맥주 프로젝트"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_22
msgid "Bubbles"
msgstr "거품"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_67
msgid "Cava Brut Il Lusio - Josep Masachs 6x75cl"
msgstr "까바 브뤼 일 루시오 - 요셉 마삭스 6x75cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_68
msgid "Cava Brut Il Lusio - Josep Masachs 75cl"
msgstr "까바 브뤼 일 루시오 - 요셉 마삭스 75cl"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_21
msgid "Chaudfontaine"
msgstr "쇼퐁텐"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_14
msgid "Coca-cola"
msgstr "코카콜라"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_51
msgid "Coca-cola 24x33cl"
msgstr "코카콜라 24x33cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Collect deposit when you deliver your customer. It is easy. When validating your delivery, you can directly set a \"Return\" and precisely count the number of each type of deposit your customer gave you. It will automatically be\n"
"                        reflected on his invoice or issue a credit note."
msgstr ""
"고객에게 배송할 때 보증금을 받으세요. 간단합니다! 배송을 확인하는 동안 '반품'을 쉽게 설정하고 고객이 제공한 각 유형의 보증금 수를 정확하게 추적할 수 있습니다. 이는 자동으로 인보이스에 반영되거나\n"
"                        크레딧 메모로 발행됩니다."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_quantity_by_deposit_product
msgid "Contains"
msgstr "포함"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Cosy and friendly, good atmosphere and super drinks. Especially the homemade"
" lemonade."
msgstr "아늑하고 친근한 분위기와 훌륭한 음료, 특히 홈메이드 레모네이드가 준비되어 있습니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create and track sales orders"
msgstr "판매 주문 생성 및 추적"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create deposit products for each different type of consigns"
msgstr "다양한 화물 유형별로 입금 상품을 생성하기"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create purchase orders"
msgstr "구매 주문 생성"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Customer Relationship Management (CRM) 🤝"
msgstr "고객 관계 관리 (CRM) 🤝"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_69
msgid "Cuvée Grillo - Villa Carumé 6x75cl"
msgstr "쿠베 그릴로 - 빌라 까루메 6x75cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_70
msgid "Cuvée Grillo - Villa Carumé 75cl"
msgstr "쿠베 그릴로 - 빌라 까루메 75cl"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_01_purchase
#: model:account.tax,name:beverage_distributor.account_tax_01_sale
msgid "DEP 0.1"
msgstr "DEP 0.1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_21_sale
msgid "DEP 2.1"
msgstr "DEP 2.1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_24_purchase
#: model:account.tax,name:beverage_distributor.account_tax_24_sale
msgid "DEP 2.4"
msgstr "DEP 2.4"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_45_purchase
#: model:account.tax,name:beverage_distributor.account_tax_45_sale
msgid "DEP 4.5"
msgstr "DEP 4.5"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_1
msgid "Default"
msgstr "기본"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.make_deposit_storable_delivery_invoice
msgid "Default fields for deposits"
msgstr "입금에 대한 기본 필드"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Delete the above image or replace it with a picture that showcases our "
"products. Click on the picture to change its rounded corner style."
msgstr "위 이미지를 삭제하거나 제품을 강조하는 이미지로 교체합니다. 이미지를 클릭하면 둥근 모서리 스타일을 조정할 수 있습니다."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.deposit_tax_group
#: model:pos.category,name:beverage_distributor.pos_category_5
msgid "Deposit"
msgstr "선결제 금액"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_9
msgid "Deposit 0.1"
msgstr "입금 0.1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_56
msgid "Deposit 2.1"
msgstr "입금 2.1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_53
msgid "Deposit 2.4"
msgstr "입금 2.4"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_55
msgid "Deposit 4.5"
msgstr "입금 4.5"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Deposit Management 🍻"
msgstr "입금 관리 🍻"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_deposit_product_1
msgid "Deposit product"
msgstr "예금 상품"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Deposit product must be set in Deposit category"
msgstr "입금 상품은 입금 카테고리로 분류되어야 합니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Do You Want to Go Further?"
msgstr "더 자세히 알고 싶으신가요?"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_21676_sale
msgid "EXC 2.1676"
msgstr "EXC 2.1676"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_coca_sale
msgid "EXC COCA"
msgstr "EXC COCA"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_empty_deposit
msgid "Empty deposit product"
msgstr "빈 보증금 상품"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Encourage your B2B clients to use the online ordering system for improved "
"efficiency"
msgstr "B2B 고객에게 온라인 주문 시스템을 사용하도록 권장하여 효율성을 높이세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Every time you need to sell a single Mobius Blanche - 33cl, if you don't "
"have enough in stock, it will split your main product in 24 units and take "
"into account an extra empty bin that will be left at the end."
msgstr ""
"모비우스 블랑쉬 - 33캔 하나를 판매해야 하는데 재고가 충분하지 않은 경우 기본 제품을 24개 단위로 나누고 남은 빈 통을 추가로 "
"계산합니다."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.excises_tax_group
msgid "Excises"
msgstr "소비세"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.action_make_deposit_storable_delivery_invoice
#: model:ir.actions.server,name:beverage_distributor.bom_server_action
#: model:ir.actions.server,name:beverage_distributor.update_sales_taxes_server_action
msgid "Execute Code"
msgstr "코드 실행"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.update_state
msgid "Execute Existing Actions"
msgstr "기존 작업 실행"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Fast Delivery"
msgstr "빠른 배송"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Follow up with potential clients"
msgstr "잠재 고객에 대한 후속 조치"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For B2C sales, the POS app enables you to:"
msgstr "B2C 판매의 경우 POS 앱을 사용하면 됩니다:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For example:"
msgstr "예를 들어:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Free shipping&amp;nbsp;<br/>"
msgstr "무료 배송&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Get rewards&amp;nbsp;<br/>"
msgstr "보상 받기&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Handle returns and consignment easily"
msgstr "간편한 반품 및 위탁 처리"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_14
msgid "IPA"
msgstr "IPA"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to automatically split products sold in a specific packaging, "
"precise a unit sale product in the so-called field and the number of units "
"contained in the main product."
msgstr ""
"특정 패키지로 판매되는 제품을 자동으로 분할하려면 해당 필드에 단위 판매 제품을 지정하고 기본 제품에 포함된 단위 수를 입력하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to easily discover every features of this package, try "
"downloading the Demo Data."
msgstr "이 패키지의 모든 기능을 쉽게 탐색하려면 데모 데이터를 다운로드하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Implement the Quality app to ensure consistent product quality"
msgstr "품질 관리 앱을 실행하여 일관된 제품 표준을 유지하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Implement the Surveys app to gather customer feedback and improve your "
"service"
msgstr "설문조사 앱을 실행하여 고객 피드백을 수집하고 서비스를 개선하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Inventory Management 📦"
msgstr "재고 관리 📦"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Iris SMITH • CEO of Beverage Co."
msgstr "Iris SMITH • Beverage Co.의 CEO"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.x_is_a_deposit
msgid "Is a deposit"
msgstr "보증금"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_23
msgid "Jack Daniels"
msgstr "잭 다니엘"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_71
msgid "Jack Daniels 70cl"
msgstr "잭 다니엘 70cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Jane SMITH • CEO of Beverage Co."
msgstr "Jane SMITH •  Beverage Co.의 CEO"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "John SMITH • CEO of Beverage Co."
msgstr "John SMITH • Beverage Co.의 CEO"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Join the Party."
msgstr "파티 참여하기."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Just be careful, once demo data are uploaded, it cannot be easily deleted. "
"But you can restart a fresh database on Odoo.com/trial"
msgstr ""
"데모 데이터가 업로드되면 쉽게 삭제할 수 없으니 주의하세요. 하지만 Odoo.com/trial에서 새 데이터베이스를 시작할 수 있습니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Keep your inventory updated in real-time"
msgstr "재고를 실시간으로 업데이트하세요"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Leave empty if this product is the smallest unit"
msgstr "제품이 가장 작은 단위인 경우 이 필드를 비워둡니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Leverage the reporting tools across all apps to gain insights into your "
"business performance"
msgstr "모든 앱의 보고서 도구를 활용하여 비즈니스 성과에 대한 귀중한 인사이트를 얻으세요."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_13
msgid "Local Brand"
msgstr "지역 브랜드"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_63
msgid "Lou Daro - Château de Gragnos 6x75cl"
msgstr "루 다로 - 샤또 드 그라뇨스 6x75cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_64
msgid "Lou Daro - Château de Gragnos 75cl"
msgstr "루 다로 - 샤또 드 그라뇨스 75cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage consignment for drinks sold in crates"
msgstr "상자 단위로 판매되는 음료 위탁 관리"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage supplier relationships"
msgstr "공급업체와의 관계 관리"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your deposit more precisely and efficiently than ever before."
msgstr "그 어느 때보다 더 정확하고 효율적으로 입금을 관리하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your product catalog"
msgstr "제품 카탈로그 관리"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your sales pipeline"
msgstr "영업 파이프라인 관리"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_12
msgid "Mobius"
msgstr "뫼비우스"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 24x33cl is your main product"
msgstr "뫼비우스 블랑슈 - 24x33cl은 주요 상품입니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 33cl is the unit sale product"
msgstr "뫼비우스 블랑슈 - 33cl은 제품 판매 단위입니다."

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_6
msgid "Mobius Blanche 24x33cl"
msgstr "뫼비우스 블랑쉬 24x33cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_7
msgid "Mobius Blanche 33cl"
msgstr "뫼비우스 블랑쉬 33cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_59
msgid "Mobius IPA 24x33cl "
msgstr "뫼비우스 IPA 24x33cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_60
msgid "Mobius IPA 33cl"
msgstr "뫼비우스 IPA 33cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_61
msgid "Mobius Triple 24x33cl"
msgstr "뫼비우스 트리플 24x33cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_62
msgid "Mobius Triple 33cl"
msgstr "뫼비우스 트리플 33cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Next"
msgstr "다음"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_16
msgid "No"
msgstr "아니오"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Odoo offers additional possibilities to enhance your drink distribution "
"business:"
msgstr "Odoo는 음료 유통 비즈니스를 향상시킬 수 있는 다양한 옵션을 제공합니다:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Order online&amp;nbsp;<br/>"
msgstr "온라인 주문&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Our Partners"
msgstr "협력사 소개"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"Our professional yet convivial service will make your "
"supply&amp;nbsp;experience unforgettable."
msgstr "전문적이면서도 친절한 서비스로 잊을 수 없는 공급 경험을 보장합니다."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_bom
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_rr
msgid "Parent product"
msgstr "상위 품목"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Perform efficient stock takes"
msgstr "효율적인 재고 관리 수행"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Place orders online 24/7"
msgstr "24시간 온라인 주문"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Point of Sale (POS) 💳"
msgstr "POS 💳"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Previous"
msgstr "이전"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process incoming and outgoing shipments"
msgstr "입고 및 출고 배송 처리"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process quick sales at your distribution center"
msgstr "물류 센터에서 신속한 판매 처리"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Purchase Management 🛒"
msgstr "구매 관리  🛒"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Quality Products"
msgstr "고품질 제품"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Reach us"
msgstr "연락하기"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_10
msgid "Red"
msgstr "빨간색"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Regularly update your website with new products and promotions"
msgstr "새로운 제품 및 프로모션으로 웹사이트를 정기적으로 업데이트하세요."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_11
msgid "Rose"
msgstr "장미"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_65
msgid "Rosé Cosmos - Château de Gragnos 6x75cl"
msgstr "로제 코스모스 - 샤또 드 그라뇨스 6x75cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_66
msgid "Rosé Cosmos - Château de Gragnos 75cl"
msgstr "로제 코스모스 - 샤또 드 그라뇨스 75cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Sales Management 🍾"
msgstr "판매 관리 🍾"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Scan products for quick data entry"
msgstr "빠른 데이터 입력을 위한 제품 스캔"

#. module: beverage_distributor
#: model:website.menu,name:beverage_distributor.website_menu_11
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Services"
msgstr "서비스"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Set a deposit product on your product to apply these taxes. This will ensure"
" that you invoice correctly the price of consigns."
msgstr "이 세금을 적용하려면 제품에 입금 상품을 설정하세요. 이렇게 하면 위탁품 가격을 정확하게 인보이스에 표시할 수 있습니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up pricing strategies for different customer segments"
msgstr "다양한 고객 세그먼트에 맞는 가격 책정 전략 설정"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up reordering rules for popular products"
msgstr "인기 상품에 대한 재주문 규칙 설정"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Shop our products"
msgstr "제품 구매하기"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_8
msgid "Soda"
msgstr "소다"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_11
msgid "Soda type"
msgstr "소다 유형"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_1
#: model:pos.category,name:beverage_distributor.pos_category_7
#: model:product.public.category,name:beverage_distributor.product_public_category_1
msgid "Sodas"
msgstr "소다"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_13
msgid "Spa"
msgstr "스파"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_54
msgid "Spa Still 24x25cl"
msgstr "스파 스틸 24x25cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_57
msgid "Spa Still 25cl"
msgstr "스파 스틸 25cl"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_3
#: model:product.public.category,name:beverage_distributor.product_public_category_2
msgid "Spirits"
msgstr "주류"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_4
msgid "Stout"
msgstr "스타우트"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Streamline picking and packing processes"
msgstr "피킹 및 포장 프로세스 간소화"

#. module: beverage_distributor
#: model:delivery.carrier,name:beverage_distributor.delivery_carrier_1
#: model:product.template,name:beverage_distributor.product_product_delivery_product_template
msgid "Take Away"
msgstr "테이크아웃"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Barcode app enhances your inventory operations:"
msgstr "바코드 앱은 재고 관리 작업을 향상시켜 줍니다:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The CRM app helps you:"
msgstr "CRM 앱을 사용하면:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Inventory app is the core of your distribution business:"
msgstr "재고 관리 앱은 유통 비즈니스의 핵심입니다:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Purchase app allows you to:"
msgstr "구매 앱을 사용하면 다음과 같이 할 수 있습니다:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The inventory of all products will be updated accordingly."
msgstr "모든 제품의 재고가 함께 업데이트됩니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"These features can be added to your current subscription. Feel free to "
"explore and expand your Odoo experience!"
msgstr "해당 기능은 기존 구독에 추가할 수 있습니다. 자유롭게 탐색하고 Odoo 경험을 향상시켜 보세요!"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This deposit management system is a specific features of this package. "
"Proceed carefully if you need to adapt anything."
msgstr "이 입금 관리 시스템은 이 패키지의 고유한 기능입니다. 변경이 필요한 경우 신중하게 진행하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "This place is perfect for groups or a casual date night."
msgstr "단체 모임이나 캐주얼한 데이트용으로 완벽한 장소입니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This will automatically create a Bill of Material and process a Manufacture "
"Order each time you need to split your product and reflect this operation in"
" your stock perfectly."
msgstr ""
"이 기능은 제품을 분할해야 할 때마다 자동으로 자재 명세서를 생성하고 제조 주문을 처리하여 이 작업이 재고에 정확하게 반영되도록 합니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"네 번째 열을 추가하려면 각 블록의 오른쪽 아이콘을 사용하여 이 세 열의 크기를 줄이십시오. 그런 다음 열 중 하나를 복제하여 복사본으로"
" 새 열을 만듭니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track incoming shipments"
msgstr "입고 배송 추적"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track leads and opportunities"
msgstr "잠재 고객 및 기회 추적"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track stock levels across multiple warehouses"
msgstr "여러 물류창고의 재고 수준 추적"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Train your team on efficient use of the barcode system in warehouse "
"operations"
msgstr "창고 운영에서 바코드 시스템을 효과적으로 사용하는 방법을 팀원들에게 교육하세요."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_unit_sale_product
msgid "Unit sale product"
msgstr "단위 판매 상품"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_9
msgid "Unit sales"
msgstr "단위 판매량"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.update_sales_taxes
msgid "Update Taxes"
msgstr "업데이트: 세금"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use barcode scanning for all inventory movements to ensure accuracy"
msgstr "모든 재고 이동에 바코드 스캔을 사용하여 정확성을 유지하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Use the Marketing Automation app to nurture leads and promote new products"
msgstr "마케팅 자동화 앱을 사용하여 잠재고객을 육성하고 신제품을 홍보하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use the Sales app to:"
msgstr "판매 앱을 사용하면:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Utilize the Fleet app to manage your delivery vehicles efficiently"
msgstr "차량 관리 앱을 활용하여 배송 차량을 효율적으로 관리하세요."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "View their order history and reorder easily"
msgstr "주문 내역을 확인하고 간편하게 재주문하기"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_7
msgid "Water"
msgstr "물"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Welcome to your new Odoo Drink Distributors package! This guide will help "
"you navigate the key features and get your distribution business running "
"smoothly, with a focus on inventory management, efficient operations, and "
"online sales."
msgstr ""
"새로운 Odoo 음료 유통업체 패키지를 소개합니다! 이 가이드는 재고 관리, 효율적인 운영, 온라인 판매에 중점을 두고 주요 기능을 "
"탐색하고 유통 비즈니스를 원활하게 운영하는 데 도움이 될 것입니다."

#. module: beverage_distributor
#: model_terms:web_tour.tour,rainbow_man_message:beverage_distributor.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "환영합니다! 마음껏 둘러 보세요."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_1
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_9
msgid "White"
msgstr "흰색"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_12
msgid "Wine type"
msgstr "와인 종류"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_4
#: model:pos.category,name:beverage_distributor.pos_category_8
#: model:product.public.category,name:beverage_distributor.product_public_category_3
msgid "Wines"
msgstr "와인"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Would you like to discuss your Odoo setup with us or explore more features?"
msgstr "Odoo 설정에 대해 논의하거나 더 많은 기능에 대해 자세히 알아보고 싶으신가요?"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_15
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_25
msgid "Yes"
msgstr "예"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"You can also collect deposit directly on your Point of Sale. Select the "
"correct product and encode a negative quantity."
msgstr "POS에서 직접 보증금을 수금할 수도 있습니다. 적절한 제품을 선택하고 마이너스 수량을 입력합니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "You can still do it by upgrading your package in Apps."
msgstr "앱에서 패키지를 업그레이드하여 추가 기능을 이용할 수 있습니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Your B2B eCommerce portal allows clients to:"
msgstr "B2B 이커머스 포털을 통해 고객은 다음과 같이 할 수 있습니다:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[EMPTY 33 BIN] Regular 24x33 Bin is the empty deposit product (2.1$ value)"
msgstr "[EMPTY 33 BIN] 일반 24x33 용기는 빈 보증금 상품입니다 (2.1$ 가치)."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[FULL 33 BIN] Regular 24x33 Bin is the complete deposit product (4.5$ value)"
msgstr "[FULL 33 BIN] 일반 24x33 용기는 완전 입금 상품입니다 (4.5$ 가치)."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "deliveries each week"
msgstr "매주 배송"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "eCommerce Website 🌐"
msgstr "이커머스 웹사이트 🌐"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "ex. Delta I.P.A. - 33cl"
msgstr "예: 델타 I.P.A. - 33c"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "for B2B partners"
msgstr "B2B 파트너"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "happy customers"
msgstr "고객을 만족시킵니다."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "labeled bottles on rack"
msgstr "선반에 라벨이 부착된 병"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "references"
msgstr "참조"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Barcode"
msgstr "🎓 바코드"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Inventory"
msgstr "🎓 재고 관리"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Point of Sale"
msgstr "🎓 POS"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Purchase"
msgstr "🎓 구매"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 판매"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 웹사이트"
