# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* beverage_distributor
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-06 10:35+0000\n"
"PO-Revision-Date: 2024-10-06 01:20+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Enjoy our B2B loyalty program<br/>"
msgstr "&amp;nbsp;Unser B2B-Treueprogramm<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;For order beyond 300$<br/>"
msgstr "&amp;nbsp;Für Bestellungen über 300 €<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Get delivered next week.<br/>"
msgstr "&amp;nbsp;Lieferung jede Woche.<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Relies on amazing partners<br/>"
msgstr "&amp;nbsp;Wir vertrauen auf erstklassige Partner<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "&amp;nbsp;Visit us to get a free degustation for B2B partners !<br/>"
msgstr ""
"&amp;nbsp;Besuchen uns für eine kostenlose Verkostung für B2B-Partner!<br/>"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_17
msgid "+16"
msgstr "+16"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_18
msgid "+18"
msgstr "+18"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Thanks to our new wine specialist, we are now proud to propose more than 100 different wines in our stock.</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">Dank unserer Weinexperten können wir nun stolz mehr als 100 unterschiedliche Weine in unserem Bestand führen.</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<font class=\"text-o-color-5\">Welcome our wine assortment</font>\n"
"                                    <br/>"
msgstr ""
"<font class=\"text-o-color-5\">Begrüßen Sie unsere Weinauslese</font>\n"
"                                    <br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<font class=\"text-o-color-5\">​</font>"
msgstr "<font class=\"text-o-color-5\">​</font>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">⚠️</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">✅</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">💡</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"o_editor_banner_icon mb-3 fst-normal\">🚀</i>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Weiter</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Weiter</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                        <span class=\"visually-hidden\">Zurück</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                                    <span class=\"visually-hidden\">Zurück</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"<span class=\"display-4-fs\"><strong>Odoo for Beverages "
"distributor</strong></span>"
msgstr ""
"<span class=\"display-4-fs\"><strong>Odoo für "
"Getränkehändler</strong></span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<span class=\"h1-fs\">Basics</span>"
msgstr "<span class=\"h1-fs\">Grundlagen</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">10%</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">10 %</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">1000+</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">1.000+</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">184</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "<span class=\"s_number display-4 o_default_snippet_text\">2545</span>"
msgstr "<span class=\"s_number display-4 o_default_snippet_text\">2.545</span>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "<strong><span class=\"h2-fs\">Tips for Success</span></strong>"
msgstr "<strong><span class=\"h2-fs\">Tipps zum Erfolg</span></strong>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"A truly exquisite drinking experience. I highly recommend the Margarita, out"
" of this world."
msgstr ""
"Ein wirklich exquisites Trinkerlebnis. Ich empfehle den Margarita, der nicht"
" von dieser Welt ist."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"A wide selection of beverages with local products. We’re constantly looking "
"to propose new drinks and flavors. While respecting seasons' products and "
"nature."
msgstr ""
"Ein breites Sortiment an Getränken mit lokalen Produkten. Wir sind ständig "
"auf der Suche nach neuen Getränken und Geschmäckern. <b>Dabei respektieren "
"wir die Produkte der Saison und die Natur.</b>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Access customer-specific pricing"
msgstr "Erhalten Sie kundenspezifische Preise"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr ""
"Passen Sie diese drei Spalten an Ihre Designanforderungen an. Um Spalten zu "
"duplizieren, zu löschen oder zu verschieben, markieren Sie die Spalte und "
"verwenden Sie die oberen Symbole, um Ihre Aktion auszuführen."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_15
msgid "Age Limit"
msgstr "Altersbeschränkung"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_5
msgid "Amber"
msgstr "Amber"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Apply DEP XX taxes to these consigns."
msgstr "Wenden Sie Steuern für PFAND XX für diese Lieferungen an."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"At the Bistro, you can expect a wide selection of beverages to accompany "
"your meal. Come and visit us for a Sunday brunch, refreshing juices full of "
"flavor, or delicious cocktails that satisfy your thirst. And each day, "
"you’ll discover our Today’s Special."
msgstr ""
"Im Bistro erwartet Sie eine große Auswahl an Getränken, die Sie zu Ihrer "
"Mahlzeit genießen können. Besuchen Sie uns zum Sonntagsbrunch, für "
"erfrischende, geschmackvolle Säfte oder köstliche Cocktails, die Ihren Durst"
" stillen. Und jeden Tag können Sie unser Tagesgetränk entdecken."

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.auto_production
msgid "Auto Production"
msgstr "Automatische Produktion"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_aut_production
msgid "Auto-production"
msgstr "Automatische Produktion"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.bom_automation
msgid "Automated BoM"
msgstr "Automatisierte Stückliste"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_2
msgid "B2B"
msgstr "B2B"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Barcode Scanning 📱"
msgstr "Barcodes scannen 📱"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Basics"
msgstr "Basics"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_10
msgid "Beer type"
msgstr "Biertyp"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_2
#: model:pos.category,name:beverage_distributor.pos_category_6
#: model:product.public.category,name:beverage_distributor.product_public_category_4
msgid "Beers"
msgstr "Biere"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Beverage Selection"
msgstr "Getränkesortiment"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_2
msgid "Blond"
msgstr "Hell"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_16
msgid "Brand"
msgstr "Marke"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_3
msgid "Brown"
msgstr "Braun"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_24
msgid "Brussels beer project"
msgstr "Brussels Beer Project"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_22
msgid "Bubbles"
msgstr "Schaumwein"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_67
msgid "Cava Brut Il Lusio - Josep Masachs 6x75cl"
msgstr "Cava Brut Il Lusio - Josep Masachs 6x75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_68
msgid "Cava Brut Il Lusio - Josep Masachs 75cl"
msgstr "Cava Brut Il Lusio - Josep Masachs 75 cl"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_21
msgid "Chaudfontaine"
msgstr "Chaudfontaine"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_14
msgid "Coca-cola"
msgstr "Coca Cola"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_51
msgid "Coca-cola 24x33cl"
msgstr "Coca Cola 24x33 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Collect deposit when you deliver your customer. It is easy. When validating your delivery, you can directly set a \"Return\" and precisely count the number of each type of deposit your customer gave you. It will automatically be\n"
"                        reflected on his invoice or issue a credit note."
msgstr ""
"Kassieren Sie das Pfand ein, wenn Sie Ihren Kunden beliefern. Es ist ganz einfach. Wenn Sie Ihre Lieferung bestätigen, können Sie direkt eine „Retoure“ festlegen und die Anzahl der von Ihrem Kunden hinterlegten Pfandbeträge genau zählen. Dies wird automatisch auf seiner Rechnung vermerkt\n"
"                         oder es wird eine Gutschrift ausgestellt."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_quantity_by_deposit_product
msgid "Contains"
msgstr "Enthält"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Cosy and friendly, good atmosphere and super drinks. Especially the homemade"
" lemonade."
msgstr ""
"Gemütlich und freundlich, gute Atmosphäre und super Essen. Vor allem die "
"hausgemachte Limonade."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create and track sales orders"
msgstr "Erstellen und verfolgen Sie Verkaufsaufträge"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create deposit products for each different type of consigns"
msgstr "Erstellen Sie Pfandprodukte für jede Art von Lieferungen"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Create purchase orders"
msgstr "Erstellen Sie Bestellungen"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Customer Relationship Management (CRM) 🤝"
msgstr "Customer Relationship Management (CRM) 🤝"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_69
msgid "Cuvée Grillo - Villa Carumé 6x75cl"
msgstr "Cuvée Grillo - Villa Carumé 6x75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_70
msgid "Cuvée Grillo - Villa Carumé 75cl"
msgstr "Cuvée Grillo - Villa Carumé 75 cl"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_01_purchase
#: model:account.tax,name:beverage_distributor.account_tax_01_sale
msgid "DEP 0.1"
msgstr "PFAND 0,1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_21_sale
msgid "DEP 2.1"
msgstr "PFAND 2,1"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_24_purchase
#: model:account.tax,name:beverage_distributor.account_tax_24_sale
msgid "DEP 2.4"
msgstr "PFAND 2,4"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_45_purchase
#: model:account.tax,name:beverage_distributor.account_tax_45_sale
msgid "DEP 4.5"
msgstr "PFAND 4,5"

#. module: beverage_distributor
#: model:product.pricelist,name:beverage_distributor.product_pricelist_1
msgid "Default"
msgstr "Standard"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.make_deposit_storable_delivery_invoice
msgid "Default fields for deposits"
msgstr "Standardfelder für Pfand"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"Delete the above image or replace it with a picture that showcases our "
"products. Click on the picture to change its rounded corner style."
msgstr ""
"Löschen Sie das obige Bild oder ersetzen Sie es durch ein Bild, das Ihre "
"Produkte präsentiert. Klicken Sie auf das Bild, um den Stil der abgerundeten"
" Ecken zu ändern."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.deposit_tax_group
#: model:pos.category,name:beverage_distributor.pos_category_5
msgid "Deposit"
msgstr "Pfand"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_9
msgid "Deposit 0.1"
msgstr "Pfand 0,1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_56
msgid "Deposit 2.1"
msgstr "Pfand 2,1"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_53
msgid "Deposit 2.4"
msgstr "Pfand 2,4"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_55
msgid "Deposit 4.5"
msgstr "Pfand 4,5"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Deposit Management 🍻"
msgstr "Pfandverwaltung 🍻"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_deposit_product_1
msgid "Deposit product"
msgstr "Pfandprodukt"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Deposit product must be set in Deposit category"
msgstr "Pfandprodukt muss in der Pfandkategorie eingestellt sein"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Do You Want to Go Further?"
msgstr "Möchten Sie noch weiter gehen?"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_21676_sale
msgid "EXC 2.1676"
msgstr "VSt. 2.1676"

#. module: beverage_distributor
#: model:account.tax,name:beverage_distributor.account_tax_exc_coca_sale
msgid "EXC COCA"
msgstr "VSt. COLA"

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_empty_deposit
msgid "Empty deposit product"
msgstr "Leeres Pfandprodukt"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Encourage your B2B clients to use the online ordering system for improved "
"efficiency"
msgstr ""
"Motivieren Sie Ihre B2B-Kunden, das Online-Bestellsystem für verbesserte "
"Effizienz zu verwenden."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Every time you need to sell a single Mobius Blanche - 33cl, if you don't "
"have enough in stock, it will split your main product in 24 units and take "
"into account an extra empty bin that will be left at the end."
msgstr ""
"Jedes Mal, wenn Sie eine einzelne Mobius Blanche - 33 cl verkaufen müssen "
"und nicht genug auf Lager haben, wird Ihr Hauptprodukt in 24 Einheiten "
"aufgeteilt und es wird ein zusätzlicher leerer Behälter berücksichtigt, der "
"am Ende übrig bleibt."

#. module: beverage_distributor
#: model:account.tax.group,name:beverage_distributor.excises_tax_group
msgid "Excises"
msgstr "Verbrauchssteuer"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.action_make_deposit_storable_delivery_invoice
#: model:ir.actions.server,name:beverage_distributor.bom_server_action
#: model:ir.actions.server,name:beverage_distributor.update_sales_taxes_server_action
msgid "Execute Code"
msgstr "Code ausführen"

#. module: beverage_distributor
#: model:ir.actions.server,name:beverage_distributor.update_state
msgid "Execute Existing Actions"
msgstr "Vorhandene Aktionen ausführen"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Fast Delivery"
msgstr "Schnelle Lieferung"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Follow up with potential clients"
msgstr "Gewinnen Sie potenzielle Kunden."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For B2C sales, the POS app enables you to:"
msgstr "Die Kassensystemapp ermöglicht Ihnen Folgendes bei B2B-Verkäufen:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "For example:"
msgstr "Zum Beispiel:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Free shipping&amp;nbsp;<br/>"
msgstr "Kostenloser Versand&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Get rewards&amp;nbsp;<br/>"
msgstr "Belohnungen erhalten&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Handle returns and consignment easily"
msgstr "Verarbeiten Sie einfach Retouren und Sendungen."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_14
msgid "IPA"
msgstr "IPA"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to automatically split products sold in a specific packaging, "
"precise a unit sale product in the so-called field and the number of units "
"contained in the main product."
msgstr ""
"Wenn Sie Produkte, die in einer bestimmten Verpackung verkauft werden, "
"automatisch aufteilen möchten, geben Sie im sogenannten Feld die verkaufte "
"Produkteinheit und die Anzahl der im Hauptprodukt enthaltenen Einheiten an."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"If you want to easily discover every features of this package, try "
"downloading the Demo Data."
msgstr ""
"Wenn Sie alle Funktionen dieses Pakets einfach entdecken möchten, laden Sie "
"die Demodaten herunter."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Implement the Quality app to ensure consistent product quality"
msgstr ""
"Implementieren Sie die Qualitätsapp, um eine konstante Produktqualität zu "
"gewährleisten."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Implement the Surveys app to gather customer feedback and improve your "
"service"
msgstr ""
"Implementieren Sie die Umfragenapp, um Kundenfeedback zu sammeln und Ihre "
"Services zu verbessern."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Inventory Management 📦"
msgstr "Lagerverwaltung 📦"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Iris SMITH • CEO of Beverage Co."
msgstr "Iris SMITH • CEO von Beverage Co."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.x_is_a_deposit
msgid "Is a deposit"
msgstr "Ist Pfand"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_23
msgid "Jack Daniels"
msgstr "Jack Daniels"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_71
msgid "Jack Daniels 70cl"
msgstr "Jack Daniels 70 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Jane SMITH • CEO of Beverage Co."
msgstr "Jane SMITH • CEO von Beverage Co."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "John SMITH • CEO of Beverage Co."
msgstr "John SMITH • CEO von Beverage Co."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Join the Party."
msgstr "Nehmen Sie an der Party teil!"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Just be careful, once demo data are uploaded, it cannot be easily deleted. "
"But you can restart a fresh database on Odoo.com/trial"
msgstr ""
"Seien Sie nur vorsichtig, denn sobald Demodaten hochgeladen wurden, können "
"sie nicht einfach gelöscht werden. Sie können jedoch eine neue Datenbank auf"
" Odoo.com/trial neu starten."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Keep your inventory updated in real-time"
msgstr "Aktualisieren Sie Ihen Bestand in Echtzeit."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "Leave empty if this product is the smallest unit"
msgstr "Leer lassen, wenn dieses Produkt die kleinste Einheit ist"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Leverage the reporting tools across all apps to gain insights into your "
"business performance"
msgstr ""
"Nutzen Sie die Berichtstools in allen Apps, um Einblicke in Ihre "
"Unternehmensleistung zu erhalten."

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_13
msgid "Local Brand"
msgstr "Lokale Marke"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_63
msgid "Lou Daro - Château de Gragnos 6x75cl"
msgstr "Lou Daro - Château de Gragnos 6x75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_64
msgid "Lou Daro - Château de Gragnos 75cl"
msgstr "Lou Daro - Château de Gragnos 75 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage consignment for drinks sold in crates"
msgstr ""
"Verwalten Sie die Lieferung von Getränken, die in Kisten verkauft werden."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage supplier relationships"
msgstr "Verwalten Sie die Lieferantenbindungen."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your deposit more precisely and efficiently than ever before."
msgstr "Verwalten Sie Pfand präziser und effizienter als je zuvor."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your product catalog"
msgstr "Verwalten Sie Ihren Produktkatalog."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Manage your sales pipeline"
msgstr "Verwalten Sie Ihre Verkaufspipeline."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_12
msgid "Mobius"
msgstr "Mobius"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 24x33cl is your main product"
msgstr "Mobius Blanche - 24x33 cl ist unser Hauptprodukt"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Mobius Blanche - 33cl is the unit sale product"
msgstr "Mobius Blanche - 33 cl ist das Produkt für Einheitsverkäufe"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_6
msgid "Mobius Blanche 24x33cl"
msgstr "Mobius Blanche 24x33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_7
msgid "Mobius Blanche 33cl"
msgstr "Mobius Blanche 33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_59
msgid "Mobius IPA 24x33cl "
msgstr "Mobius IPA 24x33 cl "

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_60
msgid "Mobius IPA 33cl"
msgstr "Mobius IPA 33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_61
msgid "Mobius Triple 24x33cl"
msgstr "Mobius Triple 24x33 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_62
msgid "Mobius Triple 33cl"
msgstr "Mobius Triple 33 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Next"
msgstr "Weiter"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_16
msgid "No"
msgstr "Nein"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Odoo offers additional possibilities to enhance your drink distribution "
"business:"
msgstr ""
"Odoo bietet Ihnen noch mehr Möglichkeiten, Ihren Getränkehandel zu "
"verbessern:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Order online&amp;nbsp;<br/>"
msgstr "Online bestellen&amp;nbsp;<br/>"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Our Partners"
msgstr "Unsere Partner"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid ""
"Our professional yet convivial service will make your "
"supply&amp;nbsp;experience unforgettable."
msgstr ""
"Unser professioneller sowie freundlicher Service wird Ihre Erfahrung mit "
"unserem Angebot unvergesslich machen."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_bom
#: model:ir.model.fields,field_description:beverage_distributor.field_parent_product_rr
msgid "Parent product"
msgstr "Übergeordnetes Produkt"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Perform efficient stock takes"
msgstr "Nehmen Sie die Bestände effizient auf."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Place orders online 24/7"
msgstr "Bestellen Sie 24/7 online"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Point of Sale (POS) 💳"
msgstr "Kassensystem 💳"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Previous"
msgstr "Zurück"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process incoming and outgoing shipments"
msgstr "Verarbeiten Sie Warenein- und ausgänge."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Process quick sales at your distribution center"
msgstr "Verarbeiten Sie Schnellverkäufe bei Ihrem Großhändler."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Purchase Management 🛒"
msgstr "Einkaufsverwaltung 🛒"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Quality Products"
msgstr "Qualitätsprodukte"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Reach us"
msgstr "Kontakt"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_10
msgid "Red"
msgstr "Rot"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Regularly update your website with new products and promotions"
msgstr ""
"Aktualisieren Sie Ihre Website regelmäßig mit neuen Produkten und "
"Werbeaktionen."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_11
msgid "Rose"
msgstr "Rosé"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_65
msgid "Rosé Cosmos - Château de Gragnos 6x75cl"
msgstr "Rosé Cosmos - Château de Gragnos 6x75 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_66
msgid "Rosé Cosmos - Château de Gragnos 75cl"
msgstr "Rosé Cosmos - Château de Gragnos 75 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Sales Management 🍾"
msgstr "Verkaufsverwaltung 🍾"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Scan products for quick data entry"
msgstr "Scannen Sie Produkte zur schnellen Dateneingabe."

#. module: beverage_distributor
#: model:website.menu,name:beverage_distributor.website_menu_11
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "Services"
msgstr "Dienstleistungen"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Set a deposit product on your product to apply these taxes. This will ensure"
" that you invoice correctly the price of consigns."
msgstr ""
"Legen Sie für Ihr Produkt ein Pfandprodukt fest, um diese Steuern "
"anzuwenden. Dadurch wird sichergestellt, dass Sie den Preis der Sendungen "
"korrekt in Rechnung stellen."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up pricing strategies for different customer segments"
msgstr "Richten Sie Preisstrategien für andere Kundensegmente ein."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Set up reordering rules for popular products"
msgstr "Richten Sie Nachbestellregeln für beliebte Produkte ein."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "Shop our products"
msgstr "Unsere Produkte kaufen"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_8
msgid "Soda"
msgstr "Sprudelig"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_11
msgid "Soda type"
msgstr "Sprudelig/Still"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_1
#: model:pos.category,name:beverage_distributor.pos_category_7
#: model:product.public.category,name:beverage_distributor.product_public_category_1
msgid "Sodas"
msgstr "Sprudelige Getränke"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_13
msgid "Spa"
msgstr "Spa"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_54
msgid "Spa Still 24x25cl"
msgstr "Spa Still 24x25 cl"

#. module: beverage_distributor
#: model:product.template,name:beverage_distributor.product_template_57
msgid "Spa Still 25cl"
msgstr "Spa Still 25 cl"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_3
#: model:product.public.category,name:beverage_distributor.product_public_category_2
msgid "Spirits"
msgstr "Spirituosen"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_4
msgid "Stout"
msgstr "Starkbier"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Streamline picking and packing processes"
msgstr "Rationalisieren Sie Kommissionierungen und Packvorgänge"

#. module: beverage_distributor
#: model:delivery.carrier,name:beverage_distributor.delivery_carrier_1
#: model:product.template,name:beverage_distributor.product_product_delivery_product_template
msgid "Take Away"
msgstr "Zum Mitnehmen"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Barcode app enhances your inventory operations:"
msgstr "Die Barcode-App verbessert die Lagervorgänge:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The CRM app helps you:"
msgstr "Die CRM-App hilft Ihnen wie folgt:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Inventory app is the core of your distribution business:"
msgstr "Die Lagerapp ist das Herz Ihres Handels:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The Purchase app allows you to:"
msgstr "Die Einkaufsapp ermöglicht Ihnen Folgendes:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "The inventory of all products will be updated accordingly."
msgstr ""
"Der Bestand aller Produkte wird automatisch entsprechend aktualisiert."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"These features can be added to your current subscription. Feel free to "
"explore and expand your Odoo experience!"
msgstr ""
"Diese Funktionen können zu Ihrem aktuellen Abonnement hinzugefügt werden. "
"Erforschen und erweitern Sie die Möglichkeiten von Odoo!"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This deposit management system is a specific features of this package. "
"Proceed carefully if you need to adapt anything."
msgstr ""
"Dieses Pfandverwaltungssystem ist eine Besonderheit dieses Pakets. Gehen Sie"
" vorsichtig vor, wenn Sie etwas anpassen müssen."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid "This place is perfect for groups or a casual date night."
msgstr "Der perfekte Ort für Gruppen und lockere Verabredungen."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"This will automatically create a Bill of Material and process a Manufacture "
"Order each time you need to split your product and reflect this operation in"
" your stock perfectly."
msgstr ""
"Dadurch wird automatisch eine Stückliste erstellt und jedes Mal ein "
"Fertigungsauftrag bearbeitet, wenn Sie Ihr Produkt aufteilen müssen, und "
"dieser Vorgang wird in Ihrem Lagerbestand perfekt wiedergegeben."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.services
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr ""
"Um eine vierte Spalte hinzuzufügen, verkleinern Sie diese drei Spalten mit "
"dem rechten Symbol des jeweiligen Blocks. Duplizieren Sie dann eine der "
"Spalten, um eine neue Spalte als Kopie zu erstellen."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track incoming shipments"
msgstr "Verfolgen Sie Wareneingänge."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track leads and opportunities"
msgstr "Verfolgen Sie Leads und Verkaufschancen."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Track stock levels across multiple warehouses"
msgstr "Verfolgen Sie Bestandslevels in allen Lagerhäusern."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Train your team on efficient use of the barcode system in warehouse "
"operations"
msgstr ""
"Schulen Sie Ihr Team in der effizienten Nutzung des Barcodesystems im "
"Lagerbetrieb."

#. module: beverage_distributor
#: model:ir.model.fields,field_description:beverage_distributor.field_unit_sale_product
msgid "Unit sale product"
msgstr "Produkt für Einheitsverkauf"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_9
msgid "Unit sales"
msgstr "Einheitsverkäufe"

#. module: beverage_distributor
#: model:base.automation,name:beverage_distributor.update_sales_taxes
msgid "Update Taxes"
msgstr "Steuern aktualisieren"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use barcode scanning for all inventory movements to ensure accuracy"
msgstr ""
"Verwenden Sie Barcodes für alle Lagerbewegungen, um die Genauigkeit "
"sicherzustellen."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Use the Marketing Automation app to nurture leads and promote new products"
msgstr ""
"Verwenden Sie die Marketing-Automatisierungsapp, um Leads zu pflegen und "
"neue Produkte zu bewerben."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Use the Sales app to:"
msgstr "Verwenden Sie die Verkaufsapp:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Utilize the Fleet app to manage your delivery vehicles efficiently"
msgstr ""
"Verwenden Sie die Fuhrparkapp, um Ihre Lieferwagen effizient zu verwalten."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "View their order history and reorder easily"
msgstr "Sehen Sie den Bestellverlauf und bestellen Sie einfach neu."

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_7
msgid "Water"
msgstr "Wasser"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Welcome to your new Odoo Drink Distributors package! This guide will help "
"you navigate the key features and get your distribution business running "
"smoothly, with a focus on inventory management, efficient operations, and "
"online sales."
msgstr ""
"Willkommen bei Ihrem neuen Odoo-Paket für Getränkehändler! Dieser Leitfaden "
"hilft Ihnen, sich in den wichtigsten Funktionen zurechtzufinden und Ihr "
"Handelsgeschäft reibungslos zu gestalten, wobei der Schwerpunkt auf "
"Bestandsverwaltung, effizienten Abläufen und Online-Verkäufen liegt."

#. module: beverage_distributor
#: model_terms:web_tour.tour,rainbow_man_message:beverage_distributor.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Willkommen! Viel Spaß beim Erkunden!"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_1
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_9
msgid "White"
msgstr "Weiß"

#. module: beverage_distributor
#: model:product.attribute,name:beverage_distributor.product_attribute_12
msgid "Wine type"
msgstr "Weintyp"

#. module: beverage_distributor
#: model:pos.category,name:beverage_distributor.pos_category_4
#: model:pos.category,name:beverage_distributor.pos_category_8
#: model:product.public.category,name:beverage_distributor.product_public_category_3
msgid "Wines"
msgstr "Weine"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"Would you like to discuss your Odoo setup with us or explore more features?"
msgstr ""
"Möchten Sie Ihre Odoo-Einrichtung mit uns besprechen oder weitere Funktionen"
" entdecken?"

#. module: beverage_distributor
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_15
#: model:product.attribute.value,name:beverage_distributor.product_attribute_value_25
msgid "Yes"
msgstr "Ja"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"You can also collect deposit directly on your Point of Sale. Select the "
"correct product and encode a negative quantity."
msgstr ""
"Sie können die Pfandbeträge auch direkt in Ihrem Kassensystem einziehen. "
"Wählen Sie das richtige Produkt aus und geben Sie eine negative Menge ein."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "You can still do it by upgrading your package in Apps."
msgstr ""
"Sie können dies weiterhin tun, indem Sie Ihr Paket in Apps aktualisieren."

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "Your B2B eCommerce portal allows clients to:"
msgstr "Ihr B2B-E-Commerce-Portal ermöglicht Ihren Kunden Folgendes:"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[EMPTY 33 BIN] Regular 24x33 Bin is the empty deposit product (2.1$ value)"
msgstr ""
"[LEERER 33-Behälter] Regulärer Behälter 24x33 ist das leere Pfandprodukt "
"(Wert von 2,1 €)"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid ""
"[FULL 33 BIN] Regular 24x33 Bin is the complete deposit product (4.5$ value)"
msgstr ""
"[VOLLER 33-Behälter] Regulärer Behälter 24x33 ist das volle Pfandprodukt "
"(Wert von 4,5 €)"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "deliveries each week"
msgstr "Lieferungen pro Woche"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "eCommerce Website 🌐"
msgstr "E-Commerce-Website 🌐"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.product_template_form_custom
msgid "ex. Delta I.P.A. - 33cl"
msgstr "z. B. Delta I.P.A. - 33 cl"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "for B2B partners"
msgstr "für B2B-Partner"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "happy customers"
msgstr "zufriedene Kunden"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "labeled bottles on rack"
msgstr "Flaschen mit Etikett auf Regal"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.homepage
msgid "references"
msgstr "Referenzen"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Barcode"
msgstr "🎓 Barcode"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 CRM"
msgstr "🎓 CRM"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Inventory"
msgstr "🎓 Lager"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Point of Sale"
msgstr "🎓 Kassensystem"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Purchase"
msgstr "🎓 Einkauf"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Sales"
msgstr "🎓 Verkauf"

#. module: beverage_distributor
#: model_terms:ir.ui.view,arch_db:beverage_distributor.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 Website"
