<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_attribute_10" model="product.attribute">
        <field name="display_type">color</field>
        <field name="create_variant">no_variant</field>
        <field name="name">Beer type</field>
        <field name="sequence" eval="-7"/>
    </record>
    <record id="product_attribute_11" model="product.attribute">
        <field name="create_variant">no_variant</field>
        <field name="name">Soda type</field>
        <field name="sequence" eval="-1"/>
    </record>
    <record id="product_attribute_12" model="product.attribute">
        <field name="display_type">color</field>
        <field name="create_variant">no_variant</field>
        <field name="name">Wine type</field>
        <field name="sequence" eval="-2"/>
    </record>
    <record id="product_attribute_13" model="product.attribute">
        <field name="create_variant">no_variant</field>
        <field name="name">Local Brand</field>
        <field name="sequence" eval="-3"/>
    </record>
    <record id="product_attribute_14" model="product.attribute">
        <field name="create_variant">no_variant</field>
        <field name="name">IPA</field>
        <field name="sequence" eval="-4"/>
    </record>
    <record id="product_attribute_15" model="product.attribute">
        <field name="name">Age Limit</field>
        <field name="create_variant">no_variant</field>
        <field name="sequence" eval="-5"/>
    </record>
    <record id="product_attribute_16" model="product.attribute">
        <field name="create_variant">no_variant</field>
        <field name="name">Brand</field>
        <field name="sequence" eval="-6"/>
    </record>
</odoo>
