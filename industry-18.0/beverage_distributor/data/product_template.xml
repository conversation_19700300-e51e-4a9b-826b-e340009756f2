<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_53" model="product.template">
        <field name="name">Deposit 2.4</field>
        <field name="categ_id" ref="product_category_7"/>
        <field name="list_price" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="service_type">manual</field>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/9-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="taxes_id" eval="[(6, 0, [ref('account_tax_24_sale')])]"/>
        <field name="supplier_taxes_id" eval="[(6, 0, [ref('account_tax_24_purchase')])]"/>
    </record>
    <record id="product_template_51" model="product.template" context="{'create_product_product': False}">
        <field name="name">Coca-cola 24x33cl</field>
        <field name="categ_id" ref="product_category_12"/>
        <field name="list_price">38.0</field>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="x_deposit_product_1" ref="product_template_53"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/51-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="x_quantity_by_deposit_product">24</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_1')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_1')])]"/>
    </record>
    <record id="product_template_55" model="product.template" >
        <field name="name">Deposit 4.5</field>
        <field name="categ_id" ref="product_category_7"/>
        <field name="list_price" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/9-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="x_quantity_by_deposit_product">24</field>
        <field name="taxes_id" eval="[(6, 0, [ref('account_tax_45_sale')])]"/>
        <field name="supplier_taxes_id" eval="[(6, 0, [ref('account_tax_45_purchase')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_5')])]"/>
    </record>
    <record id="product_template_56" model="product.template" >
        <field name="name">Deposit 2.1</field>
        <field name="categ_id" ref="product_category_7"/>
        <field name="list_price" eval="False"/>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/9-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="x_quantity_by_deposit_product">24</field>
        <field name="taxes_id" eval="[(6, 0, [ref('account_tax_21_sale')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_5')])]"/>
    </record>
    <record id="product_template_9" model="product.template" >
        <field name="name">Deposit 0.1</field>
        <field name="categ_id" ref="product_category_7"/>
        <field name="list_price" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/9-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="taxes_id" eval="[(6, 0, [ref('account_tax_01_sale')])]"/>
        <field name="supplier_taxes_id" eval="[(6, 0, [ref('account_tax_01_purchase')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_5')])]"/>
    </record>
    <record id="product_template_60" model="product.template" context="{'create_product_product': False}">
        <field name="name">Mobius IPA 33cl</field>
        <field name="categ_id" ref="product_category_9"/>
        <field name="list_price">2.5</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('mrp.route_warehouse0_manufacture')])]"/>
        <field name="x_deposit_product_1" ref="product_template_9"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/60-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_6')])]"/>
    </record>
    <record id="product_template_57" model="product.template"  context="{'create_product_product': False}">
        <field name="name">Spa Still 25cl</field>
        <field name="categ_id" ref="product_category_12"/>
        <field name="list_price">1.2</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('mrp.route_warehouse0_manufacture')])]"/>
        <field name="x_deposit_product_1" ref="product_template_9"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/57-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_1')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_7')])]"/>
    </record>
    <record id="product_template_7" model="product.template" context="{'create_product_product': False}">
        <field name="name">Mobius Blanche 33cl</field>
        <field name="categ_id" ref="product_category_9"/>
        <field name="list_price">2.5</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('mrp.route_warehouse0_manufacture')])]"/>
        <field name="x_deposit_product_1" ref="product_template_9"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/7-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_6')])]"/>
    </record>
    <record id="product_template_62" model="product.template" context="{'create_product_product': False}">
        <field name="name">Mobius Triple 33cl</field>
        <field name="categ_id" ref="product_category_9"/>
        <field name="list_price">2.5</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('mrp.route_warehouse0_manufacture')])]"/>
        <field name="x_deposit_product_1" ref="product_template_9"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/62-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_6')])]"/>
    </record>
    <record id="product_template_64" model="product.template" context="{'create_product_product': False}">
        <field name="name">Lou Daro - Château de Gragnos 75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">10.0</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/63-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_8')])]"/>
    </record>
    <record id="product_template_66" model="product.template" context="{'create_product_product': False}">
        <field name="name">Rosé Cosmos - Château de Gragnos 75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">10.0</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('mrp.route_warehouse0_manufacture')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/65-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_8')])]"/>
    </record>
    <record id="product_template_68" model="product.template" context="{'create_product_product': False}" >
        <field name="name">Cava Brut Il Lusio - Josep Masachs 75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">12.0</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('mrp.route_warehouse0_manufacture')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/68-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_8')])]"/>
    </record>
    <record id="product_template_70" model="product.template" context="{'create_product_product': False}">
        <field name="name">Cuvée Grillo - Villa Carumé 75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">10.0</field>
        <field name="purchase_ok" eval="False"/>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('mrp.route_warehouse0_manufacture')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/70-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_8')])]"/>
    </record>
    <record id="product_template_71" model="product.template" context="{'create_product_product': False}">
        <field name="name">Jack Daniels 70cl</field>
        <field name="categ_id" ref="product_category_13"/>
        <field name="list_price">19.99</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/71-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_2')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_3')])]"/>
    </record>
</odoo>
