<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="res_config_settings_enable" model="res.config.settings">
        <field name="group_mrp_byproducts" eval="1"/>
        <field name="group_mrp_wo_shop_floor" eval="0"/>
        <field name="group_product_pricelist" eval="1"/>
        <field name="group_product_variant" eval="1"/>
        <field name="pos_use_pricelist" eval="1"/>
        <field name="show_availability" eval="1"/>
    </record>
    <function model="res.config.settings" name="execute" eval="[ref('res_config_settings_enable')]"/>
</odoo>
