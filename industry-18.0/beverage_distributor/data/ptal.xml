<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_attribute_line_72" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_14"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_25')])]"/>
        <field name="product_tmpl_id" ref="product_template_65"/>
    </record>
    <record id="product_template_attribute_line_71" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_14"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_25')])]"/>
        <field name="product_tmpl_id" ref="product_template_60"/>
    </record>
    <record id="product_template_attribute_line_40" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_17')])]"/>
        <field name="product_tmpl_id" ref="product_template_62"/>
    </record>
    <record id="product_template_attribute_line_32" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_17')])]"/>
        <field name="product_tmpl_id" ref="product_template_60"/>
    </record>
    <record id="product_template_attribute_line_20" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_17')])]"/>
        <field name="product_tmpl_id" ref="product_template_7"/>
    </record>
    <record id="product_template_attribute_line_46" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_64"/>
    </record>
    <record id="product_template_attribute_line_66" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_71"/>
    </record>
    <record id="product_template_attribute_line_64" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_70"/>
    </record>
    <record id="product_template_attribute_line_58" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_68"/>
    </record>
    <record id="product_template_attribute_line_52" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_66"/>
    </record>
    <record id="product_template_attribute_line_38" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_12')])]"/>
        <field name="product_tmpl_id" ref="product_template_62"/>
    </record>
    <record id="product_template_attribute_line_30" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_12')])]"/>
        <field name="product_tmpl_id" ref="product_template_60"/>
    </record>
    <record id="product_template_attribute_line_11" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_13')])]"/>
        <field name="product_tmpl_id" ref="product_template_57"/>
    </record>
    <record id="product_template_attribute_line_14" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_14')])]"/>
        <field name="product_tmpl_id" ref="product_template_51"/>
    </record>
    <record id="product_template_attribute_line_4" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_12')])]"/>
        <field name="product_tmpl_id" ref="product_template_7"/>
    </record>
    <record id="product_template_attribute_line_65" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_23')])]"/>
        <field name="product_tmpl_id" ref="product_template_71"/>
    </record>


    <record id="product_template_attribute_value_71" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_71"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_25"/>
    </record>
    <record id="product_template_attribute_value_44" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_40"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_17"/>
    </record>
    <record id="product_template_attribute_value_34" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_32"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_17"/>
    </record>
    <record id="product_template_attribute_value_21" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_20"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_17"/>
    </record>
    <record id="product_template_attribute_value_51" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_46"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_template_attribute_value_77" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_66"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_template_attribute_value_73" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_64"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_template_attribute_value_66" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_58"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_template_attribute_value_58" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_52"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_template_attribute_value_42" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_38"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_12"/>
    </record>
    <record id="product_template_attribute_value_32" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_30"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_12"/>
    </record>
    <record id="product_template_attribute_value_12" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_11"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_13"/>
    </record>
    <record id="product_template_attribute_value_15" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_14"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_14"/>
    </record>
    <record id="product_template_attribute_value_5" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_4"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_12"/>
    </record>
    <record id="product_template_attribute_value_76" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_65"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_23"/>
    </record>
</odoo>
