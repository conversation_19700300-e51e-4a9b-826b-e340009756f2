<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="field_aut_production" model="ir.model.fields">
        <field name="name">x_auto_production</field>
        <field name="field_description">Auto-production</field>
        <field name="ttype">boolean</field>
        <field name="model_id" ref="mrp.model_mrp_bom"/>
    </record>
    <record id="field_quantity_by_deposit_product" model="ir.model.fields">
        <field name="name">x_quantity_by_deposit_product</field>
        <field name="field_description">Contains</field>
        <field name="ttype">integer</field>
        <field name="model_id" ref="product.model_product_template"/>
    </record>
    <record id="field_parent_product_bom" model="ir.model.fields">
        <field name="name">x_parent_product</field>
        <field name="field_description">Parent product</field>
        <field name="ttype">many2one</field>
        <field name="model_id" ref="mrp.model_mrp_bom"/>
        <field name="relation">product.template</field>
    </record>
    <record id="field_parent_product_rr" model="ir.model.fields">
        <field name="name">x_parent_product</field>
        <field name="field_description">Parent product</field>
        <field name="ttype">many2one</field>
        <field name="model_id" ref="stock.model_stock_warehouse_orderpoint"/>
        <field name="relation">product.template</field>
    </record>
    <record id="x_is_a_deposit" model="ir.model.fields">
        <field name="name">x_is_a_deposit</field>
        <field name="field_description">Is a deposit</field>
        <field name="ttype">boolean</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="depends">categ_id</field>
        <field name="readonly" eval="True"/>
        <field name="compute"><![CDATA[
for product in self:
    product['x_is_a_deposit'] = product.categ_id == self.env.ref('beverage_distributor.product_category_7')
        ]]></field>
    </record>
    <record id="field_empty_deposit" model="ir.model.fields">
        <field name="name">x_deposit_product</field>
        <field name="field_description">Empty deposit product</field>
        <field name="ttype">many2one</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="relation">product.template</field>
        <field name="domain">[('x_is_a_deposit', '=', True)]</field>
    </record>
    <record id="field_unit_sale_product" model="ir.model.fields">
        <field name="name">x_unit_sale_product</field>
        <field name="field_description">Unit sale product</field>
        <field name="ttype">many2one</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="relation">product.template</field>
        <field name="copied" eval="False"/>
    </record>
    <record id="field_deposit_product_1" model="ir.model.fields">
        <field name="name">x_deposit_product_1</field>
        <field name="field_description">Deposit product</field>
        <field name="ttype">many2one</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="relation">product.template</field>
        <field name="domain">[('x_is_a_deposit', '=', True)]</field>
    </record>
</odoo>
