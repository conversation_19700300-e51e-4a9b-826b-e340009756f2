<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_delivery" model="product.product">
        <field name="name">Take Away</field>
        <field name="default_code">Delivery_008</field>
        <field name="type">service</field>
        <field name="categ_id" ref="delivery.product_category_deliveries"/>
        <field name="sale_ok" eval="False"/>
        <field name="purchase_ok" eval="False"/>
        <field name="list_price">0.0</field>
    </record>
    <record id="delivery_carrier_1" model="delivery.carrier">
        <field name="name">Take Away</field>
        <field name="fixed_price">0.0</field>
        <field name="sequence">1</field>
        <field name="delivery_type">fixed</field>
        <field name="product_id" ref="product_product_delivery"/>
    </record>
</odoo>
