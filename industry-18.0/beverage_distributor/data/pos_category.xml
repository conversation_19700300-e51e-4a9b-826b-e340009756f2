<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="pos_category_1" model="pos.category">
        <field name="name">Sodas</field>
        <field name="sequence">1</field>
        <field name="color">3</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/1-image_128"/>
    </record>
    <record id="pos_category_2" model="pos.category">
        <field name="name">Beers</field>
        <field name="color">2</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/2-image_128"/>
    </record>
    <record id="pos_category_3" model="pos.category">
        <field name="name">Spirits</field>
        <field name="sequence">2</field>
        <field name="color">4</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/3-image_128"/>
    </record>
    <record id="pos_category_4" model="pos.category">
        <field name="name">Wines</field>
        <field name="sequence">3</field>
        <field name="color">1</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/4-image_128"/>
    </record>
    <record id="pos_category_5" model="pos.category">
        <field name="name">Deposit</field>
        <field name="sequence">4</field>
        <field name="color">6</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/5-image_128"/>
    </record>
    <record id="pos_category_9" model="pos.category">
        <field name="name">Unit sales</field>
        <field name="color">7</field>
    </record>
    <record id="pos_category_6" model="pos.category">
        <field name="name">Beers</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/6-image_128"/>
        <field name="parent_id" ref="pos_category_9"/>
        <field name="color">7</field>
    </record>
    <record id="pos_category_7" model="pos.category">
        <field name="name">Sodas</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/7-image_128"/>
        <field name="parent_id" ref="pos_category_9"/>
        <field name="color">7</field>
    </record>
    <record id="pos_category_8" model="pos.category">
        <field name="name">Wines</field>
        <field name="image_128" type="base64" file="beverage_distributor/static/src/binary/pos_category/8-image_128"/>
        <field name="parent_id" ref="pos_category_9"/>
        <field name="color">7</field>
    </record>
</odoo>
