<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id='deposit_tax_group' model='account.tax.group'>
      <field name='name'>Deposit</field>
    </record>

    <record id="account_tax_01_sale" model="account.tax">
        <field name="amount">0.1</field>
        <field name="amount_type">fixed</field>
        <field name="name">DEP 0.1</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="deposit_tax_group"/>
    </record>
    <record id="account_tax_21_sale" model="account.tax">
        <field name="amount">2.1</field>
        <field name="amount_type">fixed</field>
        <field name="name">DEP 2.1</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="deposit_tax_group"/>
    </record>
    <record id="account_tax_24_sale" model="account.tax">
        <field name="amount">2.4</field>
        <field name="amount_type">fixed</field>
        <field name="name">DEP 2.4</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="deposit_tax_group"/>
    </record>
    <record id="account_tax_45_sale" model="account.tax">
        <field name="amount">4.5</field>
        <field name="amount_type">fixed</field>
        <field name="name">DEP 4.5</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="deposit_tax_group"/>
    </record>
    <record id="account_tax_01_purchase" model="account.tax">
        <field name="amount">0.1</field>
        <field name="amount_type">fixed</field>
        <field name="name">DEP 0.1</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="deposit_tax_group"/>
    </record>
    <record id="account_tax_24_purchase" model="account.tax">
        <field name="amount">2.4</field>
        <field name="amount_type">fixed</field>
        <field name="name">DEP 2.4</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="deposit_tax_group"/>
    </record>
    <record id="account_tax_45_purchase" model="account.tax">
        <field name="amount">4.5</field>
        <field name="amount_type">fixed</field>
        <field name="name">DEP 4.5</field>
        <field name="type_tax_use">purchase</field>
        <field name="tax_group_id" ref="deposit_tax_group"/>
    </record>

    <record id='excises_tax_group' model='account.tax.group'>
        <field name='name'>Excises</field>
      </record>

    <record id="account_tax_exc_coca_sale" model="account.tax">
        <field name="amount">1.21</field>
        <field name="amount_type">fixed</field>
        <field name="name">EXC COCA</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="excises_tax_group"/>
    </record>
    <record id="account_tax_exc_21676_sale" model="account.tax">
        <field name="amount">2.1676</field>
        <field name="amount_type">fixed</field>
        <field name="name">EXC 2.1676</field>
        <field name="type_tax_use">sale</field>
        <field name="tax_group_id" ref="excises_tax_group"/>
    </record>
</odoo>
