<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="bom_server_action" model="ir.actions.server">
        <field name="code"><![CDATA[
if record.x_quantity_by_deposit_product == 1 and record.x_unit_sale_product.id:
    raise UserError("Unit sale product should be undefined if this product can not be sold in a smaller unit.")
if record.x_unit_sale_product:
    existing_bom = env['mrp.bom'].search([('product_tmpl_id', '=',record.x_unit_sale_product.id)], limit=1)
    reordering_rule = env['stock.warehouse.orderpoint'].search_count([('product_id', '=', record.x_unit_sale_product.product_variant_id.id)], limit=1)
    bom_vals = {
        'product_qty': record.x_quantity_by_deposit_product,
        'type': 'normal',
        'x_parent_product': record.id,
        'x_auto_production': True,
        'bom_line_ids': [(5, 0), (0, 0, {
            'product_id': record.product_variant_id.id,
            'product_qty': 1,
        })],
    }
    if record.x_deposit_product:
        bom_vals['byproduct_ids'] = [(5, 0), (0, 0, {
            'product_id': record.x_deposit_product.product_variant_id.id,
            'product_qty': 1.0,
        })]
    if existing_bom:
        existing_bom.write(bom_vals)
    else:
        bom_vals['product_tmpl_id'] = record.x_unit_sale_product.id
        env['mrp.bom'].create(bom_vals)
    if not reordering_rule:
        env['stock.warehouse.orderpoint'].create({
            'product_id': record.x_unit_sale_product.product_variant_id.id,
            'x_parent_product': record.id,
            'product_min_qty': 0,
            'product_max_qty': 0,
            'qty_multiple': record.x_quantity_by_deposit_product or 1,
            'route_id': env.ref('mrp.route_warehouse0_manufacture').id,
            'location_id': env.ref('stock.stock_location_stock').id,
        })
else:
    existing_bom = env['mrp.bom'].search([('x_parent_product', '=',record.id)], limit=1)
    reordering_rule = env['stock.warehouse.orderpoint'].search([('x_parent_product', '=', record.id)], limit=1)
    if existing_bom:
        existing_bom.unlink()
    if reordering_rule:
        reordering_rule.unlink()
]]></field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="name">Execute Code</field>
    </record>
    <record id="update_sales_taxes_server_action" model="ir.actions.server">
        <field name="code"><![CDATA[
product_taxe = record.taxes_id.filtered(lambda t: t.tax_group_id.name != 'Deposit')
supplier_taxe = record.supplier_taxes_id.filtered(lambda t: t.tax_group_id.name != 'Deposit')
if record.x_deposit_product_1:
    product_taxe += record.x_deposit_product_1.taxes_id
    supplier_taxe += record.x_deposit_product_1.supplier_taxes_id
record['taxes_id'] = [(6, 0, (product_taxe).ids)]
record['supplier_taxes_id'] = [(6, 0, (supplier_taxe).ids)]
]]></field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="name">Execute Code</field>
    </record>
    <record id="update_state" model="ir.actions.server">
        <field name="child_ids" eval="[(6, 0, [ref('mrp.action_production_order_mark_done')])]"/>
        <field name="model_id" ref="mrp.model_mrp_production"/>
        <field name="state">multi</field>
        <field name="name">Execute Existing Actions</field>
    </record>
    <record id="action_make_deposit_storable_delivery_invoice" model="ir.actions.server">
        <field name="code"><![CDATA[
if record.x_is_a_deposit:
    record.write({'invoice_policy':'delivery', 'type': 'consu', 'is_storable': True})
]]></field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="name">Execute Code</field>
    </record>
</odoo>
