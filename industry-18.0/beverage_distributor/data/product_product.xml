<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_30" model="product.product">
        <field name="product_tmpl_id" ref="product_template_51"/>
    </record>
    <record id="product_product_35" model="product.product">
        <field name="product_tmpl_id" ref="product_template_57"/>
    </record>
    <record id="product_product_39" model="product.product">
        <field name="product_tmpl_id" ref="product_template_62"/>
    </record>
    <record id="product_product_41" model="product.product">
        <field name="product_tmpl_id" ref="product_template_64"/>
    </record>
    <record id="product_product_43" model="product.product">
        <field name="product_tmpl_id" ref="product_template_66"/>
    </record>
    <record id="product_product_45" model="product.product">
        <field name="product_tmpl_id" ref="product_template_68"/>
    </record>
    <record id="product_product_47" model="product.product">
        <field name="product_tmpl_id" ref="product_template_70"/>
    </record>
    <record id="product_product_37" model="product.product">
        <field name="product_tmpl_id" ref="product_template_60"/>
    </record>
    <record id="product_product_7" model="product.product">
        <field name="product_tmpl_id" ref="product_template_7"/>
    </record>
    <record id="product_product_48" model="product.product">
        <field name="product_tmpl_id" ref="product_template_71"/>
    </record>
</odoo>
