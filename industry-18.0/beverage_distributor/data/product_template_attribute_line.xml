<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_template_attribute_line_13" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_57"/>
    </record>
    <record id="product_template_attribute_line_15" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_51"/>
    </record>
    <record id="product_template_attribute_line_16" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_11"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_8')])]"/>
        <field name="product_tmpl_id" ref="product_template_51"/>
    </record>
    <record id="product_template_attribute_line_18" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_11"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_7')])]"/>
        <field name="product_tmpl_id" ref="product_template_57"/>
    </record>
    <record id="product_template_attribute_line_2" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_10"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_1')])]"/>
        <field name="product_tmpl_id" ref="product_template_7"/>
    </record>
    <record id="product_template_attribute_line_29" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_10"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_2')])]"/>
        <field name="product_tmpl_id" ref="product_template_60"/>
    </record>
    <record id="product_template_attribute_line_31" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_60"/>
    </record>
    <record id="product_template_attribute_line_37" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_10"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_3')])]"/>
        <field name="product_tmpl_id" ref="product_template_62"/>
    </record>
    <record id="product_template_attribute_line_39" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_62"/>
    </record>
    <record id="product_template_attribute_line_44" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_10')])]"/>
        <field name="product_tmpl_id" ref="product_template_64"/>
    </record>
    <record id="product_template_attribute_line_45" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_64"/>
    </record>
    <record id="product_template_attribute_line_5" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_7"/>
    </record>
    <record id="product_template_attribute_line_50" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_11')])]"/>
        <field name="product_tmpl_id" ref="product_template_66"/>
    </record>
    <record id="product_template_attribute_line_51" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_66"/>
    </record>
    <record id="product_template_attribute_line_56" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_22')])]"/>
        <field name="product_tmpl_id" ref="product_template_68"/>
    </record>
    <record id="product_template_attribute_line_57" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_68"/>
    </record>
    <record id="product_template_attribute_line_62" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_9')])]"/>
        <field name="product_tmpl_id" ref="product_template_70"/>
    </record>
    <record id="product_template_attribute_line_63" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_70"/>
    </record>
    <record id="product_template_attribute_line_67" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_71"/>
    </record>
</odoo>
