<?xml version='1.0' encoding='UTF-8'?>
    <odoo noupdate="1">
    <record id="product_template_54" model="product.template" context="{'create_product_product': False}">
        <field name="name">Spa Still 24x25cl</field>
        <field name="categ_id" ref="product_category_12"/>
        <field name="list_price">28.0</field>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="x_deposit_product_1" ref="product_template_55"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/54-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_1')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_1')])]"/>
    </record>
    <record id="product_template_attribute_line_17" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_11"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_7')])]"/>
        <field name="product_tmpl_id" ref="product_template_54"/>
    </record>
    <record id="product_template_attribute_value_18" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_17"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_7"/>
    </record>
    <record id="product_template_attribute_line_10" model="product.template.attribute.line"  context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_54"/>
    </record>
    <record id="product_template_attribute_value_11" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_10"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_15"/>
    </record>
    <record id="product_template_attribute_line_8" model="product.template.attribute.line"  context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_13')])]"/>
        <field name="product_tmpl_id" ref="product_template_54"/>
    </record>
    <record id="product_template_attribute_value_9" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_8"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_13"/>
    </record>
    <record id="product_product_32" model="product.product">
        <field name="product_tmpl_id" ref="product_template_54"/>
    </record>
    <record id="product_template_54" model="product.template" context="{'create_product_product': False}">
        <field name="x_quantity_by_deposit_product">24</field>
        <field name="x_deposit_product" ref="product_template_56"/>
        <field name="x_unit_sale_product" ref="product_template_57"/>
    </record>
    <record id="product_template_59" model="product.template" context="{'create_product_product': False}">
        <field name="name">Mobius IPA 24x33cl </field>
        <field name="categ_id" ref="product_category_9"/>
        <field name="list_price">48.0</field>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="x_deposit_product_1" ref="product_template_55"/>
        <field name="is_storable" eval="True"/>
        <field name="compare_list_price">48.0</field>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/61-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_2')])]"/>
    </record>
    <record id="product_template_attribute_line_25" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_10"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_2')])]"/>
        <field name="product_tmpl_id" ref="product_template_59"/>
    </record>
    <record id="product_template_attribute_value_30" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_25"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_2"/>
    </record>
    <record id="product_template_attribute_line_26" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_12')])]"/>
        <field name="product_tmpl_id" ref="product_template_59"/>
    </record>
    <record id="product_template_attribute_value_27" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_26"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_12"/>
    </record>
    <record id="product_template_attribute_line_27" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_59"/>
    </record>
    <record id="product_template_attribute_value_28" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_27"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_15"/>
    </record>
    <record id="product_product_36" model="product.product">
        <field name="product_tmpl_id" ref="product_template_59"/>
    </record>
    <record id="product_template_attribute_line_28" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_17')])]"/>
        <field name="product_tmpl_id" ref="product_template_59"/>
    </record>
    <record id="product_template_attribute_value_29" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_28"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_17"/>
    </record>
    <record id="product_template_59" model="product.template" context="{'create_product_product': False}">
        <field name="x_deposit_product" ref="product_template_56"/>
        <field name="x_unit_sale_product" ref="product_template_60"/>
        <field name="x_quantity_by_deposit_product">24</field>
    </record>
    <record id="product_template_6" model="product.template" context="{'create_product_product': False}">
        <field name="name">Mobius Blanche 24x33cl</field>
        <field name="categ_id" ref="product_category_9"/>
        <field name="list_price">48.0</field>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="x_deposit_product_1" ref="product_template_55"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/6-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_2')])]"/>
    </record>
    <record id="product_template_attribute_line_7" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_6"/>
    </record>
    <record id="product_template_attribute_value_8" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_7"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_15"/>
    </record>
    <record id="product_template_attribute_line_6" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_12')])]"/>
        <field name="product_tmpl_id" ref="product_template_6"/>
    </record>
    <record id="product_template_attribute_value_7" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_6"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_12"/>
    </record>
    <record id="product_product_6" model="product.product">
        <field name="product_tmpl_id" ref="product_template_6"/>
    </record>
    <record id="product_template_attribute_line_1" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_10"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_1')])]"/>
        <field name="product_tmpl_id" ref="product_template_6"/>
    </record>
    <record id="product_template_attribute_value_1" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_1"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_1"/>
    </record>
    <record id="product_template_attribute_line_19" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_17')])]"/>
        <field name="product_tmpl_id" ref="product_template_6"/>
    </record>
    <record id="product_template_attribute_value_20" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_19"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_17"/>
    </record>
    <record id="product_template_6" model="product.template" context="{'create_product_product': False}">
        <field name="x_quantity_by_deposit_product">24</field>
        <field name="x_deposit_product" ref="product_template_56"/>
        <field name="x_unit_sale_product" ref="product_template_7"/>
    </record>
    <record id="product_template_61" model="product.template" context="{'create_product_product': False}">
        <field name="name">Mobius Triple 24x33cl</field>
        <field name="categ_id" ref="product_category_9"/>
        <field name="list_price">48.0</field>
        <field name="invoice_policy">delivery</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="x_deposit_product_1" ref="product_template_55"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/61-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_4')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_2')])]"/>
    </record>
    <record id="product_template_attribute_line_33" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_10"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_3')])]"/>
        <field name="product_tmpl_id" ref="product_template_61"/>
    </record>
    <record id="product_template_attribute_value_40" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_33"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_3"/>
    </record>
    <record id="product_template_attribute_line_34" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_16"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_12')])]"/>
        <field name="product_tmpl_id" ref="product_template_61"/>
    </record>
    <record id="product_template_attribute_value_37" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_34"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_12"/>
    </record>
    <record id="product_template_attribute_line_35" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_15')])]"/>
        <field name="product_tmpl_id" ref="product_template_61"/>
    </record>
    <record id="product_template_attribute_value_38" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_35"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_15"/>
    </record>
    <record id="product_product_38" model="product.product">
        <field name="product_tmpl_id" ref="product_template_61"/>
    </record>
    <record id="product_template_attribute_line_36" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_17')])]"/>
        <field name="product_tmpl_id" ref="product_template_61"/>
    </record>
    <record id="product_template_attribute_value_39" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_36"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_17"/>
    </record>
    <record id="product_template_61" model="product.template" context="{'create_product_product': False}">
        <field name="x_quantity_by_deposit_product">24</field>
        <field name="x_deposit_product" ref="product_template_56"/>
        <field name="x_unit_sale_product" ref="product_template_62"/>
    </record>
    <record id="product_template_63" model="product.template" context="{'create_product_product': False}">
        <field name="name">Lou Daro - Château de Gragnos 6x75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">55.0</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/63-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_4')])]"/>
    </record>
    <record id="product_template_attribute_line_41" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_10')])]"/>
        <field name="product_tmpl_id" ref="product_template_63"/>
    </record>
    <record id="product_template_attribute_value_46" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_41"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_10"/>
    </record>
    <record id="product_template_attribute_line_42" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_63"/>
    </record>
    <record id="product_template_attribute_value_47" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_42"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_16"/>
    </record>
    <record id="product_template_attribute_line_43" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_63"/>
    </record>
    <record id="product_template_attribute_value_48" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_43"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_product_40" model="product.product">
        <field name="product_tmpl_id" ref="product_template_63"/>
    </record>
    <record id="product_template_63" model="product.template" context="{'create_product_product': False}">
        <field name="x_quantity_by_deposit_product">6</field>
        <field name="x_unit_sale_product" ref="product_template_64"/>
    </record>
    <record id="product_template_65" model="product.template" context="{'create_product_product': False}">
        <field name="name">Rosé Cosmos - Château de Gragnos 6x75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">50.0</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/65-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_4')])]"/>
    </record>
    <record id="product_template_attribute_line_47" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_11')])]"/>
        <field name="product_tmpl_id" ref="product_template_65"/>
    </record>
    <record id="product_template_attribute_value_55" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_47"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_11"/>
    </record>
    <record id="product_template_attribute_line_48" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_65"/>
    </record>
    <record id="product_template_attribute_value_53" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_48"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_16"/>
    </record>
    <record id="product_template_attribute_line_49" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_65"/>
    </record>
    <record id="product_template_attribute_value_54" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_49"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_product_42" model="product.product">
        <field name="product_tmpl_id" ref="product_template_65"/>
    </record>
    <record id="product_template_65" model="product.template" context="{'create_product_product': False}">
        <field name="x_quantity_by_deposit_product">6</field>
        <field name="x_unit_sale_product" ref="product_template_66"/>
    </record>
    <record id="product_template_67" model="product.template" context="{'create_product_product': False}">
        <field name="name">Cava Brut Il Lusio - Josep Masachs 6x75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">80.0</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/68-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_4')])]"/>
    </record>
    <record id="product_template_attribute_line_53" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_22')])]"/>
        <field name="product_tmpl_id" ref="product_template_67"/>
    </record>
    <record id="product_template_attribute_value_63" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_53"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_22"/>
    </record>
    <record id="product_template_attribute_line_54" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_67"/>
    </record>
    <record id="product_template_attribute_value_61" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_54"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_16"/>
    </record>
    <record id="product_template_attribute_line_55" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_67"/>
    </record>
    <record id="product_template_attribute_value_62" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_55"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_product_44" model="product.product">
        <field name="product_tmpl_id" ref="product_template_67"/>
    </record>
    <record id="product_template_67" model="product.template" context="{'create_product_product': False}">
        <field name="x_quantity_by_deposit_product">6</field>
        <field name="x_unit_sale_product" ref="product_template_68"/>
    </record>
    <record id="product_template_69" model="product.template" context="{'create_product_product': False}">
        <field name="name">Cuvée Grillo - Villa Carumé 6x75cl</field>
        <field name="categ_id" ref="product_category_14"/>
        <field name="list_price">55.0</field>
        <field name="invoice_policy">order</field>
        <field name="available_in_pos" eval="True"/>
        <field name="service_type">manual</field>
        <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
        <field name="is_storable" eval="True"/>
        <field name="image_1920" type="base64" file="beverage_distributor/static/src/binary/product_template/70-image_1920"/>
        <field name="purchase_method">receive</field>
        <field name="public_categ_ids" eval="[(6, 0, [ref('product_public_category_3')])]"/>
        <field name="pos_categ_ids" eval="[(6, 0, [ref('pos_category_4')])]"/>
    </record>
    <record id="product_template_attribute_line_59" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_12"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_9')])]"/>
        <field name="product_tmpl_id" ref="product_template_69"/>
    </record>
    <record id="product_template_attribute_value_70" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_59"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_9"/>
    </record>
    <record id="product_template_attribute_line_60" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_13"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_16')])]"/>
        <field name="product_tmpl_id" ref="product_template_69"/>
    </record>
    <record id="product_template_attribute_value_68" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_60"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_16"/>
    </record>
    <record id="product_template_attribute_line_61" model="product.template.attribute.line" context="{'create_product_product': False}">
        <field name="attribute_id" ref="product_attribute_15"/>
        <field name="value_ids" eval="[(6, 0, [ref('product_attribute_value_18')])]"/>
        <field name="product_tmpl_id" ref="product_template_69"/>
    </record>
    <record id="product_template_attribute_value_69" model="product.template.attribute.value">
        <field name="attribute_line_id" ref="product_template_attribute_line_61"/>
        <field name="product_attribute_value_id" ref="product_attribute_value_18"/>
    </record>
    <record id="product_product_46" model="product.product">
        <field name="product_tmpl_id" ref="product_template_69"/>
    </record>
    <record id="product_template_69" model="product.template" context="{'create_product_product': False}">
        <field name="x_quantity_by_deposit_product">6</field>
        <field name="x_unit_sale_product" ref="product_template_70"/>
    </record>
</odoo>
