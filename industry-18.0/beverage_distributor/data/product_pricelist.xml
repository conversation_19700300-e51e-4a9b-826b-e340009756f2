<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_pricelist_1" model="product.pricelist">
        <field name="sequence">10</field>
        <field name="name">Default</field>
    </record>
    <record id="product_pricelist_2" model="product.pricelist">
        <field name="sequence">11</field>
        <field name="name">B2B</field>
        <field name="item_ids" eval="[(0, 0, {
            'display_applied_on': '2_product_category',
            'applied_on': '2_product_category',
            'categ_id': ref('product_category_11'),
            'compute_price': 'formula',
            'price_discount' : '10',
            'price_round' : '1.0',
            'price_surcharge' : '-0.01',
        })]"/>
    </record>
</odoo>
