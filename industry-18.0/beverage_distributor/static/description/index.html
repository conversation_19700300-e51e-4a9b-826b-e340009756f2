<h2><strong>Odoo for&nbsp;Beverages Distributors</strong></h2>
<p>This industry package includes everything you need to run your company efficiently with Odoo.</p>
<p>
    Discover everything you need to know about this package by visiting the <strong><font class="text-o-color-1">Knowledge App</font></strong> after installing it.
</p>
<h3><strong>Basics</strong></h3>
<ul>
    <li>
        Use the <strong><font class="text-o-color-1">Inventory App</font></strong> as your central hub for managing stock, consignments, and warehouse operations.
    </li>
    <li>
        Employ the <strong><font class="text-o-color-1">Sales App</font></strong> to handle client orders and manage your product catalog.
    </li>
    <li>
        Utilize the <strong><font class="text-o-color-1">CRM App</font></strong> to track leads, manage your sales pipeline, and nurture client relationships.
    </li>
    <li>
        Use the <strong><font class="text-o-color-1">Purchase App</font></strong> to order from suppliers and manage your supply chain.
    </li>
    <li>
        Leverage the <strong><font class="text-o-color-1">Point of Sale App</font></strong> for efficient B2C sales at your distribution center.
    </li>
    <li>
        Streamline operations with the <strong><font class="text-o-color-1">Barcode App</font></strong> for quick and accurate inventory management.
    </li>
    <li>
        Expand your reach with a <strong><font class="text-o-color-1">B2B eCommerce Website</font></strong> for online ordering.
    </li>
    <li>
        Use the <strong><font class="text-o-color-1">Contacts App</font></strong> to maintain a comprehensive database of clients and suppliers.
    </li>
</ul>
<h3><strong>Included Customizations</strong></h3>
<ul>
    <li>Additional fields on products to manage deposit (<em>Deposit Product, Quantity by Deposit Product, Unit Sale Product, and Empty Deposit Product</em>).</li>
    <li>Auto-addition of taxes from the Deposit Product to the main product.</li>
    <li>Auto-creation of a "00 reordering rule" for each product selected in the <em>Unit Sale Product</em> field, with a manufacturing route.</li>
    <li>Auto-creation of a Bill of Material (BOM) based on the <em>Unit Sale Product</em> field, which will generate the exact number of units specified in the <em>Quantity by Deposit</em> field from a single unit of the main product.</li>
    <li>Additional "Auto-production" field on the BOM to ensure automatic production of manufacturing orders associated with the BOM.</li>
</ul>
