<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function name="write" model="ir.ui.view">
        <value model="ir.ui.view" eval="obj().env['website'].with_context(website_id=ref('website.default_website')).viewref('website.homepage').id"/>
        <value model="ir.ui.view" eval="{'arch': obj().env.ref('solar_installation.homepage').arch}"/>
    </function>

    <function model="website.menu" name="unlink">
        <value model="website.menu" eval="obj().search([
            ('website_id', '=', ref('website.default_website')),
            ('url', '=', '/contactus'),
        ]).id"/>
    </function>
</odoo>
