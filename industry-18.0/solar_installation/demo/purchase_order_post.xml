<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function name="button_confirm" model="purchase.order">
        <value eval="[
            ref('purchase_order_1'),
            ref('purchase_order_2'),
            ref('purchase_order_3')]"
        />
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_6')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_3').picking_ids.id)]).id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_2'), 'lot_name': obj().env.ref('solar_installation.stock_lot_2').name}"/>
    </function>
    <function name="button_validate" model="stock.picking">
        <value model="stock.picking" eval="(obj().env.ref('solar_installation.purchase_order_3')).group_id.stock_move_ids.picking_id.ids"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[0].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_66'), 'lot_name': obj().env.ref('solar_installation.stock_lot_66').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[1].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_67'), 'lot_name': obj().env.ref('solar_installation.stock_lot_67').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[2].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_68'), 'lot_name': obj().env.ref('solar_installation.stock_lot_68').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[3].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_69'), 'lot_name': obj().env.ref('solar_installation.stock_lot_69').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[4].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_70'), 'lot_name': obj().env.ref('solar_installation.stock_lot_70').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[5].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_71'), 'lot_name': obj().env.ref('solar_installation.stock_lot_71').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[6].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_72'), 'lot_name': obj().env.ref('solar_installation.stock_lot_72').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[7].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_73'), 'lot_name': obj().env.ref('solar_installation.stock_lot_73').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[8].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_74'), 'lot_name': obj().env.ref('solar_installation.stock_lot_74').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[9].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_75'), 'lot_name': obj().env.ref('solar_installation.stock_lot_75').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[10].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_76'), 'lot_name': obj().env.ref('solar_installation.stock_lot_76').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[11].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_77'), 'lot_name': obj().env.ref('solar_installation.stock_lot_77').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[12].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_78'), 'lot_name': obj().env.ref('solar_installation.stock_lot_78').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[13].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_79'), 'lot_name': obj().env.ref('solar_installation.stock_lot_79').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[14].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_80'), 'lot_name': obj().env.ref('solar_installation.stock_lot_80').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[15].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_81'), 'lot_name': obj().env.ref('solar_installation.stock_lot_81').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[16].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_82'), 'lot_name': obj().env.ref('solar_installation.stock_lot_82').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[17].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_83'), 'lot_name': obj().env.ref('solar_installation.stock_lot_83').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[18].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_84'), 'lot_name': obj().env.ref('solar_installation.stock_lot_84').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[19].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_85'), 'lot_name': obj().env.ref('solar_installation.stock_lot_85').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[20].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_86'), 'lot_name': obj().env.ref('solar_installation.stock_lot_86').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[21].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_87'), 'lot_name': obj().env.ref('solar_installation.stock_lot_87').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[22].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_88'), 'lot_name': obj().env.ref('solar_installation.stock_lot_88').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[23].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_89'), 'lot_name': obj().env.ref('solar_installation.stock_lot_89').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[24].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_90'), 'lot_name': obj().env.ref('solar_installation.stock_lot_90').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[25].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_91'), 'lot_name': obj().env.ref('solar_installation.stock_lot_91').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[26].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_92'), 'lot_name': obj().env.ref('solar_installation.stock_lot_92').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[27].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_93'), 'lot_name': obj().env.ref('solar_installation.stock_lot_93').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[28].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_94'), 'lot_name': obj().env.ref('solar_installation.stock_lot_94').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_7')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[29].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_95'), 'lot_name': obj().env.ref('solar_installation.stock_lot_95').name}"/>
    </function>

    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_8')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[0].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_96'), 'lot_name': obj().env.ref('solar_installation.stock_lot_96').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[1].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_97'), 'lot_name': obj().env.ref('solar_installation.stock_lot_97').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[2].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_98'), 'lot_name': obj().env.ref('solar_installation.stock_lot_98').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[3].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_99'), 'lot_name': obj().env.ref('solar_installation.stock_lot_99').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[4].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_100'), 'lot_name': obj().env.ref('solar_installation.stock_lot_100').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[5].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_101'), 'lot_name': obj().env.ref('solar_installation.stock_lot_101').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[6].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_102'), 'lot_name': obj().env.ref('solar_installation.stock_lot_102').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[7].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_103'), 'lot_name': obj().env.ref('solar_installation.stock_lot_103').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[8].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_104'), 'lot_name': obj().env.ref('solar_installation.stock_lot_104').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[9].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_105'), 'lot_name': obj().env.ref('solar_installation.stock_lot_105').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[10].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_106'), 'lot_name': obj().env.ref('solar_installation.stock_lot_106').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[11].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_107'), 'lot_name': obj().env.ref('solar_installation.stock_lot_107').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[12].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_108'), 'lot_name': obj().env.ref('solar_installation.stock_lot_108').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[13].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_109'), 'lot_name': obj().env.ref('solar_installation.stock_lot_109').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[14].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_110'), 'lot_name': obj().env.ref('solar_installation.stock_lot_110').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[15].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_111'), 'lot_name': obj().env.ref('solar_installation.stock_lot_111').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[16].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_112'), 'lot_name': obj().env.ref('solar_installation.stock_lot_112').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[17].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_113'), 'lot_name': obj().env.ref('solar_installation.stock_lot_113').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[18].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_114'), 'lot_name': obj().env.ref('solar_installation.stock_lot_114').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[19].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_115'), 'lot_name': obj().env.ref('solar_installation.stock_lot_115').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[20].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_116'), 'lot_name': obj().env.ref('solar_installation.stock_lot_116').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[21].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_117'), 'lot_name': obj().env.ref('solar_installation.stock_lot_117').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[22].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_118'), 'lot_name': obj().env.ref('solar_installation.stock_lot_118').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[23].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_119'), 'lot_name': obj().env.ref('solar_installation.stock_lot_119').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[24].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_120'), 'lot_name': obj().env.ref('solar_installation.stock_lot_120').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[25].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_121'), 'lot_name': obj().env.ref('solar_installation.stock_lot_121').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[26].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_122'), 'lot_name': obj().env.ref('solar_installation.stock_lot_122').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[27].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_123'), 'lot_name': obj().env.ref('solar_installation.stock_lot_123').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[28].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_124'), 'lot_name': obj().env.ref('solar_installation.stock_lot_124').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_8')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[29].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_125'), 'lot_name': obj().env.ref('solar_installation.stock_lot_125').name}"/>
    </function>
    
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[0].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_126'), 'lot_name': obj().env.ref('solar_installation.stock_lot_126').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[1].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_127'), 'lot_name': obj().env.ref('solar_installation.stock_lot_127').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[2].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_128'), 'lot_name': obj().env.ref('solar_installation.stock_lot_128').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[3].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_129'), 'lot_name': obj().env.ref('solar_installation.stock_lot_129').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[4].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_130'), 'lot_name': obj().env.ref('solar_installation.stock_lot_130').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[5].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_131'), 'lot_name': obj().env.ref('solar_installation.stock_lot_131').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[6].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_132'), 'lot_name': obj().env.ref('solar_installation.stock_lot_132').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[7].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_133'), 'lot_name': obj().env.ref('solar_installation.stock_lot_133').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[8].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_134'), 'lot_name': obj().env.ref('solar_installation.stock_lot_134').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[9].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_135'), 'lot_name': obj().env.ref('solar_installation.stock_lot_135').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[10].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_136'), 'lot_name': obj().env.ref('solar_installation.stock_lot_136').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[11].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_137'), 'lot_name': obj().env.ref('solar_installation.stock_lot_137').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[12].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_138'), 'lot_name': obj().env.ref('solar_installation.stock_lot_138').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[13].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_139'), 'lot_name': obj().env.ref('solar_installation.stock_lot_139').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[14].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_140'), 'lot_name': obj().env.ref('solar_installation.stock_lot_140').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[15].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_141'), 'lot_name': obj().env.ref('solar_installation.stock_lot_141').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[16].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_142'), 'lot_name': obj().env.ref('solar_installation.stock_lot_142').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[17].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_143'), 'lot_name': obj().env.ref('solar_installation.stock_lot_143').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[18].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_144'), 'lot_name': obj().env.ref('solar_installation.stock_lot_144').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[19].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_145'), 'lot_name': obj().env.ref('solar_installation.stock_lot_145').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[20].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_146'), 'lot_name': obj().env.ref('solar_installation.stock_lot_146').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[21].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_147'), 'lot_name': obj().env.ref('solar_installation.stock_lot_147').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[22].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_148'), 'lot_name': obj().env.ref('solar_installation.stock_lot_148').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[23].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_149'), 'lot_name': obj().env.ref('solar_installation.stock_lot_149').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[24].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_150'), 'lot_name': obj().env.ref('solar_installation.stock_lot_150').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[25].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_151'), 'lot_name': obj().env.ref('solar_installation.stock_lot_151').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[26].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_152'), 'lot_name': obj().env.ref('solar_installation.stock_lot_152').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[27].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_153'), 'lot_name': obj().env.ref('solar_installation.stock_lot_153').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[28].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_154'), 'lot_name': obj().env.ref('solar_installation.stock_lot_154').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
            ('product_id', '=', ref('product_product_9')),
            ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_1').picking_ids.id)])[29].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_155'), 'lot_name': obj().env.ref('solar_installation.stock_lot_155').name}"/>
    </function>

    <function name="button_validate" model="stock.picking">
        <value model="stock.picking" eval="(obj().env.ref('solar_installation.purchase_order_1')).group_id.stock_move_ids.picking_id.ids"/>
    </function>

    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[0].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_35'), 'lot_name': obj().env.ref('solar_installation.stock_lot_35').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[1].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_36'), 'lot_name': obj().env.ref('solar_installation.stock_lot_36').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[2].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_37'), 'lot_name': obj().env.ref('solar_installation.stock_lot_37').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[3].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_38'), 'lot_name': obj().env.ref('solar_installation.stock_lot_38').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[4].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_39'), 'lot_name': obj().env.ref('solar_installation.stock_lot_39').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[5].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_40'), 'lot_name': obj().env.ref('solar_installation.stock_lot_40').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[6].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_41'), 'lot_name': obj().env.ref('solar_installation.stock_lot_41').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[7].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_42'), 'lot_name': obj().env.ref('solar_installation.stock_lot_42').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[8].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_43'), 'lot_name': obj().env.ref('solar_installation.stock_lot_43').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[9].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_44'), 'lot_name': obj().env.ref('solar_installation.stock_lot_44').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[10].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_45'), 'lot_name': obj().env.ref('solar_installation.stock_lot_45').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[11].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_46'), 'lot_name': obj().env.ref('solar_installation.stock_lot_46').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[12].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_47'), 'lot_name': obj().env.ref('solar_installation.stock_lot_47').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[13].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_48'), 'lot_name': obj().env.ref('solar_installation.stock_lot_48').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[14].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_49'), 'lot_name': obj().env.ref('solar_installation.stock_lot_49').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[15].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_50'), 'lot_name': obj().env.ref('solar_installation.stock_lot_50').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[16].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_51'), 'lot_name': obj().env.ref('solar_installation.stock_lot_51').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[17].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_52'), 'lot_name': obj().env.ref('solar_installation.stock_lot_52').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[18].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_53'), 'lot_name': obj().env.ref('solar_installation.stock_lot_53').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[19].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_54'), 'lot_name': obj().env.ref('solar_installation.stock_lot_54').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[20].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_55'), 'lot_name': obj().env.ref('solar_installation.stock_lot_55').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[21].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_56'), 'lot_name': obj().env.ref('solar_installation.stock_lot_56').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[22].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_57'), 'lot_name': obj().env.ref('solar_installation.stock_lot_57').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[23].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_58'), 'lot_name': obj().env.ref('solar_installation.stock_lot_58').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[24].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_59'), 'lot_name': obj().env.ref('solar_installation.stock_lot_59').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[25].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_60'), 'lot_name': obj().env.ref('solar_installation.stock_lot_60').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[26].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_61'), 'lot_name': obj().env.ref('solar_installation.stock_lot_61').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[27].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_62'), 'lot_name': obj().env.ref('solar_installation.stock_lot_62').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[28].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_63'), 'lot_name': obj().env.ref('solar_installation.stock_lot_63').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_11')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[29].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_64'), 'lot_name': obj().env.ref('solar_installation.stock_lot_64').name}"/>
    </function>

    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[0].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_4'), 'lot_name': obj().env.ref('solar_installation.stock_lot_4').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[1].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_5'), 'lot_name': obj().env.ref('solar_installation.stock_lot_5').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[2].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_6'), 'lot_name': obj().env.ref('solar_installation.stock_lot_6').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[3].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_7'), 'lot_name': obj().env.ref('solar_installation.stock_lot_7').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[4].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_8'), 'lot_name': obj().env.ref('solar_installation.stock_lot_8').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[5].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_9'), 'lot_name': obj().env.ref('solar_installation.stock_lot_9').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[6].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_10'), 'lot_name': obj().env.ref('solar_installation.stock_lot_10').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[7].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_11'), 'lot_name': obj().env.ref('solar_installation.stock_lot_11').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[8].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_12'), 'lot_name': obj().env.ref('solar_installation.stock_lot_12').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[9].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_13'), 'lot_name': obj().env.ref('solar_installation.stock_lot_13').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[10].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_14'), 'lot_name': obj().env.ref('solar_installation.stock_lot_14').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[11].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_15'), 'lot_name': obj().env.ref('solar_installation.stock_lot_15').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[12].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_16'), 'lot_name': obj().env.ref('solar_installation.stock_lot_16').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[13].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_17'), 'lot_name': obj().env.ref('solar_installation.stock_lot_17').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[14].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_18'), 'lot_name': obj().env.ref('solar_installation.stock_lot_18').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[15].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_19'), 'lot_name': obj().env.ref('solar_installation.stock_lot_19').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[16].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_20'), 'lot_name': obj().env.ref('solar_installation.stock_lot_20').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[17].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_21'), 'lot_name': obj().env.ref('solar_installation.stock_lot_21').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[18].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_22'), 'lot_name': obj().env.ref('solar_installation.stock_lot_22').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[19].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_23'), 'lot_name': obj().env.ref('solar_installation.stock_lot_23').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[20].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_24'), 'lot_name': obj().env.ref('solar_installation.stock_lot_24').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[21].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_25'), 'lot_name': obj().env.ref('solar_installation.stock_lot_25').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[22].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_26'), 'lot_name': obj().env.ref('solar_installation.stock_lot_26').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[23].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_27'), 'lot_name': obj().env.ref('solar_installation.stock_lot_27').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[24].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_28'), 'lot_name': obj().env.ref('solar_installation.stock_lot_28').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[25].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_29'), 'lot_name': obj().env.ref('solar_installation.stock_lot_29').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[26].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_30'), 'lot_name': obj().env.ref('solar_installation.stock_lot_30').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[27].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_31'), 'lot_name': obj().env.ref('solar_installation.stock_lot_31').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[28].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_32'), 'lot_name': obj().env.ref('solar_installation.stock_lot_32').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([
        ('product_id', '=', ref('product_product_10')),
        ('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)])[29].id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_33'), 'lot_name': obj().env.ref('solar_installation.stock_lot_33').name}"/>
    </function>

    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([('product_id', '=', ref('product_product_6')),('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)]).id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_1'), 'lot_name': obj().env.ref('solar_installation.stock_lot_1').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([('product_id', '=', ref('product_product_14')),('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)]).id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_3'), 'lot_name': obj().env.ref('solar_installation.stock_lot_3').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([('product_id', '=', ref('product_product_15')),('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)]).id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_34'), 'lot_name': obj().env.ref('solar_installation.stock_lot_34').name}"/>
    </function>
    <function name="write" model="stock.move.line">
        <value model="stock.move.line" eval="obj().search([('product_id', '=', ref('product_product_16')),('picking_id', '=', obj().env.ref('solar_installation.purchase_order_2').picking_ids.id)]).id"/>
        <value model="stock.lot" eval="{'lot_id': ref('stock_lot_65'), 'lot_name': obj().env.ref('solar_installation.stock_lot_65').name}"/>
    </function>

    <function name="button_validate" model="stock.picking">
        <value model="stock.picking" eval="(obj().env.ref('solar_installation.purchase_order_2')).group_id.stock_move_ids.picking_id.ids"/>
    </function>
</odoo>
