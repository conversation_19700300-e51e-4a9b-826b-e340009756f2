<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function name="action_confirm" model="sale.order">
        <value eval="[
            ref('sale_order_20'),
            ref('sale_order_23'),
            ref('sale_order_24'),
            ref('sale_order_25'),
            ref('sale_order_26'),
            ref('sale_order_27'),
        ]"/>
    </function>
    <function name="button_validate" model="stock.picking" context="{'skip_sms': True}">
        <value model="stock.picking" eval="
        (
                obj().env.ref('solar_installation.sale_order_20') +
                obj().env.ref('solar_installation.sale_order_23') +
                obj().env.ref('solar_installation.sale_order_24') +
                obj().env.ref('solar_installation.sale_order_25') +
                obj().env.ref('solar_installation.sale_order_26') +
                obj().env.ref('solar_installation.sale_order_27')
        ).picking_ids.ids
        "/>
    </function>
    <function name="_create_invoices" model="sale.order">
        <value eval="[
            ref('sale_order_20'),
            ref('sale_order_23'),
            ref('sale_order_24'),
            ref('sale_order_25'),
            ref('sale_order_26'),
            ref('sale_order_27'),
        ]"/>
    </function>
</odoo>
