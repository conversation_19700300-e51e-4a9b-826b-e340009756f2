<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="crm_lead_1" model="crm.lead">
        <field name="name">Need to install 3 kW solar system</field>
        <field name="partner_id" ref="res_partner_10"/>
        <field name="tag_ids" eval="[(6, 0, [ref('crm_tag_2'), ref('crm_tag_3'), ref('crm_tag_9')])]"/>
        <field name="source_id" ref="utm.utm_source_search_engine"/>
        <field name="expected_revenue">10000.0</field>
        <field name="medium_id" ref="utm.utm_medium_email"/>
        <field name="stage_id" ref="crm.stage_lead3"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_2" model="crm.lead">
        <field name="name">Quote for 12 kW</field>
        <field name="partner_id" ref="res_partner_9"/>
        <field name="tag_ids" eval="[(6, 0, [ref('crm_tag_4'), ref('crm_tag_5'), ref('crm_tag_9')])]"/>
        <field name="source_id" ref="utm.utm_source_search_engine"/>
        <field name="expected_revenue">20000.0</field>
        <field name="medium_id" ref="utm.utm_medium_email"/>
        <field name="stage_id" ref="crm.stage_lead2"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_3" model="crm.lead">
        <field name="name">Justin Parker's opportunity</field>
        <field name="partner_id" ref="res_partner_11"/>
        <field name="tag_ids" eval="[(6, 0, [ref('crm_tag_3'), ref('crm_tag_7'), ref('crm_tag_8')])]"/>
        <field name="expected_revenue">7000.0</field>
        <field name="stage_id" ref="crm.stage_lead4"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_10" model="crm.lead">
        <field name="name">Want to install 2 kW solar system</field>
        <field name="partner_id" ref="res_partner_23"/>
        <field name="tag_ids" eval="[(6, 0, [ref('crm_tag_3'), ref('crm_tag_8'), ref('crm_tag_10')])]"/>
        <field name="medium_id" ref="utm.utm_medium_website"/>
        <field name="stage_id" ref="crm.stage_lead4"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_12" model="crm.lead">
        <field name="name">Need to install 1 kW solar system's opportunity</field>
        <field name="partner_id" ref="res_partner_16"/>
        <field name="expected_revenue">7000.0</field>
        <field name="stage_id" ref="crm.stage_lead4"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_13" model="crm.lead">
        <field name="name">Want to install 1 kW solar system</field>
        <field name="partner_id" ref="res_partner_25"/>
        <field name="medium_id" ref="utm.utm_medium_website"/>
        <field name="stage_id" ref="crm.stage_lead4"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_14" model="crm.lead">
        <field name="name">I want to install a 2 kW solar system at my home</field>
        <field name="partner_id" ref="res_partner_36"/>
        <field name="phone">+91 1111122222</field>
        <field name="tag_ids" eval="[(6, 0, [ref('crm_tag_3'), ref('crm_tag_10')])]"/>
        <field name="expected_revenue">7000.0</field>
        <field name="medium_id" ref="utm.utm_medium_website"/>
        <field name="stage_id" ref="crm.stage_lead4"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_15" model="crm.lead">
        <field name="name">Website Visitor #177's New Lead</field>
        <field name="description">
            <![CDATA[
                <p>
                    Helpdesk Bot: Here we go, help is on the way!<br/>
                    Helpdesk Bot: First, what is the nature of your issue?<br/>
                    Visitor #177: Equipment(s) is not working<br/>
                    Helpdesk Bot: Please select which equipment is not working.<br/>
                    Visitor #177: ACDB<br/>
                    Helpdesk Bot: Please mention serial number of the ACDB<br/>
                    Visitor #177: 44<br/>
                    Helpdesk Bot: Please mention your invoice number<br/>
                    Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.
                </p>
        ]]>
        </field>
    </record>
    <record id="crm_lead_16" model="crm.lead">
        <field name="name">I want to install 2 kW solar system</field>
        <field name="partner_id" ref="res_partner_47"/>
        <field name="phone">+912222233333</field>
        <field name="medium_id" ref="utm.utm_medium_website"/>
        <field name="stage_id" ref="crm.stage_lead4"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
    <record id="crm_lead_17" model="crm.lead">
        <field name="name">I want 2 kW installation at my home</field>
        <field name="partner_id" ref="res_partner_49"/>
        <field name="phone">+914444455555</field>
        <field name="medium_id" ref="utm.utm_medium_website"/>
        <field name="stage_id" ref="crm.stage_lead4"/>
        <field name="user_id" ref="base.user_admin"/>
    </record>
</odoo>
