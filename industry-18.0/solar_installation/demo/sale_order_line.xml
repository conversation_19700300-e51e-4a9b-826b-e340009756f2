<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_line_104" model="sale.order.line">
        <field name="product_id" ref="product_product_18"/>
        <field name="order_id" ref="sale_order_20"/>
    </record>
    <record id="sale_order_line_105" model="sale.order.line">
        <field name="product_id" ref="product_product_23"/>
        <field name="product_uom_qty">10.0</field>
        <field name="order_id" ref="sale_order_20"/>
    </record>
    <record id="sale_order_line_106" model="sale.order.line">
        <field name="product_id" ref="product_product_24"/>
        <field name="order_id" ref="sale_order_20"/>
    </record>
    <record id="sale_order_line_107" model="sale.order.line">
        <field name="product_id" ref="product_product_12"/>
        <field name="order_id" ref="sale_order_20"/>
    </record>
    <record id="sale_order_line_108" model="sale.order.line">
        <field name="product_id" ref="product_product_13"/>
        <field name="order_id" ref="sale_order_20"/>
    </record>
    <record id="sale_order_line_111" model="sale.order.line">
        <field name="product_id" ref="product_product_5"/>
        <field name="order_id" ref="sale_order_23"/>
    </record>
    <record id="sale_order_line_112" model="sale.order.line">
        <field name="product_id" ref="product_product_23"/>
        <field name="product_uom_qty">6.0</field>
        <field name="order_id" ref="sale_order_23"/>
    </record>
    <record id="sale_order_line_113" model="sale.order.line">
        <field name="product_id" ref="product_product_24"/>
        <field name="order_id" ref="sale_order_23"/>
    </record>
    <record id="sale_order_line_114" model="sale.order.line">
        <field name="product_id" ref="product_product_12"/>
        <field name="order_id" ref="sale_order_23"/>
    </record>
    <record id="sale_order_line_115" model="sale.order.line">
        <field name="product_id" ref="product_product_13"/>
        <field name="order_id" ref="sale_order_23"/>
    </record>
    <record id="sale_order_line_116" model="sale.order.line">
        <field name="product_id" ref="product_product_5"/>
        <field name="order_id" ref="sale_order_24"/>
        <field name="has_displayed_warning_upsell" eval="True"/>
    </record>
    <record id="sale_order_line_117" model="sale.order.line">
        <field name="product_id" ref="product_product_23"/>
        <field name="product_uom_qty">6.0</field>
        <field name="order_id" ref="sale_order_24"/>
    </record>
    <record id="sale_order_line_118" model="sale.order.line">
        <field name="product_id" ref="product_product_24"/>
        <field name="order_id" ref="sale_order_24"/>
    </record>
    <record id="sale_order_line_119" model="sale.order.line">
        <field name="product_id" ref="product_product_12"/>
        <field name="order_id" ref="sale_order_24"/>
    </record>
    <record id="sale_order_line_120" model="sale.order.line">
        <field name="product_id" ref="product_product_13"/>
        <field name="order_id" ref="sale_order_24"/>
    </record>
    <record id="sale_order_line_121" model="sale.order.line">
        <field name="product_id" ref="product_product_18"/>
        <field name="order_id" ref="sale_order_25"/>
        <field name="has_displayed_warning_upsell" eval="True"/>
    </record>
    <record id="sale_order_line_122" model="sale.order.line">
        <field name="product_id" ref="product_product_23"/>
        <field name="product_uom_qty">10.0</field>
        <field name="order_id" ref="sale_order_25"/>
    </record>
    <record id="sale_order_line_123" model="sale.order.line">
        <field name="product_id" ref="product_product_24"/>
        <field name="order_id" ref="sale_order_25"/>
    </record>
    <record id="sale_order_line_124" model="sale.order.line">
        <field name="product_id" ref="product_product_12"/>
        <field name="order_id" ref="sale_order_25"/>
    </record>
    <record id="sale_order_line_125" model="sale.order.line">
        <field name="product_id" ref="product_product_13"/>
        <field name="order_id" ref="sale_order_25"/>
    </record>
    <record id="sale_order_line_126" model="sale.order.line">
        <field name="product_id" ref="product_product_18"/>
        <field name="order_id" ref="sale_order_26"/>
        <field name="has_displayed_warning_upsell" eval="True"/>
    </record>
    <record id="sale_order_line_127" model="sale.order.line">
        <field name="product_id" ref="product_product_23"/>
        <field name="product_uom_qty">10.0</field>
        <field name="order_id" ref="sale_order_26"/>
    </record>
    <record id="sale_order_line_128" model="sale.order.line">
        <field name="product_id" ref="product_product_24"/>
        <field name="order_id" ref="sale_order_26"/>
    </record>
    <record id="sale_order_line_129" model="sale.order.line">
        <field name="product_id" ref="product_product_12"/>
        <field name="order_id" ref="sale_order_26"/>
    </record>
    <record id="sale_order_line_130" model="sale.order.line">
        <field name="product_id" ref="product_product_13"/>
        <field name="order_id" ref="sale_order_26"/>
    </record>
    <record id="sale_order_line_131" model="sale.order.line">
        <field name="product_id" ref="product_product_18"/>
        <field name="order_id" ref="sale_order_27"/>
        <field name="has_displayed_warning_upsell" eval="True"/>
    </record>
    <record id="sale_order_line_132" model="sale.order.line">
        <field name="product_id" ref="product_product_23"/>
        <field name="product_uom_qty">10.0</field>
        <field name="order_id" ref="sale_order_27"/>
    </record>
    <record id="sale_order_line_133" model="sale.order.line">
        <field name="product_id" ref="product_product_24"/>
        <field name="order_id" ref="sale_order_27"/>
    </record>
    <record id="sale_order_line_134" model="sale.order.line">
        <field name="product_id" ref="product_product_12"/>
        <field name="order_id" ref="sale_order_27"/>
    </record>
    <record id="sale_order_line_135" model="sale.order.line">
        <field name="product_id" ref="product_product_13"/>
        <field name="order_id" ref="sale_order_27"/>
    </record>
</odoo>
