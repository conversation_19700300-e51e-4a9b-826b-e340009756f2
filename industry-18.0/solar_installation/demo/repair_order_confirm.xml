<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function name="action_validate" model="repair.order">
        <value eval="[ref('repair_order_1')]"/>
    </function>
    <function name="action_validate" model="repair.order">
        <value eval="[ref('repair_order_3')]"/>
    </function>

    <record id="open_repair_wizard" model="stock.warn.insufficient.qty.repair">
        <field name="repair_id" ref="repair_order_1"/>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="quantity">1.0</field>
        <field name="product_uom_name" ref="uom.product_uom_unit"/>
    </record>

    <function  name="action_done" model="stock.warn.insufficient.qty.repair">
        <value eval="[ref('open_repair_wizard')]"/>
    </function>
    <function name="action_repair_start" model="repair.order">
        <value eval="[ref('repair_order_1')]"/>
    </function>
    <function name="action_repair_start" model="repair.order">
        <value eval="[ref('repair_order_3')]"/>
    </function>
    <function name="action_repair_end" model="repair.order">
        <value eval="[ref('repair_order_1')]"/>
    </function>
    <function  name="action_repair_end" model="repair.order">
        <value eval="[ref('repair_order_3')]"/>
    </function>
</odoo>
