<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="stock_lot_1" model="stock.lot">
        <field name="name">SOL34</field>
        <field name="product_id" ref="product_product_6"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_10" model="stock.lot">
        <field name="name">ACDB57</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_100" model="stock.lot">
        <field name="name">Battery190</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_101" model="stock.lot">
        <field name="name">Battery191</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_102" model="stock.lot">
        <field name="name">Battery192</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_103" model="stock.lot">
        <field name="name">Battery193</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_104" model="stock.lot">
        <field name="name">Battery194</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_105" model="stock.lot">
        <field name="name">195</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_106" model="stock.lot">
        <field name="name">Battery196</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_107" model="stock.lot">
        <field name="name">Battery197</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_108" model="stock.lot">
        <field name="name">Battery198</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_109" model="stock.lot">
        <field name="name">Battery199</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_11" model="stock.lot">
        <field name="name">ACDB58</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_110" model="stock.lot">
        <field name="name">Battery200</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_111" model="stock.lot">
        <field name="name">Battery201</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_112" model="stock.lot">
        <field name="name">Battery202</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_113" model="stock.lot">
        <field name="name">Battery203</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_114" model="stock.lot">
        <field name="name">Battery204</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_115" model="stock.lot">
        <field name="name">Battery205</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_116" model="stock.lot">
        <field name="name">Battery206</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_117" model="stock.lot">
        <field name="name">Battery207</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_118" model="stock.lot">
        <field name="name">Battery208</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_119" model="stock.lot">
        <field name="name">Battery209</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_12" model="stock.lot">
        <field name="name">ACDB59</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_120" model="stock.lot">
        <field name="name">Battery210</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_121" model="stock.lot">
        <field name="name">Battery211</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_122" model="stock.lot">
        <field name="name">Battery212</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_123" model="stock.lot">
        <field name="name">Battery213</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_124" model="stock.lot">
        <field name="name">Battery214</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_125" model="stock.lot">
        <field name="name">Battery215</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_126" model="stock.lot">
        <field name="name">UPS216</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_127" model="stock.lot">
        <field name="name">UPS217</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_128" model="stock.lot">
        <field name="name">UPS218</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_129" model="stock.lot">
        <field name="name">UPS219</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_13" model="stock.lot">
        <field name="name">ACDB60</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_130" model="stock.lot">
        <field name="name">UPS220</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_131" model="stock.lot">
        <field name="name">UPS221</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_132" model="stock.lot">
        <field name="name">UPS222</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_133" model="stock.lot">
        <field name="name">UPS223</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_134" model="stock.lot">
        <field name="name">UPS224</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_135" model="stock.lot">
        <field name="name">225</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_136" model="stock.lot">
        <field name="name">UPS226</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_137" model="stock.lot">
        <field name="name">UPS227</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_138" model="stock.lot">
        <field name="name">UPS228</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_139" model="stock.lot">
        <field name="name">UPS229</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_14" model="stock.lot">
        <field name="name">ACDB61</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_140" model="stock.lot">
        <field name="name">UPS230</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_141" model="stock.lot">
        <field name="name">UPS231</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_142" model="stock.lot">
        <field name="name">UPS232</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_143" model="stock.lot">
        <field name="name">UPS233</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_144" model="stock.lot">
        <field name="name">UPS234</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_145" model="stock.lot">
        <field name="name">UPS235</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_146" model="stock.lot">
        <field name="name">UPS236</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_147" model="stock.lot">
        <field name="name">UPS237</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_148" model="stock.lot">
        <field name="name">UPS238</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_149" model="stock.lot">
        <field name="name">UPS239</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_15" model="stock.lot">
        <field name="name">62</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_150" model="stock.lot">
        <field name="name">UPS240</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_151" model="stock.lot">
        <field name="name">UPS241</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_152" model="stock.lot">
        <field name="name">UPS242</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_153" model="stock.lot">
        <field name="name">UPS243</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_154" model="stock.lot">
        <field name="name">UPS244</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_155" model="stock.lot">
        <field name="name">UPS245</field>
        <field name="product_id" ref="product_product_9"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_156" model="stock.lot">
        <field name="name">SP/07/78</field>
        <field name="product_id" ref="product_product_6"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_16" model="stock.lot">
        <field name="name">ACDB63</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_17" model="stock.lot">
        <field name="name">ACDB64</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_18" model="stock.lot">
        <field name="name">ACDB65</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_19" model="stock.lot">
        <field name="name">66</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_2" model="stock.lot">
        <field name="name">SOL/35</field>
        <field name="product_id" ref="product_product_6"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_20" model="stock.lot">
        <field name="name">ACDB67</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_21" model="stock.lot">
        <field name="name">ACDB68</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_22" model="stock.lot">
        <field name="name">ACDB69</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_23" model="stock.lot">
        <field name="name">ACDB70</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_24" model="stock.lot">
        <field name="name">ACDB71</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_25" model="stock.lot">
        <field name="name">ACDB72</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_26" model="stock.lot">
        <field name="name">ACDB73</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_27" model="stock.lot">
        <field name="name">ACDB74</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_28" model="stock.lot">
        <field name="name">ACDB75</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_29" model="stock.lot">
        <field name="name">ACDB76</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_3" model="stock.lot">
        <field name="name">WR/56</field>
        <field name="product_id" ref="product_product_14"/>
         <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_30" model="stock.lot">
        <field name="name">ACDB77</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_31" model="stock.lot">
        <field name="name">ACDB78</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_32" model="stock.lot">
        <field name="name">ACDB79</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_33" model="stock.lot">
        <field name="name">ACDB80</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_34" model="stock.lot">
        <field name="name">WDC/87</field>
        <field name="product_id" ref="product_product_15"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_35" model="stock.lot">
        <field name="name">DCDB126</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_36" model="stock.lot">
        <field name="name">DCDB127</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_37" model="stock.lot">
        <field name="name">DCDB128</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_38" model="stock.lot">
        <field name="name">129</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_39" model="stock.lot">
        <field name="name">DCDB130</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_4" model="stock.lot">
        <field name="name">ACDB51</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_40" model="stock.lot">
        <field name="name">DCDB131</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_41" model="stock.lot">
        <field name="name">DCDB132</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_42" model="stock.lot">
        <field name="name">DCDB133</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_43" model="stock.lot">
        <field name="name">DCDB134</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_44" model="stock.lot">
        <field name="name">DCDB135</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_45" model="stock.lot">
        <field name="name">DCDB136</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_46" model="stock.lot">
        <field name="name">DCDB137</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_47" model="stock.lot">
        <field name="name">DCDB138</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_48" model="stock.lot">
        <field name="name">DCDB139</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_49" model="stock.lot">
        <field name="name">DCDB140</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_5" model="stock.lot">
        <field name="name">ACDB52</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_50" model="stock.lot">
        <field name="name">DCDB141</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_51" model="stock.lot">
        <field name="name">DCDB142</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_52" model="stock.lot">
        <field name="name">DCDB143</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_53" model="stock.lot">
        <field name="name">DCDB144</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_54" model="stock.lot">
        <field name="name">DCDB145</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_55" model="stock.lot">
        <field name="name">146</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_56" model="stock.lot">
        <field name="name">DCDB147</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_57" model="stock.lot">
        <field name="name">148</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_58" model="stock.lot">
        <field name="name">DCDB149</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_59" model="stock.lot">
        <field name="name">DCDB150</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_6" model="stock.lot">
        <field name="name">ACDB53</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_60" model="stock.lot">
        <field name="name">DCDB151</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_61" model="stock.lot">
        <field name="name">DCDB152</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_62" model="stock.lot">
        <field name="name">DCDB153</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_63" model="stock.lot">
        <field name="name">DCDB154</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_64" model="stock.lot">
        <field name="name">DCDB155</field>
        <field name="product_id" ref="product_product_11"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_65" model="stock.lot">
        <field name="name">MR/045/43</field>
        <field name="product_id" ref="product_product_16"/>
         <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_66" model="stock.lot">
        <field name="name">CC156</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_67" model="stock.lot">
        <field name="name">CC157</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_68" model="stock.lot">
        <field name="name">CC158</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_69" model="stock.lot">
        <field name="name">CC159</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_7" model="stock.lot">
        <field name="name">ACDB54</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_70" model="stock.lot">
        <field name="name">CC160</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_71" model="stock.lot">
        <field name="name">CC161</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_72" model="stock.lot">
        <field name="name">CC162</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_73" model="stock.lot">
        <field name="name">CC163</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_74" model="stock.lot">
        <field name="name">CC164</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_75" model="stock.lot">
        <field name="name">CC165</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_76" model="stock.lot">
        <field name="name">CC166</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_77" model="stock.lot">
        <field name="name">CC167</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_78" model="stock.lot">
        <field name="name">CC168</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_79" model="stock.lot">
        <field name="name">CC169</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_8" model="stock.lot">
        <field name="name">ACDB55</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_80" model="stock.lot">
        <field name="name">CC170</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_81" model="stock.lot">
        <field name="name">CC171</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_82" model="stock.lot">
        <field name="name">CC172</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_83" model="stock.lot">
        <field name="name">CC173</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_84" model="stock.lot">
        <field name="name">CC174</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_85" model="stock.lot">
        <field name="name">CC175</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_86" model="stock.lot">
        <field name="name">CC176</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_87" model="stock.lot">
        <field name="name">CC177</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_88" model="stock.lot">
        <field name="name">CC178</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_89" model="stock.lot">
        <field name="name">CC179</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_9" model="stock.lot">
        <field name="name">ACDB56</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_90" model="stock.lot">
        <field name="name">180</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_91" model="stock.lot">
        <field name="name">CC181</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_92" model="stock.lot">
        <field name="name">CC182</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_93" model="stock.lot">
        <field name="name">CC183</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_94" model="stock.lot">
        <field name="name">CC184</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_95" model="stock.lot">
        <field name="name">CC185</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="x_warranty_date" eval="datetime.now()"/>
    </record>
    <record id="stock_lot_96" model="stock.lot">
        <field name="name">Battery186</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_97" model="stock.lot">
        <field name="name">Battery187</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_98" model="stock.lot">
        <field name="name">Battery188</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
    <record id="stock_lot_99" model="stock.lot">
        <field name="name">Battery189</field>
        <field name="product_id" ref="product_product_8"/>
        <field name="location_id" ref="stock.stock_location_customers"/>
    </record>
</odoo>
