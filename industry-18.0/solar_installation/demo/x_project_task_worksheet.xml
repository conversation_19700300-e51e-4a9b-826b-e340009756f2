<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="x_project_task_worksheet_template_1_2" model="x_project_task_worksheet_template_1_studio">
        <field name="x_name">S00020 - Solar installation charge (2 kW-Off grid)</field>
        <field name="x_comments"><![CDATA[ <p>Done installation properly</p>]]></field>
        <field name="x_project_task_id" model="project.task" eval="obj().search([('sale_order_id', 'in', [ref('sale_order_20')])]).id"/>
        <field name="x_completion_date_time" eval="(datetime.now() - relativedelta(days=5, hours=1))"/>
        <field name="x_date" eval="(datetime.now() - relativedelta(days=5))"/>
        <field name="x_declaration"><![CDATA[ <p>I agree that all information detailed above is correct.</p>]]></field>
        <field name="x_customer_signature" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/2-x_customer_signature"/>
        <field name="x_customer" ref="res_partner_23"/>
        <field name="x_sales_order_no" ref="sale_order_20"/>
        <field name="x_kw_plan" ref="sale_order_template_3"/>
        <field name="x_commencement_date_time" eval="(datetime.now() - relativedelta(days=5, seconds=40))"/>
        <field name="x_image_1" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/2-x_image_1"/>
        <field name="x_image_2" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/2-x_image_2"/>
        <field name="x_image_3" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/2-x_image_3"/>
    </record>
    <record id="x_project_task_worksheet_template_1_3" model="x_project_task_worksheet_template_1_studio">
        <field name="x_name">S00023 - Solar installation charge</field>
        <field name="x_comments"><![CDATA[ <p>No issue faced during installation.</p>]]></field>
        <field name="x_project_task_id" model="project.task" eval="obj().search([('sale_order_id', 'in', [ref('sale_order_23')])]).id"/>
        <field name="x_completion_date_time" eval="(datetime.now() - relativedelta(days=6, hours=4))"/>
        <field name="x_date" eval="(datetime.now() - relativedelta(days=6))"/>
        <field name="x_declaration"><![CDATA[ <p>I agree that all information detailed above is correct.</p>]]></field>
        <field name="x_customer_signature" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/3-x_customer_signature"/>
        <field name="x_customer" ref="res_partner_16"/>
        <field name="x_sales_order_no" ref="sale_order_23"/>
        <field name="x_kw_plan" ref="sale_order_template_2"/>
        <field name="x_commencement_date_time" eval="(datetime.now() - relativedelta(days=6, seconds=1600))"/>
        <field name="x_image_1" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/3-x_image_1"/>
        <field name="x_image_2" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/3-x_image_2"/>
        <field name="x_image_3" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/3-x_image_3"/>
    </record>
    <record id="x_project_task_worksheet_template_1_4" model="x_project_task_worksheet_template_1_studio">
        <field name="x_name">S00024 - Solar installation charge</field>
        <field name="x_project_task_id" model="project.task" eval="obj().search([('sale_order_id', 'in', [ref('sale_order_24')])]).id"/>
        <field name="x_date" eval="datetime.now()"/>
        <field name="x_declaration"><![CDATA[ <p>I agree that all information detailed above is correct.</p>]]></field>
        <field name="x_customer_signature" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/4-x_customer_signature"/>
        <field name="x_customer" ref="res_partner_25"/>
        <field name="x_sales_order_no" ref="sale_order_24"/>
        <field name="x_kw_plan" ref="sale_order_template_2"/>
        <field name="x_commencement_date_time" eval="datetime.now()"/>
    </record>
    <record id="x_project_task_worksheet_template_1_5" model="x_project_task_worksheet_template_1_studio">
        <field name="x_name">S00025 - Solar installation charge (2 kW-Off grid)</field>
        <field name="x_comments"><![CDATA[ <p>Done installation without problem.</p>]]></field>
        <field name="x_project_task_id" model="project.task" eval="obj().search([('sale_order_id', 'in', [ref('sale_order_25')])]).id"/>
        <field name="x_completion_date_time" eval="(datetime.now() - relativedelta(days=4, hours=3))"/>
        <field name="x_date" eval="(datetime.now() - relativedelta(days=4))"/>
        <field name="x_declaration"><![CDATA[ <p>I agree that all information detailed above is correct.</p>]]></field>
        <field name="x_customer_signature" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/5-x_customer_signature"/>
        <field name="x_customer" ref="res_partner_36"/>
        <field name="x_sales_order_no" ref="sale_order_25"/>
        <field name="x_kw_plan" ref="sale_order_template_3"/>
        <field name="x_commencement_date_time" eval="(datetime.now() - relativedelta(days=4, seconds=1100))"/>
        <field name="x_image_1" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/5-x_image_1"/>
        <field name="x_image_2" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/5-x_image_2"/>
    </record>
    <record id="x_project_task_worksheet_template_1_6" model="x_project_task_worksheet_template_1_studio">
        <field name="x_name">S00026 - Solar installation charge (2 kW-Off grid)</field>
        <field name="x_project_task_id" model="project.task" eval="obj().search([('sale_order_id', 'in', [ref('sale_order_26')])]).id"/>
        <field name="x_completion_date_time" eval="(datetime.now() - relativedelta(days=3, hours=2))"/>
        <field name="x_date" eval="(datetime.now() - relativedelta(days=3))"/>
        <field name="x_declaration"><![CDATA[ <p>I agree that all information detailed above is correct.</p>]]></field>
        <field name="x_customer_signature" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/6-x_customer_signature"/>
        <field name="x_customer" ref="res_partner_47"/>
        <field name="x_sales_order_no" ref="sale_order_26"/>
        <field name="x_kw_plan" ref="sale_order_template_3"/>
        <field name="x_commencement_date_time" eval="(datetime.now() - relativedelta(days=3, seconds=1000))"/>
        <field name="x_image_1" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/6-x_image_1"/>
        <field name="x_image_2" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/6-x_image_2"/>
        <field name="x_image_3" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/6-x_image_3"/>
    </record>
    <record id="x_project_task_worksheet_template_1_7" model="x_project_task_worksheet_template_1_studio">
        <field name="x_name">S00027 - Solar installation charge (2 kW-Off grid)</field>
        <field name="x_project_task_id" model="project.task" eval="obj().search([('sale_order_id', 'in', [ref('sale_order_27')])]).id"/>
        <field name="x_completion_date_time" eval="(datetime.now() - relativedelta(days=4, hours=5))"/>
        <field name="x_date" eval="(datetime.now() - relativedelta(days=4))"/>
        <field name="x_declaration"><![CDATA[ <p>I agree that all information detailed above is correct.</p>]]></field>
        <field name="x_customer_signature" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/7-x_customer_signature"/>
        <field name="x_customer" ref="res_partner_49"/>
        <field name="x_sales_order_no" ref="sale_order_27"/>
        <field name="x_kw_plan" ref="sale_order_template_3"/>
        <field name="x_commencement_date_time" eval="(datetime.now() - relativedelta(days=4, seconds=1800))"/>
        <field name="x_image_1" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/7-x_image_1"/>
        <field name="x_image_2" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/7-x_image_2"/>
        <field name="x_image_3" type="base64" file="solar_installation/static/src/binary/x_project_task_worksheet_template_1/7-x_image_3"/>
    </record>
</odoo>
