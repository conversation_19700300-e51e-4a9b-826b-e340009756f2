<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <function name="write" model="project.task">
        <value model="project.task" eval="obj().search([('sale_order_id', '=', ref('sale_order_27'))]).id"/>
        <value model="project.task" eval="{'stage_id': ref('project_task_type_17')}"/>
    </function>
    <function name="write" model="project.task">
        <value model="project.task" eval="obj().search([('sale_order_id', '=', ref('sale_order_26'))]).id"/>
        <value model="project.task" eval="{'stage_id': ref('project_task_type_46')}"/>
    </function>
    <function name="write" model="project.task">
        <value model="project.task" eval="obj().search([('sale_order_id', 'in', [ref('sale_order_25'), ref('sale_order_24'), ref('sale_order_23'), ref('sale_order_20')])]).ids"/>
        <value model="project.task" eval="{'stage_id': ref('project_task_type_46'), 'state': '1_done'}"/>
    </function>
</odoo>
