<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="repair_order_1" model="repair.order">
        <field name="name">WH/RO/00003</field>
        <field name="ticket_id" ref="helpdesk_ticket_7"/>
        <field name="partner_id" ref="res_partner_25"/>
        <field name="message_is_follower" eval="True"/>
        <field name="under_warranty" eval="True"/>
        <field name="recycle_location_id" ref="stock.stock_location_stock"/>
        <field name="product_uom_category_id" ref="uom.product_uom_categ_unit"/>
        <field name="message_has_error" eval="True"/>
        <field name="product_uom" ref="uom.product_uom_unit"/>
        <field name="picking_type_id" ref="repair.picking_type_warehouse0_repair"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="internal_notes"><![CDATA[ 
            <p>
                Helpdesk Bot: Here we go, help is on the way!<br />
                Helpdesk Bot: First, what is the nature of your issue?<br />
                raj sharma: Equipment(s) is not working<br />
                Helpdesk Bot: Please select which equipment is not working.<br />
                raj sharma: ACDB<br />
                Helpdesk Bot: Please mention serial number of the ACDB<br />
                raj sharma: 55<br />
                Helpdesk Bot: Please mention your invoice number<br />
                raj sharma: INV/2023/00004<br />
                Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.<br />
            </p>            
            ]]></field>
        <field name="lot_id" ref="stock_lot_8"/>
        <field name="schedule_date" eval="datetime.now()"/>
        <field name="is_returned" eval="True"/>
        <field name="product_id" ref="product_product_10"/>
        <field name="company_id" ref="base.main_company"/>
    </record>
    <record id="repair_order_2" model="repair.order">
        <field name="name">WH/RO/00005</field>
        <field name="ticket_id" ref="helpdesk_ticket_8"/>
        <field name="partner_id" ref="res_partner_36"/>
        <field name="message_is_follower" eval="True"/>
        <field name="under_warranty" eval="True"/>
        <field name="recycle_location_id" ref="stock.stock_location_stock"/>
        <field name="product_uom_category_id" ref="uom.product_uom_categ_unit"/>
        <field name="product_uom" ref="uom.product_uom_unit"/>
        <field name="picking_type_id" ref="repair.picking_type_warehouse0_repair"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="internal_notes"><![CDATA[ 
            <p>
                Helpdesk Bot: Here we go, help is on the way!<br />
                Helpdesk Bot: First, what is the nature of your issue?<br />
                Mehul Vyas: Equipment(s) is not working<br />
                Helpdesk Bot: Please select which equipment is not working.<br />
                Mehul Vyas: ACDB<br />
                Helpdesk Bot: Please mention serial number of the ACDB<br />
                Mehul Vyas: 54<br />
                Helpdesk Bot: Please mention your invoice number<br />
                Mehul Vyas: INV/2023/00003<br />
                Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.<br />
            </p>            
            ]]></field>
        <field name="lot_id" ref="stock_lot_9"/>
        <field name="schedule_date" eval="datetime.now()"/>
        <field name="user_id" eval="False"/>
        <field name="product_id" ref="product_product_10"/>
        <field name="company_id" ref="base.main_company"/>
    </record>
    <record id="repair_order_3" model="repair.order">
        <field name="name">WH/RO/00006</field>
        <field name="partner_id" ref="res_partner_49"/>
        <field name="ticket_id" ref="helpdesk_ticket_11"/>
        <field name="message_is_follower" eval="True"/>
        <field name="under_warranty" eval="True"/>
        <field name="recycle_location_id" ref="stock.stock_location_stock"/>
        <field name="product_uom_category_id" ref="uom.product_uom_categ_unit"/>
        <field name="message_has_error" eval="True"/>
        <field name="product_uom" ref="uom.product_uom_unit"/>
        <field name="picking_type_id" ref="repair.picking_type_warehouse0_repair"/>
        <field name="location_id" ref="stock.stock_location_stock"/>
        <field name="internal_notes"><![CDATA[ 
            <p>
                Please contact me on: <strong><EMAIL></strong><br/>
                <br />
                Helpdesk Bot: Here we go, help is on the way!<br />
                Helpdesk Bot: First, what is the nature of your issue?<br/>
                Visitor #200: Equipment(s) is not working<br />
                Helpdesk Bot: Please select which equipment is not working.<br/>
                Visitor #200: ACDB<br />
                Helpdesk Bot: Please mention serial number of the ACDB<br/>
                Visitor #200: 58<br />
                Helpdesk Bot: Please provide your email address.<br/>
                Visitor #200: <EMAIL><br/>
                Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.<br/>
            </p>            
            ]]></field>
        <field name="lot_id" ref="stock_lot_11"/>
        <field name="schedule_date" eval="datetime.now()"/>
        <field name="user_id" eval="False"/>
        <field name="product_id" ref="product_product_10"/>
        <field name="company_id" ref="base.main_company"/>
    </record>
</odoo>
