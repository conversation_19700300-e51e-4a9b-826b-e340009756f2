<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="res_partner_9" model="res.partner">
        <field name="name">Stanford University</field>
        <field name="email">"<PERSON><PERSON>z <PERSON>" &lt;<EMAIL>&gt;</field>
        <field name="city">Stanford</field>
        <field name="zip">94305</field>
        <field name="country_id" ref="base.us"/>
        <field name="is_company" eval="True"/>
        <field name="state_id" ref="base.state_us_5"/>
    </record>
    <record id="res_partner_10" model="res.partner">
        <field name="name"><PERSON> Mike</field>
        <field name="email"><EMAIL></field>
        <field name="city">Prescott</field>
        <field name="zip">86313</field>
        <field name="country_id" ref="base.us"/>
        <field name="is_company" eval="True"/>
        <field name="state_id" ref="base.state_us_3"/>
    </record>
    <record id="res_partner_11" model="res.partner">
        <field name="name"><PERSON></field>
        <field name="city"><PERSON></field>
        <field name="zip">86313</field>
        <field name="country_id" ref="base.us"/>
        <field name="is_company" eval="True"/>
        <field name="state_id" ref="base.state_us_3"/>
    </record>
    <record id="res_partner_14" model="res.partner">
        <field name="name">Solar Mounts LLC</field>
    </record>
    <record id="res_partner_15" model="res.partner">
        <field name="name">SRNE Solar</field>
    </record>
    <record id="res_partner_16" model="res.partner">
        <field name="name">Ruby Johnson</field>
        <field name="city">Prescott</field>
        <field name="zip">86313</field>
        <field name="country_id" ref="base.us"/>
        <field name="is_company" eval="True"/>
        <field name="state_id" ref="base.state_us_3"/>
    </record>
    <record id="res_partner_17" model="res.partner">
        <field name="name">Alice Parker</field>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_23" model="res.partner">
        <field name="name">Charles Wood</field>
        <field name="email"><EMAIL></field>
        <field name="city">Prescott</field>
        <field name="country_id" ref="base.us"/>
        <field name="state_id" ref="base.state_us_3"/>
    </record>
    <record id="res_partner_25" model="res.partner">
        <field name="name">Raj Sharma</field>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_32" model="res.partner">
        <field name="name">Raj Sharma</field>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_36" model="res.partner">
        <field name="name">Mehul Vyas</field>
        <field name="email"><EMAIL></field>
        <field name="phone">+916666655555</field>
    </record>
    <record id="res_partner_44" model="res.partner">
        <field name="name">Ravi Thakkar</field>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_45" model="res.partner">
        <field name="name"><EMAIL></field>
        <field name="email"><EMAIL></field>
    </record>
    <record id="res_partner_47" model="res.partner">
        <field name="name">Michale Ford</field>
        <field name="email"><EMAIL></field>
        <field name="phone">+91 7777788888</field>
    </record>
    <record id="res_partner_49" model="res.partner">
        <field name="name">Peter Parker</field>
        <field name="email"><EMAIL></field>
        <field name="phone">+919999966666</field>
    </record>
</odoo>
