<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="helpdesk_ticket_7" model="helpdesk.ticket">
        <field name="name"><PERSON>'s Ticket</field>
        <field name="stage_id" ref="helpdesk.stage_solved"/>
        <field name="answered_customer_message_count">1</field>
        <field name="product_id" ref="product_product_10"/>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="ticket_ref">07</field>
        <field name="partner_id" ref="res_partner_25"/>
        <field name="lot_id" ref="stock_lot_8"/>
        <field name="description">
            <![CDATA[
                <p>
                    Helpdesk Bot: Here we go, help is on the way!<br/>
                    Helpdesk Bot: First, what is the nature of your issue?<br/>
                    raj sharma: Equipment(s) is not working<br/>
                    Helpdesk Bot: Please select which equipment is not working.<br/>
                    raj sharma: ACDB<br/>
                    Helpdesk Bot: Please mention serial number of the ACDB<br/>
                    raj sharma: 55<br/>
                    Helpdesk Bot: Please mention your invoice number<br/>
                    raj sharma: INV/2023/00004<br/>
                    Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.
                </p>
            ]]>
        </field>
        <field name="close_date" eval="datetime.now()"/>
    </record>
    <record id="helpdesk_ticket_8" model="helpdesk.ticket">
        <field name="name">Mehul Vyas's Ticket</field>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="product_id" ref="product_product_10"/>
        <field name="ticket_ref">08</field>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="partner_id" ref="res_partner_36"/>
        <field name="lot_id" ref="stock_lot_9"/>
        <field name="description">
            <![CDATA[
                <p>
                    Helpdesk Bot: Here we go, help is on the way!<br/>
                    Helpdesk Bot: First, what is the nature of your issue?<br/>
                    Mehul Vyas: Equipment(s) is not working<br/>
                    Helpdesk Bot: Please select which equipment is not working.<br/>
                    Mehul Vyas: ACDB<br/>
                    Helpdesk Bot: Please mention serial number of the ACDB<br/>
                    Mehul Vyas: 56<br/>
                    Helpdesk Bot: Please mention your invoice number<br/>
                    Mehul Vyas: INV/2023/00003<br/>
                    Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.
                </p>
        ]]>
        </field>
    </record>
    <record id="helpdesk_ticket_9" model="helpdesk.ticket">
        <field name="name">Website Visitor #177's Ticket</field>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="ticket_ref">09</field>
        <field name="partner_id" ref="res_partner_44"/>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="description">
            <![CDATA[
                <p>
                    Please contact me on: <strong><EMAIL></strong><br/>
                    <br/>
                    Helpdesk Bot: Here we go, help is on the way!<br/>
                    Helpdesk Bot: First, what is the nature of your issue?<br/>
                    Visitor #177: Equipment(s) is not working<br/>
                    Helpdesk Bot: Please select which equipment is not working.<br/>
                    Visitor #177: ACDB<br/>
                    Helpdesk Bot: Please mention serial number of the ACDB<br/>
                    Visitor #177: sadasd<br/>
                    Helpdesk Bot: description<br/>
                    Visitor #177: here you go<br/>
                    Helpdesk Bot: email address<br/>
                    Visitor #177: <EMAIL><br/>
                    Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.
                </p>
        ]]>
        </field>
    </record>
    <record id="helpdesk_ticket_10" model="helpdesk.ticket">
        <field name="name">Website Visitor #178's Ticket</field>
        <field name="stage_id" ref="helpdesk.stage_new"/>
        <field name="ticket_ref">10</field>
        <field name="team_id" ref="helpdesk.helpdesk_team1"/>
        <field name="partner_id" ref="res_partner_45"/>
        <field name="description">
            <![CDATA[
                <p>
                    Please contact me on: <strong><EMAIL></strong><br/>
                    <br/>
                    Helpdesk Bot: Here we go, help is on the way!<br/>
                    Helpdesk Bot: First, what is the nature of your issue?<br/>
                    Visitor #178: Equipment(s) is not working<br/>
                    Helpdesk Bot: Please select which equipment is not working.<br/>
                    Visitor #178: ACDB<br/>
                    Helpdesk Bot: Please mention serial number of the ACDB<br/>
                    Visitor #178: 111<br/>
                    Helpdesk Bot: description<br/>
                    Visitor #178: test 1<br/>
                    Helpdesk Bot: email address<br/>
                    Visitor #178: <EMAIL><br/>
                    Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.
                </p>
        ]]>
        </field>
    </record>
    <record id="helpdesk_ticket_11" model="helpdesk.ticket">
        <field name="name">Website Visitor #200's Ticket</field>
        <field name="stage_id" ref="helpdesk.stage_in_progress"/>
        <field name="product_id" ref="product_product_10"/>
        <field name="ticket_ref">11</field>
        <field name="partner_id" ref="res_partner_49"/>
        <field name="lot_id" ref="stock_lot_11"/>
        <field name="description">
            <![CDATA[
                <p>
                    Please contact me on: <strong><EMAIL></strong><br/>
                    <br/>
                    Helpdesk Bot: Here we go, help is on the way!<br/>
                    Helpdesk Bot: First, what is the nature of your issue?<br/>
                    Visitor #200: Equipment(s) is not working<br/>
                    Helpdesk Bot: Please select which equipment is not working.<br/>
                    Visitor #200: ACDB<br/>
                    Helpdesk Bot: Please mention serial number of the ACDB<br/>
                    Visitor #200: 58<br/>
                    Helpdesk Bot: Please provide your email address.<br/>
                    Visitor #200: <EMAIL><br/>
                    Helpdesk Bot: OK, I just created a ticket for you. You should receive an email confirmation very soon.
                </p>
        ]]>
        </field>
    </record>
</odoo>
