<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_supplierinfo_1" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_6').product_tmpl_id.id"/>
        <field name="price">17.45</field>
    </record>
    <record id="product_supplierinfo_2" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_15"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_7').product_tmpl_id.id"/>
        <field name="price">320.0</field>
    </record>
    <record id="product_supplierinfo_3" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_15"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_8').product_tmpl_id.id"/>
        <field name="price">6700.0</field>
    </record>
    <record id="product_supplierinfo_4" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_14').product_tmpl_id.id"/>
        <field name="price">2.4</field>
    </record>
    <record id="product_supplierinfo_5" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_10').product_tmpl_id.id"/>
        <field name="price">42.0</field>
    </record>
    <record id="product_supplierinfo_6" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_15').product_tmpl_id.id"/>
        <field name="price">2.4</field>
    </record>
    <record id="product_supplierinfo_7" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_11').product_tmpl_id.id"/>
        <field name="price">42.0</field>
    </record>
    <record id="product_supplierinfo_8" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_15"/>
        <field name="delay">0</field>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_12').product_tmpl_id.id"/>
        <field name="price">38.0</field>
    </record>
    <record id="product_supplierinfo_9" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_15"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_9').product_tmpl_id.id"/>
        <field name="price">1380.0</field>
    </record>
    <record id="product_supplierinfo_10" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_16').product_tmpl_id.id"/>
        <field name="price">3.3</field>
    </record>
    <record id="product_supplierinfo_11" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_13').product_tmpl_id.id"/>
        <field name="price">32.0</field>
    </record>
    <record id="product_supplierinfo_12" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_14"/>
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_23').product_tmpl_id.id"/>
    </record>
</odoo>
