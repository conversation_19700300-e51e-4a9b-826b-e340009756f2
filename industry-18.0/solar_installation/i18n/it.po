# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* solar_installation
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:53+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "+1 (079) 400-5001"
msgstr "+1 (079) 400-5001"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Assigned user will confirm the Repair, start the repair, finish the "
"repairing work and inform the Helpdesk user for the same through Chatter "
"functionality."
msgstr ""
"- L'utente assegnato confermerà la riparazione, inizierà la riparazione, "
"terminerà il lavoro di riparazione e informerà l'utente dell'Helpdesk "
"tramite il chatter."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Customer can <font style=\"color: rgb(107, 165, 74);\"><strong>raise a ticket</strong></font> to Customer Care team for Repair through mail, \"Submit a Ticket\" form or clicking on Chat bot button in Help page in Website. Support\n"
"            Ticket will be generated in Helpdesk app."
msgstr ""
"- I clienti possono <font style=\"color: rgb(107, 165, 74);\"><strong>aprire un ticket</strong></font> e indirizzarlo al team supporto clienti per la riparazione grazie al modulo \"Invia ticket\" oppure facendo clic sul pulsante Chat bot presente nella pagina Aiuto del sito web. I ticket\n"
"            di supporto verranno generati nell'app Helpdesk."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk User will create Repair in draft mode and fills up Customer's "
"details in it like equipment to be repaired, warranty end date, etc."
msgstr ""
"- L'utente dell'Helpdesk crea la riparazione in modalità bozza e inserisce i"
" dati del cliente, come l'apparecchiatura da riparare, la data di scadenza "
"della garanzia, ecc."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk user will confirm with the customer whether they will pick up the"
" equipment from repairing site or request to deliver the equipment to their "
"address."
msgstr ""
"- L'utente dell'Helpdesk confermerà con il cliente se intende ritirare "
"l'apparecchiatura dal sito di riparazione o richiedere la consegna "
"dell'apparecchiatura al suo indirizzo."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of delivery to customer's address, Helpdesk user will communicate with the customer after delivery and take confirmation from customer about equipment working and\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">closes the Ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"- In caso di spedizione a domicilio, l'utente dell'Helpdesk comunicherà con il cliente dopo aver consegnato e ottenuto la conferma del cliente sull'attrezzatura e\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">chiuderà il ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of pick up, ticket will be closed after customer's confirmation of"
" checking equipment at repairing site."
msgstr ""
"- In caso di ritiro, il ticket verrà chiuso dopo la conferma del cliente di "
"aver controllato l'attrezzatura presso il sito di riparazione."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Inventory user <strong><font style=\"color: rgb(107, 165, 74);\">will validate</font></strong> returned equipment in Return and check the <font style=\"color: rgb(107, 165, 74);\"><strong>warranty end date</strong></font>. If the\n"
"            warranty date is not under the warranty period, an invoice will be raised for the parts replaced and repairing charge."
msgstr ""
"- L'utente del magazzino <strong><font style=\"color: rgb(107, 165, 74);\">convaliderà</font></strong> l'attrezzatura restituita in Reso e verificherà la <font style=\"color: rgb(107, 165, 74);\"><strong>data di fine della garanzia</strong></font>. Se la\n"
"            data della garanzia non è compresa nel periodo di garanzia, verrà emessa una fattura per i componenti sostituiti e per il costo della riparazione."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "- Warranty end date is fetched from delivery of the equipment."
msgstr ""
"- La data di fine garanzia viene rilevata dalla consegna "
"dell'apparecchiatura."

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_7
msgid "1 kW"
msgstr "1 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_4
msgid "12 kW"
msgstr "12 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_10
msgid "2 kW"
msgstr "2 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_2
msgid "3 kW"
msgstr "3 kW"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"
msgstr ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2704
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Sun Power "
"Solar Services</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Servizi Sun "
"Power</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"Shining a Light on Solar Energy: Powering a Sustainable Future\"</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"Accendiamo una luce sull'energia solare: per un futuro sostenibile\"</span>\n"
"                                    <br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Numero di telefono</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Azienda</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">E-mail</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Name</span>"
msgstr "<span class=\"s_website_form_label_content\">Nome</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Requirement</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Requisiti</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Field Service Task for Installation</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Intervento sul campo per "
"l'installazione</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Quotation for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">Preventivo per l'installazione</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Task Assignment &amp; Planning for "
"Installation and Delivery of Equipments</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Assegnazione del lavoro, programmazione "
"dell'installazione e consegna dell'attrezzatura</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Worksheet for Installation</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Foglio di lavoro per l'installazione</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 1: Website Form -&gt; CRM -&gt; Sales "
"-&gt; Accounting -&gt; Field Service </span>"
msgstr ""
"<span style=\"font-size: 18px;\">Flusso 1: modulo sito web -&gt; CRM -&gt; "
"Vendite -&gt; Contabilità -&gt; Assistenza sul campo </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 2: Manual Opportunity -&gt; CRM -&gt; "
"Sales -&gt; Accounting -&gt; Field Service </span><br/>"
msgstr ""
"<span style=\"font-size: 18px;\">Flusso 2: opportunità manuale -&gt; CRM "
"-&gt; Vendite -&gt; Contabilità -&gt; Assistenza sul campo </span><br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 18px;\">Flow 3: Helpdesk -&gt; Repair </span>"
msgstr ""
"<span style=\"font-size: 18px;\">Flusso 3: Helpdesk -&gt; Riparazione "
"</span>"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_14_product_template
msgid "AC Wire"
msgstr "Cablaggio CA"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_12
#: model:product.template,name:solar_installation.product_product_10_product_template
msgid "ACDB"
msgstr "Scatola ACDB"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "About us"
msgstr "Chi siamo"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Assignees fill up the Worksheet after completion of the Installation. Several <strong><font style=\"color: rgb(107, 165, 74);\">images of Solar System installation</font></strong> of Client's site are captured along with the\n"
"            customer's confirmation and signature in Worksheet. The worksheet includes customized fields like Start date, End date, plan, signature, etc which are configured through\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio App</font></strong>."
msgstr ""
"Gli assegnatari compilano il foglio di lavoro dopo aver portato a termine l'installazione. Nel foglio di lavoro vengono inserite varie <strong><font style=\"color: rgb(107, 165, 74);\">immagini relative all'installazione</font></strong> sul sito del cliente\n"
"            nsieme con la conferma e la firma. Il foglio di lavoro include campi personalizzati come la data di inizio, la data di fine, il piano, la firma, ecc. configurati tramite\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio</font></strong>."

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_8
#: model:product.template,name:solar_installation.product_product_8_product_template
msgid "Battery"
msgstr "Batteria"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "CRM Stages"
msgstr "Fasi CRM"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_47
msgid "Canceled"
msgstr "Annullata"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_9
#: model:product.template,name:solar_installation.product_product_7_product_template
msgid "Charge Controller"
msgstr "Regolatore di carica"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_template_4
msgid "Client Site Survey (Free)"
msgstr "Sopralluogo cliente (Gratis)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Collaboration and Partnerships<br/>"
msgstr "Collaborazioni e partnership<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_commencement_date_time
msgid "Commencement Date & Time"
msgstr "Data/ora inizio"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_5
msgid "Commercial"
msgstr "Commerciale"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_completion_date_time
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date & Time"
msgstr "Data e ora completamento"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date &amp; Time"
msgstr "Data e ora completamento"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Confirmation of Sales Order <strong><font style=\"color: rgb(107, 165, "
"74);\">creates 1 Field Service Task</font></strong> automatically for Solar "
"system installation in a Project dedicated to Installation."
msgstr ""
"La conferma dell'ordine di vendita <strong><font style=\"color: rgb(107, "
"165, 74);\">crea 1  lavoro di tipo assistenza sul campo</font></strong> in "
"automatico per l'installazione dei pannelli solari in un progetto dedicato. "

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Connect with us"
msgstr "Entra in contatto con noi"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Contact us"
msgstr "Contattaci"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_3
msgid "Created on"
msgstr "Data creazione"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Customer"
msgstr "Cliente"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_many2one_customer
msgid "Customer "
msgstr "Cliente"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_signature_defaul_customer_signature
msgid "Customer Signature"
msgstr "Firma cliente"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_15_product_template
msgid "DC Wire"
msgstr "Cablaggio CD"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_10
#: model:product.template,name:solar_installation.product_product_11_product_template
msgid "DCDB"
msgstr "Scatola DCDB"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_date_default_wor_date
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Date"
msgstr "Data"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_html_default_wor_html
msgid "Declaration"
msgstr "Dichiarazione"

#. module: solar_installation
#: model:account.analytic.plan,name:solar_installation.account_analytic_plan_1
msgid "Default"
msgstr "Predefinito"

#. module: solar_installation
#: model:ir.model,name:solar_installation.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "Foglio di lavoro predefinito"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_46
msgid "Done"
msgstr "Completato"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_12_product_template
msgid "Earthing Kit"
msgstr "Kit messa a terra"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Education and Awareness<br/>"
msgstr "Informazione e consapevolezza<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Educational campaigns can be conducted to inform individuals, businesses, "
"and communities about the advantages of solar energy, including reduced "
"carbon emissions, energy cost savings, and energy independence.<br/>"
msgstr ""
"Le campagne d'informazione possono essere realizzate per informare persone, "
"aziende e comunità sui vantaggi dell'energia solare tra cui riduzione delle "
"emissioni, risparmio dei costi dell'energia e indipendenza energetica.<br/>"

#. module: solar_installation
#: model:ir.actions.server,name:solar_installation.ir_act_server_1017
msgid "Execute Code"
msgstr "Esegui codice"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Feature"
msgstr "Funzione"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Final Quotation for installation is confirmed and sent to the customer and "
"<strong><font style=\"color: rgb(107, 165, 74);\">100% advance payment is "
"received</font></strong>."
msgstr ""
"Il preventivo definitivo per l'installazione viene confermato e inviato al "
"cliente e <strong><font style=\"color: rgb(107, 165, 74);\">il pagamento in "
"anticipo viene ricevuto</font></strong>."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Follow us"
msgstr "Seguici"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_6
msgid "Free Site Survey"
msgstr "Sopralluogo gratuito"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Government Incentives and Policies<br/>"
msgstr "Incentivi e politiche governative<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Governments can play a crucial role in promoting solar energy by offering "
"incentives such as tax credits, grants, and subsidies for solar "
"installations. They can also implement policies that require a certain "
"percentage of energy to come from renewable sources, including solar.<br/>"
msgstr ""
"I governi possono giocare un ruolo fondamentale nel promuovere l'energia "
"solare offrendo incentivi come crediti di imposta, contributi in conto "
"capitale e agevolazioni per l'installazione di sistemi solari. Inoltre, "
"possono implementare le politiche che richiedono un certa percentuale di "
"energia proveniente da risorse rinnovabili, tra cui l'energia solare.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_default_handover
msgid "Handover Performed By"
msgstr "Consegna effettuata da"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Helpdesk Ticket"
msgstr "Ticket helpdesk"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Home"
msgstr "Home"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "I want to install 2 kW solar system"
msgstr "Voglio installare un sistema solare di 2 kW"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_binary_field_4
msgid "Image 1"
msgstr "Immagine 1"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image
msgid "Image 2"
msgstr "Immagine 2"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image_3
msgid "Image 3"
msgstr "Immagine 3"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Images"
msgstr "Immagini"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_17
msgid "Installation in progress"
msgstr "Installazione in corso"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_1
#: model:project.project,name:solar_installation.project_project_1
msgid "Internal"
msgstr "Interno"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_11
#: model:product.template,name:solar_installation.product_product_9_product_template
msgid "Inverter/UPS"
msgstr "Inverter/UPS"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_16_product_template
msgid "MC4 connector"
msgstr "Connettore MC4"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "Mail to&amp;"
msgstr "E-mail a&amp;"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.ir_model_fields_12686
msgid "Name"
msgstr "Nome"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_14
msgid "New"
msgstr "Nuova"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_8
msgid "Off Grid"
msgstr "Off grid"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_24_product_template
msgid "Off Grid Set"
msgstr "Opzione off grid"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_9
msgid "On Grid"
msgstr "On grid"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_25_product_template
msgid "On Grid Set"
msgstr "Opzione on grid"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Open website Request a quote form"
msgstr "Aprire il modulo Richiedi preventivo del sito web"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Opportunity is created manually in CRM. From CRM, the flow is same as above"
msgstr ""
"L'opportunità viene creata manualmente nel CRM. Dal CRM, il flusso è lo "
"stesso"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_13_product_template
msgid "Panel Stand"
msgstr "Supporto pannello"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_16
msgid "Planned"
msgstr "Pianificata"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_29
msgid "Please mention serial number of DCDB"
msgstr "Menziona il numero di serie della scatola DCDB"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_30
msgid "Please mention serial number of Inverter/UPS"
msgstr "Menziona il numero di serie dell'inverter/UPS"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_31
msgid "Please mention serial number of the ACDB"
msgstr "Menziona il numero di serie della scatola ACDB"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_28
msgid "Please mention serial number of the Charge Controller"
msgstr "Menziona il numero di serie del regolatore di carica"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Potential customer fills up the\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">Request a quote</span></font>\n"
"            </strong>\n"
"            form and Submit it, in <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">opportunity is created</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"Il potenziale cliente completa il modulo\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">Richiedi preventivo</span></font>\n"
"            </strong>\n"
"            e invialo. Nel <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">viene creata l'opportunità</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_comments_remarks_record
msgid "Remarks"
msgstr "Commenti"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Repair"
msgstr "Riparazione"

#. module: solar_installation
#: model:website.menu,name:solar_installation.website_menu_1
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3356
msgid "Request a Quote"
msgstr "Richiedi un preventivo"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_3
msgid "Residential"
msgstr "Residenziale"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Roof-top solar refers to the installation of solar panels on the roof of a "
"building to generate electricity from sunlight. It is a popular and "
"sustainable way to harness renewable energy and reduce reliance on "
"traditional power sources.<br/>"
msgstr ""
"Per roof-top solar si intende l'installazione di pannelli solari sul tetto "
"di un edificio per generare elettricità dalla luce solare. È un modo "
"popolare e sostenibile per sfruttare le energie rinnovabili e ridurre la "
"dipendenza dalle fonti di energia tradizionali.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_sale_order_no
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Sales Order No."
msgstr "N. ordine di vendita"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Sales Person is assigned and <strong><font style=\"color: rgb(107, 165, 74);\">Opportunity is moved</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">to Qualified stage</font></strong>. Sales Person follows up with\n"
"            customers through Activities like Calls, Meetings, etc. If the customer agrees the quotation, a site survey is planned and Opportunity is moved to the Site Survey stage."
msgstr ""
"Un addetto vendite viene assegnato e <strong><font style=\"color: rgb(107, 165, 74);\">l'opportunità viene spostata</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">alla fase Qualificata</font></strong>. L'addetto vendite seguirà i\n"
"            clienti attraverso attività come chiamate, riunioni, ecc. Se il cliente accetta il preventivo, viene pianificato un sopralluogo e l'opportunità passa alla fase di sopralluogo."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Secured distributorship for a new line of inverters, strengthening our "
"market position. Expanded the team to 150+, fostering growth and "
"expertise.<br/>"
msgstr ""
"Abbiamo aumentato la sicurezza della distribuzione per una nuova linea di "
"inverter, rafforzando la posizione sul mercato. Ora il nostro team si "
"compone di più di 150 persone esperte.<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Solar Installation"
msgstr "Installazione pannelli solari"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_5_product_template
msgid "Solar Installation Charge (1 kW-Off grid)"
msgstr "Costo installazione pannelli (1 kW-Off grid)"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_4
#: model:project.project,name:solar_installation.project_project_4
msgid "Solar Installation Project"
msgstr "Progetto installazione"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_6_product_template
msgid "Solar Panel "
msgstr "Pannello solare"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_23_product_template
msgid "Solar Panel set"
msgstr "Set pannelli solari"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_20_product_template
msgid "Solar installation charge (1 kW-On grid)"
msgstr "Costo installazione pannelli (1 kW-On grid)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_18_product_template
msgid "Solar installation charge (2 kW-Off grid)"
msgstr "Costo installazione pannelli (2 kW-Off grid)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_21_product_template
msgid "Solar installation charge (2 kW-On grid)"
msgstr "Costo installazione pannelli (2 kW-On grid)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_19_product_template
msgid "Solar installation charge (5 kW-Off grid)"
msgstr "Costo installazione pannelli (5 kW-Off grid)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_22_product_template
msgid "Solar installation charge (5 kW-On grid)"
msgstr "Costo installazione pannelli (5 kW-On grid)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "Submit"
msgstr "Invia"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.project_task_id_record
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Task"
msgstr "Lavoro"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2many_defaul_many2many
msgid "Task Assignees"
msgstr "Assegnatari lavoro"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Task is assigned to employees/workers. Site visit is planned and <strong><font style=\"color: rgb(107, 165, 74);\">task is moved to Planned stage in project</font></strong>. <strong/> Assignees takes all the equipments listed\n"
"            in <strong><font style=\"color: rgb(107, 165, 74);\">Delivery Order</font></strong> along with them while visiting Client's site for Installation."
msgstr ""
"Il lavoro viene assegnato ai dipendenti/lavoratori. La visita del posto è pianificata e <strong><font style=\"color: rgb(107, 165, 74);\">il lavoro viene spostato alla fase Pianificato</font></strong>. <strong/> Gli assegnatari portano con sé l'attrezzatura elencata\n"
"            nell'<strong><font style=\"color: rgb(107, 165, 74);\">ordine di consegna</font></strong> durante la visita presso il cliente per l'installazione."

#. module: solar_installation
#: model:project.project,label_tasks:solar_installation.project_project_1
#: model:project.project,label_tasks:solar_installation.project_project_4
msgid "Tasks"
msgstr "Lavori"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"This setup is for companies providing solar equipment and its installation service. Usually, residential customers place orders from 1 kW to 10 kW while others up to 100 kW. Solar panels and other equipment are installed at customer\n"
"            site based on kW capacity."
msgstr ""
"Questa configurazione è destinata alle aziende che forniscono apparecchiature solari e il relativo servizio di installazione. Di solito, i clienti residenziali effettuano ordini da 1 kW a 10 kW, mentre altri arrivano fino a 100 kW. I pannelli solari e le altre apparecchiature vengono installati presso\n"
"             il sito del cliente in base alla capacità in kW."

#. module: solar_installation
#: model:base.automation,name:solar_installation.base_automation_4
msgid "Update warranty date on serial number"
msgstr "Aggiorna data garanzia su numero di serie"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Useful Links"
msgstr "Collegamenti utili"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.field_warranty_date_stock_move_line
#: model:ir.model.fields,field_description:solar_installation.new_date_lot_serial_warranty_date
msgid "Warranty Date"
msgstr "Data garanzia"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_related_field_warranty_end_date
msgid "Warranty End Date"
msgstr "Data fine garanzia"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems."
msgstr ""
"Siamo un team di persone che, con passione, si è posto l'obiettivo di "
"migliorare la vita di tutti grazie a prodotti rivoluzionari. Sviluppiamo "
"soluzioni eccellenti per risolvere i problemi della tua azienda."

#. module: solar_installation
#: model_terms:web_tour.tour,rainbow_man_message:solar_installation.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Benvenuto! Buona visita."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Work Flows"
msgstr "Flussi di lavoro"

#. module: solar_installation
#: model:ir.actions.act_window,name:solar_installation.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Fogli di lavoro"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_kw_plan
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "kW plan"
msgstr "Piano kW"
