# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* solar_installation
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>don <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:53+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "+1 (079) 400-5001"
msgstr "+1 (079) 400-5001"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Assigned user will confirm the Repair, start the repair, finish the "
"repairing work and inform the Helpdesk user for the same through Chatter "
"functionality."
msgstr ""
"- El usuario asignado confirmará la reparación, iniciará a trabajar en ella,"
" la finalizará y luego le informará al usuario de Servicio de asistencia "
"acerca de la misma mediante el chatter."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Customer can <font style=\"color: rgb(107, 165, 74);\"><strong>raise a ticket</strong></font> to Customer Care team for Repair through mail, \"Submit a Ticket\" form or clicking on Chat bot button in Help page in Website. Support\n"
"            Ticket will be generated in Helpdesk app."
msgstr ""
"- El cliente puede <font style=\"color: rgb(107, 165, 74);\"><strong>enviar un ticket</strong></font> mediante un correo electrónico, el formulario “Enviar un ticket” o al hacer clic en el botón de bot de chat en la página de ayuda del sitio web para que el equipo de soporte realice una reparación. El ticket\n"
"            de soporte estará en la aplicación Servicio de asistencia."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk User will create Repair in draft mode and fills up Customer's "
"details in it like equipment to be repaired, warranty end date, etc."
msgstr ""
"- El usuario de Servicio de asistencia creará una reparación en modo "
"borrador y completará los datos del cliente, como el equipo a reparar, la "
"fecha de finalización de la garantía, etc."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk user will confirm with the customer whether they will pick up the"
" equipment from repairing site or request to deliver the equipment to their "
"address."
msgstr ""
"- El usuario de Servicio de asistencia confirmará con el cliente si este se "
"encargará de ir por el equipo al lugar donde se llevó a cabo la reparación o"
" si necesita que se lo entreguen en su domicilio."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of delivery to customer's address, Helpdesk user will communicate with the customer after delivery and take confirmation from customer about equipment working and\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">closes the Ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"- En caso de que se realice la entrega en el domicilio del cliente, el usuario de Servicio de asistencia se comunicará con el cliente después de la entrega para que confirme si el equipo funciona, luego\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">cerrará el ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of pick up, ticket will be closed after customer's confirmation of"
" checking equipment at repairing site."
msgstr ""
"- En caso de que el cliente vaya por el equipo, deberá cerrar el ticket "
"luego de que el cliente compruebe en el sitio donde se llevó a cabo la "
"reparación que este funciona."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Inventory user <strong><font style=\"color: rgb(107, 165, 74);\">will validate</font></strong> returned equipment in Return and check the <font style=\"color: rgb(107, 165, 74);\"><strong>warranty end date</strong></font>. If the\n"
"            warranty date is not under the warranty period, an invoice will be raised for the parts replaced and repairing charge."
msgstr ""
"- El usuario de inventario <strong><font style=\"color: rgb(107, 165, 74);\">validará</font></strong> el equipo devuelto y revisará la <font style=\"color: rgb(107, 165, 74);\"><strong>fecha de finalización de la garantía</strong></font>. Si el equipo\n"
"            ya no se encuentra dentro del periodo de garantía, se emitirá una factura para cobrar las piezas reemplazadas y la reparación."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "- Warranty end date is fetched from delivery of the equipment."
msgstr ""
"- La fecha de finalización de la garantía se calcula a partir de la entrega "
"del equipo."

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_7
msgid "1 kW"
msgstr "1 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_4
msgid "12 kW"
msgstr "12 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_10
msgid "2 kW"
msgstr "2 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_2
msgid "3 kW"
msgstr "3 kW"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"
msgstr ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2704
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Sun Power "
"Solar Services</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Derechos reservados © Servicios"
" solares Sun Power</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"Shining a Light on Solar Energy: Powering a Sustainable Future\"</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">“Luz sobre la energía solar: Energía para un futuro sostenible”</span>\n"
"                                    <br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Número de teléfono</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Su empresa</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Su correo electrónico</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Name</span>"
msgstr "<span class=\"s_website_form_label_content\">Su nombre</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Requirement</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Sus requisitos</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Field Service Task for Installation</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Tarea de servicio externo para la "
"instalación</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Quotation for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">Presupuesto para la instalación</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Task Assignment &amp; Planning for "
"Installation and Delivery of Equipments</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Asignación de tareas y planificación para "
"la instalación y entrega de equipos</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Worksheet for Installation</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Hoja de trabajo para la instalación</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 1: Website Form -&gt; CRM -&gt; Sales "
"-&gt; Accounting -&gt; Field Service </span>"
msgstr ""
"<span style=\"font-size: 18px;\">Flujo 1: Formulario del sitio web -&gt; CRM"
" -&gt; Ventas -&gt; Contabilidad -&gt; Servicio externo </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 2: Manual Opportunity -&gt; CRM -&gt; "
"Sales -&gt; Accounting -&gt; Field Service </span><br/>"
msgstr ""
"<span style=\"font-size: 18px;\">Flujo 2: Oportunidad manual -&gt; CRM -&gt;"
" Ventas -&gt; Contabilidad -&gt; Servicio externo </span><br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 18px;\">Flow 3: Helpdesk -&gt; Repair </span>"
msgstr ""
"<span style=\"font-size: 18px;\">Flujo 3: Servicio de asistencia -&gt; "
"Reparación </span>"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_14_product_template
msgid "AC Wire"
msgstr "Cable de CA"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_12
#: model:product.template,name:solar_installation.product_product_10_product_template
msgid "ACDB"
msgstr "Caja de distribución de corriente alterna"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "About us"
msgstr "Sobre nosotros"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Assignees fill up the Worksheet after completion of the Installation. Several <strong><font style=\"color: rgb(107, 165, 74);\">images of Solar System installation</font></strong> of Client's site are captured along with the\n"
"            customer's confirmation and signature in Worksheet. The worksheet includes customized fields like Start date, End date, plan, signature, etc which are configured through\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio App</font></strong>."
msgstr ""
"Las personas asignadas completan la hoja de trabajo después de finalizar la instalación. Toman varias <strong><font style=\"color: rgb(107, 165, 74);\">fotografías de la instalación del sistema solar</font></strong> en la ubicación del cliente y\n"
"            obtienen la confirmación y firma del cliente en la hoja de trabajo. La hoja de trabajo incluye campos personalizados como fecha de inicio, fecha de finalización, planes, firma, etc. que se configuran a través de\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio</font></strong>."

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_8
#: model:product.template,name:solar_installation.product_product_8_product_template
msgid "Battery"
msgstr "Batería"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "CRM Stages"
msgstr "Etapas del CRM"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_47
msgid "Canceled"
msgstr "Cancelado"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_9
#: model:product.template,name:solar_installation.product_product_7_product_template
msgid "Charge Controller"
msgstr "Regulador de carga"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_template_4
msgid "Client Site Survey (Free)"
msgstr "Encuesta del sitio del cliente (gratis)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Collaboration and Partnerships<br/>"
msgstr "Colaboración y socios<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_commencement_date_time
msgid "Commencement Date & Time"
msgstr "Fecha y hora de inicio"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_5
msgid "Commercial"
msgstr "Comercial"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_completion_date_time
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date & Time"
msgstr "Fecha y hora de finalización"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date &amp; Time"
msgstr "Fecha y hora de finalización"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Confirmation of Sales Order <strong><font style=\"color: rgb(107, 165, "
"74);\">creates 1 Field Service Task</font></strong> automatically for Solar "
"system installation in a Project dedicated to Installation."
msgstr ""
"Confirmar el pedido de venta <strong><font style=\"color: rgb(107, 165, "
"74);\">crea automáticamente una tarea de servicio externo</font></strong> "
"para llevar a cabo la instalación del sistema solar en un proyecto "
"específico para la instalación."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Connect with us"
msgstr "Contáctenos"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Contact us"
msgstr "Contáctenos"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_3
msgid "Created on"
msgstr "Creado el"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Customer"
msgstr "Cliente"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_many2one_customer
msgid "Customer "
msgstr "Cliente"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_signature_defaul_customer_signature
msgid "Customer Signature"
msgstr "Firma del cliente"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_15_product_template
msgid "DC Wire"
msgstr "Cable de CD"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_10
#: model:product.template,name:solar_installation.product_product_11_product_template
msgid "DCDB"
msgstr "Caja de distribución de corriente directa"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_date_default_wor_date
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Date"
msgstr "Fecha"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_html_default_wor_html
msgid "Declaration"
msgstr "Declaración"

#. module: solar_installation
#: model:account.analytic.plan,name:solar_installation.account_analytic_plan_1
msgid "Default"
msgstr "Por defecto"

#. module: solar_installation
#: model:ir.model,name:solar_installation.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "Hoja de trabajo por defecto"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_46
msgid "Done"
msgstr "Hecho"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_12_product_template
msgid "Earthing Kit"
msgstr "Kit de puesta a tierra"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Education and Awareness<br/>"
msgstr "Educación y sensibilización<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Educational campaigns can be conducted to inform individuals, businesses, "
"and communities about the advantages of solar energy, including reduced "
"carbon emissions, energy cost savings, and energy independence.<br/>"
msgstr ""
"Es posible realizar campañas para educar a personas, empresas y comunidades "
"sobre las ventajas de la energía solar, como la reducción de emisiones de "
"CO2, el ahorro en costes de energía y la independencia energética.<br/>"

#. module: solar_installation
#: model:ir.actions.server,name:solar_installation.ir_act_server_1017
msgid "Execute Code"
msgstr "Ejecutar código"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Feature"
msgstr "Destacado"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Final Quotation for installation is confirmed and sent to the customer and "
"<strong><font style=\"color: rgb(107, 165, 74);\">100% advance payment is "
"received</font></strong>."
msgstr ""
"Se confirma el presupuesto final para la instalación y se envía al cliente y"
" <strong><font style=\"color: rgb(107, 165, 74);\">se erecibe un anticipo "
"del 100 %</font></strong>."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Follow us"
msgstr "Síganos"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_6
msgid "Free Site Survey"
msgstr "Encuesta de sitio gratuita"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Government Incentives and Policies<br/>"
msgstr "Incentivos y políticas del gobierno<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Governments can play a crucial role in promoting solar energy by offering "
"incentives such as tax credits, grants, and subsidies for solar "
"installations. They can also implement policies that require a certain "
"percentage of energy to come from renewable sources, including solar.<br/>"
msgstr ""
"Los gobiernos pueden tener un papel muy importante en la promoción de la "
"energía solar, ya que ofrecen incentivos como créditos fiscales, "
"subvenciones y subsidios para instalaciones solares. También pueden aplicar "
"políticas que exijan un determinado porcentaje de energía procedente de "
"fuentes renovables, incluida la energía solar.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_default_handover
msgid "Handover Performed By"
msgstr "Entrega realizada por"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Helpdesk Ticket"
msgstr "Ticket de asistencia"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Home"
msgstr "Inicio"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "I want to install 2 kW solar system"
msgstr "Quiero instalar un sistema solar de 2 kW"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_binary_field_4
msgid "Image 1"
msgstr "Imagen 1"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image
msgid "Image 2"
msgstr "Imagen 2"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image_3
msgid "Image 3"
msgstr "Imagen 3"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Images"
msgstr "Imagenes"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_17
msgid "Installation in progress"
msgstr "Instalación en progreso"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_1
#: model:project.project,name:solar_installation.project_project_1
msgid "Internal"
msgstr "Interno"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_11
#: model:product.template,name:solar_installation.product_product_9_product_template
msgid "Inverter/UPS"
msgstr "Inversor/UPS"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_16_product_template
msgid "MC4 connector"
msgstr "Conector MC4"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "Mail to&amp;"
msgstr "Correo a"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.ir_model_fields_12686
msgid "Name"
msgstr "Nombre"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_14
msgid "New"
msgstr "Nuevo"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_8
msgid "Off Grid"
msgstr "Sistema autónomo"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_24_product_template
msgid "Off Grid Set"
msgstr "Set de sistema autónomo"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_9
msgid "On Grid"
msgstr "Sistema conectado a la red"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_25_product_template
msgid "On Grid Set"
msgstr "Set de sistema conectado a la red"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Open website Request a quote form"
msgstr "Abra el formulario de sitio web “Solicitar un presupuesto”."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Opportunity is created manually in CRM. From CRM, the flow is same as above"
msgstr ""
"La oportunidad se crea manualmente en CRM. Una vez allí el flujo es el mismo"
" que el anterior."

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_13_product_template
msgid "Panel Stand"
msgstr "Soporte del panel"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_16
msgid "Planned"
msgstr "Planificado"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_29
msgid "Please mention serial number of DCDB"
msgstr ""
"Mencione el número de serie de la caja de distribución de corriente directa"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_30
msgid "Please mention serial number of Inverter/UPS"
msgstr "Mencione el número de serie del inversor/UPS"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_31
msgid "Please mention serial number of the ACDB"
msgstr ""
"Mencione el número de serie de la caja de distribución de corriente alterna"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_28
msgid "Please mention serial number of the Charge Controller"
msgstr "Mencione el número de serie del regulador de carga"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Potential customer fills up the\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">Request a quote</span></font>\n"
"            </strong>\n"
"            form and Submit it, in <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">opportunity is created</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"El cliente potencial completa el formulario\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">“Solicitar un presupuesto”</span></font>\n"
"            </strong>\n"
"            y lo envía, esto <strong><font style=\"color: rgb(107, 165, 74);\">crea una oportunidad</font></strong> en <font class=\"text-o-color-5\">CRM</font><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_comments_remarks_record
msgid "Remarks"
msgstr "Comentarios"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Repair"
msgstr "Reparación"

#. module: solar_installation
#: model:website.menu,name:solar_installation.website_menu_1
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3356
msgid "Request a Quote"
msgstr "Solicitar un presupuesto"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_3
msgid "Residential"
msgstr "Residencial"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Roof-top solar refers to the installation of solar panels on the roof of a "
"building to generate electricity from sunlight. It is a popular and "
"sustainable way to harness renewable energy and reduce reliance on "
"traditional power sources.<br/>"
msgstr ""
"La energía solar en techos se refiere a la instalación de paneles solares en"
" el techo de un edificio para generar electricidad a partir de la luz solar."
" Es una forma popular y sostenible de aprovechar la energía renovable y "
"reducir el uso de fuentes de energía tradicionales.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_sale_order_no
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Sales Order No."
msgstr "N.° del pedido de venta"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Sales Person is assigned and <strong><font style=\"color: rgb(107, 165, 74);\">Opportunity is moved</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">to Qualified stage</font></strong>. Sales Person follows up with\n"
"            customers through Activities like Calls, Meetings, etc. If the customer agrees the quotation, a site survey is planned and Opportunity is moved to the Site Survey stage."
msgstr ""
"Se asigna a un comercial y la <strong><font style=\"color: rgb(107, 165, 74);\">oportunidad pasa</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">a la etapa “Calificado”</font></strong>. El comercial se comunica con los\n"
"            clientes a través de actividades como llamadas, reuniones, etc. Si el cliente está de acuerdo con el presupuesto, se planifica una inspección y la oportunidad pasa a la etapa “Encuesta de sitio”."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Secured distributorship for a new line of inverters, strengthening our "
"market position. Expanded the team to 150+, fostering growth and "
"expertise.<br/>"
msgstr ""
"Somos distribuidores de una nueva línea de inversores, lo que fortalece "
"nuestra posición en el mercado. Más de 150 personas forman parte de nuestro "
"equipo, fomentando el crecimiento y la experiencia.<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Solar Installation"
msgstr "Instalación de paneles solares"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_5_product_template
msgid "Solar Installation Charge (1 kW-Off grid)"
msgstr "Carga de instalación solar (1 kW - sistema autónomo)"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_4
#: model:project.project,name:solar_installation.project_project_4
msgid "Solar Installation Project"
msgstr "Proyecto de instalación de paneles solares"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_6_product_template
msgid "Solar Panel "
msgstr "Panel solar"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_23_product_template
msgid "Solar Panel set"
msgstr "Set de paneles solares"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_20_product_template
msgid "Solar installation charge (1 kW-On grid)"
msgstr "Carga de instalación solar (1 kW - sistema conectado a la red)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_18_product_template
msgid "Solar installation charge (2 kW-Off grid)"
msgstr "Carga de instalación solar (2 kW - sistema autónomo)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_21_product_template
msgid "Solar installation charge (2 kW-On grid)"
msgstr "Carga de instalación solar (2 kW - sistema conectado a la red)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_19_product_template
msgid "Solar installation charge (5 kW-Off grid)"
msgstr "Carga de instalación solar (5 kW - sistema autónomo)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_22_product_template
msgid "Solar installation charge (5 kW-On grid)"
msgstr "Carga de instalación solar (5 kW - sistema conectado a la red)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "Submit"
msgstr "Enviar"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.project_task_id_record
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Task"
msgstr "Tarea"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2many_defaul_many2many
msgid "Task Assignees"
msgstr "Personas asignadas a la tarea"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Task is assigned to employees/workers. Site visit is planned and <strong><font style=\"color: rgb(107, 165, 74);\">task is moved to Planned stage in project</font></strong>. <strong/> Assignees takes all the equipments listed\n"
"            in <strong><font style=\"color: rgb(107, 165, 74);\">Delivery Order</font></strong> along with them while visiting Client's site for Installation."
msgstr ""
"La tarea se asigna a los empleados o trabajadores. Se planifica una visita en el sitio y <strong><font style=\"color: rgb(107, 165, 74);\">la tarea pasa a la fase “Planificado” del proyecto</font></strong>. <strong/> Las personas asignadas llevan el equipo\n"
"           incluido en la <strong><font style=\"color: rgb(107, 165, 74);\">orden de entrega</font></strong> con ellos a la visita del sitio del cliente para realizar la instalación."

#. module: solar_installation
#: model:project.project,label_tasks:solar_installation.project_project_1
#: model:project.project,label_tasks:solar_installation.project_project_4
msgid "Tasks"
msgstr "Tareas"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"This setup is for companies providing solar equipment and its installation service. Usually, residential customers place orders from 1 kW to 10 kW while others up to 100 kW. Solar panels and other equipment are installed at customer\n"
"            site based on kW capacity."
msgstr ""
"Esta configuración es para las empresas que ofrecen equipos solares y también llevan a cabo su respectiva instalación. Por lo general, los clientes residenciales solicitan equipos de 1 kW a 10 kW, mientras que otros de hasta 100 kW. Los paneles solares y otros equipos se instalan\n"
"            en las ubicaciones del cliente de acuerdo a los kW."

#. module: solar_installation
#: model:base.automation,name:solar_installation.base_automation_4
msgid "Update warranty date on serial number"
msgstr "Actualizar la fecha de garantía en el número de serie"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Useful Links"
msgstr "Enlaces útiles"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.field_warranty_date_stock_move_line
#: model:ir.model.fields,field_description:solar_installation.new_date_lot_serial_warranty_date
msgid "Warranty Date"
msgstr "Fecha de la garantía"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_related_field_warranty_end_date
msgid "Warranty End Date"
msgstr "Fecha de finalización de la garantía"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems."
msgstr ""
"Somos un equipo de personas apasionadas cuyo objetivo es mejorar la vida de "
"todo el mundo a través de nuestros productos revolucionarios. Creamos "
"grandes productos para resolver sus problemas empresariales."

#. module: solar_installation
#: model_terms:web_tour.tour,rainbow_man_message:solar_installation.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Bienvenido! Disfrute del sitio."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Work Flows"
msgstr "Flujos de trabajo"

#. module: solar_installation
#: model:ir.actions.act_window,name:solar_installation.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Hojas de trabajo"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "<EMAIL>"
msgstr "info@sucompañía.example.com"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_kw_plan
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "kW plan"
msgstr "Plan de kW"
