# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* solar_installation
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:53+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "+1 (079) 400-5001"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Assigned user will confirm the Repair, start the repair, finish the "
"repairing work and inform the Helpdesk user for the same through Chatter "
"functionality."
msgstr ""
"- ผู้ใช้ที่ได้รับมอบหมายจะยืนยันการซ่อมแซม เริ่มการซ่อมแซม "
"ดำเนินการซ่อมแซมให้เสร็จสิ้น "
"และแจ้งให้ผู้ใช้ฝ่ายช่วยเหลือทราบผ่านฟังก์ชันช่องแชท"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Customer can <font style=\"color: rgb(107, 165, 74);\"><strong>raise a ticket</strong></font> to Customer Care team for Repair through mail, \"Submit a Ticket\" form or clicking on Chat bot button in Help page in Website. Support\n"
"            Ticket will be generated in Helpdesk app."
msgstr ""
"- ลูกค้าสามารถ <font style=\"color: rgb(107, 165, 74);\"><strong>ส่งทิกเก็ต</strong></font> ถึงทีมดูแลลูกค้าเพื่อขอซ่อมได้ทางอีเมล แบบฟอร์ม \"ส่งทิกเก็ต\" หรือคลิกปุ่มแชทบอทในหน้าช่วยเหลือในเว็บไซต์\n"
"            ทิกเก้ตช่วยเหลือจะถูกสร้างขึ้นในแอป Helpdesk"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk User will create Repair in draft mode and fills up Customer's "
"details in it like equipment to be repaired, warranty end date, etc."
msgstr ""
"- ผู้ใช้ฝ่ายช่วยเหลือจะสร้างการซ่อมแซมในโหมดร่างและกรอกรายละเอียดของลูกค้า "
"เช่น อุปกรณ์ที่ต้องซ่อมแซม วันสิ้นสุดการรับประกัน เป็นต้น"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk user will confirm with the customer whether they will pick up the"
" equipment from repairing site or request to deliver the equipment to their "
"address."
msgstr ""
"- ผู้ใช้ Helpdesk "
"จะยืนยันกับลูกค้าว่าจะมารับอุปกรณ์จากสถานที่ซ่อมหรือขอให้ส่งอุปกรณ์ไปที่อยู่ของตนเอง"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of delivery to customer's address, Helpdesk user will communicate with the customer after delivery and take confirmation from customer about equipment working and\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">closes the Ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"- กรณีส่งถึงที่อยู่ลูกค้า ผู้ใช้งาน Helpdesk จะติดต่อสื่อสารกับลูกค้าหลังส่งและรับคำยืนยันจากลูกค้าเกี่ยวกับการทำงานของอุปกรณ์และ\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">ปิดทิกเก็ต</font></strong><font style=\"color: rgb(181, 214, 165);\"></font>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of pick up, ticket will be closed after customer's confirmation of"
" checking equipment at repairing site."
msgstr ""
"- กรณีรับสินค้าเอง ทิกเก็ตจะถูกปิดหลังจากลูกค้ายืนยันการตรวจเช็คอุปกรณ์ ณ "
"จุดซ่อมแล้ว"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Inventory user <strong><font style=\"color: rgb(107, 165, 74);\">will validate</font></strong> returned equipment in Return and check the <font style=\"color: rgb(107, 165, 74);\"><strong>warranty end date</strong></font>. If the\n"
"            warranty date is not under the warranty period, an invoice will be raised for the parts replaced and repairing charge."
msgstr ""
"- ผู้ใช้ระบบสินค้าคงคลังจะ<strong><font style=\"color: rgb(107, 165, 74);\">ตรวจสอบ</font></strong>อุปกรณ์ที่ส่งคืนในระบบส่งคืนและตรวจสอบ<font style=\"color: rgb(107, 165, 74);\"><strong>วันที่สิ้นสุดการรับประกัน</strong></font> หากวันที่\n"
"           รับประกันไม่อยู่ในระยะเวลารับประกัน จะมีการเรียกเก็บใบแจ้งหนี้สำหรับชิ้นส่วนที่เปลี่ยนและค่าซ่อมแซม"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "- Warranty end date is fetched from delivery of the equipment."
msgstr ""

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_7
msgid "1 kW"
msgstr ""

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_4
msgid "12 kW"
msgstr ""

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_10
msgid "2 kW"
msgstr ""

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_2
msgid "3 kW"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"
msgstr ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2704
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Sun Power "
"Solar Services</span>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"Shining a Light on Solar Energy: Powering a Sustainable Future\"</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"ส่องสว่างด้วยพลังงานแสงอาทิตย์: ขับเคลื่อนอนาคตที่ยั่งยืน\"</span>\n"
"                                    <br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">หมายเลขโทรศัพท์</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">บริษัทของคุณ</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">อีเมลของคุณ</span>\n"
"                                                <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Name</span>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Requirement</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Field Service Task for Installation</span>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Quotation for Installation</span>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Task Assignment &amp; Planning for "
"Installation and Delivery of Equipments</span>"
msgstr ""
"<span style=\"font-size: 14px;\">การมอบหมายงาน &amp; "
"การวางแผนการติดตั้งและการส่งมอบอุปกรณ์</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Worksheet for Installation</span>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 1: Website Form -&gt; CRM -&gt; Sales "
"-&gt; Accounting -&gt; Field Service </span>"
msgstr ""
"<span style=\"font-size: 18px;\">ขั้นตอนที่ 1: แบบฟอร์มเว็บไซต์ -&gt; CRM "
"-&gt; การขาย -&gt; ระบบบัญชี -&gt; การบริการภาคสนาม </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 2: Manual Opportunity -&gt; CRM -&gt; "
"Sales -&gt; Accounting -&gt; Field Service </span><br/>"
msgstr ""
"<span style=\"font-size: 18px;\">ขั้นตอนที่ 2: โอกาสด้วยตนเอง -&gt; CRM "
"-&gt; การขาย -&gt; ระบบบัญชี -&gt; การบริการภาคสนาม </span><br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 18px;\">Flow 3: Helpdesk -&gt; Repair </span>"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_14_product_template
msgid "AC Wire"
msgstr ""

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_12
#: model:product.template,name:solar_installation.product_product_10_product_template
msgid "ACDB"
msgstr "ACDB"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "About us"
msgstr "เกี่ยวกับเรา"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Assignees fill up the Worksheet after completion of the Installation. Several <strong><font style=\"color: rgb(107, 165, 74);\">images of Solar System installation</font></strong> of Client's site are captured along with the\n"
"            customer's confirmation and signature in Worksheet. The worksheet includes customized fields like Start date, End date, plan, signature, etc which are configured through\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio App</font></strong>."
msgstr ""
"ผู้ได้รับมอบหมายจะกรอกแบบฟอร์มหลังจากการติดตั้งเสร็จสิ้น <strong><font style=\"color: rgb(107, 165, 74);\">ภาพการติดตั้งระบบโซลาร์เซลล์ที่ไซต์</font></strong> ของลูกค้าหลายภาพจะถูกบันทึกพร้อมกับ\n"
"            คำยืนยันและลายเซ็นของลูกค้าในแบบฟอร์ม แบบฟอร์มประกอบด้วยฟิลด์ที่กำหนดเอง เช่น วันเริ่มต้น วันสิ้นสุด แผนผัง ลายเซ็น และอื่นๆ ซึ่งกำหนดค่าผ่าน\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">แอปสตูดิโอ</font></strong>"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_8
#: model:product.template,name:solar_installation.product_product_8_product_template
msgid "Battery"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "CRM Stages"
msgstr "CRM ขั้นตอน"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_47
msgid "Canceled"
msgstr "ถูกยกเลิก"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_9
#: model:product.template,name:solar_installation.product_product_7_product_template
msgid "Charge Controller"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_template_4
msgid "Client Site Survey (Free)"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Collaboration and Partnerships<br/>"
msgstr ""

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_commencement_date_time
msgid "Commencement Date & Time"
msgstr ""

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_5
msgid "Commercial"
msgstr ""

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_completion_date_time
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date & Time"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date &amp; Time"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Confirmation of Sales Order <strong><font style=\"color: rgb(107, 165, "
"74);\">creates 1 Field Service Task</font></strong> automatically for Solar "
"system installation in a Project dedicated to Installation."
msgstr ""
"การยืนยันใบสั่งขาย<strong><font style=\"color: rgb(107, 165, "
"74);\">จะสร้างงานบริการภาคสนาม 1 "
"งาน</font></strong>โดยอัตโนมัติสำหรับการติดตั้งระบบโซลาร์ในโครงการเฉพาะสำหรับการติดตั้ง"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Connect with us"
msgstr "เชื่อมต่อกับเรา"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Contact us"
msgstr "ติดต่อเรา"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_3
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Customer"
msgstr "ลูกค้า"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_many2one_customer
msgid "Customer "
msgstr ""

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_signature_defaul_customer_signature
msgid "Customer Signature"
msgstr "ลายเซ็นลูกค้า"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_15_product_template
msgid "DC Wire"
msgstr ""

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_10
#: model:product.template,name:solar_installation.product_product_11_product_template
msgid "DCDB"
msgstr "DCDB"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_date_default_wor_date
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Date"
msgstr "วันที่"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_html_default_wor_html
msgid "Declaration"
msgstr ""

#. module: solar_installation
#: model:account.analytic.plan,name:solar_installation.account_analytic_plan_1
msgid "Default"
msgstr "เริ่มต้น"

#. module: solar_installation
#: model:ir.model,name:solar_installation.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "แผ่นงานเริ่มต้น"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_46
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_12_product_template
msgid "Earthing Kit"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Education and Awareness<br/>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Educational campaigns can be conducted to inform individuals, businesses, "
"and communities about the advantages of solar energy, including reduced "
"carbon emissions, energy cost savings, and energy independence.<br/>"
msgstr ""
"สามารถจัดทำแคมเปญด้านการศึกษาเพื่อแจ้งให้บุคคล ธุรกิจ "
"และชุมชนทราบเกี่ยวกับข้อดีของพลังงานแสงอาทิตย์ รวมถึงการลดการปล่อยคาร์บอน "
"การประหยัดต้นทุนด้านพลังงาน และความเป็นอิสระด้านพลังงาน<br/>"

#. module: solar_installation
#: model:ir.actions.server,name:solar_installation.ir_act_server_1017
msgid "Execute Code"
msgstr "ดำเนินการรหัส"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Feature"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Final Quotation for installation is confirmed and sent to the customer and "
"<strong><font style=\"color: rgb(107, 165, 74);\">100% advance payment is "
"received</font></strong>."
msgstr ""
"ใบเสนอราคาสุดท้ายสำหรับการติดตั้งได้รับการยืนยันและส่งไปให้ลูกค้า<strong><font"
" style=\"color: rgb(107, 165, 74);\">พร้อมรับการชำระเงินล่วงหน้า "
"100%</font></strong>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Follow us"
msgstr "ติดตามเรา"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_6
msgid "Free Site Survey"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Government Incentives and Policies<br/>"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Governments can play a crucial role in promoting solar energy by offering "
"incentives such as tax credits, grants, and subsidies for solar "
"installations. They can also implement policies that require a certain "
"percentage of energy to come from renewable sources, including solar.<br/>"
msgstr ""
"รัฐบาลสามารถมีบทบาทสำคัญในการส่งเสริมพลังงานแสงอาทิตย์ได้ด้วยการเสนอแรงจูงใจ"
" เช่น เครดิตภาษี เงินช่วยเหลือ "
"และเงินอุดหนุนสำหรับการติดตั้งพลังงานแสงอาทิตย์ นอกจากนี้ "
"รัฐบาลยังสามารถดำเนินนโยบายที่กำหนดให้พลังงานบางส่วนต้องมาจากแหล่งพลังงานหมุนเวียน"
" เช่น พลังงานแสงอาทิตย์<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_default_handover
msgid "Handover Performed By"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Helpdesk Ticket"
msgstr "ทิกเก็ตช่วยเหลือ"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Home"
msgstr "หน้าแรก"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "I want to install 2 kW solar system"
msgstr ""

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_binary_field_4
msgid "Image 1"
msgstr "รูปภาพ 1"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image
msgid "Image 2"
msgstr "รูปภาพ 2"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image_3
msgid "Image 3"
msgstr "รูปภาพ 3"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Images"
msgstr "รูปภาพ"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_17
msgid "Installation in progress"
msgstr ""

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_1
#: model:project.project,name:solar_installation.project_project_1
msgid "Internal"
msgstr "ภายใน"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_11
#: model:product.template,name:solar_installation.product_product_9_product_template
msgid "Inverter/UPS"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_16_product_template
msgid "MC4 connector"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "Mail to&amp;"
msgstr ""

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.ir_model_fields_12686
msgid "Name"
msgstr "ชื่อ"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_14
msgid "New"
msgstr "ใหม่"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_8
msgid "Off Grid"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_24_product_template
msgid "Off Grid Set"
msgstr ""

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_9
msgid "On Grid"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_25_product_template
msgid "On Grid Set"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Open website Request a quote form"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Opportunity is created manually in CRM. From CRM, the flow is same as above"
msgstr "โอกาสจะถูกสร้างด้วยตนเองใน CRM จาก CRM กระแสข้อมูลจะเหมือนกับข้างต้น"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_13_product_template
msgid "Panel Stand"
msgstr ""

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_16
msgid "Planned"
msgstr "วางแผน"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_29
msgid "Please mention serial number of DCDB"
msgstr ""

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_30
msgid "Please mention serial number of Inverter/UPS"
msgstr ""

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_31
msgid "Please mention serial number of the ACDB"
msgstr ""

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_28
msgid "Please mention serial number of the Charge Controller"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Potential customer fills up the\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">Request a quote</span></font>\n"
"            </strong>\n"
"            form and Submit it, in <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">opportunity is created</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"ลูกค้าที่ผ่านการประเมินกรอกแบบฟอร์ม\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">ขอใบเสนอราคา</span></font>\n"
"            </strong>\n"
"            และส่งในแอป <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">โอกาสทางการขายจึงถูกสร้างขึ้น</font></strong><font style=\"color: rgb(181, 214, 165);\"></font>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_comments_remarks_record
msgid "Remarks"
msgstr "หมายเหตุ"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Repair"
msgstr "ซ่อมแซม"

#. module: solar_installation
#: model:website.menu,name:solar_installation.website_menu_1
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3356
msgid "Request a Quote"
msgstr "ขอใบเสนอราคา"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_3
msgid "Residential"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Roof-top solar refers to the installation of solar panels on the roof of a "
"building to generate electricity from sunlight. It is a popular and "
"sustainable way to harness renewable energy and reduce reliance on "
"traditional power sources.<br/>"
msgstr ""
"แผงโซลาร์บนหลังคาหมายถึงการติดตั้งแผงโซลาร์เซลล์บนหลังคาอาคารเพื่อผลิตไฟฟ้าจากแสงอาทิตย์"
" "
"ถือเป็นวิธีที่ได้รับความนิยมและยั่งยืนในการใช้พลังงานหมุนเวียนและลดการพึ่งพาแหล่งพลังงานแบบดั้งเดิม<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_sale_order_no
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Sales Order No."
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Sales Person is assigned and <strong><font style=\"color: rgb(107, 165, 74);\">Opportunity is moved</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">to Qualified stage</font></strong>. Sales Person follows up with\n"
"            customers through Activities like Calls, Meetings, etc. If the customer agrees the quotation, a site survey is planned and Opportunity is moved to the Site Survey stage."
msgstr ""
"พนักงานขายได้รับมอบหมายและ <strong><font style=\"color: rgb(107, 165, 74);\">โอกาสทางการขายจะถูกย้าย</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">ไปยังขั้นตอนการประเมินคุณสมบัติ</font></strong> พนักงานขายติดตาม\n"
"            ลูกค้าผ่านกิจกรรมต่างๆ เช่น การโทร การประชุม เป็นต้น หากลูกค้าตกลงกับใบเสนอราคา จะมีการวางแผนเพื่อลงพื้นที่และโอกาสทางการขายจะถูกย้ายไปยังขั้นตอนลงพื้นที่"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Secured distributorship for a new line of inverters, strengthening our "
"market position. Expanded the team to 150+, fostering growth and "
"expertise.<br/>"
msgstr ""
"ได้รับสิทธิ์เป็นตัวแทนจำหน่ายตัวแปลงรุ่นใหม่ "
"ซึ่งทำให้ตำแหน่งทางการตลาดของเราแข็งแกร่งยิ่งขึ้น เราได้ขยายทีมงานมากถึง 150"
" คน เพื่อส่งเสริมการเติบโตและความเชี่ยวชาญในอุตสาหกรรม <br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Solar Installation"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_5_product_template
msgid "Solar Installation Charge (1 kW-Off grid)"
msgstr ""

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_4
#: model:project.project,name:solar_installation.project_project_4
msgid "Solar Installation Project"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_6_product_template
msgid "Solar Panel "
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_23_product_template
msgid "Solar Panel set"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_20_product_template
msgid "Solar installation charge (1 kW-On grid)"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_18_product_template
msgid "Solar installation charge (2 kW-Off grid)"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_21_product_template
msgid "Solar installation charge (2 kW-On grid)"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_19_product_template
msgid "Solar installation charge (5 kW-Off grid)"
msgstr ""

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_22_product_template
msgid "Solar installation charge (5 kW-On grid)"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "Submit"
msgstr "ส่ง"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.project_task_id_record
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Task"
msgstr "งาน"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2many_defaul_many2many
msgid "Task Assignees"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Task is assigned to employees/workers. Site visit is planned and <strong><font style=\"color: rgb(107, 165, 74);\">task is moved to Planned stage in project</font></strong>. <strong/> Assignees takes all the equipments listed\n"
"            in <strong><font style=\"color: rgb(107, 165, 74);\">Delivery Order</font></strong> along with them while visiting Client's site for Installation."
msgstr ""
"งานได้รับมอบหมายให้กับพนักงาน/คนงาน มีการวางแผนการเยี่ยมชมไซต์งานและ<strong><font style=\"color: rgb(107, 165, 74);\">ย้ายงานไปยังขั้นตอนที่วางแผนไว้ในโปรเจ็กต์</font></strong> <strong/>ผู้ได้รับมอบหมายจะนำอุปกรณ์ทั้งหมดที่ระบุไว้\n"
"           ใน<strong><font style=\"color: rgb(107, 165, 74);\">ใบสั่งจัดส่ง</font></strong>ไปด้วยในขณะที่เยี่ยมชมไซต์งานของลูกค้าเพื่อการติดตั้ง"

#. module: solar_installation
#: model:project.project,label_tasks:solar_installation.project_project_1
#: model:project.project,label_tasks:solar_installation.project_project_4
msgid "Tasks"
msgstr "งาน"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"This setup is for companies providing solar equipment and its installation service. Usually, residential customers place orders from 1 kW to 10 kW while others up to 100 kW. Solar panels and other equipment are installed at customer\n"
"            site based on kW capacity."
msgstr ""
"การติดตั้งนี้มีไว้สำหรับบริษัทที่จัดหาอุปกรณ์โซลาร์เซลล์และบริการติดตั้ง โดยทั่วไป ลูกค้าตามบ้านจะสั่งซื้อตั้งแต่ 1 กิโลวัตต์ถึง 10 กิโลวัตต์ ในขณะที่บางรายสั่งซื้อมากถึง 100 กิโลวัตต์ แผงโซลาร์เซลล์และอุปกรณ์อื่นๆ จะถูกติดตั้ง\n"
"           ที่ไซต์ของลูกค้าตามความจุกิโลวัตต์"

#. module: solar_installation
#: model:base.automation,name:solar_installation.base_automation_4
msgid "Update warranty date on serial number"
msgstr "อัปเดตวันที่รับประกันบนหมายเลขซีเรียล"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Useful Links"
msgstr "ลิงก์ที่มีประโยชน์"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.field_warranty_date_stock_move_line
#: model:ir.model.fields,field_description:solar_installation.new_date_lot_serial_warranty_date
msgid "Warranty Date"
msgstr "วันที่รับประกัน"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_related_field_warranty_end_date
msgid "Warranty End Date"
msgstr ""

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems."
msgstr ""
"พวกเราคือทีมงานที่มุ่งมั่นและมีเป้าหมายที่จะปรับปรุงคุณภาพชีวิตของทุกคนผ่านผลิตภัณฑ์ที่สร้างสรรค์"
" เราสร้างผลิตภัณฑ์ที่ยอดเยี่ยมเพื่อแก้ไขปัญหาด้านธุรกิจของคุณ"

#. module: solar_installation
#: model_terms:web_tour.tour,rainbow_man_message:solar_installation.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ยินดีต้อนรับ! ขอให้สนุกกับการค้นพบ"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Work Flows"
msgstr ""

#. module: solar_installation
#: model:ir.actions.act_window,name:solar_installation.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "แผ่นงาน"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_kw_plan
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "kW plan"
msgstr ""
