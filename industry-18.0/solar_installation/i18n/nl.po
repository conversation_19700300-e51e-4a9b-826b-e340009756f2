# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* solar_installation
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>o, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:53+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "+1 (079) 400-5001"
msgstr "+1 (079) 400-5001"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Assigned user will confirm the Repair, start the repair, finish the "
"repairing work and inform the Helpdesk user for the same through Chatter "
"functionality."
msgstr ""
"- De toegewezen gebruiker bevestigt de reparatie, start de reparatie, "
"voltooit de reparatie en informeert de Helpdesk-gebruiker hierover via de "
"Chatter-functionaliteit."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Customer can <font style=\"color: rgb(107, 165, 74);\"><strong>raise a ticket</strong></font> to Customer Care team for Repair through mail, \"Submit a Ticket\" form or clicking on Chat bot button in Help page in Website. Support\n"
"            Ticket will be generated in Helpdesk app."
msgstr ""
"- De klant kan <font style=\"color: rgb(107, 165, 74);\"><strong>een ticket indienen</strong></font> bij de klantenservice voor reparatie via e-mail, het formulier \"Ticket indienen\" of door te klikken op de knop Chat bot op de Help-pagina van de website. Ondersteuning\n"
"            Ticket wordt gegenereerd in Helpdesk app."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk User will create Repair in draft mode and fills up Customer's "
"details in it like equipment to be repaired, warranty end date, etc."
msgstr ""
"- De Helpdesk-gebruiker maakt de reparatie aan in conceptmodus en vult de "
"gegevens van de klant in, zoals de apparatuur die gerepareerd moet worden, "
"de einddatum van de garantie, enz."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk user will confirm with the customer whether they will pick up the"
" equipment from repairing site or request to deliver the equipment to their "
"address."
msgstr ""
"- De Helpdesk-gebruiker bevestigt met de klant of hij de apparatuur ophaalt "
"bij de reparatielocatie of verzoekt om de apparatuur op zijn adres af te "
"leveren."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of delivery to customer's address, Helpdesk user will communicate with the customer after delivery and take confirmation from customer about equipment working and\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">closes the Ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"- In het geval van levering op het adres van de klant, zal de Helpdesk-gebruiker na levering met de klant communiceren en van de klant de bevestiging krijgen dat de apparatuur werkt en\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">sluit het Ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of pick up, ticket will be closed after customer's confirmation of"
" checking equipment at repairing site."
msgstr ""
"- In het geval van afhalen wordt het ticket gesloten nadat de klant heeft "
"bevestigd dat de apparatuur is gecontroleerd op de reparatieplek."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Inventory user <strong><font style=\"color: rgb(107, 165, 74);\">will validate</font></strong> returned equipment in Return and check the <font style=\"color: rgb(107, 165, 74);\"><strong>warranty end date</strong></font>. If the\n"
"            warranty date is not under the warranty period, an invoice will be raised for the parts replaced and repairing charge."
msgstr ""
"- Voorraad gebruiker <strong><font style=\"color: rgb(107, 165, 74);\">valideert</font></strong> geretourneerde apparatuur in Retour en controleert de <font style=\"color: rgb(107, 165, 74);\"><strong>einddatum garantie</strong></font>. Als de\n"
"            datum niet onder de garantieperiode valt, wordt er een factuur opgesteld voor de vervangen onderdelen en reparatiekosten."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "- Warranty end date is fetched from delivery of the equipment."
msgstr ""
"- De einddatum van de garantie wordt gehaald uit de levering van de "
"apparatuur."

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_7
msgid "1 kW"
msgstr "1 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_4
msgid "12 kW"
msgstr "12 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_10
msgid "2 kW"
msgstr "2 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_2
msgid "3 kW"
msgstr "3 kW"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"
msgstr ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2704
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Sun Power "
"Solar Services</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Sun Power "
"Solar Services</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"Shining a Light on Solar Energy: Powering a Sustainable Future\"</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"Licht op zonne-energie: Energie voor een duurzame toekomst\"</span>\n"
"                                    <br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">Telefoonnummer</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">Je bedrijf</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Je E-mail</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Name</span>"
msgstr "<span class=\"s_website_form_label_content\">Je naam</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Requirement</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Jouw Vereiste</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Field Service Task for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">Velddiensttaak voor installatie</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Quotation for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">Offerte voor installatie</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Task Assignment &amp; Planning for "
"Installation and Delivery of Equipments</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Taakverdeling en planning voor installatie "
"en levering van apparatuur</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Worksheet for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">Werkblad voor installatie</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 1: Website Form -&gt; CRM -&gt; Sales "
"-&gt; Accounting -&gt; Field Service </span>"
msgstr ""
"<span style=\"font-size: 18px;\">Stroom 1: Website formulier -&gt; CRM -&gt;"
" Verkoop -&gt; Boekhouding -&gt; Buitendienst </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 2: Manual Opportunity -&gt; CRM -&gt; "
"Sales -&gt; Accounting -&gt; Field Service </span><br/>"
msgstr ""
"<span style=\"font-size: 18px;\">Flow 2: Handmatige verkoopkans -&gt; CRM "
"-&gt; Verkoop -&gt; Boekhouding -&gt; Buitendienst </span><br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 18px;\">Flow 3: Helpdesk -&gt; Repair </span>"
msgstr ""
"<span style=\"font-size: 18px;\">Stroom 3: Helpdesk -&gt; Reparatie </span>"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_14_product_template
msgid "AC Wire"
msgstr "AC-draad"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_12
#: model:product.template,name:solar_installation.product_product_10_product_template
msgid "ACDB"
msgstr "ACDB"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "About us"
msgstr "Over ons"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Assignees fill up the Worksheet after completion of the Installation. Several <strong><font style=\"color: rgb(107, 165, 74);\">images of Solar System installation</font></strong> of Client's site are captured along with the\n"
"            customer's confirmation and signature in Worksheet. The worksheet includes customized fields like Start date, End date, plan, signature, etc which are configured through\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio App</font></strong>."
msgstr ""
"Assignees vullen het werkblad in na voltooiing van de installatie. Verschillende <strong><font style=\"color: rgb(107, 165, 74);\">beelden van de Zonnesysteeminstallatie</font></strong> van de locatie van de klant worden vastgelegd, samen met de bevestiging en handtekening van de\n"
"            bevestiging en handtekening van de klant in het werkblad. Het werkblad bevat aangepaste velden zoals begindatum, einddatum, plan, handtekening, enz. die worden geconfigureerd via\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio-app</font></strong>."

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_8
#: model:product.template,name:solar_installation.product_product_8_product_template
msgid "Battery"
msgstr "Batterij"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "CRM Stages"
msgstr "CRM fases"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_47
msgid "Canceled"
msgstr "Geannuleerd"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_9
#: model:product.template,name:solar_installation.product_product_7_product_template
msgid "Charge Controller"
msgstr "Lastregelaar"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_template_4
msgid "Client Site Survey (Free)"
msgstr "Onderzoek klantlocatie (gratis)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Collaboration and Partnerships<br/>"
msgstr "Samenwerking en partnerschappen<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_commencement_date_time
msgid "Commencement Date & Time"
msgstr "Aanvangsdatum en -tijd"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_5
msgid "Commercial"
msgstr "Commercieel"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_completion_date_time
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date & Time"
msgstr "Datum en tijd van voltooiing"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date &amp; Time"
msgstr "Datum & tijd van voltooiing"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Confirmation of Sales Order <strong><font style=\"color: rgb(107, 165, "
"74);\">creates 1 Field Service Task</font></strong> automatically for Solar "
"system installation in a Project dedicated to Installation."
msgstr ""
"Bevestiging van verkooporder <strong><font style=\"color: rgb(107, 165, "
"74);\">maakt 1 buitendiensttaak</font></strong> automatisch voor de "
"installatie van het Zonnesysteem in een Project gewijd aan Installatie."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Connect with us"
msgstr "Volg ons"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Contact us"
msgstr "Contact"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_3
msgid "Created on"
msgstr "Aangemaakt op"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Customer"
msgstr "Klant"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_many2one_customer
msgid "Customer "
msgstr "Klant "

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_signature_defaul_customer_signature
msgid "Customer Signature"
msgstr "Handtekening klant"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_15_product_template
msgid "DC Wire"
msgstr "DC Draad"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_10
#: model:product.template,name:solar_installation.product_product_11_product_template
msgid "DCDB"
msgstr "DCDB"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_date_default_wor_date
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Date"
msgstr "Datum"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_html_default_wor_html
msgid "Declaration"
msgstr "Aangifte"

#. module: solar_installation
#: model:account.analytic.plan,name:solar_installation.account_analytic_plan_1
msgid "Default"
msgstr "Standaard"

#. module: solar_installation
#: model:ir.model,name:solar_installation.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "Standaard werkbon"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_46
msgid "Done"
msgstr "Gereed"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_12_product_template
msgid "Earthing Kit"
msgstr "Aardingsset"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Education and Awareness<br/>"
msgstr "Onderwijs en bewustwording<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Educational campaigns can be conducted to inform individuals, businesses, "
"and communities about the advantages of solar energy, including reduced "
"carbon emissions, energy cost savings, and energy independence.<br/>"
msgstr ""
"Er kunnen educatieve campagnes worden gevoerd om individuen, bedrijven en "
"gemeenschappen te informeren over de voordelen van zonne-energie, zoals "
"minder CO2-uitstoot, besparing op energiekosten en "
"energieonafhankelijkheid.<br/>"

#. module: solar_installation
#: model:ir.actions.server,name:solar_installation.ir_act_server_1017
msgid "Execute Code"
msgstr "Code uitvoeren"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Feature"
msgstr "Eigenschap"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Final Quotation for installation is confirmed and sent to the customer and "
"<strong><font style=\"color: rgb(107, 165, 74);\">100% advance payment is "
"received</font></strong>."
msgstr ""
"Definitieve offerte voor installatie wordt bevestigd en naar de klant "
"gestuurd en <strong><font style=\"color: rgb(107, 165, 74);\">100% advance "
"betaling is ontvangen</font></strong>."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Follow us"
msgstr "Volg ons"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_6
msgid "Free Site Survey"
msgstr "Gratis onderzoek"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Government Incentives and Policies<br/>"
msgstr "Stimuleringsmaatregelen en beleid van de overheid<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Governments can play a crucial role in promoting solar energy by offering "
"incentives such as tax credits, grants, and subsidies for solar "
"installations. They can also implement policies that require a certain "
"percentage of energy to come from renewable sources, including solar.<br/>"
msgstr ""
"Overheden kunnen een cruciale rol spelen bij het stimuleren van zonne-"
"energie door stimuleringsmaatregelen aan te bieden, zoals "
"belastingkredieten, subsidies en premies voor zonne-installaties. Ze kunnen "
"ook beleid implementeren dat vereist dat een bepaald percentage van de "
"energie afkomstig is van hernieuwbare bronnen, waaronder zonne-energie.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_default_handover
msgid "Handover Performed By"
msgstr "Overdracht uitgevoerd door"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Helpdesk Ticket"
msgstr "Helpdeskticket"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Home"
msgstr "Startpagina"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "I want to install 2 kW solar system"
msgstr "Ik wil een zonnesysteem van 2 kW installeren"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_binary_field_4
msgid "Image 1"
msgstr "Afbeelding 1"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image
msgid "Image 2"
msgstr "Afbeelding 2"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image_3
msgid "Image 3"
msgstr "Afbeelding 3"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Images"
msgstr "Afbeeldingen"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_17
msgid "Installation in progress"
msgstr "Installatie in behandeling"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_1
#: model:project.project,name:solar_installation.project_project_1
msgid "Internal"
msgstr "Intern"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_11
#: model:product.template,name:solar_installation.product_product_9_product_template
msgid "Inverter/UPS"
msgstr "Omvormer/UPS"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_16_product_template
msgid "MC4 connector"
msgstr "MC4-connector"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "Mail to&amp;"
msgstr "Mail naar&amp;"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.ir_model_fields_12686
msgid "Name"
msgstr "Naam"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_14
msgid "New"
msgstr "Nieuw"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_8
msgid "Off Grid"
msgstr "Buiten het net"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_24_product_template
msgid "Off Grid Set"
msgstr "Off Grid Set"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_9
msgid "On Grid"
msgstr "Op rooster"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_25_product_template
msgid "On Grid Set"
msgstr "Op rasterset"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Open website Request a quote form"
msgstr "Website openen Offerteaanvraagformulier"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Opportunity is created manually in CRM. From CRM, the flow is same as above"
msgstr ""
"Verkoopkans wordt handmatig aangemaakt in CRM. Vanuit CRM is de flow "
"hetzelfde als hierboven"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_13_product_template
msgid "Panel Stand"
msgstr "Paneelstandaard"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_16
msgid "Planned"
msgstr "Gepland"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_29
msgid "Please mention serial number of DCDB"
msgstr "Vermeld het serienummer van DCDB"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_30
msgid "Please mention serial number of Inverter/UPS"
msgstr "Vermeld het serienummer van de omvormer/UPS"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_31
msgid "Please mention serial number of the ACDB"
msgstr "Vermeld het serienummer van de ACDB"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_28
msgid "Please mention serial number of the Charge Controller"
msgstr "Vermeld het serienummer van de Charge Controller"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Potential customer fills up the\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">Request a quote</span></font>\n"
"            </strong>\n"
"            form and Submit it, in <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">opportunity is created</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"Potentiële klant vult de\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">Offerte aanvragen</span></font>\n"
"            </strong>\n"
"            formulier in en verstuurt het, in <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">kans wordt aangemaakt</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_comments_remarks_record
msgid "Remarks"
msgstr "Opmerkingen"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Repair"
msgstr "Reparatie"

#. module: solar_installation
#: model:website.menu,name:solar_installation.website_menu_1
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3356
msgid "Request a Quote"
msgstr "Offerte aanvragen"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_3
msgid "Residential"
msgstr "Woonbestemming"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Roof-top solar refers to the installation of solar panels on the roof of a "
"building to generate electricity from sunlight. It is a popular and "
"sustainable way to harness renewable energy and reduce reliance on "
"traditional power sources.<br/>"
msgstr ""
"Roof-top solar verwijst naar de installatie van zonnepanelen op het dak van "
"een gebouw om elektriciteit op te wekken uit zonlicht. Het is een populaire "
"en duurzame manier om hernieuwbare energie te benutten en de afhankelijkheid"
" van traditionele energiebronnen te verminderen.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_sale_order_no
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Sales Order No."
msgstr "Verkoopordernr."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Sales Person is assigned and <strong><font style=\"color: rgb(107, 165, 74);\">Opportunity is moved</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">to Qualified stage</font></strong>. Sales Person follows up with\n"
"            customers through Activities like Calls, Meetings, etc. If the customer agrees the quotation, a site survey is planned and Opportunity is moved to the Site Survey stage."
msgstr ""
"Verkoper is toegewezen en <strong><font style=\"color: rgb(107, 165, 74);\">Verkoopkans wordt verplaatst</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">naar Gekwalificeerd stadium</font></strong>. De verkoopmedewerker volgt\n"
"            klanten door middel van activiteiten zoals gesprekken, vergaderingen, enz. Als de klant akkoord gaat met de offerte, wordt een locatieonderzoek gepland en gaat de verkoopkans naar de fase locatieonderzoek."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Secured distributorship for a new line of inverters, strengthening our "
"market position. Expanded the team to 150+, fostering growth and "
"expertise.<br/>"
msgstr ""
"Distributeurschap verworven voor een nieuwe lijn omvormers, waardoor onze "
"marktpositie is versterkt. Uitbreiding van het team tot 150+, waardoor groei"
" en expertise worden gestimuleerd.<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Solar Installation"
msgstr "Zonne-installatie"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_5_product_template
msgid "Solar Installation Charge (1 kW-Off grid)"
msgstr "Installatiekosten zonne-energie (1 kW - zonder net)"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_4
#: model:project.project,name:solar_installation.project_project_4
msgid "Solar Installation Project"
msgstr "Project voor zonne-installatie"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_6_product_template
msgid "Solar Panel "
msgstr "Zonnepaneel "

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_23_product_template
msgid "Solar Panel set"
msgstr "Set zonnepanelen"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_20_product_template
msgid "Solar installation charge (1 kW-On grid)"
msgstr "Kosten zonne-installatie (1 kW-op net)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_18_product_template
msgid "Solar installation charge (2 kW-Off grid)"
msgstr "Installatietoeslag zonne-energie (2 kW - elektriciteitsnet)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_21_product_template
msgid "Solar installation charge (2 kW-On grid)"
msgstr "Kosten zonne-installatie (2 kW-op net)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_19_product_template
msgid "Solar installation charge (5 kW-Off grid)"
msgstr "Installatietoeslag zonne-energie (5 kW - off grid)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_22_product_template
msgid "Solar installation charge (5 kW-On grid)"
msgstr "Kosten zonne-installatie (5 kW-op net)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "Submit"
msgstr "Indienen"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.project_task_id_record
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Task"
msgstr "Taak"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2many_defaul_many2many
msgid "Task Assignees"
msgstr "Taakgeassocieerden"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Task is assigned to employees/workers. Site visit is planned and <strong><font style=\"color: rgb(107, 165, 74);\">task is moved to Planned stage in project</font></strong>. <strong/> Assignees takes all the equipments listed\n"
"            in <strong><font style=\"color: rgb(107, 165, 74);\">Delivery Order</font></strong> along with them while visiting Client's site for Installation."
msgstr ""
"Taken worden toegewezen aan werknemers/werkneemsters. Er wordt een locatiebezoek gepland en <strong><font style=\"color: rgb(107, 165, 74);\">taak wordt verplaatst naar Geplande fase in project</font></strong>. <strong/> Toegewezenen nemen alle apparatuur mee die vermeld staat\n"
"            in <strong><font style=\"color: rgb(107, 165, 74);\">Leveringsbon</font></strong> mee naar de locatie van de klant voor installatie."

#. module: solar_installation
#: model:project.project,label_tasks:solar_installation.project_project_1
#: model:project.project,label_tasks:solar_installation.project_project_4
msgid "Tasks"
msgstr "Taken"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"This setup is for companies providing solar equipment and its installation service. Usually, residential customers place orders from 1 kW to 10 kW while others up to 100 kW. Solar panels and other equipment are installed at customer\n"
"            site based on kW capacity."
msgstr ""
"Deze opzet is voor bedrijven die zonneapparatuur en de installatie ervan leveren. Gewoonlijk plaatsen particulieren bestellingen van 1 kW tot 10 kW en anderen tot 100 kW. Zonnepanelen en andere apparatuur worden op de\n"
"            op basis van de kW-capaciteit."

#. module: solar_installation
#: model:base.automation,name:solar_installation.base_automation_4
msgid "Update warranty date on serial number"
msgstr "Garantiedatum op serienummer bijwerken"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Useful Links"
msgstr "Handige links"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.field_warranty_date_stock_move_line
#: model:ir.model.fields,field_description:solar_installation.new_date_lot_serial_warranty_date
msgid "Warranty Date"
msgstr "Garantiedatum"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_related_field_warranty_end_date
msgid "Warranty End Date"
msgstr "Einddatum garantie"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems."
msgstr ""
"Wij zijn een team van gepassioneerde mensen met als doel ieders leven te "
"verbeteren door middel van disruptieve producten. We bouwen geweldige "
"producten om jouw bedrijfsproblemen op te lossen."

#. module: solar_installation
#: model_terms:web_tour.tour,rainbow_man_message:solar_installation.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Welkom! Veel plezier met verkennen."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Work Flows"
msgstr "Werkstromen"

#. module: solar_installation
#: model:ir.actions.act_window,name:solar_installation.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "Werkbonnen"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_kw_plan
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "kW plan"
msgstr "kW-plan"
