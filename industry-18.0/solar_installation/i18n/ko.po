# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* solar_installation
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# Sarah <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:53+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "+1 (079) 400-5001"
msgstr "+1 (079) 400-5001"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Assigned user will confirm the Repair, start the repair, finish the "
"repairing work and inform the Helpdesk user for the same through Chatter "
"functionality."
msgstr ""
"- 배정된 사용자가 수리를 확인하고 수리 프로세스를 시작하여 작업을 완료한 후 채팅 기능을 통해 헬프데스크 사용자에게 알립니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Customer can <font style=\"color: rgb(107, 165, 74);\"><strong>raise a ticket</strong></font> to Customer Care team for Repair through mail, \"Submit a Ticket\" form or clicking on Chat bot button in Help page in Website. Support\n"
"            Ticket will be generated in Helpdesk app."
msgstr ""
"- 고객은 이메일, \"티켓 제출\" 양식 또는 웹사이트의 도움말 페이지에서 챗봇 버튼을 클릭하여 수리를 위해 고객 지원팀에 <font style=\"color: rgb(107, 165, 74);\"><strong>티켓을 제출</strong></font>할 수 있습니다.\n"
"            헬프데스크 앱에서 지원 티켓이 생성됩니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk User will create Repair in draft mode and fills up Customer's "
"details in it like equipment to be repaired, warranty end date, etc."
msgstr "- 헬프데스크 사용자는 수리할 장비와 보증 만료일 등의 고객 세부 정보를 입력하여 수리 항목 초안을 작성합니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Helpdesk user will confirm with the customer whether they will pick up the"
" equipment from repairing site or request to deliver the equipment to their "
"address."
msgstr "- 헬프데스크 사용자가 고객에게 수리 현장에서 장비를 수령할 것인지 아니면 주소지로 배송을 요청할 것인지 확인합니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of delivery to customer's address, Helpdesk user will communicate with the customer after delivery and take confirmation from customer about equipment working and\n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">closes the Ticket</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"- 고객의 주소로 배송할 때 헬프데스크 사용자가 배송 후 고객과 연락하여 장비가 제대로 작동하는지 확인한 다음 \n"
"            <strong><font style=\"color: rgb(107, 165, 74);\">티켓을 닫습니다</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- In case of pick up, ticket will be closed after customer's confirmation of"
" checking equipment at repairing site."
msgstr "- 픽업의 경우 고객이 수리 센터에서 장비를 확인했음을 확인한 후 티켓이 마감됩니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"- Inventory user <strong><font style=\"color: rgb(107, 165, 74);\">will validate</font></strong> returned equipment in Return and check the <font style=\"color: rgb(107, 165, 74);\"><strong>warranty end date</strong></font>. If the\n"
"            warranty date is not under the warranty period, an invoice will be raised for the parts replaced and repairing charge."
msgstr ""
"- 재고 관리자는 반품 섹션에서 반품된 장비를 확인하고 <font style=\"color: rgb(107, 165, 74);\"><strong>보증 종료일</strong></font>을 <strong><font style=\"color: rgb(107, 165, 74);\">확인합니다.</font></strong>\n"
"            보증 기간이 만료된 경우 교체된 부품 및 수리 비용에 대한 청구서가 생성됩니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "- Warranty end date is fetched from delivery of the equipment."
msgstr "- 보증 종료 날짜는 장비 배송일로부터 가져옵니다."

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_7
msgid "1 kW"
msgstr "1 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_4
msgid "12 kW"
msgstr "12 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_10
msgid "2 kW"
msgstr "2 kW"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_2
msgid "3 kW"
msgstr "3 kW"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"
msgstr ""
"<i class=\"fa fa-envelope fa-fw me-2\"/>\n"
"                                                <span>\n"
"                                                    <span style=\"color: rgb(55, 65, 81);font-size: 14px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)\"><EMAIL></span>\n"
"                                                </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2704
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Sun Power "
"Solar Services</span>"
msgstr ""
"<span class=\"o_footer_copyright_name me-2\">Copyright ©&amp; Sun Power "
"Solar Services</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"Shining a Light on Solar Energy: Powering a Sustainable Future\"</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"oe-tabs\" style=\"width: 10.125px;\"/>\n"
"                                    <span class=\"o_animated_text o_animate o_anim_bounce_in o_visible\" style=\"\">\"태양 에너지에 빛을 비추다: 지속 가능한 미래를 위한 원동력\"</span>\n"
"                                    <br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">전화 번호</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">회사</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">이메일</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "<span class=\"s_website_form_label_content\">Your Name</span>"
msgstr "<span class=\"s_website_form_label_content\">이름</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid ""
"<span class=\"s_website_form_label_content\">Your Requirement</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">요청 사항</span>\n"
"                                                    <span class=\"s_website_form_mark\"> *</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Field Service Task for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">설치를 위한 현장 서비스 작업</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Quotation for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">설치 견적서</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">Task Assignment &amp; Planning for "
"Installation and Delivery of Equipments</span>"
msgstr "<span style=\"font-size: 14px;\">장비 설치 및 배송을 위한 작업 할당 및 계획.</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 14px;\">Worksheet for Installation</span>"
msgstr "<span style=\"font-size: 14px;\">설치용 작업 기록</span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 1: Website Form -&gt; CRM -&gt; Sales "
"-&gt; Accounting -&gt; Field Service </span>"
msgstr ""
"<span style=\"font-size: 18px;\">프로세스 1: 웹사이트 양식 -&gt; CRM -&gt; 판매 -&gt; 회계"
" -&gt; 현장 서비스 </span>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"<span style=\"font-size: 18px;\">Flow 2: Manual Opportunity -&gt; CRM -&gt; "
"Sales -&gt; Accounting -&gt; Field Service </span><br/>"
msgstr ""
"<span style=\"font-size: 18px;\">프로세스 2: 수동으로 기회 생성 -&gt; CRM -&gt; 판매 -&gt;"
" 회계 -&gt; 현장 서비스 </span><br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "<span style=\"font-size: 18px;\">Flow 3: Helpdesk -&gt; Repair </span>"
msgstr "<span style=\"font-size: 18px;\">프로세스 3: 헬프데스크 -&gt; 수리 </span>"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_14_product_template
msgid "AC Wire"
msgstr "AC 전선"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_12
#: model:product.template,name:solar_installation.product_product_10_product_template
msgid "ACDB"
msgstr "ACDB"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "About us"
msgstr "회사 소개"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Assignees fill up the Worksheet after completion of the Installation. Several <strong><font style=\"color: rgb(107, 165, 74);\">images of Solar System installation</font></strong> of Client's site are captured along with the\n"
"            customer's confirmation and signature in Worksheet. The worksheet includes customized fields like Start date, End date, plan, signature, etc which are configured through\n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">Studio App</font></strong>."
msgstr ""
"담당자가 설치를 완료한 후 작업 기록을 작성합니다. 작업 기록에는 고객의 확인 및 서명과 함께 고객 현장의  <strong><font style=\"color: rgb(107, 165, 74);\">태양광 시스템 설치 이미지</font></strong>가 캡처됩니다.\n"
"            작업 기록에는 시작 날짜, 종료 날짜, 계획, 서명 등과 같은 사용자 지정 필드가 포함되어 있으며, \n"
"            <strong><font style=\"color: rgb(165, 74, 123);\">스튜디오 앱</font></strong>을 통해 설정할 수 있습니다."

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_8
#: model:product.template,name:solar_installation.product_product_8_product_template
msgid "Battery"
msgstr "배터리"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "CRM Stages"
msgstr "CRM 단계"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_47
msgid "Canceled"
msgstr "취소됨"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_9
#: model:product.template,name:solar_installation.product_product_7_product_template
msgid "Charge Controller"
msgstr "충전기 컨트롤러"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_template_4
msgid "Client Site Survey (Free)"
msgstr "고객 사이트 설문조사 (무료)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Collaboration and Partnerships<br/>"
msgstr "협업 및 파트너십<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_commencement_date_time
msgid "Commencement Date & Time"
msgstr "시작 날짜 및 시간"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_5
msgid "Commercial"
msgstr "광고"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_datetime_default_completion_date_time
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date & Time"
msgstr "완료 날짜 및 시간"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Completion Date &amp; Time"
msgstr "완료 날짜 및 시간"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Confirmation of Sales Order <strong><font style=\"color: rgb(107, 165, "
"74);\">creates 1 Field Service Task</font></strong> automatically for Solar "
"system installation in a Project dedicated to Installation."
msgstr ""
"판매 주문이 확인되면 전용 설치 프로젝트 내에서 태양광 시스템 설치에 대한 <strong><font style=\"color: "
"rgb(107, 165, 74);\">현장 서비스 작업 하나가 자동으로 생성</font></strong>됩니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Connect with us"
msgstr "고객지원팀 연락처"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Contact us"
msgstr "문의하기"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_3
msgid "Created on"
msgstr "작성일자"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Customer"
msgstr "고객"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_many2one_customer
msgid "Customer "
msgstr "고객"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_signature_defaul_customer_signature
msgid "Customer Signature"
msgstr "고객 서명"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_15_product_template
msgid "DC Wire"
msgstr "DC 전선"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_10
#: model:product.template,name:solar_installation.product_product_11_product_template
msgid "DCDB"
msgstr "DCDB"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_date_default_wor_date
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Date"
msgstr "날짜"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_html_default_wor_html
msgid "Declaration"
msgstr "신고서"

#. module: solar_installation
#: model:account.analytic.plan,name:solar_installation.account_analytic_plan_1
msgid "Default"
msgstr "기본"

#. module: solar_installation
#: model:ir.model,name:solar_installation.x_project_task_worksheet_template_1_ir_model_1
msgid "Default Worksheet"
msgstr "기본 작업 계획표"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_46
msgid "Done"
msgstr "완료"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_12_product_template
msgid "Earthing Kit"
msgstr "접지 키트"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Education and Awareness<br/>"
msgstr "교육 및 인식<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Educational campaigns can be conducted to inform individuals, businesses, "
"and communities about the advantages of solar energy, including reduced "
"carbon emissions, energy cost savings, and energy independence.<br/>"
msgstr ""
"탄소 배출량 감소, 에너지 비용 절감, 에너지 자립 등 태양광 에너지의 이점에 대해 개인, 기업, 지역사회에 알리기 위한 교육 캠페인을 "
"기획할 수 있습니다.<br/>"

#. module: solar_installation
#: model:ir.actions.server,name:solar_installation.ir_act_server_1017
msgid "Execute Code"
msgstr "코드 실행"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Feature"
msgstr "기능"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Final Quotation for installation is confirmed and sent to the customer and "
"<strong><font style=\"color: rgb(107, 165, 74);\">100% advance payment is "
"received</font></strong>."
msgstr ""
"설치에 대한 최종 견적이 확정되어 고객에게 <strong><font style=\"color: rgb(107, 165, "
"74);\">100% 선불 영수증과 함께 전송</font></strong>됩니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Follow us"
msgstr "함께하기"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_6
msgid "Free Site Survey"
msgstr "무료 사이트 조사"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid "Government Incentives and Policies<br/>"
msgstr "정부 인센티브 및 정책<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Governments can play a crucial role in promoting solar energy by offering "
"incentives such as tax credits, grants, and subsidies for solar "
"installations. They can also implement policies that require a certain "
"percentage of energy to come from renewable sources, including solar.<br/>"
msgstr ""
"정부는 태양광 설치에 대한 세금 공제, 보조금, 보조금 등의 인센티브를 제공함으로써 태양광 에너지를 크게 장려할 수 있습니다. 또한 특정"
" 비율의 에너지를 태양광을 포함한 재생 에너지로 공급하도록 의무화하는 정책을 수립할 수도 있습니다.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_default_handover
msgid "Handover Performed By"
msgstr "인수인계 담당자"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Helpdesk Ticket"
msgstr "고객센터 티켓"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Home"
msgstr "홈"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "I want to install 2 kW solar system"
msgstr "2kW 태양광 시스템을 설치하려고 합니다."

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_binary_field_4
msgid "Image 1"
msgstr "이미지 1"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image
msgid "Image 2"
msgstr "이미지 2"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_image_default_wo_image_3
msgid "Image 3"
msgstr "이미지 3"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.x_project_task_worksheet_template_1_ir_ui_view_1
msgid "Images"
msgstr "이미지"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_17
msgid "Installation in progress"
msgstr "설치 진행 중"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_1
#: model:project.project,name:solar_installation.project_project_1
msgid "Internal"
msgstr "내부"

#. module: solar_installation
#: model:chatbot.script.answer,name:solar_installation.chatbot_script_answer_11
#: model:product.template,name:solar_installation.product_product_9_product_template
msgid "Inverter/UPS"
msgstr "인버터/UPS"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_16_product_template
msgid "MC4 connector"
msgstr "MC4 커넥터"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "Mail to&amp;"
msgstr "메일 주소&amp;"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.ir_model_fields_12686
msgid "Name"
msgstr "이름"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_14
msgid "New"
msgstr "신규"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_8
msgid "Off Grid"
msgstr "오프 그리드"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_24_product_template
msgid "Off Grid Set"
msgstr "그리드 연결 안 함"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_9
msgid "On Grid"
msgstr "그리드"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_25_product_template
msgid "On Grid Set"
msgstr "그리드 연결"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Open website Request a quote form"
msgstr "웹사이트 견적 요청 양식 열기"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Opportunity is created manually in CRM. From CRM, the flow is same as above"
msgstr "CRM에서 수동으로 기회를 생성합니다. 거기서부터 프로세스는 위와 동일한 단계를 따릅니다."

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_13_product_template
msgid "Panel Stand"
msgstr "패널 스탠드"

#. module: solar_installation
#: model:project.task.type,name:solar_installation.project_task_type_16
msgid "Planned"
msgstr "계획됨"

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_29
msgid "Please mention serial number of DCDB"
msgstr "DCDB의 일련 번호를 기재하세요."

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_30
msgid "Please mention serial number of Inverter/UPS"
msgstr "인버터/UPS의 일련 번호를 기재하세요."

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_31
msgid "Please mention serial number of the ACDB"
msgstr "ACDB의 일련 번호를 기재하세요."

#. module: solar_installation
#: model:chatbot.script.step,message:solar_installation.chatbot_script_step_28
msgid "Please mention serial number of the Charge Controller"
msgstr "충전 컨트롤러의 일련 번호를 기재해 주세요."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Potential customer fills up the\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">Request a quote</span></font>\n"
"            </strong>\n"
"            form and Submit it, in <font class=\"text-o-color-5\">CRM</font> <strong><font style=\"color: rgb(107, 165, 74);\">opportunity is created</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"
msgstr ""
"잠재 고객이\n"
"            <strong>\n"
"                <font class=\"text-o-color-5\"><span style=\"font-weight: normal;\">견적 요청 양식</span></font>을\n"
"            </strong>\n"
"            작성하여 제출하면 <font class=\"text-o-color-5\">CRM</font> 앱에  <strong><font style=\"color: rgb(107, 165, 74);\">영업 기회가 생성됩니다</font></strong><font style=\"color: rgb(181, 214, 165);\">.</font>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_comments_remarks_record
msgid "Remarks"
msgstr "비고"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Repair"
msgstr "수리"

#. module: solar_installation
#: model:website.menu,name:solar_installation.website_menu_1
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3356
msgid "Request a Quote"
msgstr "견적 요청하기"

#. module: solar_installation
#: model:crm.tag,name:solar_installation.crm_tag_3
msgid "Residential"
msgstr "주거용"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Roof-top solar refers to the installation of solar panels on the roof of a "
"building to generate electricity from sunlight. It is a popular and "
"sustainable way to harness renewable energy and reduce reliance on "
"traditional power sources.<br/>"
msgstr ""
"옥상 태양광은 건물 옥상에 태양광 패널을 설치하여 햇빛을 전기로 변환하는 방식입니다.재생 에너지를 확보하고 기존 전력원에 대한 의존도를 "
"낮추기 위해 선호되는 친환경적인 방법입니다.<br/>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_sale_order_no
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Sales Order No."
msgstr "판매 주문 번호"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Sales Person is assigned and <strong><font style=\"color: rgb(107, 165, 74);\">Opportunity is moved</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">to Qualified stage</font></strong>. Sales Person follows up with\n"
"            customers through Activities like Calls, Meetings, etc. If the customer agrees the quotation, a site survey is planned and Opportunity is moved to the Site Survey stage."
msgstr ""
"영업 담당자가 배정되고 <strong><font style=\"color: rgb(107, 165, 74);\">영업 기회는</font></strong> <strong><font style=\"color: rgb(107, 165, 74);\">적격 단계로 이동합니다</font></strong>. 영업 담당자는\n"
"            전화, 미팅 등의 활동을 통해 고객과 후속 조치를 취합니다. 고객이 견적에 동의하면 현장 조사가 계획되고 기회는 현장 조사 단계로 이동합니다."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.homepage
msgid ""
"Secured distributorship for a new line of inverters, strengthening our "
"market position. Expanded the team to 150+, fostering growth and "
"expertise.<br/>"
msgstr ""
"새로운 인버터 라인의 총판권을 확보하여 시장 입지를 강화했습니다. 팀을 150여 명으로 확대하여 성장과 전문성을 강화했습니다.<br/>"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Solar Installation"
msgstr "태양광 설치"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_5_product_template
msgid "Solar Installation Charge (1 kW-Off grid)"
msgstr "태양광 설치 요금 (1kW-그리드 외)"

#. module: solar_installation
#: model:account.analytic.account,name:solar_installation.account_analytic_account_4
#: model:project.project,name:solar_installation.project_project_4
msgid "Solar Installation Project"
msgstr "태양열 설치 프로젝트"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_6_product_template
msgid "Solar Panel "
msgstr "태양광 패널"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_23_product_template
msgid "Solar Panel set"
msgstr "태양광 패널 세트"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_20_product_template
msgid "Solar installation charge (1 kW-On grid)"
msgstr "태양광 설치 요금 (1kW-그리드 기준)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_18_product_template
msgid "Solar installation charge (2 kW-Off grid)"
msgstr "태양광 설치 요금 (2kW-그리드 외)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_21_product_template
msgid "Solar installation charge (2 kW-On grid)"
msgstr "태양광 설치 요금 (2kW-그리드 기준)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_19_product_template
msgid "Solar installation charge (5 kW-Off grid)"
msgstr "태양광 설치 요금 (5kW-그리드 외)"

#. module: solar_installation
#: model:product.template,name:solar_installation.product_product_22_product_template
msgid "Solar installation charge (5 kW-On grid)"
msgstr "태양광 설치 요금 (5kW-그리드 기준)"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.get_a_quote
msgid "Submit"
msgstr "제출"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.project_task_id_record
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "Task"
msgstr "작업"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2many_defaul_many2many
msgid "Task Assignees"
msgstr "작업 담당자"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"Task is assigned to employees/workers. Site visit is planned and <strong><font style=\"color: rgb(107, 165, 74);\">task is moved to Planned stage in project</font></strong>. <strong/> Assignees takes all the equipments listed\n"
"            in <strong><font style=\"color: rgb(107, 165, 74);\">Delivery Order</font></strong> along with them while visiting Client's site for Installation."
msgstr ""
"작업이 직원/작업자에게 할당되고 현장 방문이 예약되어 <strong><font style=\"color: rgb(107, 165, 74);\">프로젝트에서 작업이 계획 단계로 이동</font></strong>합니다.\n"
"            <strong/>배정자는 설치를 위해 고객사 현장을 방문하는 동안 <strong><font style=\"color: rgb(107, 165, 74);\">배송 주문서</font></strong>에 기재된 모든 장비를 가지고 방문합니다."

#. module: solar_installation
#: model:project.project,label_tasks:solar_installation.project_project_1
#: model:project.project,label_tasks:solar_installation.project_project_4
msgid "Tasks"
msgstr "작업"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid ""
"This setup is for companies providing solar equipment and its installation service. Usually, residential customers place orders from 1 kW to 10 kW while others up to 100 kW. Solar panels and other equipment are installed at customer\n"
"            site based on kW capacity."
msgstr ""
"이 설정은 태양광 장비 및 설치 서비스를 제공하는 회사를 위해 설계되었습니다. 일반적으로 주거용 고객은 1kW에서 10kW 사이를 주문하고, 다른 고객은 최대 100kW까지 주문할 수 있습니다. 태양광 패널 및 관련 장비는 kW 용량에 따라\n"
"            고객 시설에 설치됩니다."

#. module: solar_installation
#: model:base.automation,name:solar_installation.base_automation_4
msgid "Update warranty date on serial number"
msgstr "일련번호의 보증 날짜 업데이트"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid "Useful Links"
msgstr "유용한 링크"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.field_warranty_date_stock_move_line
#: model:ir.model.fields,field_description:solar_installation.new_date_lot_serial_warranty_date
msgid "Warranty Date"
msgstr "보증 날짜"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.x_related_field_warranty_end_date
msgid "Warranty End Date"
msgstr "보증 만료일"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_2702
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems."
msgstr ""
"저희는 모든 사람들이 더 나은 삶을 영위할 수 있도록 혁신적인 제품을 제작하고자 하는 열정을 가지고 구성된 팀입니다. 고객님이 비즈니스를"
" 운영하는 중 겪을 수 있는 문제를 해결할 훌륭한 제품을 만들기 위해 노력하고 있습니다."

#. module: solar_installation
#: model_terms:web_tour.tour,rainbow_man_message:solar_installation.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "환영합니다! 마음껏 둘러 보세요."

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.welcome_article_body
msgid "Work Flows"
msgstr "워크플로우"

#. module: solar_installation
#: model:ir.actions.act_window,name:solar_installation.x_project_task_worksheet_template_1_ir_actions_act_window_1
msgid "Worksheets"
msgstr "작업 계획표"

#. module: solar_installation
#: model_terms:ir.ui.view,arch_db:solar_installation.ir_ui_view_3265
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: solar_installation
#: model:ir.model.fields,field_description:solar_installation.new_many2one_kw_plan
#: model_terms:ir.ui.view,arch_db:solar_installation.report_custom_x_project_task_worksheet_template_1
msgid "kW plan"
msgstr "kW 요금제"
