<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="new_date_default_wor_date" model="ir.model.fields">
        <field name="name">x_date</field>
        <field name="field_description">Date</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">date</field>
    </record>
    <record id="new_date_lot_serial_warranty_date" model="ir.model.fields">
        <field name="name">x_warranty_date</field>
        <field name="field_description">Warranty Date</field>
        <field name="model_id" ref="stock.model_stock_lot"/>
        <field name="ttype">date</field>
    </record>
    <record id="field_warranty_date_stock_move_line" model="ir.model.fields">
        <field name="name">x_warranty_date</field>
        <field name="field_description">Warranty Date</field>
        <field name="model_id" ref="stock.model_stock_move_line"/>
        <field name="ttype">date</field>
        <field name="related">lot_id.x_warranty_date</field>
    </record>
    <record id="new_datetime_default_commencement_date_time" model="ir.model.fields">
        <field name="name">x_commencement_date_time</field>
        <field name="field_description">Commencement Date &amp; Time</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">datetime</field>
    </record>
    <record id="new_datetime_default_completion_date_time" model="ir.model.fields">
        <field name="name">x_completion_date_time</field>
        <field name="field_description">Completion Date &amp; Time</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">datetime</field>
    </record>
    <record id="new_html_default_wor_html" model="ir.model.fields">
        <field name="name">x_declaration</field>
        <field name="field_description">Declaration</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">html</field>
    </record>
    <record id="project_task_id_record" model="ir.model.fields">
        <field name="name">x_project_task_id</field>
        <field name="field_description">Task</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="on_delete">cascade</field>
        <field name="relation">project.task</field>
        <field name="required" eval="True"/>
        <field name="ttype">many2one</field>
    </record>
    <record id="x_comments_remarks_record" model="ir.model.fields">
        <field name="name">x_comments</field>
        <field name="field_description">Remarks</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">html</field>
    </record>
    <record id="ir_model_fields_12686" model="ir.model.fields">
        <field name="name">x_name</field>
        <field name="field_description">Name</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="related">x_project_task_id.name</field>
        <field name="ttype">char</field>
    </record>
    <record id="new_image_default_wo_binary_field_4" model="ir.model.fields">
        <field name="name">x_image_1</field>
        <field name="field_description">Image 1</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">binary</field>
    </record>
    <record id="new_image_default_wo_image" model="ir.model.fields">
        <field name="name">x_image_2</field>
        <field name="field_description">Image 2</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">binary</field>
    </record>
    <record id="new_image_default_wo_image_3" model="ir.model.fields">
        <field name="name">x_image_3</field>
        <field name="field_description">Image 3</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">binary</field>
    </record>
    <record id="new_many2many_defaul_many2many" model="ir.model.fields">
        <field name="name">x_task_assignees</field>
        <field name="field_description">Task Assignees</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="relation">res.users</field>
        <field name="relation_table">x_res_users_x_project_task_worksheet_template_1_rel</field>
        <field name="ttype">many2many</field>
    </record>
    <record id="new_many2one_default_handover" model="ir.model.fields">
        <field name="name">x_handover_performed_by</field>
        <field name="field_description">Handover Performed By</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="relation">res.users</field>
        <field name="ttype">many2one</field>
    </record>
    <record id="new_many2one_kw_plan" model="ir.model.fields">
        <field name="name">x_kw_plan</field>
        <field name="field_description">kW plan</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="relation">sale.order.template</field>
        <field name="ttype">many2one</field>
    </record>
    <record id="new_many2one_sale_order_no" model="ir.model.fields">
        <field name="name">x_sales_order_no</field>
        <field name="field_description">Sales Order No.</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="relation">sale.order</field>
        <field name="ttype">many2one</field>
    </record>
    <record id="x_many2one_customer" model="ir.model.fields">
        <field name="name">x_customer</field>
        <field name="field_description">Customer </field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="relation">res.partner</field>
        <field name="ttype">many2one</field>
    </record>
    <record id="x_related_field_warranty_end_date" model="ir.model.fields">
        <field name="name">x_warranty_end_date</field>
        <field name="field_description">Warranty End Date</field>
        <field name="model_id" ref="helpdesk.model_helpdesk_ticket"/>
        <field name="related">lot_id.x_warranty_date</field>
        <field name="ttype">date</field>
    </record>
    <record id="new_signature_defaul_customer_signature" model="ir.model.fields">
        <field name="name">x_customer_signature</field>
        <field name="field_description">Customer Signature</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="ttype">binary</field>
    </record>
</odoo>
