<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="chatbot_script_step_28" model="chatbot.script.step">
        <field name="message">Please mention serial number of the Charge Controller</field>
        <field name="step_type">question_selection</field>
        <field name="triggering_answer_ids" eval="[(6, 0, [ref('chatbot_script_answer_9')])]"/>
        <field name="sequence">6</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
    </record>
    <record id="chatbot_script_step_29" model="chatbot.script.step">
        <field name="message">Please mention serial number of DCDB</field>
        <field name="step_type">question_selection</field>
        <field name="triggering_answer_ids" eval="[(6, 0, [ref('chatbot_script_answer_10')])]"/>
        <field name="sequence">7</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
    </record>
    <record id="chatbot_script_step_30" model="chatbot.script.step">
        <field name="message">Please mention serial number of Inverter/UPS</field>
        <field name="step_type">question_selection</field>
        <field name="triggering_answer_ids" eval="[(6, 0, [ref('chatbot_script_answer_11')])]"/>
        <field name="sequence">8</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
    </record>
    <record id="chatbot_script_step_31" model="chatbot.script.step">
        <field name="message">Please mention serial number of the ACDB</field>
        <field name="step_type">question_selection</field>
        <field name="triggering_answer_ids" eval="[(6, 0, [ref('chatbot_script_answer_12')])]"/>
        <field name="sequence">9</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
    </record>
    <record id="website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref" model="chatbot.script.step">
        <field name="message">Please describe in detail what kind of technical advice you need</field>
        <field name="step_type">question_selection</field>
        <field name="triggering_answer_ids" eval="[(6, 0, [ref('website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_technical')])]"/>
        <field name="sequence">3</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
    </record>
    <record id="website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch" model="chatbot.script.step">
        <field name="message">First, what is the nature of your issue?</field>
        <field name="step_type">question_selection</field>
        <field name="sequence">2</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
        <field name="answer_ids" eval="[(6, 0, [ref('website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_technical'), ref('website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_administrative')])]"/>
    </record>
    <record id="website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial" model="chatbot.script.step">
        <field name="message">Please select which equipment is not working.</field>
        <field name="step_type">question_selection</field>
        <field name="triggering_answer_ids" eval="[(6, 0, [ref('website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_administrative')])]"/>
        <field name="sequence">4</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
        <field name="answer_ids" eval="[(6, 0, [ref('chatbot_script_answer_8'), ref('chatbot_script_answer_9'), ref('chatbot_script_answer_10'), ref('chatbot_script_answer_11'), ref('chatbot_script_answer_12')])]"/>
    </record>
    <record id="website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_thanks" model="chatbot.script.step">
        <field name="message">Please mention Serial Number of the Battery</field>
        <field name="step_type">question_selection</field>
        <field name="triggering_answer_ids" eval="[(6, 0, [ref('chatbot_script_answer_8')])]"/>
        <field name="sequence">5</field>
        <field name="chatbot_script_id" ref="website_helpdesk_livechat.chatbot_script_helpdesk_bot"/>
    </record>
    <record id="website_helpdesk_livechat.chatbot_script_helpdesk_step_ticket" model="chatbot.script.step">
        <field name="helpdesk_team_id" ref="helpdesk.helpdesk_team1"/>
    </record>
    <record id="website_helpdesk_livechat.chatbot_script_helpdesk_bot" model="chatbot.script">
        <field name="script_step_ids" eval="[(3, ref('website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_issue'))]"/>
    </record>
</odoo>
