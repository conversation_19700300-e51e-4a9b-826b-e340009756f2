<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="base_automation_4" model="base.automation">
        <field name="name">Update warranty date on serial number</field>
        <field name="model_id" ref="stock.model_stock_move_line"/>
        <field name="filter_domain">["&amp;", ("state", "=", "done"), ("picking_code", "=", "outgoing")]</field>
        <field name="trigger">on_create_or_write</field>
        <field name="trigger_field_ids" eval="[(6, 0, [ref('stock.field_stock_move_line__state')])]"/>
    </record>
</odoo>
