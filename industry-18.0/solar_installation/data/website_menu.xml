<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="website_menu_1" model="website.menu">
        <field name="name">Request a Quote</field>
        <field name="page_id" ref="website_page_1"/>
        <field name="url">/get-a-quote</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="parent_id" model="website.menu" eval="obj().search([
                ('website_id', '=', ref('website.default_website')),
                ('url', '=', '/default-main-menu'),
        ]).id"/>
    </record>
    <function name="write" model="website.menu">
        <value model="website.menu" eval="obj().search([
            ('website_id', '=', ref('website.default_website')),
            ('url', '=', '/contactus'),
        ]).id"/>
        <value model="website.menu" eval="{'website_id': False}"/>
    </function>
</odoo>
