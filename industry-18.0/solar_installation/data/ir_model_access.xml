<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="ir_model_access_1151" model="ir.model.access">
        <field name="name">x_project_task_worksheet_template_1_user_access</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="group_id" ref="project.group_project_user"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="perm_write" eval="True"/>
    </record>
</odoo>
