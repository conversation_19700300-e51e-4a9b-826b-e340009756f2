<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="welcome_article_body">
        <h1>Solar Installation</h1>
        <p>
            This setup is for companies providing solar equipment and its installation service. Usually, residential customers place orders from 1 kW to 10 kW while others up to 100 kW. Solar panels and other equipment are installed at customer
            site based on kW capacity.
        </p>
        <h1>Work Flows</h1>
        <h2><span style="font-size: 18px;">Flow 1: Website Form -&gt; CRM -&gt; Sales -&gt; Accounting -&gt; Field Service </span></h2>
        <p>Open website Request a quote form</p>
        <p>
            Potential customer fills up the
            <strong>
                <font class="text-o-color-5"><span style="font-weight: normal;">Request a quote</span></font>
            </strong>
            form and Submit it, in <font class="text-o-color-5">CRM</font> <strong><font style="color: rgb(107, 165, 74);">opportunity is created</font></strong><font style="color: rgb(181, 214, 165);">.</font>
        </p>
        <p>CRM Stages</p>
        <p>
            Sales Person is assigned and <strong><font style="color: rgb(107, 165, 74);">Opportunity is moved</font></strong> <strong><font style="color: rgb(107, 165, 74);">to Qualified stage</font></strong>. Sales Person follows up with
            customers through Activities like Calls, Meetings, etc. If the customer agrees the quotation, a site survey is planned and Opportunity is moved to the Site Survey stage.
        </p>
        <p>
            <span style="font-size: 14px;">Quotation for Installation</span>
        </p>
        <p>
            Final Quotation for installation is confirmed and sent to the customer and <strong><font style="color: rgb(107, 165, 74);">100% advance payment is received</font></strong>.
        </p>
        <p>
            <span style="font-size: 14px;">Field Service Task for Installation</span>
        </p>
        <p>
            Confirmation of Sales Order <strong><font style="color: rgb(107, 165, 74);">creates 1 Field Service Task</font></strong> automatically for Solar system installation in a Project dedicated to Installation.
        </p>
        <p>
            <span style="font-size: 14px;">Task Assignment &amp; Planning for Installation and Delivery of Equipments</span>
        </p>
        <p>
            Task is assigned to employees/workers. Site visit is planned and <strong><font style="color: rgb(107, 165, 74);">task is moved to Planned stage in project</font></strong>. <strong></strong> Assignees takes all the equipments listed
            in <strong><font style="color: rgb(107, 165, 74);">Delivery Order</font></strong> along with them while visiting Client's site for Installation.
        </p>
        <p>
            <span style="font-size: 14px;">Worksheet for Installation</span>
        </p>
        <p>
            Assignees fill up the Worksheet after completion of the Installation. Several <strong><font style="color: rgb(107, 165, 74);">images of Solar System installation</font></strong> of Client's site are captured along with the
            customer's confirmation and signature in Worksheet. The worksheet includes customized fields like Start date, End date, plan, signature, etc which are configured through
            <strong><font style="color: rgb(165, 74, 123);">Studio App</font></strong>.
        </p>
        <h2><span style="font-size: 18px;">Flow 2: Manual Opportunity -&gt; CRM -&gt; Sales -&gt; Accounting -&gt; Field Service </span><br /></h2>
        <p>
            Opportunity is created manually in CRM. From CRM, the flow is same as above
        </p>
        <h2><span style="font-size: 18px;">Flow 3: Helpdesk -&gt; Repair </span></h2>
        <p>
            Helpdesk Ticket
        </p>
        <p>
            - Customer can <font style="color: rgb(107, 165, 74);"><strong>raise a ticket</strong></font> to Customer Care team for Repair through mail, "Submit a Ticket" form or clicking on Chat bot button in Help page in Website. Support
            Ticket will be generated in Helpdesk app.
        </p>
        <p>- Helpdesk User will create Repair in draft mode and fills up Customer's details in it like equipment to be repaired, warranty end date, etc.</p>
        <p>
            - Warranty end date is fetched from delivery of the equipment.
        </p>
        <p>
            Repair
        </p>
        <p>
            - Inventory user <strong><font style="color: rgb(107, 165, 74);">will validate</font></strong> returned equipment in Return and check the <font style="color: rgb(107, 165, 74);"><strong>warranty end date</strong></font>. If the
            warranty date is not under the warranty period, an invoice will be raised for the parts replaced and repairing charge.
        </p>
        <p>
            - Assigned user will confirm the Repair, start the repair, finish the repairing work and inform the Helpdesk user for the same through Chatter functionality.
        </p>
        <p>
            - Helpdesk user will confirm with the customer whether they will pick up the equipment from repairing site or request to deliver the equipment to their address.
        </p>
        <p>
            - In case of pick up, ticket will be closed after customer's confirmation of checking equipment at repairing site.
        </p>
        <p>
            - In case of delivery to customer's address, Helpdesk user will communicate with the customer after delivery and take confirmation from customer about equipment working and
            <strong><font style="color: rgb(107, 165, 74);">closes the Ticket</font></strong><font style="color: rgb(181, 214, 165);">.</font>
        </p>
    </template>    

    <record id="welcome_article" model="knowledge.article">
        <field name="name">Solar Energy Systems</field>
        <field name="icon">🛠️</field>
        <field name="internal_permission">write</field>
        <field name="cover_image_id" ref="knowledge_cover_6"/>
        <field name="is_article_visible_by_everyone" eval="True"/>
        <field name="is_locked" eval="True"/>
        <field name="body">
            <![CDATA[]]>
        </field>
    </record>
</odoo>
