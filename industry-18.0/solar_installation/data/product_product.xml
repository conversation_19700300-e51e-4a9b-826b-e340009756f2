<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_5" model="product.product">
        <field name="name">Solar Installation Charge (1 kW-Off grid)</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/5-image_1920"/>
        <field name="worksheet_template_id" ref="fsm_worksheet_template"/>
        <field name="service_type">timesheet</field>
        <field name="service_tracking">task_global_project</field>
        <field name="type">service</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="categ_id" ref="product_category_4"/>
        <field name="list_price">700.0</field>
    </record>
    <record id="product_product_6" model="product.product">
        <field name="name">Solar Panel </field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/6-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="expiration_time">7300</field>
        <field name="tracking">lot</field>
        <field name="list_price">220.0</field>
        <field name="use_expiration_date" eval="True"/>
        <field name="barcode">78578477</field>
        <field name="standard_price">17.45</field>
    </record>
    <record id="product_product_7" model="product.product">
        <field name="name">Charge Controller</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/7-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="expiration_time">730</field>
        <field name="tracking">serial</field>
        <field name="list_price">350.0</field>
        <field name="use_expiration_date" eval="True"/>
        <field name="barcode">89757874</field>
        <field name="standard_price">320.0</field>
    </record>
    <record id="product_product_8" model="product.product">
        <field name="name">Battery</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/8-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="expiration_time">730</field>
        <field name="tracking">serial</field>
        <field name="list_price">1200.0</field>
        <field name="use_expiration_date" eval="True"/>
        <field name="barcode">45345464</field>
        <field name="standard_price">6700.0</field>
    </record>
    <record id="product_product_9" model="product.product">
        <field name="name">Inverter/UPS</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/9-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="expiration_time">730</field>
        <field name="tracking">serial</field>
        <field name="list_price">1500.0</field>
        <field name="use_expiration_date" eval="True"/>
        <field name="barcode">65766894</field>
        <field name="standard_price">1380.0</field>
    </record>
    <record id="product_product_10" model="product.product">
        <field name="name">ACDB</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/10-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="expiration_time">730</field>
        <field name="tracking">serial</field>
        <field name="list_price">50.0</field>
        <field name="use_expiration_date" eval="True"/>
        <field name="barcode">698765926</field>
        <field name="standard_price">42.0</field>
    </record>
    <record id="product_product_11" model="product.product">
        <field name="name">DCDB</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/11-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="expiration_time">730</field>
        <field name="tracking">serial</field>
        <field name="list_price">50.0</field>
        <field name="use_expiration_date" eval="True"/>
        <field name="barcode">5948689774</field>
        <field name="standard_price">42.0</field>
    </record>
    <record id="product_product_12" model="product.product">
        <field name="name">Earthing Kit</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/12-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="list_price">50.0</field>
        <field name="barcode">676736</field>
        <field name="standard_price">38.0</field>
    </record>
    <record id="product_product_13" model="product.product">
        <field name="name">Panel Stand</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/13-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="list_price">40.0</field>
        <field name="standard_price">32.0</field>
    </record>
    <record id="product_product_14" model="product.product">
        <field name="name">AC Wire</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/14-image_1920"/>
        <field name="uom_po_id" ref="uom.product_uom_meter"/>
        <field name="uom_id" ref="uom.product_uom_meter"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="tracking">lot</field>
        <field name="list_price">3.0</field>
        <field name="standard_price">2.4</field>
    </record>
    <record id="product_product_15" model="product.product">
        <field name="name">DC Wire</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/15-image_1920"/>
        <field name="uom_po_id" ref="uom.product_uom_meter"/>
        <field name="uom_id" ref="uom.product_uom_meter"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="tracking">lot</field>
        <field name="list_price">3.0</field>
        <field name="standard_price">2.4</field>
    </record>
    <record id="product_product_16" model="product.product">
        <field name="name">MC4 connector</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/16-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="tracking">lot</field>
        <field name="list_price">4.0</field>
        <field name="standard_price">3.300</field>
    </record>
    <record id="product_product_18" model="product.product">
        <field name="name">Solar installation charge (2 kW-Off grid)</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/18-image_1920"/>
        <field name="worksheet_template_id" ref="fsm_worksheet_template"/>
        <field name="service_type">timesheet</field>
        <field name="service_tracking">task_global_project</field>
        <field name="type">service</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="categ_id" ref="product_category_4"/>
        <field name="list_price">1350.0</field>
    </record>
    <record id="product_product_19" model="product.product">
        <field name="name">Solar installation charge (5 kW-Off grid)</field>
        <field name="worksheet_template_id" ref="fsm_worksheet_template"/>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/19-image_1920"/>
        <field name="service_type">timesheet</field>
        <field name="service_tracking">task_global_project</field>
        <field name="type">service</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="categ_id" ref="product_category_4"/>
        <field name="list_price">2800.0</field>
    </record>
    <record id="product_product_20" model="product.product">
        <field name="name">Solar installation charge (1 kW-On grid)</field>
        <field name="worksheet_template_id" ref="fsm_worksheet_template"/>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/20-image_1920"/>
        <field name="service_type">timesheet</field>
        <field name="service_tracking">task_global_project</field>
        <field name="type">service</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="categ_id" ref="product_category_4"/>
        <field name="list_price">600.0</field>
    </record>
    <record id="product_product_21" model="product.product">
        <field name="name">Solar installation charge (2 kW-On grid)</field>
        <field name="worksheet_template_id" ref="fsm_worksheet_template"/>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/21-image_1920"/>
        <field name="service_type">timesheet</field>
        <field name="service_tracking">task_global_project</field>
        <field name="type">service</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="categ_id" ref="product_category_4"/>
        <field name="list_price">1200.0</field>
    </record>
    <record id="product_product_22" model="product.product">
        <field name="name">Solar installation charge (5 kW-On grid)</field>
        <field name="worksheet_template_id" ref="fsm_worksheet_template"/>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/22-image_1920"/>
        <field name="service_type">timesheet</field>
        <field name="service_tracking">task_global_project</field>
        <field name="type">service</field>
        <field name="project_id" ref="project_project_4"/>
        <field name="categ_id" ref="product_category_4"/>
        <field name="list_price">2500.0</field>
    </record>
    <record id="product_product_23" model="product.product">
        <field name="name">Solar Panel set</field>
        <field name="image_1920" type="base64" file="solar_installation/static/src/binary/product_template/23-image_1920"/>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="list_price">224.0</field>
    </record>
    <record id="product_product_24" model="product.product">
        <field name="name">Off Grid Set</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="list_price">3150.0</field>
    </record>
    <record id="product_product_25" model="product.product">
        <field name="name">On Grid Set</field>
        <field name="is_storable">True</field>
        <field name="categ_id" ref="product_category_5"/>
        <field name="list_price">1950.0</field>
    </record>
</odoo>
