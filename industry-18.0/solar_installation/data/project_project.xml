<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_project_1" model="project.project">
        <field name="name">Internal</field>
        <field name="stage_id" ref="project.project_project_stage_0"/>
       <field name="type_ids" eval="[(6, 0, [ref('hr_timesheet.internal_project_default_stage', raise_if_not_found=False)])]"/>
    </record>
    <record id="project_project_4" model="project.project">
        <field name="name">Solar Installation Project</field>
        <field name="stage_id" ref="project.project_project_stage_0"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_14'), ref('project_task_type_16'), ref('project_task_type_17'), ref('project_task_type_46'), ref('project_task_type_47')])]"/>
        <field name="worksheet_template_id" ref="fsm_worksheet_template"/>
        <field name="company_id" ref="base.main_company"/>
        <field name="is_fsm" eval="True"/>
    </record>
</odoo>
