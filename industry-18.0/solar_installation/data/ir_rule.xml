<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="ir_rule_322" model="ir.rule">
        <field name="name">x_project_task_worksheet_template_1_own</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="domain_force"><![CDATA[[('create_uid', '=', user.id)]]]></field>
        <field name="groups" eval="[(6, 0, [ref('project.group_project_user', raise_if_not_found=False)])]"/>
    </record>
    <record id="ir_rule_323" model="ir.rule">
        <field name="name">x_project_task_worksheet_template_1_all</field>
        <field name="model_id" ref="x_project_task_worksheet_template_1_ir_model_1"/>
        <field name="domain_force"><![CDATA[[(1, '=', 1)]]]></field>
        <field name="groups" eval="[(6, 0, [ref('project.group_project_manager', raise_if_not_found=False), ref('industry_fsm.group_fsm_user', raise_if_not_found=False)])]"/>
    </record>
</odoo>
