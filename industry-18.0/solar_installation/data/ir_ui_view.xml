<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="helpdesk_ticket_form" model="ir.ui.view">
        <field name="name">helpdesk.ticket.form.customization</field>
        <field name="model">helpdesk.ticket</field>
        <field name="active" eval="True"/>
        <field name="inherit_id" ref="helpdesk_stock.helpdesk_ticket_view_form_inherit_helpdesk_stock"/>
        <field name="priority">800</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <field name="tracking" position="after">
                <field name="product_id" groups="stock.group_stock_user"/>
                <field name="lot_id" groups="stock.group_stock_user"/>
                <field name="x_warranty_end_date" groups="stock.group_stock_user"/>
            </field>
        </field>
    </record>
    <record id="stock_pr_lot_form" model="ir.ui.view">
        <field name="name">stock.production.lot.form.customization</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_form"/>
        <field name="mode">extension</field>
        <field name="active" eval="True"/>
        <field name="priority">160</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//field[@name='removal_date']" position="after">
                    <field name="x_warranty_date"/>
                </xpath>
            </data>
        </field>
    </record>
    <record id="x_project_task_worksheet_template_1_ir_ui_view_1" model="ir.ui.view">
        <field name="name">template_view_Default_Worksheet</field>
        <field name="model">x_project_task_worksheet_template_1_studio</field>
        <field name="active" eval="True"/>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form create="false" duplicate="false">
                <sheet>
                    <group>
                        <group>
                            <field name="x_customer"/>
                            <field name="x_sales_order_no"/>
                            <field name="x_handover_performed_by"/>
                            <field name="x_project_task_id" domain="[('project_id.task_ids', 'ilike', 'Solar Installation')]"/>
                            <field name="x_task_assignees" widget="many2many_tags"/>
                        </group>
                        <group>
                            <field name="x_date"/>
                            <field name="x_commencement_date_time"/>
                            <field name="x_completion_date_time"/>
                            <field name="x_kw_plan"/>
                            <field name="x_declaration" force_save="True" readonly="1"/>
                            <field name="x_comments"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Images">
                            <group>
                                <group>
                                    <field widget="image" name="x_image_1" options="{'zoom':true, 'size':[0,90]}"/>
                                    <field widget="image" name="x_image_2" options="{'size':[0,90], 'zoom':true}"/>
                                </group>
                                <group>
                                    <field widget="image" name="x_image_3" options="{'size':[0,90], 'zoom':true}"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                    <separator/>
                    <group>
                        <group>
                            <field widget="signature" name="x_customer_signature" options="{'size': [0,90]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="x_project_task_worksheet_template_1_ir_ui_view_2" model="ir.ui.view">
        <field name="name">tree_view_Default_Worksheet</field>
        <field name="model">x_project_task_worksheet_template_1_studio</field>
        <field name="active" eval="True"/>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list>
                <field name="create_date" widget="date"/>
                <field name="x_name"/>
            </list>
        </field>
    </record>
    <record id="x_project_task_worksheet_template_1_ir_ui_view_3" model="ir.ui.view">
        <field name="name">search_view_Default_Worksheet</field>
        <field name="model">x_project_task_worksheet_template_1_studio</field>
        <field name="active" eval="True"/>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search>
                <field name="x_name"/>
                <filter string="Created on" date="create_date" name="create_date"/>
                <filter name="group_by_month" string="Created on" context="{'group_by': 'create_date:month'}"/>
            </search>
        </field>
    </record>
</odoo>
