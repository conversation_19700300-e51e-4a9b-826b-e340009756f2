<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="mrp_bom_1" model="mrp.bom">
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_24').product_tmpl_id.id"/>
        <field name="type">phantom</field>
        <field name="product_uom_id" ref="uom.product_uom_unit"/>
    </record>
    <record id="mrp_bom_2" model="mrp.bom">
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_25').product_tmpl_id.id"/>
        <field name="type">phantom</field>
        <field name="product_uom_id" ref="uom.product_uom_unit"/>
    </record>
    <record id="mrp_bom_3" model="mrp.bom">
        <field name="product_tmpl_id" model="product.product" eval="obj().env.ref('solar_installation.product_product_23').product_tmpl_id.id"/>
        <field name="type">phantom</field>
        <field name="product_uom_id" ref="uom.product_uom_unit"/>
    </record>
</odoo>
