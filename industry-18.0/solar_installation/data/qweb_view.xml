<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="report_custom_x_project_task_worksheet_template_1" model="ir.ui.view">
        <field name="name">x_project_task_worksheet_template_1_studio</field>
        <field name="type">qweb</field>
        <field name="active" eval="True"/>
        <field name="arch" type="xml">
            <t t-name="x_project_task_worksheet_template_1">
                <div>
                    <div class="row" style="page-break-inside: avoid">
                        <div class="col-6" style="page-break-inside: avoid">
                            <div class="row mb-3" style="page-break-inside: avoid">
                                <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Customer</div>
                                <div string="Customer" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_customer"/>
                            </div>
                            <div class="row mb-3" style="page-break-inside: avoid">
                                <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Sales Order No.</div>
                                <div string="Sales Order No." t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_sales_order_no"/>
                            </div>
                            <div class="row mb-3" style="page-break-inside: avoid">
                                <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Task</div>
                                <div string="Task" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_project_task_id"/>
                            </div>
                        </div>
                        <div class="col-6" style="page-break-inside: avoid">
                            <div class="row mb-3" style="page-break-inside: avoid">
                                <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">kW plan</div>
                                <div string="kW plan" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_kw_plan"/>
                            </div>
                            <div class="row mb-3" style="page-break-inside: avoid">
                                <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Date</div>
                                <div string="Date" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_date"/>
                            </div>
                            <div class="row mb-3" style="page-break-inside: avoid">
                                <div t-att-class="('col-5' if report_type == 'pdf' else 'col-lg-5 col-12') + ' font-weight-bold'">Completion Date &amp; Time</div>
                                <div string="Completion Date &amp; Time" t-att-class="'col-7' if report_type == 'pdf' else 'col-lg-7 col-12'" t-field="worksheet.x_completion_date_time"/>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </field>
    </record>
</odoo>
