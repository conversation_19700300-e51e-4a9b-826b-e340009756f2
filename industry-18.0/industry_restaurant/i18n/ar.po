# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_restaurant
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:57+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Beef Carpaccio</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">كارباتشيو لحم البقر</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Cheese Onion Rings</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">حلقات البصل والجبن</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Fresh Soup of the Day</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">حساء الشيف الطازج لهذا اليوم</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Farm Friendly Chicken Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">دجاج سوبريم من المزرعة</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">شرائح لحم بقري 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tuna and Salmon Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">برجر التونة والسلمون</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ", and feel free to"
msgstr "، ويمكنك "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""
"1. قم أولاً بإنشاء كافة موظفيك في قاعدة البيانات. انتقل إلى تطبيق الموظفين "
"للقيام بذلك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "1. Point of sale"
msgstr "1. نقطة البيع "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""
"1. أنت مستعد للترحيب بأول عملائك. للقيام بذلك، حدد طاولة على خريطة الطابق "
"واختر المنتجات التي طلبوها من القائمة. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""
"2. قم بإنشاء أدوار موظفيك ضمن علامة التبويب \"معلومات العمل\". تم إنشاء بعض "
"الأدوار بالفعل لهذا العرض التوضيحي. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Replenishment"
msgstr "2. تجديد المخزون "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Table booking"
msgstr "2. حجز الطاولة "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this"
" one, select the \"Burger Menu Combo\" product. You can choose 2 burger "
"options and 4 different drinks. This \"Combo\" feature will allow you to "
"create a menu with several courses and choices for each."
msgstr ""
"2. لجعل الأمر أكثر تشويقاً، لا تتردد في إنشاء طلب ثانٍ. لهذا الطلب، حدد منتج"
" \"قائمة كومبو البرجر\". يمكنك اختيار خيارين من البرجر و 4 مشروبات مختلفة. "
"ستتيح لك خاصية \"كومبو\" هذه إنشاء قائمة تحتوي على عدة أطباق وخيارات لكل "
"منها. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Click the "
"\"Backend\" menu in the right corner to return to your backend, then switch "
"to the Kitchen Display App."
msgstr ""
"3. بعد تقديم طلباتك، ألق نظرة على تطبيق شاشة المطبخ. انقر على قائمة "
"\"الواجهة الخلفية\" في الزاوية اليمنى للعودة إلى الواجهة الخلفية، ثم انتقل "
"إلى تطبيق شاشة المطبخ. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Plan your services"
msgstr "3. قم بتخطيط خدماتك "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""
"3. ارجع إلى تطبيق التخطيط. يمكنك رؤية بعض المناوبات الجاهزة للتخطيط. حدد "
"السهم لأسفل بجوار زر النشر وانقر على \"التخطيط التلقائي\". سيتم تعيين "
"المناوبات تلقائياً بناءً على توفر موظفيك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Website and online menu"
msgstr "3. الموقع الإلكتروني والقائمة المتوفرة أونلاين "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"4. Open your preparation screen. Your orders are there. You can now mark "
"them as done either line by line or the entire order by clicking on the top."
msgstr ""
"4. افتح شاشة التحضير. ستجد طلباتك هناك. يمكنك الآن تحديد الطلبات كجاهزة إما "
"بنداً تلو الآخر أو عند اكتمال الطلب بأكمله عن طريق الضغط على الجزء العلوي. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. Replenishment"
msgstr "4. تجديد المخزون "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr "4. لا يزال بإمكانك إعادة تعيين أي مناوبة عمل للتكيف مع التخطيط. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr ""
"5. بمجرد الانتهاء من التخطيط، قم بإرسالها إلى زملائك في العمل عن طريق الضغط "
"على زر النشر. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "5. Plan your services"
msgstr "5. قم بالتخطيط لخدماتك "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<b class=\"o_default_snippet_text\">50,000+ companies</b> run Odoo to grow "
"their businesses."
msgstr ""
"<b class=\"o_default_snippet_text\">أكثر من 50,000 شركة</b> تستخدم أودو "
"لتنمية أعمالها. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>أكثر من 50,000 شركة</b> تستخدم أودو لتنمية أعمالها. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<br/>Let the symphony of tastes and textures transport you to distant lands,"
" where every bite is a discovery and every meal a celebration of the senses."
" Indulge in the artistry of the chef as each plate is presented with care "
"and passion, inviting you to experience a world of culinary delights right "
"at your table."
msgstr ""
"<br/>دع سيمفونية الأذواق والقوام تنقلك إلى أراضٍ بعيدة، حيث كل قضمة هي "
"اكتشاف وكل وجبة هي احتفال بالحواس. انغمس في براعة الشيف حيث يتم تقديم كل طبق"
" بعناية وشغف، مما يدعوك لتجربة عالم من المأكولات الشهية على طاولتك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "<font class=\"text-white\">Seasoned | Savory | Delicious</font>"
msgstr "<font class=\"text-white\">غني | لذيذ | لا يقاوم</font> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"mb-3 fst-normal\">⚠️</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"mb-3 fst-normal\">🎉</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"mb-3 fst-normal\">💡</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🔁</i>"
msgstr "<i class=\"mb-3 fst-normal\">🔁</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"mb-3 fst-normal\">🚀</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment
msgid "<small class=\"text-uppercase text-muted\">Table Booking Details</small>"
msgstr "<small class=\"text-uppercase text-muted\">تفاصيل حجز الطاولة</small> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<span class=\"h3-fs\">Welcome to</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Your Odoo Restaurant</font>"
msgstr ""
"<span class=\"h3-fs\">مرحباً بك في</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">مطعم أودو</font> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""
"<strong>1. سير العمل رقم 1</strong>: تحتاج أغلب المنتجات إلى تقييم بشري حيث "
"لا يمكن حساب الكمية المتبقية تلقائياً. بالنسبة لتلك المنتجات، يتم إنشاء مهمة"
" متكررة في تطبيق إدارة المشاريع. تظهر هذه المهمة مرة أخرى كل أسبوع، ويمكنك "
"تخصيصها بقائمة مراجعة لتجنب نسيان أي شيء. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""
"<strong>2. سير العمل رقم 2:</strong> بالنسبة للمنتجات التي يمكن تتبعها، مثل "
"الجعة المعبأة والمشروبات الغازية، يمكنك استخدام عمليات الشراء الآلية. "
"لتجربتها، انتقل إلى نقطة البيع الخاصة بك وقم ببيع جعة بلوند لأحد العملاء. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr "<strong>الأساسيات:</strong> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Do you want to go further?</strong>"
msgstr "<strong>أتود التعمق أكثر؟</strong> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Use Case:</strong>"
msgstr "<strong>حالة استخدام:</strong> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "A gustative travel"
msgstr "رحلة تذوقية "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Add a menu description."
msgstr "إضافة وصف للقائمة. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "All You Can Eat"
msgstr "كل ما يمكنك تناوله "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""
"في نهاية خدمتك، تذكر إغلاق السجل. للقيام بذلك، في تطبيق نقطة البيع، قد "
"بتحديد خيار \"إغلاق السجل\" في القائمة الموجودة أعلى اليمين. يمكنك بعد ذلك "
"تحديد مبلغك النقدي والتحقق من الإغلاق. "

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_2
msgid "Bar"
msgstr "الشريط "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Basics:"
msgstr "الأساسيات "

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_54_product_template
msgid "Beef 1kg"
msgstr "لحم بقري 1 كجم "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr "كارباتشيو اللحم، فيليه منيون 80 أونصة، وكعكة الجبن "

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_59_product_template
msgid "Blond Beer"
msgstr "جعة بلوند "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_header
msgid "Book a Table"
msgstr "حجز طاولة "

#. module: industry_restaurant
#: model:appointment.type,name:industry_restaurant.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Book a table"
msgstr "حجز طاولة "

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_55_product_template
msgid "Butter"
msgstr "زبدة "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr "ومن خلال القيام بذلك، لن يتبقى لديك سوى 23 علبة من الجعة في مخزونك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""
"بالمناسبة، يمكنك رؤية مشروع آخر به مهام متكررة. هذا المشروع عبارة عن عرض "
"توضيحي لمهام التنظيف. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products, an appointment type, a website, and a sample planning."
msgstr ""
"عند رفع البيانات التجريبية لهذا التطبيق، سيتم ملء قاعدة البيانات الخاصة بك "
"ببضعة منتجات، ونوع موعد، وموقع إلكتروني، ونموذج تخطيط. "

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_3
msgid "Cash"
msgstr "نقدي"

#. module: industry_restaurant
#: model:account.journal,name:industry_restaurant.cash
msgid "Cash (Restaurant)"
msgstr "النقد (مطعم) "

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_56_product_template
msgid "Cheese 250gr"
msgstr "جبنة 250 جرام "

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_2
msgid "Cleaning"
msgstr "التنظيف "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Contact us"
msgstr "تواصل معنا "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "اكتشف المزيد عن أودو من خلال التعرف على "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""
"اكتشف أساسيات هذه الباقة واستكشف جميع الإمكانيات التي يقدمها أودو لتحسين "
"تجربتك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Do you want to go further?"
msgstr "أتود التعمق أكثر؟ "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Do you want to test it and see how it looks on your website? Click on the "
"\"Preview\" button."
msgstr "هل تريد اختباره ومعرفة كيف يبدو على موقعك؟ انقر على زر \"معاينة\". "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""
"لا تنس الضغط على زر \"الطلب\" في كل مرة تقوم فيها بتغيير طلب. سيؤدي هذا إلى "
"إرسال الطلب إلى شاشة المطبخ. "

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_2
msgid "Done"
msgstr "منتهي "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Fine Dining Restaurant"
msgstr "مطعم راقي "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr ""
"من نقطة البيع، قم بفتح صندوق النقد الخاص بك على لوحة البيانات الرئيسية. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Get visibility and share your menu with a great <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."
msgstr ""
"كن مرئياً أكثر وشارك قائمتك مع الآخرين على <font class=\"text-o-"
"color-1\"><strong>موقعك الإلكتروني</strong></font> متقن التصميم. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""
"اذهب إلى تطبيق المشتريات الخاص بك لإنشاء طلبات المقترحات أو أوامر الشراء "
"بسهولة. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"القصص العظيمة <b>هي للجميع</b> حتى وإن كُتبت <b>لشخص واحد فقط</b>. إذا وضعت "
"بعين الاعتبار أنك تكتب من أجل جمهور كبير ومتنوع، ستبدو قصتك مزيفة وستنقصها "
"المشاعر الحقيقية وسيفقد الكثير اهتمامهم. اكتب من أجل شخص واحد، وإن بدت قصتك "
"صادقة ونابعة من القلب لهذا الشخص، فسيستقبلها بقية الأشخاص بهذه الطريقة "
"أيضاً. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"القصص الرائعة لها <b>طابع شخصي يميزها</b>. حاول أن تُخبر قصة رائعة تضفي "
"طابعاً شخصياً. كتابة قصة بها هذا الطابع للعملاء المحتملين سيعينك على بناء "
"وتقوية رابطة العلاقة. يظهر ذلك في اختيار الكلمات المناسبة أو العبارات. اكتب "
"من وجهة نظرك الخاصة، لا من تجربة شخص آخر. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Hello there!"
msgstr "مرحباً بك! "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""
"إذا انتقلت إلى تطبيق المشتريات، ستجد طلب شراء جديد جاهز للإرسال. يمكنك "
"اختيار التغليف إذا كان منتجك يحتوي على تغليف. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""
"إذا كنت ترغب في تعيين حساب مستخدم أودو لهم، فيمكنك تحديد مستخدم ذي صلة في "
"علامة تبويب \"إعدادات الموارد البشرية\". "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""
"إذا كنت ترغب في تنفيذ جولة إرشادية عملية لهذا التطبيق، فعليك استيراد "
"البيانات التوضيحية وتجربة حالة الاستخدام التجريبية. (في مقال المعرفة التالي)"
" "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Join us and make your company a better place."
msgstr "انضم إلينا واجعل من شركتك مكاناً أفضل. "

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_57_product_template
msgid "Ketchup 500ml"
msgstr "كاتشب 500 مل "

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_3
msgid "Kitchen"
msgstr "المطبخ "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Let your customer book a table anytime with the <font class=\"text-o-"
"color-1\"><strong>Appointment App</strong></font>."
msgstr ""
"أتح لعملائك إمكانية حجز طاولاتهم بأنفسهم على <font class=\"text-o-"
"color-1\"><strong>تطبيق المواعيد</strong></font>. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Main Course"
msgstr "الطبق الرئيسي "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing Automation</strong></font>, <font"
" class=\"text-o-color-1\"><strong>Email Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Social Media Marketing</strong></font>, and"
" <font class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."
msgstr ""
"تمكن من إدارة كافة قنوات التواصل في مكان واحد باستخدام <font class=\"text-o-"
"color-1\"><strong>تطبيق أتمتة التسويق</strong></font>، <font class=\"text-o-"
"color-1\"><strong>التسويق عبر البريد الإلكتروني</strong></font>، <font "
"class=\"text-o-color-1\"><strong>التسويق الاجتماعي</strong></font>، و <font "
"class=\"text-o-color-1\"><strong>التسويق عبر الرسائل النصية "
"القصيرة</strong></font>. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<font class=\"text-o-color-1\"><strong>Accounting "
"App</strong></font>)"
msgstr ""
"تمكن من إدارة المحاسبة بشكل أسهل من أي وقت مضى مع بيئة متكاملة تماماً. "
"(<font class=\"text-o-color-1\"><strong>تطبيق المحاسبة</strong></font>) "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Manage your booking using the Appointment App."
msgstr "قم بإدارة حجوزاتك باستخدام تطبيق المواعيد. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr "بوفيه شرقي من المقبلات والأطباق الرئيسية والحلويات "

#. module: industry_restaurant
#: model:website.menu,name:industry_restaurant.website_menu_14
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu"
msgstr "القائمة"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu One"
msgstr "القائمة رقم واحد "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu Two"
msgstr "القائمة رقم اثنان "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""
"أودو مدمج بالكامل في تطبيق واحد. قم بتثبيته لتحول هاتفك إلى كاشير إضافي "
"(وغير ذلك الكثير). "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "يمنحك أودو احتمالات لا حصر لها، مثل: "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"بالطبع، هذه مجرد لمحة عامة عن الخصائص التي تشملها هذه الباقة. لا تتردد في "
"إضافة تطبيقات جديدة، وحذف/تعديل البيانات التجريبية، واختبار كل شيء حولك! "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""
"بمجرد توفر المنتجات هناك، يمكنك الضغط على زر \"الإيصال\" الذكي للتحقق من صحة"
" الإيصال الخاص بك وإضافة هذه المنتجات الجديدة إلى مخزونك. "

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_2
msgid "Online Payment"
msgstr "الدفع عبر الإنترنت "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the <font "
"class=\"text-o-color-1\"><strong>Events App</strong></font>."
msgstr ""
"قم بتنظيم فعالياتك وتواصل مع عملائك بسهولة باستخدام <font class=\"text-o-"
"color-1\"><strong>تطبيق الفعاليات</strong></font>. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Our Menus"
msgstr "قوائمنا "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Our menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr "قائمتنا<span class=\"fa fa-angle-right ms-2\"/> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr "تمكن من تخطيط خدماتك بسهولة باستخدام تطبيق التخطيط. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Reach us"
msgstr "تواصل معنا "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""
"يتم عرض قواعد إعادة الطلب في الأزرار الذكية الموجودة أعلى المنتج. يمكنك رؤية"
" الحد الأدنى للكمية والحد الأقصى. في كل مرة ينخفض ​​فيها المخزون المتوقع إلى"
" ما دون الحد الأدنى للمخزون، يتم إنشاء أمر شراء تلقائياً لتجديد الكمية إلى "
"الحد الأقصى. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr "يعد تجديد المخزون أمراً ضرورياً ولكنه يمثل تحدياً لمالك الحانة. "

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_58_product_template
msgid "Sanitizer 250ml"
msgstr "معقم 250 مل  "

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_1
msgid "Service"
msgstr "الخدمة"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Setting different starts for the same service allows you to give your "
"customer the possibility to book every 15 minutes in this setup."
msgstr ""
"يتيح لك تعيين بدايات مختلفة لنفس الخدمة منح عميلك إمكانية الحجز كل 15 دقيقة "
"في هذا الإعداد. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Share a direct link with external services using the \"Share\" option."
msgstr "قم بمشاركة رابط مباشر مع الخدمات الخارجية باستخدام خيار \"مشاركة\". "

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_1
msgid "Sodexo Card Payment"
msgstr "الدفع عن طريق بطاقة Sodexo "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Start receiving orders directly in Odoo or go live with your <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in a few minutes."
msgstr ""
"ابدأ بتلقي الطلبات مباشرةً في أودو أو قم بتشغيل <font class=\"text-o-"
"color-1\"><strong>متجرك الإلكتروني</strong></font> في بضع دقائق. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Starter"
msgstr "بداية "

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_1
msgid "Suppliers Orders"
msgstr "طلبات الموردين "

#. module: industry_restaurant
#: model:project.project,label_tasks:industry_restaurant.project_project_1
#: model:project.project,label_tasks:industry_restaurant.project_project_2
msgid "Tasks"
msgstr "المهام"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"><strong>Kitchen Display</strong></font> "
"will ensure a great follow-up of every order in your bar and kitchen."
msgstr ""
"ستضمن <font class=\"text-o-color-1\"><strong>شاشة المطبخ</strong></font> "
"متابعة رائعة لكل طلب في الحانة والمطبخ. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""
"لن يتم تحديث المخزون إلا عند إغلاق آلة تسجيل النقد. فلنقم بذلك قبل الانتقال "
"إلى الخطوة التالية. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "كافة هذه الخصائص مجانية في اشتراكك الحالي؛ فلا تتردد في استكشافها! 🙃 "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"This setup is made to provide availability from Monday to Friday, with 2 "
"services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM)."
msgstr ""
"تم إعداد هذا الإعداد لتمكين التوافر من الاثنين إلى الجمعة، مع خدمتين يومياً "
"(من الساعة 12:00 صباحاً إلى الساعة 15:00 مساءً ومن الساعة 18:00 مساءً إلى "
"الساعة 23:00 مساءً). "

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_1
msgid "To Do"
msgstr "المهام المراد تنفيذها"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Use case:"
msgstr "حالة استخدام: "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Inventory App</strong></font>"
" to manage your stock and receive products."
msgstr ""
"استخدم <font class=\"text-o-color-1\"><strong>تطبيق المخزون</strong></font> "
"لإدارة المخزون واستلام المنتجات. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Planning App</strong></font> "
"to schedule and share your shifts with your employees."
msgstr ""
"استخدم <font class=\"text-o-color-1\"><strong>تطبيق التخطيط</strong></font> "
"لجدولة نوبات العمل الخاصة بك ومشاركتها مع موظفيك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Point of Sale</strong></font>"
" at the desk for your sales. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""
"استخدم <font class=\"text-o-color-1\"><strong>نقطة البيع</strong></font> عند"
" مكتب المبيعات الخاص بك. يمكنك أيضاً تنزيل تطبيق أودو على الهاتف المحمول على"
" أي هاتف لتلقي الطلبات. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Project App</strong></font> "
"to never miss a reordering or a cleaning task."
msgstr ""
"استخدم <font class=\"text-o-color-1\"><strong>تطبيق المشاريع</strong></font>"
" لكي لا تفوت أبداً مهمة التنظيف أو إعادة الطلب. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Purchase App</strong></font> "
"to reorder your products."
msgstr ""
"استخدم <font class=\"text-o-color-1\"><strong>تطبيق "
"المشتريات</strong></font> لتقوم بإعادة طلب المنتجات. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr "سلطة الخضروات وبرجر اللحم وآيسكريم المانجو "

#. module: industry_restaurant
#: model_terms:web_tour.tour,rainbow_man_message:industry_restaurant.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "أهلاً وسهلاً بك! نتمنى لك رحلة استكشافية سعيدة. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "هل ترغب في مناقشة إعدادات أودو معنا أو تريد التعمق أكثر؟ "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"يمكنك الوصول إلى كل تطبيق تم تثبيته في قاعدة بيانات أودو الخاصة بك على لوحة "
"بياناتك الرئيسية. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can discover a pre-configured Appointment Type named \"Book a table\". "
"Use it to schedule services, the duration of each booking, and every "
"communication you want to send to your customers when they place a booking."
msgstr ""
"يمكنك اكتشاف نوع موعد مُهيأ مسبقًا باسم \"حجز طاولة\". استخدمه لجدولة "
"الخدمات ومدة كل حجز وكل رسالة تريد إرسالها إلى عملائك عند إجراء حجز. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection step of your Point of Sale."
msgstr ""
"بإمكانك تعديل خريطة الطابق بسهولة من خلال النقر على \"تعديل الخطة\" في "
"القائمة الموجودة في أعلى اليمين أثناء وجودك في خطوة اختيار الجدول في نقطة "
"البيع الخاصة بك."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""
"يمكنك الآن إرسال طلب عرض الأسعار الخاص بك عبر البريد الإلكتروني إلى البائع "
"الخاص بك أو تأكيده يدويًا."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory &gt; Products &gt; Blond Beer"
msgstr ""
"يمكنك أن ترى أن هناك قاعدة إعادة ترتيب تم تكوينها لهذا المنتج إذا انتقلت إلى"
" المخزون &gt; المنتجات &gt; البيرة الأشقر "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"لقد أكملت حالة الاستخدام التجريبية هذه! هناك ملايين الطرق الأخرى لتكييف "
"إعدادات Odoo لتناسب احتياجات عملك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""
"لديك حاليًا خطوتان في شاشة المطبخ: واحدة للمطبخ/البار وواحدة للخدمة. يمكنك "
"تكوين الخطوات في قائمة تكوين شاشة المطبخ. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fine Dining Restaurant package and check the related box."
msgstr ""
"لم تقم باستيراد بيانات العرض التوضيحي؟ لا يزال بإمكانك القيام بذلك. انتقل "
"إلى التطبيقات &gt; الصناعات &gt; قم بترقية باقة مطعمك الفاخر وحدد المربع ذي "
"الصلة. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr "لديك إمكانيتان رئيسيتان في Odoo لتسهيل عملية تجديد حسابك. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You just installed the Odoo for Fine Dining Restaurant package. By doing so,"
" we have installed many necessary apps to run your restaurant efficiently."
msgstr ""
"لقد قمت للتو بتثبيت حزمة Odoo للمطاعم الفاخرة. وبذلك نكون قد قمنا بتثبيت "
"العديد من التطبيقات الضرورية لتشغيل مطعمك بكفاءة. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"يجب أن تكون قادرًا على تنفيذ التدفقات التالية للحصول على نظرة عامة على "
"التدفقات المختلفة التي يمكنك تنفيذها بسرعة باستخدام هذه الحزمة باستخدام "
"Odoo. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment_confirmed
msgid "Your table has successfully been booked!"
msgstr "لقد تم حجز طاولتك بنجاح! "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Your website management has never been so easy. Go to the \"Website App\" to"
" discover a sample website and explore millions of possibilities by clicking"
" on the \"Edit\" button."
msgstr ""
"لم تكن إدارة موقع الويب الخاص بك بهذه السهولة من قبل. انتقل إلى \"تطبيق موقع"
" الويب\" لاكتشاف موقع ويب نموذجي واستكشاف ملايين الاحتمالات من خلال النقر "
"فوق الزر \"تحرير\". "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "academy"
msgstr "أكاديمية "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "and"
msgstr "و"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "documentation"
msgstr "التوثيق"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "if you need help! <br/>"
msgstr "إذا احتجت إلى المساعدة! <br/> "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "request a demo"
msgstr "طلب عرض توضيحي "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 الموقع الإلكتروني "
