# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_restaurant
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:57+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\"><PERSON>f <PERSON></span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Carpaccio di manzo</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">10,50 $</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Cheese Onion Rings</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Anelli di cipolla al formaggio</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">9,00 $</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Fresh Soup of the Day</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Zuppa del giorno dello chef</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">7,50 $</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Farm Friendly Chicken Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Suprema di pollo</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">15,50 $</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 230gr</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">15,50 $</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tuna and Salmon Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Burger di tonno e salmone</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">12,00 $</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ", and feel free to"
msgstr "e non esitare a"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""
"1. Per prima cosa, crea tutti i dipendenti e aggiungili nel database. Per "
"farlo, apri l'app Dipendenti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "1. Point of sale"
msgstr "1. Punto Vendita"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""
"1. Sei pronto ad accogliere i tuoi primi clienti. Seleziona un tavolo nella "
"mappa del piano e seleziona i prodotti dalla lista."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""
"2. Aggiungi i ruoli dei dipendenti nella scheda \"Informazioni lavoro\". "
"Alcuni sono stati già creati per questa dimostrazione pratica."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Replenishment"
msgstr "2. Rifornimento"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Table booking"
msgstr "2. Prenotazione tavolo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this"
" one, select the \"Burger Menu Combo\" product. You can choose 2 burger "
"options and 4 different drinks. This \"Combo\" feature will allow you to "
"create a menu with several courses and choices for each."
msgstr ""
"2. Per rendere tutto più interessante, crea un secondo ordine. Seleziona il "
"prodotto \"Combo menu burger\". Puoi scegliere 2 opzioni burger e 4 bevande "
"diverse. La funzionalità \"Combo\" ti consente di creare un menu con varie "
"portate e scelte."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Click the "
"\"Backend\" menu in the right corner to return to your backend, then switch "
"to the Kitchen Display App."
msgstr ""
"3. Dopo aver effettuato gli ordini, guarda lo Schermo cucina. Fai clic sul "
"menu \"Backend\" nell'angolo a destra per tornare al backend, poi passa "
"all'app Schermo cucina."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Plan your services"
msgstr "3. Pianifica i servizi"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""
"3. Aprendo l'app Pianificazione potrai vedere una serie di turni pronti per "
"essere pianificati. Seleziona la freccia in basso accanto al bottone per la "
"pubblicazione e fai clic su \"Pianificazione automatica\". I tuoi verranno "
"assegnati automaticamente in base alla disponibilità dei dipendenti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Website and online menu"
msgstr "3. Sito web e menu online"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"4. Open your preparation screen. Your orders are there. You can now mark "
"them as done either line by line or the entire order by clicking on the top."
msgstr ""
"4. Apri lo schermo di preparazione per vedere gli ordini. Puoi "
"contrassegnarli come svolti riga per riga oppure l'intero ordine facendo "
"clic in alto."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. Replenishment"
msgstr "4. Rifornimento"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr ""
"4. Puoi ancora riassegnare qualsiasi turno per adattare la pianificazione."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr ""
"5. Una volta soddisfatto, inviali ai lavoratori facendo clic sul pulsante di"
" pubblicazione."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "5. Plan your services"
msgstr "5. Pianificazione dei servizi"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<b class=\"o_default_snippet_text\">50,000+ companies</b> run Odoo to grow "
"their businesses."
msgstr ""
"<b class=\"o_default_snippet_text\">+ di 50.000 aziende</b> usano Odoo per "
"far crescere il proprio business."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"<b>Oltre 50.000 aziende</b> fanno crescere le loro attività grazie a Odoo."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<br/>Let the symphony of tastes and textures transport you to distant lands,"
" where every bite is a discovery and every meal a celebration of the senses."
" Indulge in the artistry of the chef as each plate is presented with care "
"and passion, inviting you to experience a world of culinary delights right "
"at your table."
msgstr ""
"<br/>Lascia che una sinfonia di sapori ti trasporti in terre lontane, dove "
"ogni boccone è una scoperta e ogni pasto una celebrazione dei sensi. "
"Lasciati trasportare dalla maestria dello chef, che presenta ogni piatto con"
" cura e passione, invitandoti a sperimentare un mondo di delizie culinarie."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "<font class=\"text-white\">Seasoned | Savory | Delicious</font>"
msgstr "<font class=\"text-white\">Stagionato | Saporito | Delizioso</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"mb-3 fst-normal\">⚠️</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"mb-3 fst-normal\">🎉</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"mb-3 fst-normal\">💡</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🔁</i>"
msgstr "<i class=\"mb-3 fst-normal\">🔁</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"mb-3 fst-normal\">🚀</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment
msgid "<small class=\"text-uppercase text-muted\">Table Booking Details</small>"
msgstr ""
"<small class=\"text-uppercase text-muted\">Dettagli prenotazione "
"tavolo</small>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<span class=\"h3-fs\">Welcome to</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Your Odoo Restaurant</font>"
msgstr ""
"<span class=\"h3-fs\">Benvenuto nel</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">tuo ristorante Odoo</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""
"<strong>1. Flusso 1:</strong> la maggior parte dei prodotti necessitano di "
"una verifica di persona perché la quantità rimanente non può essere "
"calcolata automaticamente. Per questo motivo, nell'app Progetti è stato "
"creato un Lavoro ricorrente. Il lavoro appare ogni settimana e puoi "
"personalizzarlo con un elenco per evitare di dimenticare tutto."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""
"<strong>2. Flusso 2:</strong> per i prodotti che possono essere tracciati "
"come birre in bottiglia e bevande gassate, è possibile automatizzare gli "
"acquisti. Per fare una prova, vai nel Punto Vendita e vendi una Birra bionda"
" ad un cliente."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr "<strong>Nozioni di base:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Do you want to go further?</strong>"
msgstr "<strong>Vuoi saperne di più?</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Use Case:</strong>"
msgstr "<strong>Caso d'uso:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "A gustative travel"
msgstr "Un viaggio gustoso"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Add a menu description."
msgstr "Aggiungi una descrizione per il menù"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "All You Can Eat"
msgstr "All You Can Eat"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""
"Al termine del servizio, ricorda di chiudere il registratore di cassa. Per "
"farlo, nell'app Punto Vendita, seleziona \"Chiudi cassa\" nel menu in alto a"
" destra. In seguito, puoi precisare l'importo in contanti e convalidare la "
"chiusura."

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_2
msgid "Bar"
msgstr "Barra"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Basics:"
msgstr "Informazioni di base:"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_54_product_template
msgid "Beef 1kg"
msgstr "Manzo 1kg"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr "Carpaccio di manzo, 227 grammi di filet mignon e cheesecake"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_59_product_template
msgid "Blond Beer"
msgstr "Birra Bionda"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_header
msgid "Book a Table"
msgstr "Prenota un tavolo"

#. module: industry_restaurant
#: model:appointment.type,name:industry_restaurant.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Book a table"
msgstr "Prenota un tavolo"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_55_product_template
msgid "Butter"
msgstr "Burro"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr "Per farlo, hai solo 23 birre rimanenti in magazzino."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""
"Ad ogni modo, puoi vedere un altro progetto con lavori ricorrenti. "
"Quest'ultimo è un progetto demo per i lavori di pulizia."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products, an appointment type, a website, and a sample planning."
msgstr ""
"In seguito al caricamento dei dati demo del modulo, nel database saranno "
"presenti alcuni prodotti, un tipo di appuntamento, un sito web, e un esempio"
" di pianificazione."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_3
msgid "Cash"
msgstr "Cassa"

#. module: industry_restaurant
#: model:account.journal,name:industry_restaurant.cash
msgid "Cash (Restaurant)"
msgstr "Cassa (Ristorante)"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_56_product_template
msgid "Cheese 250gr"
msgstr "Formaggio 250gr"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_2
msgid "Cleaning"
msgstr "Pulizia"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Contact us"
msgstr "Contattaci"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Scopri di più su Odoo esplorando il nostro"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""
"Scopri le caratteristiche di base del pacchetto ed esplora tutte le "
"possibilità offerte da Odoo per migliorare la tua esperienza."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Do you want to go further?"
msgstr "Vuoi saperne di più?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Do you want to test it and see how it looks on your website? Click on the "
"\"Preview\" button."
msgstr ""
"Vuoi testarlo e vedere come appare sul sito web? Fai clic sul pulsante "
"\"Anteprima\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""
"Non dimenticare di fare clic sul pulsante \"Ordina\" ogni volta che "
"modifichi un ordine. L'azione permetterà di inviare l'ordine allo Schermo "
"cucina."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_2
msgid "Done"
msgstr "Completato"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Fine Dining Restaurant"
msgstr "Ristorante gourmet"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr ""
"Dall'app Punto Vendita, apri il registratore di cassa nella dashboard "
"principale."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Get visibility and share your menu with a great <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."
msgstr ""
"Ottieni visibilità e condividi il menu grazie al <font class=\"text-o-"
"color-1\"><strong>Sito web</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""
"Apri l'app Acquisti per creare facilmente Richieste di preventivo oppure "
"Ordini di acquisto."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Le grandi storie sono <b>per tutti</b>, anche se sono scritte <b>per una "
"sola persona</b>. Se cerchi di scrivere per un pubblico ampio e generico, la"
" tua storia suonerà finta e senza emozione. Nessuno sarà interessato. "
"Scrivila per una persona. Se è autentica per una persona, sarà autentica per"
" tutti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Le grandi storie hanno <b>personalità</b>. Valuta l'opportunità di "
"raccontare una grande storia che esprime carattere. Scrivere per i "
"potenziali clienti una storia con personalità aiuta a stabilire una "
"relazione. Questo si evidenzia nei piccoli dettagli come la scelta delle "
"parole o delle frasi. Scrivi usando il tuo punto di vista, non l'esperienza "
"di qualcun altro."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Hello there!"
msgstr "Ciao!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""
"Nell'app Acquisti, vedrai un nuovo Ordine di acquisto già inviato. Puoi "
"scegliere una confezione se esiste per il tuo prodotto."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""
"Se vuoi assegnarli ad un account utente Odoo, puoi selezionare un utente "
"nella scheda \"Impostazioni HR\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""
"Se vuoi effettuare un tour guidato del modulo, devi importare i dati demo e "
"provare il Caso d'uso - Demo (nel prossimo articolo Knowledge)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Join us and make your company a better place."
msgstr "Unisciti a noi e migliora la tua azienda."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_57_product_template
msgid "Ketchup 500ml"
msgstr "Ketchup 500ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_3
msgid "Kitchen"
msgstr "Cucina"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Let your customer book a table anytime with the <font class=\"text-o-"
"color-1\"><strong>Appointment App</strong></font>."
msgstr ""
"Permetti ai clienti di prenotare un tavolo in qualsiasi momento grazie "
"all'app <font class=\"text-o-color-1\"><strong>Appuntamenti</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Main Course"
msgstr "Piatto principale"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing Automation</strong></font>, <font"
" class=\"text-o-color-1\"><strong>Email Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Social Media Marketing</strong></font>, and"
" <font class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."
msgstr ""
"Gestisci tutti i canali di comunicazione in un posto grazie alle app di "
"Odoo: <font class=\"text-o-color-1\"><strong>Automazione "
"Marketing</strong></font>, <font class=\"text-o-color-1\"><strong>Email "
"Marketing</strong></font>, <font class=\"text-o-color-1\"><strong>Social "
"Media Marketing</strong></font> e <font class=\"text-o-color-1\"><strong>SMS"
" Marketing</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<font class=\"text-o-color-1\"><strong>Accounting "
"App</strong></font>)"
msgstr ""
"Gestisci la contabilità nel modo più semplice possibile grazie ad un "
"ambiente completamente integrato. (<font class=\"text-o-"
"color-1\"><strong>App Contabilità</strong></font>)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Manage your booking using the Appointment App."
msgstr "Gestisci le prenotazioni utilizzando l'app Appuntamenti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr "Buffet mediterraneo di antipasti, piatti principali e dolci"

#. module: industry_restaurant
#: model:website.menu,name:industry_restaurant.website_menu_14
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu"
msgstr "Menù"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu One"
msgstr "Menu uno"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu Two"
msgstr "Menu due"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""
"Odoo è disponibile anche nella versione mobile. Scarica l'app sul tuo "
"smartphone per trasformarlo in una cassa extra (e molto altro)."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo ti offre possibilità infinite, come:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Naturalmente, questa è solo una panoramica delle funzionalità incluse nel "
"pacchetto. Sentiti libero di aggiungere nuove applicazioni, "
"cancellare/modificare i dati demo e provare tutto!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""
"Una volta aggiunti i prodotti, fai clic sul pulsante \"Ricevuta\" per "
"convalidare lo scontrino e aggiungere i nuovi prodotti nel magazzino."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_2
msgid "Online Payment"
msgstr "Pagamento online"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the <font "
"class=\"text-o-color-1\"><strong>Events App</strong></font>."
msgstr ""
"Organizza gli eventi e comunica con i clienti in modo sempòice grazie "
"all'app <font class=\"text-o-color-1\"><strong>Eventi</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Our Menus"
msgstr "I nostri menu"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Our menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr "Il nostro menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr "Pianifica facilmente i servizi utilizzando l'app Pianificazione."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Reach us"
msgstr "Contattaci"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""
"Le regole di riordino vengono visualizzate nei pulsanti intelligenti al di "
"sopra dei prodotti. Puoi vedere la quantità minima e massima. Ogni volta che"
" la previsione delle scorte scende al di sotto del valore minimo, viene "
"creato automaticamente un ordine di acquisto per rifornire e arrivare alla "
"quantità massima."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr ""
"Il rifornimento è essenziale ma potrebbe essere difficile da rendere "
"sistematico per il proprietario di un bar."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_58_product_template
msgid "Sanitizer 250ml"
msgstr "Disinfettante 250ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_1
msgid "Service"
msgstr "Servizio"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Setting different starts for the same service allows you to give your "
"customer the possibility to book every 15 minutes in this setup."
msgstr ""
"La configurazione di più orari per lo stesso servizio ti permette di dare al"
" cliente la possibilità di prenotare ogni 15 minuti grazie a "
"quest'impostazione."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Share a direct link with external services using the \"Share\" option."
msgstr ""
"Condividi un link diretto con i servizi esterni utilizzano l'opzione "
"\"Condividi\":"

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_1
msgid "Sodexo Card Payment"
msgstr "Pagamento con Sodexo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Start receiving orders directly in Odoo or go live with your <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in a few minutes."
msgstr ""
"Inizia a ricevere ordini direttamente su Odoo oppure pubblica il tuo <font "
"class=\"text-o-color-1\"><strong>negozio online</strong></font> in pochi "
"minuti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Starter"
msgstr "Antipasto"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_1
msgid "Suppliers Orders"
msgstr "Ordini fornitori"

#. module: industry_restaurant
#: model:project.project,label_tasks:industry_restaurant.project_project_1
#: model:project.project,label_tasks:industry_restaurant.project_project_2
msgid "Tasks"
msgstr "Lavori"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"><strong>Kitchen Display</strong></font> "
"will ensure a great follow-up of every order in your bar and kitchen."
msgstr ""
"Lo <font class=\"text-o-color-1\"><strong>Schermo cucina</strong></font> "
"garantirà un monitoraggio perfetto dell'avanzamento di ogni ordine nel bar e"
" nella cucina. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""
"Le scorte vengono aggiornate solamente quando chiudi la cassa. Facciamolo "
"prima di passare al prossimo step."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Tutto gratuito nel tuo abbonamento attuale. Cosa aspetti? Fai un giro! 🙃"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"This setup is made to provide availability from Monday to Friday, with 2 "
"services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM)."
msgstr ""
"La configurazione prevede disponibilità da lunedì a venerdì, con 2 servizi "
"al giorno (dalle 12:00 alle 15:00 PM e dalle 18:00 alle 23:00)."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_1
msgid "To Do"
msgstr "Da fare"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Use case:"
msgstr "Caso d'uso:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Inventory App</strong></font>"
" to manage your stock and receive products."
msgstr ""
"Usa l'app <font class=\"text-o-color-1\"><strong>Magazzino</strong></font> "
"per gestire le scorte e ricevere i prodotti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Planning App</strong></font> "
"to schedule and share your shifts with your employees."
msgstr ""
"Usa l'app <font class=\"text-o-"
"color-1\"><strong>Pianificazione</strong></font> per pianificare e "
"condividere turni con i dipendenti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Point of Sale</strong></font>"
" at the desk for your sales. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""
"Usa l'app <font class=\"text-o-color-1\"><strong>Punto "
"Vendita</strong></font> per le vendite al banco. Inoltre, puoi scaricare "
"l'app mobile Odoo per prendere ordini su qualsiasi telefono."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Project App</strong></font> "
"to never miss a reordering or a cleaning task."
msgstr ""
"Usa l'app <strong><font class=\"text-o-color-1\">Progetti</font></strong> "
"per non dimenticare lavori da portare a termine come mettere in ordine e "
"pulire."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Purchase App</strong></font> "
"to reorder your products."
msgstr ""
"Usa <strong><font class=\"text-o-color-1\">l'app Acquisti</font></strong> "
"per ordinare di nuovo i prodotti."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr "Insalata di verdure, hamburger di manzo e gelato al mango"

#. module: industry_restaurant
#: model_terms:web_tour.tour,rainbow_man_message:industry_restaurant.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Benvenuto! Buona visita."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Vorresti parlare della configurazione di Odoo oppure ottenere nuove "
"informazioni?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Puoi accedere a tutte le app installate nel tuo database Odoo dalla "
"dashboard principale. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can discover a pre-configured Appointment Type named \"Book a table\". "
"Use it to schedule services, the duration of each booking, and every "
"communication you want to send to your customers when they place a booking."
msgstr ""
"Puoi scoprire un Tipo di appuntamento preconfigurato chiamato \"Prenota un "
"tavolo\". Usalo per programmare i servizi, la durata di ogni prenotazione e "
"ogni comunicazione che vuoi inviare ai clienti quando effettuano una "
"prenotazione."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection step of your Point of Sale."
msgstr ""
"È possibile modificare facilmente la mappa del piano facendo clic su "
"“Modifica piano” nel menu in alto a destra quando si seleziona il tavolo del"
" Punto Vendita."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""
"Ora puoi inviare la Richiesta di preventivo al fornitore tramite e-mail o "
"confermarla manualmente."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory &gt; Products &gt; Blond Beer"
msgstr ""
"Puoi notare la presenza di una regola di riordino configurata per il "
"prodotto accedendo all'app Magazzino &gt; Prodotti &gt; Birra bionda"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Hai completato il caso d'uso demo! Ci sono milioni di altri modi per "
"adattare la configurazione di Odoo ai bisogni del tuo business."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""
"Nello schermo cucina ci sono due step da seguire: uno per la cucina/bar e "
"uno per il servizio. È possibile configurare gli step nel menu di "
"configurazione dello schermo cucina."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fine Dining Restaurant package and check the related box."
msgstr ""
"Non hai importato i dati demo? Puoi ancora farlo. Vai su App &gt; Industrie "
"&gt; Aggiorna il pacchetto Ristorante gourmet e spunta la casella "
"corrispondente."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr ""
"In Odoo, hai due possibilità per facilitare il processo di rifornimento."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You just installed the Odoo for Fine Dining Restaurant package. By doing so,"
" we have installed many necessary apps to run your restaurant efficiently."
msgstr ""
"Hai appena installato il pacchetto Ristorante gourmet di Odoo. Di "
"conseguenza, abbiamo installato una serie di app necessarie per gestire in "
"modo efficiente il tuo ristorante."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"Dovresti essere in grado di eseguire i seguenti flussi per avere una "
"panoramica di quelli che puoi sfruttare con il pacchetto utilizzando Odoo."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment_confirmed
msgid "Your table has successfully been booked!"
msgstr "Tavolo prenotato con successo!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Your website management has never been so easy. Go to the \"Website App\" to"
" discover a sample website and explore millions of possibilities by clicking"
" on the \"Edit\" button."
msgstr ""
"La gestione di un sito web non è mai stata più semplice di così. Apri l'app "
"\"Sito web\" per visualizzare un sito web di esempio e scopri infinite "
"possibilità di personalizzazione facendo clic sul pulsante \"Modifica\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "academy"
msgstr "academy"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "and"
msgstr "e"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "documentation"
msgstr "documentazione"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "if you need help! <br/>"
msgstr "se hai bisogno di aiuto! <br/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "request a demo"
msgstr "richiedi una demo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 Sito web"
