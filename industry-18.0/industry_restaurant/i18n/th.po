# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_restaurant
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:57+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\"><PERSON><PERSON></span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Cheese Onion Rings</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Fresh Soup of the Day</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">ซุปสดใหม่ประจำวันของเชฟ</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Farm Friendly Chicken Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">สเต็กไก่</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tuna and Salmon Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">เบอร์เกอร์ปลาทูน่าและแซลมอน</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ", and feel free to"
msgstr "และโปรด"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""
"1. ขั้นแรก ให้สร้างพนักงานทั้งหมดของคุณในฐานข้อมูล "
"จากนั้นไปที่แอปข้อมูลพนักงานเพื่อดำเนินการดังกล่าว"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "1. Point of sale"
msgstr "1. การขายหน้าร้าน"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""
"1. คุณพร้อมที่จะต้อนรับลูกค้ารายแรกของคุณแล้ว "
"โดยเลือกตารางบนแผนที่ชั้นและเลือกผลิตภัณฑ์ที่พวกเขาขอจากรายการ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""
"2. สร้างหน้าที่ของพนักงานของคุณภายใต้แท็บ \"ข้อมูลงาน\" "
"มีการสร้างหน้าที่บางส่วนไว้แล้วสำหรับการสาธิตนี้"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Replenishment"
msgstr "2. การเติมสินค้า"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Table booking"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this"
" one, select the \"Burger Menu Combo\" product. You can choose 2 burger "
"options and 4 different drinks. This \"Combo\" feature will allow you to "
"create a menu with several courses and choices for each."
msgstr ""
"2. หากต้องการให้น่าสนใจยิ่งขึ้น ให้สร้างออร์เดอร์ที่สองขึ้นมา "
"สำหรับออร์เดอร์นี้ ให้เลือกผลิตภัณฑ์ \"เมนูเบอร์เกอร์คอมโบ\" "
"คุณสามารถเลือกเบอร์เกอร์ได้ 2 แบบและเครื่องดื่ม 4 แบบ ฟีเจอร์ \"คอมโบ\" "
"นี้จะช่วยให้คุณสร้างเมนูที่มีคอร์สและตัวเลือกต่างๆ มากมายสำหรับแต่ละคอร์สได้"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Click the "
"\"Backend\" menu in the right corner to return to your backend, then switch "
"to the Kitchen Display App."
msgstr ""
"3. หลังจากวางคำสั่งซื้อแล้ว ให้ดูที่จอแสดงผลห้องครัวของเรา คลิกเมนู "
"\"การทำงานส่วนหลัง\" ที่มุมขวาเพื่อกลับไปยังการทำงานส่วนหลังของคุณ "
"จากนั้นสลับไปที่แอปจอแสดงผลห้องครัว"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Plan your services"
msgstr "3. วางแผนบริการของคุณ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""
"3. กลับไปที่แอปการวางแผน คุณจะเห็นกะงานบางส่วนที่พร้อมสำหรับการวางแผน "
"เลือกลูกศรลงข้างปุ่มเผยแพร่ และคลิก \"วางแผนอัตโนมัติ\" "
"กะงานของคุณจะถูกกำหนดโดยอัตโนมัติตามความพร้อมของพนักงานของคุณ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Website and online menu"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"4. Open your preparation screen. Your orders are there. You can now mark "
"them as done either line by line or the entire order by clicking on the top."
msgstr ""
"4. เปิดหน้าจอเตรียมการของคุณ คำสั่งของคุณจะแสดงอยู่ที่นั่น "
"คุณสามารถทำเครื่องหมายว่าเสร็จสิ้นแล้วโดยคลิกด้านบนเป็นรายการต่อรายการหรือทั้งคำสั่งซื้อ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. Replenishment"
msgstr "4. การเติมสินค้า"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr "4. คุณยังสามารถมอบหมายกะงานใหม่เพื่อปรับเปลี่ยนการวางแผนได้"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr ""
"5. เมื่อคุณพึงพอใจแล้ว ส่งให้เพื่อนร่วมงานของคุณโดยการกดปุ่มเผยแพร่ได้เลย"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "5. Plan your services"
msgstr "5. วางแผนบริการของคุณ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<b class=\"o_default_snippet_text\">50,000+ companies</b> run Odoo to grow "
"their businesses."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>50,000+ บริษัท</b> ที่เรียกใช้ Odoo เพื่อขยายธุรกิจของพวกเขา"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<br/>Let the symphony of tastes and textures transport you to distant lands,"
" where every bite is a discovery and every meal a celebration of the senses."
" Indulge in the artistry of the chef as each plate is presented with care "
"and passion, inviting you to experience a world of culinary delights right "
"at your table."
msgstr ""
"<br/>ให้รสชาติและเนื้อสัมผัสพาคุณไปสู่ดินแดนอันไกลโพ้น "
"ที่ซึ่งทุกคำคือการค้นพบและทุกมื้ออาหารคือการเฉลิมฉลอง "
"ดื่มด่ำไปกับฝีมือของเชฟในขณะที่จานอาหารแต่ละจานถูกนำเสนอด้วยความเอาใจใส่ "
"ให้คุณสัมผัสประสบการณ์แห่งความสุขในโลกแห่งอาหาร"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "<font class=\"text-white\">Seasoned | Savory | Delicious</font>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">⚠️</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🎉</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">💡</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🔁</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🚀</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment
msgid "<small class=\"text-uppercase text-muted\">Table Booking Details</small>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<span class=\"h3-fs\">Welcome to</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Your Odoo Restaurant</font>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""
"<strong>1. โฟลว์ 1:</strong> ผลิตภัณฑ์ส่วนใหญ่ต้องได้รับการประเมินจากพนักงาน"
" เนื่องจากไม่สามารถคำนวณปริมาณที่เหลือโดยอัตโนมัติได้ "
"สำหรับผลิตภัณฑ์ดังกล่าว จะมีการสร้างงานประจำในแอปโปรเจ็กต์ "
"งานดังกล่าวจะปรากฏขึ้นซ้ำทุกสัปดาห์ "
"และคุณสามารถปรับแต่งงานโดยใช้รายการตรวจสอบเพื่อหลีกเลี่ยงการลืมกระทำสิ่งใดๆ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""
"<strong>2. โฟลว์ 2:</strong> สำหรับผลิตภัณฑ์ที่สามารถติดตามได้ เช่น "
"เบียร์บรรจุขวดและโซดา คุณสามารถใช้ระบบซื้ออัตโนมัติได้ หากต้องการลองใช้ "
"ให้ไปที่การขายหน้าร้านของคุณและขาย Blond Beer ให้กับลูกค้า"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr "<strong>พื้นฐาน:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Do you want to go further?</strong>"
msgstr "<strong>คุณต้องการดำเนินการต่อหรือไม่?</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Use Case:</strong>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "A gustative travel"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Add a menu description."
msgstr "เพิ่มคำบรรยายเมนู"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "All You Can Eat"
msgstr "กินได้ไม่อั้น"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""
"เมื่อสิ้นสุดการให้บริการ โปรดอย่าลืมปิดเครื่องบันทึกเงินสดของคุณ "
"ในการดำเนินการดังกล่าว ให้เลือก \"ปิดเครื่องบันทึกเงินสด\" "
"ในเมนูด้านบนขวาในแอป POS "
"จากนั้นคุณจะสามารถระบุจำนวนเงินสดและตรวจสอบการปิดเครื่องได้"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_2
msgid "Bar"
msgstr "แถบ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Basics:"
msgstr "พื้นฐาน:"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_54_product_template
msgid "Beef 1kg"
msgstr "เนื้อวัว 1กก."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr "เนื้อคาร์ปาชโช เนื้อฟิเลมิยอง 8 ออนซ์ และชีสเค้ก"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_59_product_template
msgid "Blond Beer"
msgstr "Blond Beer"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_header
msgid "Book a Table"
msgstr ""

#. module: industry_restaurant
#: model:appointment.type,name:industry_restaurant.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Book a table"
msgstr "จองโต๊ะ"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_55_product_template
msgid "Butter"
msgstr "เนย"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr "เมื่อทำเช่นนี้ คุณจะมีเบียร์เหลืออยู่ในสต็อกเพียง 23 ขวด"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""
"อีกอย่าง คุณสามารถดูโปรเจ็กต์อื่นที่มีงานที่เกิดซ้ำได้ "
"อันนี้เป็นการสาธิตสำหรับงานทำความสะอาด"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products, an appointment type, a website, and a sample planning."
msgstr ""
"โดยการอัปโหลดข้อมูลสาธิตของโมดูลนี้ "
"ฐานข้อมูลของคุณก็จะเต็มไปด้วยผลิตภัณฑ์บางรายการ ประเภทการนัดหมาย เว็บไซต์ "
"และตัวอย่างการวางแผน"

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_3
msgid "Cash"
msgstr "เงินสด"

#. module: industry_restaurant
#: model:account.journal,name:industry_restaurant.cash
msgid "Cash (Restaurant)"
msgstr "เงินสด (ร้านอาหาร)"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_56_product_template
msgid "Cheese 250gr"
msgstr "ชีส 250 กรัม"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_2
msgid "Cleaning"
msgstr "ทำความสะอาด"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Contact us"
msgstr "ติดต่อเรา"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "ค้นพบเพิ่มเติมเกี่ยวกับ Odoo โดยเข้าไปที่"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""
"ค้นพบข้อมูลพื้นฐานของแพ็คเกจนี้และสำรวจความเป็นไปได้ทั้งหมดที่ Odoo "
"นำเสนอเพื่อปรับปรุงประสบการณ์ของคุณ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Do you want to go further?"
msgstr "คุณต้องการดำเนินการต่อหรือเปล่า?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Do you want to test it and see how it looks on your website? Click on the "
"\"Preview\" button."
msgstr ""
"คุณต้องการทดสอบและดูว่าจะแสดงผลบนเว็บไซต์ของคุณอย่างไรหรือไม่ คลิกที่ปุ่ม "
"\"ดูตัวอย่าง\""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""
"อย่าลืมกดปุ่ม \"คำสั่งซื้อ\" ทุกครั้งที่คุณเปลี่ยนแปลงคำสั่งซื้อ "
"การดำเนินการนี้จะส่งคำสั่งซื้อไปยังหน้าจอแสดงสินค้าในครัว"

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_2
msgid "Done"
msgstr "เสร็จสิ้น"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Fine Dining Restaurant"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr "จากการขายหน้าร้าน เปิดเครื่องบันทึกเงินสดของคุณบนแดชบอร์ดหลัก"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Get visibility and share your menu with a great <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."
msgstr ""
"รับการมองเห็นและแบ่งปันเมนูของคุณด้วย<font class=\"text-o-"
"color-1\"><strong>เว็บไซต์</strong></font>ที่ยอดเยี่ยม"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""
"ไปที่แอปการจัดซื้อของคุณเพื่อสร้างคำขอข้อเสนอหรือใบสั่งซื้อได้อย่างง่ายดาย"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"เรื่องราวดี ๆ  <b>สำหรับทุกคน</b> "
"แม้ว่าจะถูกเขียนขึ้น<b>เพื่อคนเพียงคนเดียว</b>หากคุณพยายามเขียนโดยคำนึงถึงผู้ชมทั่วในวงกว้าง"
" เรื่องราวของคุณจะฟังดูไร้สาระและขาดอารมณ์ จะไม่มีใครสนใจ "
"เขียนเพื่อคนหนึ่งคน หากมันงดงามสำหรับหนึ่งคน ก็งดงามสำหรับคนอื่นเช่นกัน"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"เรื่องราวที่ยิ่งใหญ่มักมี "
"<b>ลักษณะเฉพาะ</b>พิจารณาการเล่าเรื่องราวที่มีลักษณะเฉพาะ "
"การเขียนเรื่องราวที่มีเอกลักษณ์นี้มีไว้สำหรับผู้มีโอกาสเป็นลูกค้าและจะช่วยในการสร้างความสัมพันธ์"
" สิ่งนี้แสดงให้เห็นเอกลักษณ์ต่าง ๆ เช่นการเลือกคำหรือวลี "
"และจงเขียนจากมุมมองของคุณเอง ไม่ใช่จากประสบการณ์ของคนอื่น"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Hello there!"
msgstr "สวัสดี !"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""
"หากคุณไปที่แอปการจัดซื้อ คุณจะเห็นใบสั่งซื้อใหม่ที่พร้อมจะส่ง "
"คุณสามารถเลือกบรรจุภัณฑ์ได้หากผลิตภัณฑ์ของคุณมีบรรจุภัณฑ์อยู่แล้ว"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""
"หากคุณต้องการกำหนดบัญชีผู้ใช้ Odoo ให้กับพวกเขา "
"คุณสามารถเลือกผู้ใช้ที่เกี่ยวข้องในแท็บ \"การตั้งค่า HR\" ได้"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""
"หากคุณต้องการดำเนินการทัวร์แนะนำแบบปฏิบัติจริงของโมดูลนี้ "
"คุณควรนำเข้าข้อมูลสาธิตและลองใช้กรณีตัวอย่างการใช้งาน "
"(ในบทความคลังข้อมูลถัดไป)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Join us and make your company a better place."
msgstr "เข้าร่วมกับเราและทำให้บริษัทของคุณเป็นสถานที่ที่ดีกว่า"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_57_product_template
msgid "Ketchup 500ml"
msgstr "ซอสมะเขือเทศ 500มล."

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_3
msgid "Kitchen"
msgstr "ห้องครัว"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Let your customer book a table anytime with the <font class=\"text-o-"
"color-1\"><strong>Appointment App</strong></font>."
msgstr ""
"ให้ลูกค้าของคุณจองโต๊ะได้ตลอดเวลาด้วย<font class=\"text-o-"
"color-1\"><strong>แอปการนัดหมาย</strong></font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Main Course"
msgstr "จานหลัก"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing Automation</strong></font>, <font"
" class=\"text-o-color-1\"><strong>Email Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Social Media Marketing</strong></font>, and"
" <font class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."
msgstr ""
"จัดการช่องทางการสื่อสารทั้งหมดของคุณในที่เดียวด้วย <font class=\"text-o-"
"color-1\"><strong>การตลาดแบบอัตโนมัติ</strong></font>, <font class=\"text-o-"
"color-1\"><strong>อีเมลมาร์เก็ตติ้ง</strong></font>, <font class=\"text-o-"
"color-1\"><strong>การตลาดผ่านโซเชียล</strong></font> และ <font class=\"text-"
"o-color-1\"><strong>การตลาดผ่าน SMS</strong></font> ของ Odoo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<font class=\"text-o-color-1\"><strong>Accounting "
"App</strong></font>)"
msgstr ""
"จัดการบัญชีของคุณได้ง่ายกว่าที่เคยด้วยการทำงานที่ผสานรวมอย่างสมบูรณ์ (<font "
"class=\"text-o-color-1\"><strong>แอประบบบัญชี</strong></font>)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Manage your booking using the Appointment App."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr ""
"บุฟเฟ่ต์อาหารเมดิเตอร์เรเนียน อาหารเรียกน้ำย่อย อาหารจานหลัก และของหวาน"

#. module: industry_restaurant
#: model:website.menu,name:industry_restaurant.website_menu_14
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu"
msgstr "เมนู"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu One"
msgstr "เมนูที่หนึ่ง"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu Two"
msgstr "เมนูที่สอง"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""
"Odoo ได้รวมระบบเข้ากับแอปอย่างสมบูรณ์ "
"ดาวน์โหลดเพื่อเปลี่ยนให้โทรศัพท์ธรรมดาของคุณกลายเป็นแคชเชียร์ (และอื่นๆ "
"อีกมากมาย)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo มอบความเป็นไปได้ที่ไม่มีที่สิ้นสุดให้กับคุณ เช่น:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"แน่นอนว่านี่เป็นเพียงภาพรวมของฟีเจอร์ที่รวมอยู่ในแพ็คเกจนี้เท่านั้น "
"เพิ่มแอปใหม่ ลบ/แก้ไขข้อมูลสาธิต และทดสอบทุกอย่างได้ตามใจชอบ!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""
"เมื่อมีผลิตภัณฑ์แล้ว คุณสามารถคลิกปุ่ม \"ใบเสร็จ\" "
"อัจฉริยะเพื่อตรวจสอบใบเสร็จและเพิ่มผลิตภัณฑ์ใหม่เหล่านี้ลงในสต็อกของคุณ"

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_2
msgid "Online Payment"
msgstr "ชำระเงินออนไลน์"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the <font "
"class=\"text-o-color-1\"><strong>Events App</strong></font>."
msgstr ""
"จัดระเบียบกิจกรรมของคุณและเชื่อมต่อกับลูกค้าของคุณได้อย่างง่ายดายด้วย <font "
"class=\"text-o-color-1\"><strong>แอปกิจกรรม</strong></font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Our Menus"
msgstr "เมนูของเรา"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Our menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr "วางแผนบริการของคุณได้อย่างง่ายดายโดยใช้แอปการวางแผน"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Reach us"
msgstr "ติดต่อเรา"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""
"กฎการสั่งซื้อซ้ำจะแสดงอยู่ในปุ่มอัจฉริยะที่ด้านบนของผลิตภัณฑ์ของคุณ "
"คุณสามารถดูปริมาณขั้นต่ำและปริมาณสูงสุดได้ "
"ทุกครั้งที่สต็อกที่คาดการณ์ไว้ลดลงต่ำกว่าสต็อกขั้นต่ำ "
"ระบบจะสร้างใบสั่งซื้อเพื่อเติมสินค้าให้ถึงจำนวนสูงสุดโดยอัตโนมัติ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr "การเติมสินค้าเป็นสิ่งจำเป็นแต่ก็ท้าทายสำหรับเจ้าของบาร์ในการจัดระบบ"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_58_product_template
msgid "Sanitizer 250ml"
msgstr "น้ำยาฆ่าเชื้อ 250มล."

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_1
msgid "Service"
msgstr "บริการ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Setting different starts for the same service allows you to give your "
"customer the possibility to book every 15 minutes in this setup."
msgstr ""
"การตั้งค่าการเริ่มต้นที่แตกต่างกันสำหรับบริการเดียวกันจะทำให้ลูกค้าของคุณสามารถจองได้ทุกๆ"
" 15 นาทีในการตั้งค่านี้"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Share a direct link with external services using the \"Share\" option."
msgstr "แชร์ลิงก์โดยตรงกับบริการภายนอกโดยใช้ตัวเลือก \"แชร์\""

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_1
msgid "Sodexo Card Payment"
msgstr "การชำระเงินด้วยบัตร Sodexo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Start receiving orders directly in Odoo or go live with your <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in a few minutes."
msgstr ""
"เริ่มรับคำสั่งซื้อโดยตรงใน Odoo หรือเริ่มใช้งาน <font class=\"text-o-"
"color-1\"><strong>E-shop</strong></font> ของคุณได้ภายในไม่กี่นาที"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Starter"
msgstr "จานเริ่ม"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_1
msgid "Suppliers Orders"
msgstr "คำสั่งซื้อจากซัพพลายเออร์"

#. module: industry_restaurant
#: model:project.project,label_tasks:industry_restaurant.project_project_1
#: model:project.project,label_tasks:industry_restaurant.project_project_2
msgid "Tasks"
msgstr "งาน"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"><strong>Kitchen Display</strong></font> "
"will ensure a great follow-up of every order in your bar and kitchen."
msgstr ""
"<font class=\"text-o-"
"color-1\"><strong>จอแสดงผลห้องครัว</strong></font>จะช่วยติดตามทุกคำสั่งซื้อในบาร์และห้องครัวของคุณได้เป็นอย่างดี"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""
"สต็อกจะได้รับการอัปเดตเมื่อคุณปิดการลงทะเบียนเท่านั้น "
"เรามาทำสิ่งนี้ก่อนที่จะดำเนินการขั้นตอนถัดไปกันเถอะ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr "ทั้งหมดนี้ฟรีในการสมัครสมาชิกปัจจุบันของคุณ อย่าลังเลที่จะลองใช้ดู! 🙃"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"This setup is made to provide availability from Monday to Friday, with 2 "
"services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM)."
msgstr ""
"การตั้งค่านี้จัดทำขึ้นเพื่อให้พร้อมให้บริการตั้งแต่วันจันทร์ถึงวันศุกร์ "
"โดยมีบริการ 2 ครั้งต่อวัน (00.00 ถึง 15.00 น. และ 18.00 ถึง 23.00 น.)"

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_1
msgid "To Do"
msgstr "ที่จะทำ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Use case:"
msgstr "กรณีการใช้งาน:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Inventory App</strong></font>"
" to manage your stock and receive products."
msgstr ""
"ใช้<font class=\"text-o-"
"color-1\"><strong>แอปสินค้าคงคลัง</strong></font>เพื่อจัดการสต็อกสินค้าและรับสินค้า"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Planning App</strong></font> "
"to schedule and share your shifts with your employees."
msgstr ""
"ใช้<font class=\"text-o-"
"color-1\"><strong>แอปการวางแผน</strong></font>เพื่อจัดกำหนดการและแชร์กะงานของคุณกับพนักงานของคุณ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Point of Sale</strong></font>"
" at the desk for your sales. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""
"ใช้ระบบ <font class=\"text-o-color-1\"><strong>POS</strong></font> "
"ที่โต๊ะเพื่อการขายของคุณ นอกจากนี้ คุณยังสามารถดาวน์โหลดแอป Odoo Mobile "
"ลงในโทรศัพท์เครื่องใดก็ได้เพื่อรับออร์เดอร์"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Project App</strong></font> "
"to never miss a reordering or a cleaning task."
msgstr ""
"ใช้<font class=\"text-o-"
"color-1\"><strong>แอปโปรเจ็กต์</strong></font>เพื่อไม่พลาดการสั่งซื้อใหม่หรือการเคลียร์งาน"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Purchase App</strong></font> "
"to reorder your products."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr "สลัดผัก เบอร์เกอร์เนื้อ และไอศกรีมมะม่วง"

#. module: industry_restaurant
#: model_terms:web_tour.tour,rainbow_man_message:industry_restaurant.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ยินดีต้อนรับ! ขอให้สนุกกับการค้นพบ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"คุณต้องการพูดคุยเกี่ยวกับการตั้งค่า Odoo "
"ของคุณกับเราหรือพูดคุยเพิ่มเติมหรือไม่"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"คุณสามารถเข้าถึงทุกแอปที่ติดตั้งในฐานข้อมูล Odoo ของคุณได้บนแดชบอร์ดหลัก"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can discover a pre-configured Appointment Type named \"Book a table\". "
"Use it to schedule services, the duration of each booking, and every "
"communication you want to send to your customers when they place a booking."
msgstr ""
"คุณสามารถค้นหาประเภทการนัดหมายที่กำหนดค่าไว้ล่วงหน้าที่มีชื่อว่า \"จองโต๊ะ\""
" ใช้เพื่อกำหนดเวลาบริการ ระยะเวลาของการจองแต่ละครั้ง "
"และการสื่อสารทุกครั้งที่คุณต้องการส่งถึงลูกค้าเมื่อพวกเขาทำการจอง"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection step of your Point of Sale."
msgstr ""
"คุณสามารถแก้ไขแผนผังชั้นได้ง่ายๆ เพียงคลิก \"แก้ไขแผนผัง\" "
"ในเมนูทางขวาบนในขณะที่อยู่ในขั้นตอนการเลือกตารางของระบบ POS"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""
"ตอนนี้คุณสามารถส่งคำขอเสนอราคาทางอีเมลไปยังผู้ขายของคุณหรือยืนยันด้วยตนเองได้"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory &gt; Products &gt; Blond Beer"
msgstr ""
"คุณจะเห็นว่ามีการกำหนดกฎการสั่งซื้อซ้ำสำหรับผลิตภัณฑ์นี้ "
"หากคุณไปที่สินค้าคงคลัง &gt; ผลิตภัณฑ์ &gt; Blond Beer"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"คุณได้ทำกรณีตัวอย่างการใช้งานสำเร็จแล้ว! "
"มีอีกนับล้านวิธีในการปรับแต่งการตั้งค่า Odoo "
"ให้เหมาะกับความต้องการทางธุรกิจของคุณ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""
"ขณะนี้คุณมีสองขั้นตอนในการแสดงข้อมูลในครัว: หนึ่งขั้นตอนสำหรับห้องครัว/บาร์ "
"และหนึ่งขั้นตอนสำหรับการบริการ คุณสามารถกำหนดค่าขั้นตอนต่างๆ "
"ได้ในเมนูการกำหนดค่าการแสดงข้อมูลในครัว"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fine Dining Restaurant package and check the related box."
msgstr ""
"คุณไม่ได้นำเข้าข้อมูลสาธิตใช่หรือไม่? คุณยังสามารถนำเข้าได้ ไปที่ แอป &gt; "
"อุตสาหกรรม &gt; อัปเกรดแพ็คเกจร้านอาหารสุดหรูของคุณ "
"และทำเครื่องหมายในช่องที่เกี่ยวข้อง"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr ""
"คุณมีสองทางเลือกหลักใน Odoo เพื่อช่วยให้กระบวนการเติมเงินของคุณง่ายขึ้น"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You just installed the Odoo for Fine Dining Restaurant package. By doing so,"
" we have installed many necessary apps to run your restaurant efficiently."
msgstr ""
"คุณเพิ่งติดตั้งแพ็กเกจ Odoo สำหรับร้านอาหาร Fine Dining "
"ดังนั้นเราจะทำการติดตั้งแอปที่จำเป็นจำนวนหนึ่งเพื่อให้ร้านอาหารของคุณดำเนินการไปอย่างมีประสิทธิภาพ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"คุณควรสามารถดำเนินการตามโฟลว์ต่อไปนี้ได้เพื่อให้มีภาพรวมของโฟลว์ต่างๆ "
"ที่คุณสามารถดำเนินการได้อย่างรวดเร็วด้วยแพ็คเกจนี้โดยใช้ Odoo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment_confirmed
msgid "Your table has successfully been booked!"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Your website management has never been so easy. Go to the \"Website App\" to"
" discover a sample website and explore millions of possibilities by clicking"
" on the \"Edit\" button."
msgstr ""
"การจัดการเว็บไซต์ของคุณที่ง่ายที่สุด ไปที่ \"แอปเว็บไซต์\" "
"เพื่อค้นหาเว็บไซต์ตัวอย่างและค้นพบเพิ่มเติมด้วยการคลิกปุ่ม \"แก้ไข\""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "academy"
msgstr "สถาบันการศึกษา"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "and"
msgstr "และ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "documentation"
msgstr "เอกสารกำกับ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "if you need help! <br/>"
msgstr "หากคุณต้องการความช่วยเหลือ! <br/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "request a demo"
msgstr "ขอสาธิตการใช้งาน"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 เว็บไซต์"
