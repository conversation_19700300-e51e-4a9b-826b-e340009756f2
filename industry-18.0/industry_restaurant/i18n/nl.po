# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_restaurant
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:57+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Beef Carpaccio</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Rundscarpaccio</span>&#13;\r\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Cheese Onion Rings</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"
msgstr ""
"&#13;n\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Kaas Uienringen</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Fresh Soup of the Day</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Verse Soep van de Dag</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Farm Friendly Chicken Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Boerderijvriendelijke Kip Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;n\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tuna and Salmon Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tonijn en Zalm Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ", and feel free to"
msgstr "en voel je vrij om"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""
"1. Maak eerst al je werknemers aan in de database. Navigeer hiervoor naar de"
" Werknemers App."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "1. Point of sale"
msgstr "1. Verkooppunt"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""
"1. Je bent klaar om je eerste klanten te verwelkomen. Kies hiervoor een "
"tafel op de plattegrond en selecteer de producten waar ze om gevraagd hebben"
" uit de lijst."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""
"2. Maak de rollen van je werknemers aan op het tabblad \"Werkinformatie\". "
"Sommige zijn al aangemaakt voor deze demo."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Replenishment"
msgstr "2. Aanvulling"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Table booking"
msgstr "2. Tabel boeken"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this"
" one, select the \"Burger Menu Combo\" product. You can choose 2 burger "
"options and 4 different drinks. This \"Combo\" feature will allow you to "
"create a menu with several courses and choices for each."
msgstr ""
"2. Om het nog interessanter te maken, kun je een tweede bestelling maken. "
"Kies hiervoor het product \"Burger Menu Combinatie\". Je kunt kiezen uit 2 "
"hamburgeropties en 4 verschillende drankjes. Met deze \"Combinatie\" functie"
" kun je een menu maken met verschillende gangen en keuzes voor elk."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Click the "
"\"Backend\" menu in the right corner to return to your backend, then switch "
"to the Kitchen Display App."
msgstr ""
"3. Bekijk na het plaatsen van je bestellingen onze Kitchen Display. Klik op "
"het menu \"Backend\" in de rechterhoek om terug te gaan naar je backend en "
"schakel vervolgens over naar de Kitchen Display App."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Plan your services"
msgstr "3. Plan je diensten"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""
"3. Ga terug naar de Planning App. Je ziet een paar diensten klaar om gepland"
" te worden. Selecteer de pijl omlaag naast de knop Publiceren en klik op "
"\"Automatisch plannen\" Je diensten worden automatisch toegewezen op basis "
"van de beschikbaarheid van je medewerkers."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Website and online menu"
msgstr "3. Website en online menu"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"4. Open your preparation screen. Your orders are there. You can now mark "
"them as done either line by line or the entire order by clicking on the top."
msgstr ""
"4. Open je voorbereidingsscherm. Je bestellingen staan daar. Je kunt ze nu "
"regel voor regel of de hele bestelling als klaar markeren door bovenaan te "
"klikken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. Replenishment"
msgstr "4. Aanvulling"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr ""
"4. Je kunt nog steeds elke dienst opnieuw toewijzen om de planning aan te "
"passen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr ""
"5. Als je er tevreden mee bent, stuur je het naar je collega's door op de "
"knop publiceren te drukken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "5. Plan your services"
msgstr "5. Plan je diensten"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<b class=\"o_default_snippet_text\">50,000+ companies</b> run Odoo to grow "
"their businesses."
msgstr ""
"<b class=\"o_default_snippet_text\">meer dan 50.000 bedrijven</b> gebruiken "
"Odoo om hun bedrijf te laten groeien."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"<b>50,000+ bedrijven</b> gebruiken Odoo om hun bedrijf te laten groeien."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<br/>Let the symphony of tastes and textures transport you to distant lands,"
" where every bite is a discovery and every meal a celebration of the senses."
" Indulge in the artistry of the chef as each plate is presented with care "
"and passion, inviting you to experience a world of culinary delights right "
"at your table."
msgstr ""
"<br/>Laat de symfonie van smaken en texturen je meevoeren naar verre landen,"
" waar elke hap een ontdekking is en elke maaltijd een feest voor de "
"zintuigen. Laat je verleiden door het vakmanschap van de chef-kok, die elk "
"bord met zorg en passie presenteert en je uitnodigt om een wereld van "
"culinaire hoogstandjes aan je tafel te ervaren."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "<font class=\"text-white\">Seasoned | Savory | Delicious</font>"
msgstr "<font class=\"text-white\">Gekruid | Hartig | Heerlijk</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"mb-3 fst-normal\">⚠️</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"mb-3 fst-normal\">🎉</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"mb-3 fst-normal\">💡</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🔁</i>"
msgstr "<i class=\"mb-3 fst-normal\">🔁</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"mb-3 fst-normal\">🚀</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment
msgid "<small class=\"text-uppercase text-muted\">Table Booking Details</small>"
msgstr ""
"<small class=\"text-uppercase text-muted\">Details tafelreservering</small>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<span class=\"h3-fs\">Welcome to</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Your Odoo Restaurant</font>"
msgstr ""
"<span class=\"h3-fs\">Welkom bij</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Jouw Odoo Restaurant</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""
"<strong>1. Stroom 1:</strong> Voor de meeste producten is een menselijke "
"beoordeling nodig omdat de resterende hoeveelheid niet automatisch kan "
"worden berekend. Hiervoor wordt een herhalende taak aangemaakt in de Project"
" App. Deze taak verschijnt elke week opnieuw en je kunt hem personaliseren "
"met een checklist om te voorkomen dat je iets vergeet."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""
"<strong>2. Flow 2:</strong> Voor producten die gevolgd kunnen worden, zoals "
"flessenbier en frisdrank, kun je automatische inkopen gebruiken. Om het uit "
"te proberen, ga je naar je verkooppunt en verkoop je een Blond Bier aan een "
"klant."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr "<strong>Grondbeginselen:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Do you want to go further?</strong>"
msgstr "<strong>Wil je verder gaan?</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Use Case:</strong>"
msgstr "<strong>Gebruikscasus:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "A gustative travel"
msgstr "Een smakelijke reis"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Add a menu description."
msgstr "Voeg een menubeschrijving toe."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "All You Can Eat"
msgstr "Alles wat je kunt eten"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""
"Vergeet niet om aan het einde van je dienst je register te sluiten. Hiervoor"
" selecteer je in de Point of Sale App \"Register sluiten\" in het menu "
"rechtsboven. Je kunt dan je contante bedrag instellen en het afsluiten "
"valideren."

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_2
msgid "Bar"
msgstr "Balk"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Basics:"
msgstr "Grondbeginselen:"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_54_product_template
msgid "Beef 1kg"
msgstr "Rundvlees 1kg"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr "Rundercarpaccio, Filet Mignon 8oz en Cheesecake"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_59_product_template
msgid "Blond Beer"
msgstr "Blond Bier"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_header
msgid "Book a Table"
msgstr "Reserveer de Paddock"

#. module: industry_restaurant
#: model:appointment.type,name:industry_restaurant.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Book a table"
msgstr "Reserveer een tafel"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_55_product_template
msgid "Butter"
msgstr "Boter"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr "Hierdoor heb je nog maar 23 bieren in je voorraad."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""
"Je kunt trouwens nog een project met herhalende taken bekijken. Dit is een "
"demo voor schoonmaaktaken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products, an appointment type, a website, and a sample planning."
msgstr ""
"Door de demodata van deze module te uploaden, is je database gevuld met een "
"paar producten, een afspraaktype, een website en een voorbeeldplanning."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_3
msgid "Cash"
msgstr "Kas"

#. module: industry_restaurant
#: model:account.journal,name:industry_restaurant.cash
msgid "Cash (Restaurant)"
msgstr "Contant (Restaurant)"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_56_product_template
msgid "Cheese 250gr"
msgstr "Kaas 250gr"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_2
msgid "Cleaning"
msgstr "Schoonmaak"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Contact us"
msgstr "Contact"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Ontdek meer over Odoo door in onze"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""
"Ontdek de basis van dit pakket en ontdek alle mogelijkheden die Odoo biedt "
"om je ervaring te verbeteren."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Do you want to go further?"
msgstr "Wil je verder gaan?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Do you want to test it and see how it looks on your website? Click on the "
"\"Preview\" button."
msgstr ""
"Wil je het testen en zien hoe het er op je website uitziet? Klik dan op de "
"knop \"Voorbeeld\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""
"Vergeet niet om elke keer dat je een bestelling wijzigt op de knop "
"\"Bestellen\" te drukken. Hierdoor wordt de bestelling doorgestuurd naar de "
"Keuken Display."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_2
msgid "Done"
msgstr "Gereed"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Fine Dining Restaurant"
msgstr "Restaurant voor fijnproevers"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr "Open vanuit het verkooppunt je register op het hoofddashboard."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Get visibility and share your menu with a great <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."
msgstr ""
"Krijg zichtbaarheid en deel je menu met een geweldige <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""
"Ga naar je Inkoop App om eenvoudig Offerteverzoeken of Inkooporders te "
"maken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Geweldige verhalen zijn <b>voor iedereen</b>, zelfs als ze maar <b>voor één "
"persoon</b> zijn geschreven. Als je probeert te schrijven met een breed, "
"algemeen publiek in gedachten, zal je verhaal nep klinken en emotie missen. "
"Niemand zal geïnteresseerd zijn. Schrijf voor één persoon. Als het echt is "
"voor de ene, is het ook echt voor de rest."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Geweldige verhalen hebben een <b>persoonlijkheid</b>. Overweeg om een "
"geweldig verhaal te vertellen dat persoonlijkheid heeft. Het schrijven van "
"een verhaal met persoonlijkheid voor potentiële klanten helpt bij het "
"opbouwen van een relatie. Dit komt tot uiting in kleine eigenaardigheden "
"zoals woordkeuzes of zinsdelen. Schrijf vanuit je standpunt, niet vanuit de "
"ervaring van iemand anders."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Hello there!"
msgstr "Hallo daar!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""
"Als je naar je Inkoop App navigeert, zie je een nieuwe inkooporder die klaar"
" is om verzonden te worden. Je kunt een verpakking kiezen als je product een"
" bestaande verpakking heeft."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""
"Als je ze een Odoo gebruikersaccount wilt toewijzen, kun je een gerelateerde"
" gebruiker selecteren op het tabblad \"HR instellingen\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""
"Als je een praktische rondleiding door deze module wilt, moet je "
"demogegevens importeren en de Demo - Use Case proberen. (In het volgende "
"Kennisartikel)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Join us and make your company a better place."
msgstr "Doe mee en maak je bedrijf een betere plek."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_57_product_template
msgid "Ketchup 500ml"
msgstr "Ketchup 500ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_3
msgid "Kitchen"
msgstr "Keuken"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Let your customer book a table anytime with the <font class=\"text-o-"
"color-1\"><strong>Appointment App</strong></font>."
msgstr ""
"Laat je klant op elk moment een tafel reserveren met de <font class=\"text-"
"o-color-1\"><strong>Afspraak App</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Main Course"
msgstr "Hoofdgerecht"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing Automation</strong></font>, <font"
" class=\"text-o-color-1\"><strong>Email Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Social Media Marketing</strong></font>, and"
" <font class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."
msgstr ""
"Beheer al je communicatiekanalen op één plek met Odoo <font class=\"text-o-"
"color-1\"><strong>Marketingautomatisering</strong></font>, <font "
"class=\"text-o-color-1\"><strong>E-mail Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Sociale Media</strong></font>en <font "
"class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<font class=\"text-o-color-1\"><strong>Accounting "
"App</strong></font>)"
msgstr ""
"Beheer je boekhouding eenvoudiger dan ooit met een volledig geïntegreerde "
"omgeving. (<font class=\"text-o-color-1\"><strong>Boekhoud "
"App</strong></font>)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Manage your booking using the Appointment App."
msgstr "Beheer je boeking met de Afspraken App."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr "Mediterraan buffet met voor-, hoofd- en nagerechten"

#. module: industry_restaurant
#: model:website.menu,name:industry_restaurant.website_menu_14
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu"
msgstr "Menu"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu One"
msgstr "Menu Eén"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu Two"
msgstr "Menu Twee"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""
"Odoo is volledig geïntegreerd in een App. Download hem om van je telefoon "
"een extra kassa te maken (en nog veel meer)."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo biedt je oneindig veel mogelijkheden, zoals :"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Natuurlijk is dit slechts een overzicht van de functies in dit pakket. Voel "
"je vrij om nieuwe apps toe te voegen, demogegevens te verwijderen/wijzigen "
"en alles uit te proberen!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""
"Zodra de producten er zijn, kun je op de slimme knop \"Ontvangst\" klikken "
"om je ontvangst te valideren en deze nieuwe producten toe te voegen aan je "
"voorraad."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_2
msgid "Online Payment"
msgstr "Online betaling"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the <font "
"class=\"text-o-color-1\"><strong>Events App</strong></font>."
msgstr ""
"Organiseer je evenementen en kom gemakkelijk in contact met je klanten met "
"de <font class=\"text-o-color-1\"><strong>Evenementen App</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Our Menus"
msgstr "Onze menu's"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Our menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr "Ons menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr "Plan je diensten eenvoudig met de Planning App."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Reach us"
msgstr "Contacteer ons"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""
"Nabestelregels worden weergegeven in de slimme knoppen bovenaan je product. "
"Je ziet een minimumhoeveelheid en een maximumhoeveelheid. Telkens als de "
"verwachte voorraad onder de minimumvoorraad zakt, wordt automatisch een "
"inkooporder aangemaakt om aan te vullen tot de maximumhoeveelheid."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr ""
"Aanvulling is essentieel maar een uitdaging voor een bareigenaar om te "
"systematiseren."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_58_product_template
msgid "Sanitizer 250ml"
msgstr "Ontsmettingsmiddel 250ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_1
msgid "Service"
msgstr "Dienst"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Setting different starts for the same service allows you to give your "
"customer the possibility to book every 15 minutes in this setup."
msgstr ""
"Door verschillende starts in te stellen voor dezelfde dienst kun je je klant"
" de mogelijkheid geven om elke 15 minuten te boeken in deze opstelling."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Share a direct link with external services using the \"Share\" option."
msgstr "Deel een directe link met externe diensten met de optie \"Delen\"."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_1
msgid "Sodexo Card Payment"
msgstr "Sodexo Kaartbetaling"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Start receiving orders directly in Odoo or go live with your <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in a few minutes."
msgstr ""
"Begin met het ontvangen van orders direct in Odoo of ga live met je <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in een paar minuten."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Starter"
msgstr "Voorgerecht"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_1
msgid "Suppliers Orders"
msgstr "Leveranciers Bestellingen"

#. module: industry_restaurant
#: model:project.project,label_tasks:industry_restaurant.project_project_1
#: model:project.project,label_tasks:industry_restaurant.project_project_2
msgid "Tasks"
msgstr "Taken"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"><strong>Kitchen Display</strong></font> "
"will ensure a great follow-up of every order in your bar and kitchen."
msgstr ""
"De <font class=\"text-o-color-1\"><strong>Keuken Display</strong></font> "
"zorgt voor een geweldige follow-up van elke bestelling in je bar en keuken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""
"De voorraad wordt alleen bijgewerkt als je het register sluit. Laten we dat "
"eerst doen voordat we verder gaan met de volgende stap."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Deze zijn allemaal gratis in je huidige abonnement; voel je vrij om ze te "
"ontdekken! 🙃"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"This setup is made to provide availability from Monday to Friday, with 2 "
"services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM)."
msgstr ""
"Deze opstelling is gemaakt om beschikbaarheid te bieden van maandag tot en "
"met vrijdag, met 2 diensten per dag (12:00 AM tot 15:00 PM en 18:00 PM tot "
"23:00 PM)."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_1
msgid "To Do"
msgstr "Te doen"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Use case:"
msgstr "Gebruikscasus:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Inventory App</strong></font>"
" to manage your stock and receive products."
msgstr ""
"Gebruik de <font class=\"text-o-color-1\"><strong>Voorraad "
"App</strong></font> om je voorraad te beheren en producten te ontvangen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Planning App</strong></font> "
"to schedule and share your shifts with your employees."
msgstr ""
"Gebruik de <font class=\"text-o-color-1\"><strong>Planning "
"App</strong></font> om je diensten te plannen en te delen met je werknemers."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Point of Sale</strong></font>"
" at the desk for your sales. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""
"Gebruik de <font class=\"text-o-"
"color-1\"><strong>Verkooppunt</strong></font> aan de balie voor je verkopen."
" Je kunt ook de Odoo Mobile App downloaden op elke telefoon om bestellingen "
"op te nemen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Project App</strong></font> "
"to never miss a reordering or a cleaning task."
msgstr ""
"Gebruik de <font class=\"text-o-color-1\"><strong>Project "
"App</strong></font> om nooit een nabestelling of opruimtaak te missen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Purchase App</strong></font> "
"to reorder your products."
msgstr ""
"Gebruik de <font class=\"text-o-color-1\"><strong>Inkoop App</strong></font>"
" om je producten opnieuw te bestellen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr "Groentesalade, Rundvleesburger en Mango-ijs"

#. module: industry_restaurant
#: model_terms:web_tour.tour,rainbow_man_message:industry_restaurant.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Welkom! Veel plezier met verkennen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr "Wil je jouw Odoo setup met ons bespreken of nog verder gaan?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Je hebt toegang tot elke geïnstalleerde App in je Odoo database op je "
"hoofddashboard."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can discover a pre-configured Appointment Type named \"Book a table\". "
"Use it to schedule services, the duration of each booking, and every "
"communication you want to send to your customers when they place a booking."
msgstr ""
"Je kunt een vooraf geconfigureerd Afspraaktype ontdekken met de naam \"Boek "
"een tafel\". Gebruik het om diensten in te plannen, de duur van elke boeking"
" en alle communicatie die je naar je klanten wilt sturen als ze een boeking "
"plaatsen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection step of your Point of Sale."
msgstr ""
"Je kunt de plattegrond eenvoudig bewerken door op \"Plattegrond bewerken\" "
"te klikken in het menu rechtsboven in de stap voor het selecteren van tafels"
" in je verkooppunt."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""
"Je kunt nu je Request for Proposal naar je leverancier e-mailen of handmatig"
" bevestigen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory &gt; Products &gt; Blond Beer"
msgstr ""
"Je kunt zien dat er een aanvulregel is geconfigureerd voor dit product als "
"je naar Voorraad &gt; Producten &gt; Blond Bier gaat"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Je hebt de demo use case afgerond! Er zijn miljoenen andere manieren om je "
"Odoo-setup aan te passen aan de behoeften van je bedrijf."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""
"Je hebt op dit moment twee stappen in je keukenweergave: een voor de "
"keuken/bar en een voor de bediening. Je kunt stappen configureren in het "
"configuratiemenu van de keukenweergave."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fine Dining Restaurant package and check the related box."
msgstr ""
"Heb je geen demogegevens geïmporteerd? Je kunt het nog steeds doen. Ga naar "
"Apps &gt; Industries &gt; Upgrade je Fine Dining Restaurant pakket en vink "
"het betreffende vakje aan."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr ""
"Je hebt in Odoo twee mogelijkheden om je aanvulproces te vereenvoudigen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You just installed the Odoo for Fine Dining Restaurant package. By doing so,"
" we have installed many necessary apps to run your restaurant efficiently."
msgstr ""
"Je hebt zojuist het Odoo for Fine Dining Restaurant pakket geïnstalleerd. "
"Daarmee hebben we veel noodzakelijke apps geïnstalleerd om je restaurant "
"efficiënt te runnen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"Je moet de volgende stromen kunnen uitvoeren om een overzicht te krijgen van"
" de verschillende stromen die je met dit pakket snel kunt uitvoeren met "
"Odoo."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment_confirmed
msgid "Your table has successfully been booked!"
msgstr "Je tafel is succesvol geboekt!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Your website management has never been so easy. Go to the \"Website App\" to"
" discover a sample website and explore millions of possibilities by clicking"
" on the \"Edit\" button."
msgstr ""
"Je website beheren was nog nooit zo eenvoudig. Ga naar de \"Website App\" om"
" een voorbeeldwebsite te ontdekken en verken miljoenen mogelijkheden door op"
" de knop \"Bewerken\" te klikken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "academy"
msgstr "academy"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "and"
msgstr "en"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "documentation"
msgstr "documentatie"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "if you need help! <br/>"
msgstr "als je hulp nodig hebt! <br/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "request a demo"
msgstr "demo aanvragen"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "🎓 Website"
msgstr "website"
