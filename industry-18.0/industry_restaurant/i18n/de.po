# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_restaurant
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:57+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\"><PERSON><PERSON></span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Rindercarpaccio</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">10,50 €</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Cheese Onion Rings</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Käse-Zwiebelringe</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">9,00 €</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Fresh Soup of the Day</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Frische Tagessuppe</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">7,50 €</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Farm Friendly Chicken Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Bio-Hähnchenbrust</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">15,50 €</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">15,50 €</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tuna and Salmon Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Thunfisch-Lachs-Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">12,00 €</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ", and feel free to"
msgstr "und"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""
"1. Erstellen Sie zuerst alle Ihre Mitarbeiter in der Datenbank. Navigieren "
"Sie dazu zur Mitarbeiterapp."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "1. Point of sale"
msgstr "1. Kassensystem"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""
"1. Sie sind bereit, Ihre ersten Kunden zu empfangen. Wählen Sie dazu einen "
"Tisch auf dem Raumplan aus und wählen Sie die gewünschten Produkte aus der "
"Liste aus."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""
"2. Erstellen Sie die Rollen Ihrer Mitarbeiter im Reiter "
"„Arbeitsinformationen“. Einige sind bereits für diese Demo erstellt."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Replenishment"
msgstr "2. Nachschub"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Table booking"
msgstr "2. Tischreservierung"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this"
" one, select the \"Burger Menu Combo\" product. You can choose 2 burger "
"options and 4 different drinks. This \"Combo\" feature will allow you to "
"create a menu with several courses and choices for each."
msgstr ""
"2. Um es interessanter zu gestalten, können Sie gerne eine zweite Bestellung"
" aufgeben. Wählen Sie für diese das Produkt „Burger-Menü-Kombination“. Sie "
"können 2 Burger-Optionen und 4 verschiedene Getränke auswählen. Mit dieser "
"„Kombinationsfunktion“ können Sie ein Menü mit mehreren Gängen und jeweils "
"mehreren Auswahlmöglichkeiten erstellen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Click the "
"\"Backend\" menu in the right corner to return to your backend, then switch "
"to the Kitchen Display App."
msgstr ""
"3. Nachdem Sie Ihre Bestellungen aufgegeben haben, sehen Sie sich unserer "
"Küchendisplay an. Klicken Sie auf das Menü „Backend“ in der rechten Ecke, um"
" zu Ihrem Backend zurückzukehren, und wechseln Sie dann zur Küchendisplay-"
"App."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Plan your services"
msgstr "3. Ihre Services planen"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""
"3. Kehren Sie zur Planungsapp zurück. Sie sehen einige Schichten, die zur "
"Planung bereitstehen. Wählen Sie den Abwärtspfeil neben der Schaltfläche "
"„Veröffentlichen“ aus und klicken Sie auf „Automatisch planen“. Ihre "
"Schichten werden automatisch auf der Grundlage der Verfügbarkeit Ihrer "
"Mitarbeiter zugewiesen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Website and online menu"
msgstr "3. Website und Online-Menü"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"4. Open your preparation screen. Your orders are there. You can now mark "
"them as done either line by line or the entire order by clicking on the top."
msgstr ""
"4. Öffnen Sie Ihren Zubereitungsdisplay. Ihre Bestellungen sind dort "
"aufgeführt. Sie können sie nun entweder zeilenweise oder die gesamte "
"Bestellung durch Klicken auf den oberen Rand als erledigt markieren."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. Replenishment"
msgstr "4. Nachschub"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr "4. Sie können jede Schicht neu zuweisen, um die Planung anzupassen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr ""
"5. Wenn Sie mit dem Ergebnis zufrieden sind, senden Sie es an Ihre Kollegen,"
" indem Sie auf die Schaltfläche „Veröffentlichen“ klicken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "5. Plan your services"
msgstr "5. Ihre Services planen"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<b class=\"o_default_snippet_text\">50,000+ companies</b> run Odoo to grow "
"their businesses."
msgstr ""
"<b class=\"o_default_snippet_text\">50.000+ Unternehmen</b> benutzen Odoo, "
"um Ihr Geschäft auszubauen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>50.000+ Unternehmen</b> benutzen Odoo, um Ihr Geschäft auszubauen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<br/>Let the symphony of tastes and textures transport you to distant lands,"
" where every bite is a discovery and every meal a celebration of the senses."
" Indulge in the artistry of the chef as each plate is presented with care "
"and passion, inviting you to experience a world of culinary delights right "
"at your table."
msgstr ""
"<br/>Lassen Sie sich von der Symphonie der Geschmäcker und Texturen in ferne"
" Länder entführen, wo jeder Bissen eine Entdeckung und jede Mahlzeit ein "
"Fest für die Sinne ist. Genießen Sie die Kunstfertigkeit des Küchenchefs, "
"der jeden Teller mit Sorgfalt und Leidenschaft präsentiert und Sie einlädt, "
"eine Welt kulinarischer Köstlichkeiten direkt an Ihrem Tisch zu erleben."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "<font class=\"text-white\">Seasoned | Savory | Delicious</font>"
msgstr "<font class=\"text-white\">Würzig | Herzhaft | Köstlich</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"mb-3 fst-normal\">⚠️</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"mb-3 fst-normal\">🎉</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"mb-3 fst-normal\">💡</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🔁</i>"
msgstr "<i class=\"mb-3 fst-normal\">🔁</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"mb-3 fst-normal\">🚀</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment
msgid "<small class=\"text-uppercase text-muted\">Table Booking Details</small>"
msgstr ""
"<small class=\"text-uppercase text-muted\">Details zur "
"Tischreservierung</small>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<span class=\"h3-fs\">Welcome to</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Your Odoo Restaurant</font>"
msgstr ""
"<span class=\"h3-fs\">Willkommen in</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Ihrem Odoo-Restaurant</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""
"<strong>1. Ablauf 1:</strong> Die meisten Produkte müssen von einem Menschen"
" bewertet werden, da die verbleibende Menge nicht automatisch berechnet "
"werden kann. Für diese Produkte wird in der Projekte-App eine wiederkehrende"
" Aufgabe erstellt. Diese Aufgabe erscheint jede Woche erneut und Sie können "
"sie mit einer Checkliste personalisieren, um nichts zu vergessen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""
"<strong>2. Ablauf 2:</strong> Für Produkte, die nachverfolgt werden können, "
"wie Flaschenbier und Limonaden, können Sie automatische Einkäufe verwenden. "
"Um dies auszuprobieren, navigieren Sie zu Ihrem Kassensystem und verkaufen "
"Sie einem Kunden ein helles Bier."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr "<strong>Grundlagen:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Do you want to go further?</strong>"
msgstr "<strong>Möchten Sie weitergehen?</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Use Case:</strong>"
msgstr "<strong>Anwendungsfall:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "A gustative travel"
msgstr "Eine Reise für den Gaumen"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Add a menu description."
msgstr "Fügen Sie eine Menübeschreibung hinzu."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "All You Can Eat"
msgstr "All You Can Eat"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""
"Denken Sie am Ende Ihres Services daran, Ihr Kassenbuch zu schließen. Wählen"
" Sie dazu in der Kassensystem-App im Menü oben rechts „Kasse schließen“ aus."
" Sie können dann Ihren Kassenbetrag präzisieren und den Abschluss "
"bestätigen."

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_2
msgid "Bar"
msgstr "Balken"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Basics:"
msgstr "Grundlagen:"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_54_product_template
msgid "Beef 1kg"
msgstr "Rind 1 kg"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr "Rindercarpaccio, Filet Mignon 8oz und Käsekuchen"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_59_product_template
msgid "Blond Beer"
msgstr "Helles Bier"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_header
msgid "Book a Table"
msgstr "Einen Tisch reservieren"

#. module: industry_restaurant
#: model:appointment.type,name:industry_restaurant.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Book a table"
msgstr "Einen Tisch reservieren"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_55_product_template
msgid "Butter"
msgstr "Butter"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr "Dadurch haben Sie nur noch 23 Biere auf Lager."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""
"Übrigens können Sie ein weiteres Projekt mit wiederkehrenden Aufgaben sehen."
" Dies ist eine Demo für Reinigungsaufgaben."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products, an appointment type, a website, and a sample planning."
msgstr ""
"Durch das Hochladen der Demodaten dieses Moduls wurde Ihre Datenbank mit "
"einigen Produkten, einer Terminart, einer Website und einer Beispielplanung "
"gefüllt."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_3
msgid "Cash"
msgstr "Bargeld"

#. module: industry_restaurant
#: model:account.journal,name:industry_restaurant.cash
msgid "Cash (Restaurant)"
msgstr "Bar (Restaurant)"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_56_product_template
msgid "Cheese 250gr"
msgstr "Käse 250 g"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_2
msgid "Cleaning"
msgstr "Reinigung"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Contact us"
msgstr "Kontaktieren Sie uns"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Erfahren Sie mehr zu Odoo in unserer"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""
"Entdecken Sie die Grundlagen dieses Pakets und erkunden Sie alle "
"Möglichkeiten, die Odoo bietet, um Ihre Erfahrung zu verbessern."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Do you want to go further?"
msgstr "Möchten Sie noch weiter gehen?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Do you want to test it and see how it looks on your website? Click on the "
"\"Preview\" button."
msgstr ""
"Möchten Sie es testen und sehen, wie es auf Ihrer Website aussieht? Klicken "
"Sie auf die Schaltfläche „Vorschau“."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""
"Vergessen Sie nicht, jedes Mal, wenn Sie eine Bestellung ändern, auf die "
"Schaltfläche „Bestellen“ zu klicken. Dadurch wird die Bestellung an den "
"Küchendisplay weitergeleitet."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_2
msgid "Done"
msgstr "Erledigt"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Fine Dining Restaurant"
msgstr "Feinschmeckerrestaurant"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr "Öffnen Sie Ihre Kasse auf dem Hauptdashboard der Kassensystemapp."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Get visibility and share your menu with a great <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."
msgstr ""
"Erhöhen Sie Ihre Sichtbarkeit und veröffentlichen Sie Ihre Speisekarte auf "
"einer großartigen <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""
"Gehen Sie zu Ihrer Einkaufsapp, um ganz einfach Angebotsanfragen oder "
"Bestellungen zu erstellen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Großartige Geschichten sind <b>für jeden</b>, auch wenn sie nur <b>für eine "
"Person</b> geschrieben werden. Wenn Sie versuchen, mit Blick auf ein "
"breites, allgemeines Publikum zu schreiben, wird Ihre Geschichte unecht "
"klingen und es wird ihr an Emotionen fehlen. Niemand wird interessiert sein."
" Schreiben Sie für eine Person. Wenn es für die eine Person echt ist, ist es"
" auch für den Rest echt."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Großartige Geschichten haben eine <b>Persönlichkeit</b>. Erwägen Sie, eine "
"großartige Geschichte zu erzählen, die Persönlichkeit hat. Das Schreiben "
"einer Geschichte mit Persönlichkeit für potenzielle Kunden hilft dabei, eine"
" Beziehungsverbindung herzustellen. Dies zeigt sich in kleinen Macken wie "
"Wortwahl oder Phrasen. Schreiben Sie aus Ihrer Sicht, nicht aus der "
"Erfahrung eines anderen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Hello there!"
msgstr "Hallo!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""
"Wenn Sie zu Ihrer Einkaufsapp navigieren, sehen Sie eine neue Bestellung, "
"die zum Absenden bereit ist. Sie können eine Verpackung auswählen, wenn für "
"Ihr Produkt bereits eine vorhanden ist."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""
"Wenn Sie ihnen ein Odoo-Benutzerkonto zuweisen möchten, können Sie im Reiter"
" „HR-Einstellungen“ einen entsprechenden Benutzer auswählen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""
"Wenn Sie eine praktische Führung durch dieses Modul durchführen möchten, "
"sollten Sie die Demodaten importieren und den Demoanwendungsfall "
"ausprobieren. (Im nächsten Artikel der Wissensdatenbank)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Join us and make your company a better place."
msgstr ""
"Besuchen Sie uns und machen Sie Ihr eigenes Unternehmen zu einem Besonderen."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_57_product_template
msgid "Ketchup 500ml"
msgstr "Ketchup 500 ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_3
msgid "Kitchen"
msgstr "Küche"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Let your customer book a table anytime with the <font class=\"text-o-"
"color-1\"><strong>Appointment App</strong></font>."
msgstr ""
"Lassen Sie Ihre Kunden jederzeit einen Tisch mit der <font class=\"text-o-"
"color-1\"><strong>Terminapp</strong></font> reservieren."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Main Course"
msgstr "Hauptgericht"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing Automation</strong></font>, <font"
" class=\"text-o-color-1\"><strong>Email Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Social Media Marketing</strong></font>, and"
" <font class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."
msgstr ""
"Verwalten Sie alle Ihre Kommunikationskanäle an einer Stelle mit Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing-Automatisierung</strong></font>, "
"<font class=\"text-o-color-1\"><strong>E-Mail-Marketing</strong></font>, "
"<font class=\"text-o-color-1\"><strong>Social Media "
"Marketing</strong></font> und <font class=\"text-o-color-1\"><strong>SMS-"
"Marketing</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<font class=\"text-o-color-1\"><strong>Accounting "
"App</strong></font>)"
msgstr ""
"Verwalten Sie Ihre Buchhaltung einfach denn je zuvor in einer vollständig "
"integrierten Umgebung. (<font class=\"text-o-"
"color-1\"><strong>Buchhaltungsapp</strong></font>)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Manage your booking using the Appointment App."
msgstr "Verwalten Sie Ihre Buchung mit der Terminapp."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr "Mediterranes Buffet mit Vorspeisen, Hauptgerichten und Desserts"

#. module: industry_restaurant
#: model:website.menu,name:industry_restaurant.website_menu_14
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu"
msgstr "Menü"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu One"
msgstr "Menü eins"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu Two"
msgstr "Menü zwei"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""
"Odoo ist vollständig in eine App integriert. Laden Sie sie herunter, um Ihr "
"Telefon in eine zusätzliche Kasse (und vieles mehr) zu verwandeln."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo bietet Ihnen grenzenlose Möglichkeiten:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Das ist natürlich nur eine Übersicht über die Funktionen, die in diesem "
"Paket enthalten sind. Sie können gerne neue Apps hinzufügen, Demodaten "
"löschen/anpassen und etwas herumspielen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""
"Sobald die Produkte da sind, können Sie auf die Schaltfläche „Wareneingang“ "
"klicken, um Ihren Wareneingang zu validieren und Ihrem Bestand diese neuen "
"Produkte hinzuzufügen."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_2
msgid "Online Payment"
msgstr "Online-Zahlung"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the <font "
"class=\"text-o-color-1\"><strong>Events App</strong></font>."
msgstr ""
"Organisieren Sie Ihre Veranstaltungen und verbinden Sie sich mit Ihren "
"Kunden über die <font class=\"text-o-color-1\"><strong>Veranstaltungen-"
"App</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Our Menus"
msgstr "Unsere Speisekarte"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Our menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr "Unsere Speisekarte&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr "Planen Sie Ihre Services ganz einfach mit der Planungsapp."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Reach us"
msgstr "Kontakt"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""
"Die Nachbestellregeln werden in den intelligenten Schaltflächen oben auf "
"Ihrem Produkt angezeigt. Sie können eine Mindestmenge und eine Höchstmenge "
"sehen. Jedes Mal, wenn der prognostizierte Bestand unter den Mindestbestand "
"fällt, wird automatisch eine Bestellung zum Auffüllen auf die Höchstmenge "
"erstellt."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr ""
"Die Auffüllung ist für einen Barbesitzer unerlässlich, aber schwierig zu "
"systematisieren."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_58_product_template
msgid "Sanitizer 250ml"
msgstr "Desinfektionsmittel 250 ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_1
msgid "Service"
msgstr "Dienstleistung"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Setting different starts for the same service allows you to give your "
"customer the possibility to book every 15 minutes in this setup."
msgstr ""
"Wenn Sie für denselben Service unterschiedliche Startzeiten festlegen, "
"können Ihre Kunden in diesem Fall alle 15 Minuten eine Reservierung tätigen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Share a direct link with external services using the \"Share\" option."
msgstr ""
"Teilen Sie einen direkten Link zu externen Services über die Option "
"„Teilen“."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_1
msgid "Sodexo Card Payment"
msgstr "Sodexo-Kartenzahlung"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Start receiving orders directly in Odoo or go live with your <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in a few minutes."
msgstr ""
"Empfangen Sie Bestellungen direkt in Odoo oder schalten Sie Ihren <font "
"class=\"text-o-color-1\"><strong>E-Shop</strong></font> in wenigen Minuten "
"live."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Starter"
msgstr "Starter"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_1
msgid "Suppliers Orders"
msgstr "Lieferantenaufträge"

#. module: industry_restaurant
#: model:project.project,label_tasks:industry_restaurant.project_project_1
#: model:project.project,label_tasks:industry_restaurant.project_project_2
msgid "Tasks"
msgstr "Aufgaben"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"><strong>Kitchen Display</strong></font> "
"will ensure a great follow-up of every order in your bar and kitchen."
msgstr ""
"Das <font class=\"text-o-color-1\"><strong>Küchendisplay</strong></font> "
"sorgt für eine hervorragende Nachverfolgung jeder Bestellung in Ihrer Bar "
"und Küche."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""
"Der Lagerbestand wird nur aktualisiert, wenn Sie Ihre Kasse schließen. "
"Lassen Sie uns dies tun, bevor wir mit dem nächsten Schritt fortfahren."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Sie sind alle in Ihrem aktuellen Abonnement enthalten. Entdecken Sie sie! 🙃"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"This setup is made to provide availability from Monday to Friday, with 2 "
"services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM)."
msgstr ""
"Diese Einrichtung ist so konzipiert, dass von Montag bis Freitag zwei "
"Reservierungen pro Tag (12:00 bis 15:00 Uhr und 18:00 bis 23:00 Uhr) "
"verfügbar sind."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_1
msgid "To Do"
msgstr "To-do"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Use case:"
msgstr "Anwendungsfall:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Inventory App</strong></font>"
" to manage your stock and receive products."
msgstr ""
"Verwenden Sie die <font class=\"text-o-"
"color-1\"><strong>Lagerapp</strong></font>, um Ihren Bestand zu verwalten "
"und Produkte zu erhalten."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Planning App</strong></font> "
"to schedule and share your shifts with your employees."
msgstr ""
"Verwenden Sie die <font class=\"text-o-"
"color-1\"><strong>Planungsapp</strong></font>, um Ihre Schichten zu planen "
"und mit Ihren Mitarbeitern zu teilen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Point of Sale</strong></font>"
" at the desk for your sales. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""
"Verwenden Sie das <font class=\"text-o-"
"color-1\"><strong>Kassensystem</strong></font> am Schalter für Ihre "
"Verkäufe. Sie können auch die Odoo Mobile App auf jedes Telefon "
"herunterladen, um Bestellungen aufzunehmen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Project App</strong></font> "
"to never miss a reordering or a cleaning task."
msgstr ""
"Verwenden Sie die <font class=\"text-o-color-1\"><strong>Projekte-"
"App</strong></font>, um keine Nachbestell- oder Reinigungsaufgabe mehr zu "
"verpassen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Purchase App</strong></font> "
"to reorder your products."
msgstr ""
"Verwenden Sie die <font class=\"text-o-"
"color-1\"><strong>Einkaufsapp</strong></font>, um Ihre Produkte erneut zu "
"bestellen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr "Gemüsesalat, Beef Burger und Mango-Eiscreme"

#. module: industry_restaurant
#: model_terms:web_tour.tour,rainbow_man_message:industry_restaurant.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Willkommen! Viel Spaß beim Erkunden!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Möchten Sie Ihre Odoo-Einrichtung mit uns besprechen oder noch weiter gehen?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Sie können auf jede installierte App in Ihrer Odoo-Datenbank über Ihr "
"Hauptdashboard zugreifen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can discover a pre-configured Appointment Type named \"Book a table\". "
"Use it to schedule services, the duration of each booking, and every "
"communication you want to send to your customers when they place a booking."
msgstr ""
"Sie können eine vorkonfigurierte Terminart namens „Einen Tisch reservieren“ "
"sehen. Verwenden Sie sie, um Dienstleistungen, die Dauer jeder Buchung und "
"jede Mitteilung, die Sie Ihren Kunden bei einer Buchung senden möchten, zu "
"planen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection step of your Point of Sale."
msgstr ""
"Sie können den Raumplan ganz einfach bearbeiten, indem Sie im Schritt zur "
"Tischauswahl in Ihrem Kassensystem oben rechts auf „Plan bearbeiten“ "
"klicken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""
"Sie können Ihre Angebotsanfrage jetzt per E-Mail an Ihren Lieferanten senden"
" oder sie manuell bestätigen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory &gt; Products &gt; Blond Beer"
msgstr ""
"Sie können sehen, dass für dieses Produkt eine Nachbestellregel konfiguriert"
" ist, wenn Sie zu Lager &gt; Produkte &gt; Helles Bier gehen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Sie haben diesen Demo-Anwendungsfall abgeschlossen! Es gibt Millionen "
"anderer Möglichkeiten, Ihre Odoo-Einrichtung an Ihre geschäftlichen "
"Anforderungen anzupassen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""
"Sie haben derzeit zwei Schritte auf Ihrem Küchendisplay: einen für die "
"Küche/Bar und einen für den Service. Sie können die Schritte im "
"Konfigurationsmenü des Küchendisplays konfigurieren."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fine Dining Restaurant package and check the related box."
msgstr ""
"Sie haben keine Demo-Daten importiert? Das können Sie immer noch tun. Gehen "
"Sie zu Apps &gt; Branchen &gt; Aktualisieren Sie Ihr Paket für "
"Feinschmeckerrestaurants und aktivieren Sie das entsprechende "
"Kontrollkästchen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr ""
"In Odoo haben Sie zwei grundlegende Möglichkeiten, Ihren Auffüllprozess zu "
"vereinfachen."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You just installed the Odoo for Fine Dining Restaurant package. By doing so,"
" we have installed many necessary apps to run your restaurant efficiently."
msgstr ""
"Sie haben gerade das Odoo-Paket für Feinschmeckerrestaurants installiert. "
"Dadurch haben wir eine Reihe von notwendigen Apps installiert, um Ihr "
"Feinschmeckerrestaurant effizient zu betreiben."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"Sie sollten in der Lage sein, die folgenden Abläufe auszuführen, um einen "
"Überblick über verschiedene Abläufe zu erhalten, die Sie mit diesem Paket "
"mithilfe von Odoo schnell ausführen können."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment_confirmed
msgid "Your table has successfully been booked!"
msgstr "Ihr Tisch wurde erfolgreich reserviert!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Your website management has never been so easy. Go to the \"Website App\" to"
" discover a sample website and explore millions of possibilities by clicking"
" on the \"Edit\" button."
msgstr ""
"Noch nie war die Verwaltung Ihrer Website so einfach. Gehen Sie zur "
"„Website-App“, um eine Beispielwebsite zu entdecken und Millionen von "
"Möglichkeiten zu erkunden, indem Sie auf die Schaltfläche „Bearbeiten“ "
"klicken."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "academy"
msgstr "Akademie"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "and"
msgstr "und"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "documentation"
msgstr "Dokumentation"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "if you need help! <br/>"
msgstr ", wenn Sie Hilfe benötigen!<br/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "request a demo"
msgstr "bitten Sie um eine Demo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 Website"
