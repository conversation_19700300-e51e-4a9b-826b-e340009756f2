# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_restaurant
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:57+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\"><PERSON><PERSON></span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Thịt bò carpaccio</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Cheese Onion Rings</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Bánh hành tây phô mai</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Fresh Soup of the Day</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Súp tươi của ngày do đầu bếp lựa chọn</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Farm Friendly Chicken Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Gà sốt supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Thịt thăn bò 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tuna and Salmon Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"
msgstr ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Burger cá ngừ và cá hồi</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ", and feel free to"
msgstr "của chúng tôi, và đừng ngần ngại"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""
"1. Đầu tiên, hãy tạo tất cả nhân viên của bạn trong cơ sở dữ liệu. Điều "
"hướng đến Ứng dụng Nhân viên để thực hiện việc này."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "1. Point of sale"
msgstr "1. Điểm bán hàng"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""
"1. Bạn đã sẵn sàng chào đón những khách hàng đầu tiên. Để làm như vậy, hãy "
"chọn một bàn trên sơ đồ tầng và chọn sản phẩm họ gọi từ danh sách."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""
"2. Tạo vai trò của nhân viên trong tab \"Thông tin công việc\". Một số vai "
"trò đã được tạo cho bản demo này."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Replenishment"
msgstr "2. Bổ sung hàng"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Table booking"
msgstr "2. Đặt bàn"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this"
" one, select the \"Burger Menu Combo\" product. You can choose 2 burger "
"options and 4 different drinks. This \"Combo\" feature will allow you to "
"create a menu with several courses and choices for each."
msgstr ""
"2. Để thú vị hơn, bạn có thể thoải mái tạo đơn hàng thứ hai. Đối với đơn "
"hàng này, hãy chọn sản phẩm \"Menu combo burger\". Bạn có thể chọn 2 lựa "
"chọn burger và 4 loại đồ uống khác nhau. Tính năng \"Combo\" này sẽ cho phép"
" bạn tạo một thực đơn với nhiều món ăn và lựa chọn cho mỗi món."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Click the "
"\"Backend\" menu in the right corner to return to your backend, then switch "
"to the Kitchen Display App."
msgstr ""
"3. Sau khi gọi món, hãy xem Màn hình nhà bếp. Nhấp vào menu \"Backend\" ở "
"góc bên phải để quay lại backend, sau đó chuyển sang Ứng dụng Màn hình nhà "
"bếp."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Plan your services"
msgstr "3. Lập kế hoạch dịch vụ của bạn"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""
"3. Quay lại ứng dụng Kế hoạch. Bạn có thể thấy một số ca làm đã sẵn sàng để "
"lập kế hoạch. Chọn mũi tên trỏ xuống bên cạnh nút đăng và nhấp vào \"Lập kế "
"hoạch tự động\". Ca làm của bạn sẽ được tự động phân công dựa trên khả năng "
"sẵn sàng của nhân viên."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Website and online menu"
msgstr "3. Trang web và menu online"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"4. Open your preparation screen. Your orders are there. You can now mark "
"them as done either line by line or the entire order by clicking on the top."
msgstr ""
"4. Mở màn hình khâu chuẩn bị của bạn. Đơn hàng của bạn đã có ở đó. Bây giờ "
"bạn có thể đánh dấu chúng là đã hoàn thành theo từng dòng hoặc toàn bộ đơn "
"hàng bằng cách nhấp vào trên cùng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. Replenishment"
msgstr "4. Bổ sung hàng"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr ""
"4. Bạn vẫn có thể phân công lại bất kỳ ca làm nào để điều chỉnh kế hoạch."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr "5. Khi đã hài lòng, hãy gửi cho đồng nghiệp bằng cách nhấn nút đăng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "5. Plan your services"
msgstr "5. Lập kế hoạch dịch vụ của bạn"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<b class=\"o_default_snippet_text\">50,000+ companies</b> run Odoo to grow "
"their businesses."
msgstr ""
"<b class=\"o_default_snippet_text\">50,000+ công ty</b> sử dụng Odoo để phát"
" triển hoạt động kinh doanh của họ."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"<b>50,000+ công ty</b> sử dụng Odoo để phát triển hoạt động kinh doanh của "
"họ."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<br/>Let the symphony of tastes and textures transport you to distant lands,"
" where every bite is a discovery and every meal a celebration of the senses."
" Indulge in the artistry of the chef as each plate is presented with care "
"and passion, inviting you to experience a world of culinary delights right "
"at your table."
msgstr ""
"<br/>Hãy để bản giao hưởng của hương vị và kết cấu đưa bạn đến những vùng "
"đất xa xôi, nơi mỗi miếng là một khám phá và mỗi bữa ăn là một lễ kỷ niệm "
"của các giác quan. Hãy đắm mình vào tác phẩm nghệ thuật của đầu bếp khi mỗi "
"đĩa thức ăn được trình bày với sự chăm chút và đam mê, mời bạn trải nghiệm "
"thế giới ẩm thực tuyệt vời ngay tại bàn ăn của mình."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "<font class=\"text-white\">Seasoned | Savory | Delicious</font>"
msgstr "<font class=\"text-white\">Đậm đà | Hấp dẫn | Thơm ngon</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">⚠️</i>"
msgstr "<i class=\"mb-3 fst-normal\">⚠️</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🎉</i>"
msgstr "<i class=\"mb-3 fst-normal\">🎉</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">💡</i>"
msgstr "<i class=\"mb-3 fst-normal\">💡</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🔁</i>"
msgstr "<i class=\"mb-3 fst-normal\">🔁</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🚀</i>"
msgstr "<i class=\"mb-3 fst-normal\">🚀</i>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment
msgid "<small class=\"text-uppercase text-muted\">Table Booking Details</small>"
msgstr "<small class=\"text-uppercase text-muted\">Thông tin đặt bàn</small>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<span class=\"h3-fs\">Welcome to</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Your Odoo Restaurant</font>"
msgstr ""
"<span class=\"h3-fs\">Chào mừng bạn đến với</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Odoo Nhà hàng</font>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""
"<strong>1. Chu trình 1:</strong> Hầu hết các sản phẩm cần đánh giá của con "
"người vì hệ thống không thể tự động tính toán số lượng còn lại. Đối với "
"những sản phẩm đó, một Nhiệm vụ định kỳ được tạo trong Ứng dụng Dự án. Nhiệm"
" vụ này xuất hiện lại hàng tuần và bạn có thể cá nhân hóa nó bằng danh sách "
"để tránh quên bất kỳ thứ gì."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""
"<strong>2. Chu trình 2:</strong> Đối với các sản phẩm có thể theo dõi, chẳng"
" hạn như bia và nước ngọt đóng chai, bạn có thể sử dụng mua hàng tự động. Để"
" dùng thử, hãy đi đến Điểm bán hàng và bán Bia blond cho khách hàng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr "<strong>Cơ sở:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Do you want to go further?</strong>"
msgstr "<strong>Bạn có muốn tiến xa hơn không?</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Use Case:</strong>"
msgstr "<strong>Trường hợp vận dụng:</strong>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "A gustative travel"
msgstr "Một chuyến du lịch trải nghiệm"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Add a menu description."
msgstr "Thêm mô tả menu."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "All You Can Eat"
msgstr "Mọi thứ bạn có thể ăn"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""
"Khi kết thúc dịch vụ, hãy nhớ đóng máy tính tiền của bạn. Để thực hiện, "
"trong Ứng dụng Điểm bán hàng, hãy chọn \"Đóng máy tính tiền\" ở menu trên "
"cùng bên phải. Sau đó, bạn có thể nhập chính xác số tiền mặt và xác nhận "
"việc đóng máy tính tiền."

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_2
msgid "Bar"
msgstr "Quán bar"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Basics:"
msgstr "Cơ sở:"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_54_product_template
msgid "Beef 1kg"
msgstr "Thịt bò 1kg"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr "Thịt bò Carpaccio, 8oz thịt thăn bò và bánh phô mai"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_59_product_template
msgid "Blond Beer"
msgstr "Bia blond"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_header
msgid "Book a Table"
msgstr "Đặt bàn"

#. module: industry_restaurant
#: model:appointment.type,name:industry_restaurant.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Book a table"
msgstr "Book a table"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_55_product_template
msgid "Butter"
msgstr "Bơ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr "Khi làm như vậy, bạn chỉ còn lại 23 chai bia trong kho."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""
"Nhân đây, bạn có thể thấy một dự án khác có các nhiệm vụ định kỳ. Đây là bản"
" demo cho các nhiệm vụ dọn dẹp."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products, an appointment type, a website, and a sample planning."
msgstr ""
"Bằng cách tải lên dữ liệu demo của phân hệ này, cơ sở dữ liệu của bạn đã có "
"sẵn một số sản phẩm, một loại lịch hẹn, một trang web, và một kế hoạch mẫu."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_3
msgid "Cash"
msgstr "Tiền mặt"

#. module: industry_restaurant
#: model:account.journal,name:industry_restaurant.cash
msgid "Cash (Restaurant)"
msgstr "Tiền mặt (Nhà hàng)"

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_56_product_template
msgid "Cheese 250gr"
msgstr "Phô mai 250gr"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_2
msgid "Cleaning"
msgstr "Dọn dẹp"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Contact us"
msgstr "Liên hệ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr "Khám phá thêm về Odoo bằng cách tìm hiểu"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""
"Khám phá những tính năng cơ bản của gói này và tìm hiểu tất cả khả năng mà "
"Odoo cung cấp để cải thiện trải nghiệm của bạn."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Do you want to go further?"
msgstr "Bạn có muốn tiến xa hơn không?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Do you want to test it and see how it looks on your website? Click on the "
"\"Preview\" button."
msgstr ""
"Bạn có muốn kiểm thử và xem nó trông như thế nào trên trang web của bạn "
"không? Nhấp vào nút \"Xem trước\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""
"Đừng quên nhấn nút \"Đặt hàng\" mỗi khi bạn thay đổi đơn hàng. Thao tác này "
"sẽ chuyển tiếp đơn hàng đến Màn hình nhà bếp."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_2
msgid "Done"
msgstr "Hoàn tất"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Fine Dining Restaurant"
msgstr "Nhà Hàng"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr "Từ Điểm bán hàng, mở máy tính trên trang chủ."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Get visibility and share your menu with a great <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."
msgstr ""
"Tăng khả năng hiển thị và chia sẻ thực đơn của bạn với một <font "
"class=\"text-o-color-1\"><strong>Trang web</strong></font> tuyệt vời."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""
"Truy cập ứng dụng Mua hàng để dễ dàng tạo Yêu cầu báo giá hoặc Đơn mua hàng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Những câu chuyện hay là <b>dành cho mọi người</b> ngay cả khi được sáng tác "
"<b>cho một người duy nhất</b>. Nếu bạn cố viết cho đối tượng khán giả quá "
"rộng, câu chuyện của bạn sẽ trở nên sáo rỗng, thiếu cảm xúc và không thu hút"
" người đọc. Hãy sáng tác cho một người. Câu chuyện chân thực với một người "
"chắc chắn sẽ chân thực với những người khác. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Những câu chuyện hay đều mang <b>bản sắc riêng</b>. Hãy cân nhắc kể những "
"câu chuyện có cá tính như vậy. Sáng tác những câu chuyện có màu sắc riêng "
"cho khách hàng tương lai sẽ giúp tạo mối quan hệ sâu sắc. Điều này được thể "
"hiện qua cả những chi tiết nhỏ nhất như lựa chọn từ ngữ. Bạn cũng nên viết "
"từ quan điểm của mình chứ không phải từ trải nghiệm của người khác. "

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Hello there!"
msgstr "Xin chào!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""
"Nếu bạn đi đến Ứng dụng Mua hàng, bạn có thể thấy một Đơn mua hàng mới đã "
"sẵn sàng để gửi đi. Bạn có thể chọn kiện hàng nếu sản phẩm của bạn đã có "
"kiện hàng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""
"Nếu bạn muốn chỉ định cho họ một tài khoản người dùng Odoo, bạn có thể chọn "
"người dùng liên quan trong tab \"Cài đặt HR\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""
"Nếu bạn muốn dùng thử tour được hướng dẫn thực tế về phân hệ này, bạn nên "
"nhập dữ liệu demo và thử Demo - Trường hợp vận dụng. (Trong Bài viết kiến "
"​​thức sau đây)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Join us and make your company a better place."
msgstr "Gia nhập với chúng tôi và tạo cho công ty bạn một vị thế tốt hơn."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_57_product_template
msgid "Ketchup 500ml"
msgstr "Tương cà 500ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_3
msgid "Kitchen"
msgstr "Nhà bếp"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Let your customer book a table anytime with the <font class=\"text-o-"
"color-1\"><strong>Appointment App</strong></font>."
msgstr ""
"Cho phép khách hàng đặt bàn bất cứ lúc nào bằng <font class=\"text-o-"
"color-1\"><strong>ứng dụng Lịch hẹn</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Main Course"
msgstr "Khóa học chính"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing Automation</strong></font>, <font"
" class=\"text-o-color-1\"><strong>Email Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Social Media Marketing</strong></font>, and"
" <font class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."
msgstr ""
"Quản lý tất cả kênh truyền thông của bạn ở một nơi với Odoo <font "
"class=\"text-o-color-1\"><strong>Tự động hoá Marketing</strong></font>, "
"<font class=\"text-o-color-1\"><strong>Marketing qua Email</strong></font>, "
"<font class=\"text-o-color-1\"><strong>Marketing qua MXH</strong></font> và "
"<font class=\"text-o-color-1\"><strong>Marketing qua SMS</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<font class=\"text-o-color-1\"><strong>Accounting "
"App</strong></font>)"
msgstr ""
"Quản lý nghiệp vụ kế toán của bạn dễ dàng hơn bao giờ hết với môi trường "
"tích hợp hoàn toàn. (<font class=\"text-o-color-1\"><strong>Ứng dụng Kế "
"toán</strong></font>)"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Manage your booking using the Appointment App."
msgstr "Quản lý đặt lịch bằng ứng dụng Lịch hẹn."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr "Buffet Địa Trung Hải gồm món khai vị, món chính và món tráng miệng"

#. module: industry_restaurant
#: model:website.menu,name:industry_restaurant.website_menu_14
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu"
msgstr "Menu"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu One"
msgstr "Menu một"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu Two"
msgstr "Menu hai"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""
"Odoo được tích hợp hoàn toàn vào một ứng dụng. Tải xuống để biến chiếc điện "
"thoại của bạn thành một quầy thu ngân bổ sung (và hơn thế nữa)."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr "Odoo cung cấp cho bạn những khả năng vô hạn, chẳng hạn như:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""
"Tất nhiên, đây chỉ là thông tin tổng quan về những tính năng có trong gói "
"này. Hãy thoải mái thêm ứng dụng mới, xóa/sửa đổi dữ liệu demo và kiểm thử "
"các chức năng!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""
"Khi sản phẩm đã có, bạn có thể nhấp vào nút \"Phiếu nhập kho\" để xác nhận "
"phiếu nhập kho và thêm những sản phẩm mới này vào kho của mình."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_2
msgid "Online Payment"
msgstr "Thanh toán online"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the <font "
"class=\"text-o-color-1\"><strong>Events App</strong></font>."
msgstr ""
"Tổ chức các sự kiện và kết nối với khách hàng của bạn một cách dễ dàng với "
"<font class=\"text-o-color-1\"><strong>Ứng dụng Sự kiện</strong></font>."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Our Menus"
msgstr "Menu của chúng tôi"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Our menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr "Menu của chúng tôi&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr "Lên kế hoạch dịch vụ của bạn một cách dễ dàng bằng Ứng dụng Kế hoạch."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Reach us"
msgstr "Liên hệ với chúng tôi"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""
"Quy tắc tái đặt hàng được hiển thị trong các nút thông minh ở trên cùng của "
"sản phẩm. Bạn có thể thấy số lượng tối thiểu và tối đa. Mỗi lần dự báo tồn "
"kho giảm xuống dưới mức tồn kho tối thiểu, hệ thống sẽ tự động tạo đơn mua "
"hàng để bổ sung đến số lượng tối đa."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr ""
"Bổ sung hàng là một hoạt động cần thiết nhưng đồng thời cũng là thách thức "
"đối với chủ quán bar trong việc hệ thống hóa."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_58_product_template
msgid "Sanitizer 250ml"
msgstr "Chất khử trùng 250ml"

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_1
msgid "Service"
msgstr "Dịch vụ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Setting different starts for the same service allows you to give your "
"customer the possibility to book every 15 minutes in this setup."
msgstr ""
"Việc thiết lập thời gian bắt đầu khác nhau cho cùng một dịch vụ cho phép "
"khách hàng có thể đặt lịch sau mỗi 15 phút trong thiết lập này."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Share a direct link with external services using the \"Share\" option."
msgstr ""
"Chia sẻ liên kết trực tiếp với các dịch vụ bên ngoài bằng tùy chọn \"Chia "
"sẻ\"."

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_1
msgid "Sodexo Card Payment"
msgstr "Thanh toán bằng thẻ Sodexo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Start receiving orders directly in Odoo or go live with your <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in a few minutes."
msgstr ""
"Bắt đầu nhận đơn hàng trực tiếp trên Odoo hoặc đưa <font class=\"text-o-"
"color-1\"><strong>cửa hàng điện tử</strong></font> của bạn vào hoạt động chỉ"
" sau vài phút."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Starter"
msgstr "Khai vị"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_1
msgid "Suppliers Orders"
msgstr "Đơn hàng nhà cung cấp"

#. module: industry_restaurant
#: model:project.project,label_tasks:industry_restaurant.project_project_1
#: model:project.project,label_tasks:industry_restaurant.project_project_2
msgid "Tasks"
msgstr "Nhiệm vụ"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"><strong>Kitchen Display</strong></font> "
"will ensure a great follow-up of every order in your bar and kitchen."
msgstr ""
"<font class=\"text-o-color-1\"><strong>Màn hình nhà bếp</strong></font> sẽ "
"đảm bảo follow-up chính xác mọi đơn hàng trong quầy bar và bếp của bạn."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""
"Tồn kho chỉ được cập nhật khi bạn đóng máy tính tiền. Hãy thực hiện việc này"
" trước khi tiếp tục bước tiếp theo."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""
"Tất cả đều miễn phí trong gói đăng ký hiện tại của bạn; hãy thoải mái khám "
"phá! 🙃"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"This setup is made to provide availability from Monday to Friday, with 2 "
"services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM)."
msgstr ""
"Thiết lập này được thực hiện để cung cấp dịch vụ từ Thứ Hai đến Thứ Sáu, 2 "
"đợt mỗi ngày (12:00 sáng đến 15:00 chiều và 18:00 tối đến 23:00 tối)."

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_1
msgid "To Do"
msgstr "Việc cần làm"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Use case:"
msgstr "Trường hợp vận dụng:"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Inventory App</strong></font>"
" to manage your stock and receive products."
msgstr ""
"Sử dụng <font class=\"text-o-color-1\"><strong>ứng dụng Tồn "
"kho</strong></font> để quản lý tồn kho và nhận sản phẩm của bạn."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Planning App</strong></font> "
"to schedule and share your shifts with your employees."
msgstr ""
"Sử dụng <font class=\"text-o-color-1\"><strong>ứng dụng Kế "
"hoạch</strong></font> để lên lịch ca làm và chia sẻ với nhân viên."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Point of Sale</strong></font>"
" at the desk for your sales. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""
"Sử dụng <font class=\"text-o-color-1\"><strong>Điểm bán hàng "
"</strong></font>để bán hàng của bạn tại quầy. Bạn cũng có thể tải ứng dụng "
"di động Odoo xuống bất kỳ điện thoại nào để nhận đơn đặt hàng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Project App</strong></font> "
"to never miss a reordering or a cleaning task."
msgstr ""
"Sử dụng <font class=\"text-o-color-1\"><strong>ứng dụng Dự "
"án</strong></font> để không bỏ lỡ bất kỳ nhiệm vụ tái đặt hàng hoặc dọn dẹp "
"nào."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Purchase App</strong></font> "
"to reorder your products."
msgstr ""
"Sử dụng <font class=\"text-o-color-1\"><strong>ứng dụng Mua "
"hàng</strong></font> để tái đặt hàng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr "Salad rau củ, bánh mì kẹp thịt bò và kem xoài"

#. module: industry_restaurant
#: model_terms:web_tour.tour,rainbow_man_message:industry_restaurant.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Chào mừng bạn! Chúc bạn một chuyến khám phá bổ ích!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""
"Bạn có muốn thảo luận với chúng tôi về thiết lập Odoo của mình hoặc tìm hiểu"
" cụ thể hơn không?"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""
"Bạn có thể truy cập mọi ứng dụng đã cài đặt trong cơ sở dữ liệu Odoo trên "
"trang chủ của mình."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can discover a pre-configured Appointment Type named \"Book a table\". "
"Use it to schedule services, the duration of each booking, and every "
"communication you want to send to your customers when they place a booking."
msgstr ""
"Bạn có thể khám phá Loại cuộc hẹn được cấu hình sẵn có tên là \"Đặt bàn\". "
"Sử dụng loại này để lên lịch dịch vụ, thời lượng của mỗi lần đặt chỗ và mọi "
"thông tin liên lạc bạn muốn gửi đến khách hàng khi họ đặt chỗ."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection step of your Point of Sale."
msgstr ""
"Bạn có thể dễ dàng chỉnh sửa sơ đồ tầng bằng cách nhấp vào \"Chỉnh sửa sơ "
"đồ\" ở menu trên cùng bên phải khi đang ở phần lựa chọn bàn của Điểm bán "
"hàng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""
"Bây giờ bạn có thể gửi Yêu cầu báo giá tới nhà cung cấp qua email hoặc xác "
"nhận thủ công."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory &gt; Products &gt; Blond Beer"
msgstr ""
"Bạn có thể thấy có một quy tắc tái sắp xếp được cấu hình cho sản phẩm này "
"nếu bạn đi đến Tồn kho &gt; Sản phẩm &gt; Bia blond"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""
"Vậy là bạn đã hoàn thành trường hợp vận dụng bản demo đó! Có hàng triệu cách"
" khác để điều chỉnh thiết lập Odoo cho phù hợp với nhu cầu kinh doanh của "
"bạn."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""
"Hiện tại bạn có hai bước trong Màn hình nhà bếp: một cho bếp/quầy bar và một"
" cho dịch vụ. Bạn có thể cấu hình các bước trong menu cấu hình Màn hình nhà "
"bếp."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fine Dining Restaurant package and check the related box."
msgstr ""
"Bạn chưa nhập dữ liệu demo? Đừng lo! Bạn vẫn có thể làm được bằng cách đi "
"đến Ứng dụng &gt; Ngành &gt; Nâng cấp gói Nhà hàng của bạn và đánh dấu vào ô"
" liên quan."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr ""
"Bạn có hai khả năng chính trong Odoo để đơn giản hóa quy trình bổ sung hàng."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You just installed the Odoo for Fine Dining Restaurant package. By doing so,"
" we have installed many necessary apps to run your restaurant efficiently."
msgstr ""
"Bạn vừa cài đặt gói Odoo dành cho Nhà hàng. Do đó, chúng tôi đã cài đặt một "
"loạt các ứng dụng cần thiết để vận hành hiệu quả nhà hàng của bạn."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""
"Bạn sẽ có thể dùng thử các chu trình sau để có cái nhìn tổng quan về nhiều "
"chu trình kinh doanh mà bạn có thể thực hiện nhanh chóng bằng gói này khi sử"
" dụng Odoo."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment_confirmed
msgid "Your table has successfully been booked!"
msgstr "Bạn đã đặt bàn thành công!"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Your website management has never been so easy. Go to the \"Website App\" to"
" discover a sample website and explore millions of possibilities by clicking"
" on the \"Edit\" button."
msgstr ""
"Quản lý trang web của bạn chưa bao giờ dễ dàng đến thế. Hãy vào \"Ứng dụng "
"Trang web\" để khám phá trang web mẫu và khám phá hàng triệu khả năng bằng "
"cách nhấp vào nút \"Chỉnh sửa\"."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "academy"
msgstr "khoá học"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "and"
msgstr "và"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "documentation"
msgstr "tài liệu"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "if you need help! <br/>"
msgstr "nếu bạn cần trợ giúp!<br/>"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "request a demo"
msgstr "đặt lịch demo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "🎓 Website"
msgstr "🎓 Trang web"
