# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_restaurant
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 05:57+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Beef Carpaccio</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$10.50</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Cheese Onion Rings</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$9.00</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Chefs Fresh Soup of the Day</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$7.50</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Farm Friendly Chicken Supreme</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Filet Mignon 8oz</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$15.50</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"&#13;\n"
"                        <span class=\"s_product_catalog_dish_name s_product_catalog_dish_dot_leaders\">Tuna and Salmon Burger</span>&#13;\n"
"                        <span class=\"s_product_catalog_dish_price ms-auto ps-2\">$12.00</span>&#13;"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ", and feel free to"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. First, create all your employees in the database. Navigate to the "
"Employee App to do so."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "1. Point of sale"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"1. You are ready to welcome your first customers. To do so, select a table "
"on the floor map and select the products they asked for from the list."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. Create your employees' roles under the \"Work Information\" tab. Some are"
" already created for this demo."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Replenishment"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "2. Table booking"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"2. To make it more interesting, feel free to create a second order. For this"
" one, select the \"Burger Menu Combo\" product. You can choose 2 burger "
"options and 4 different drinks. This \"Combo\" feature will allow you to "
"create a menu with several courses and choices for each."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. After placing your orders, look at our Kitchen Display. Click the "
"\"Backend\" menu in the right corner to return to your backend, then switch "
"to the Kitchen Display App."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Plan your services"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"3. Return to the Planning App. You can see a few shifts ready to be planned."
" Select the down arrow next to the publish button and click \"Auto Plan.\" "
"Your shifts will be automatically assigned based on your employees' "
"availability."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "3. Website and online menu"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"4. Open your preparation screen. Your orders are there. You can now mark "
"them as done either line by line or the entire order by clicking on the top."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. Replenishment"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "4. You can still reassign any shift to adapt the planning."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"5. Once you are happy with it, send it to your co-workers by smashing the "
"publish button."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "5. Plan your services"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<b class=\"o_default_snippet_text\">50,000+ companies</b> run Odoo to grow "
"their businesses."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr ""
"<b>70 000+ společností</b> provozuje Odoo, aby rozvíjeli své podnikání."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<br/>Let the symphony of tastes and textures transport you to distant lands,"
" where every bite is a discovery and every meal a celebration of the senses."
" Indulge in the artistry of the chef as each plate is presented with care "
"and passion, inviting you to experience a world of culinary delights right "
"at your table."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "<font class=\"text-white\">Seasoned | Savory | Delicious</font>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">⚠️</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🎉</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">💡</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🔁</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<i class=\"mb-3 fst-normal\">🚀</i>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment
msgid "<small class=\"text-uppercase text-muted\">Table Booking Details</small>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid ""
"<span class=\"h3-fs\">Welcome to</span>\n"
"                                    <br/>\n"
"                                    <font class=\"text-o-color-3\">Your Odoo Restaurant</font>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>1. Flow 1:</strong> Most products need a human assessment as the "
"remaining quantity cannot be automatically computed. For those, a Recurring "
"Task is created in the Project App. This task reappears every week, and you "
"can personalize it with a checklist to avoid forgetting anything."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"<strong>2. Flow 2:</strong> For products that can be tracked, such as "
"bottled beers and sodas, you can use automated purchases. To try it, "
"navigate to your Point of Sale and sell a Blond Beer to a customer."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Basics:</strong>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Do you want to go further?</strong>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "<strong>Use Case:</strong>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "A gustative travel"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Add a menu description."
msgstr "Přidejte popis nabídky."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "All You Can Eat"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"At the end of your service, remember to close your register. To do so, in "
"the Point of Sale App, select \"Close Register\" in the top-right menu. You "
"can then precise your cash amount and validate the closing."
msgstr ""

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_2
msgid "Bar"
msgstr "Bar"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Basics:"
msgstr ""

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_54_product_template
msgid "Beef 1kg"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr ""

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_59_product_template
msgid "Blond Beer"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_header
msgid "Book a Table"
msgstr ""

#. module: industry_restaurant
#: model:appointment.type,name:industry_restaurant.appointment_type_1
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Book a table"
msgstr ""

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_55_product_template
msgid "Butter"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "By doing so, you have only 23 beers left in your stock."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By the way, you can see another project with recurring tasks. This one is a "
"demo for cleaning tasks."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"By uploading this module's demo data, your database has been filled with a "
"few products, an appointment type, a website, and a sample planning."
msgstr ""

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_3
msgid "Cash"
msgstr "Hotovost"

#. module: industry_restaurant
#: model:account.journal,name:industry_restaurant.cash
msgid "Cash (Restaurant)"
msgstr ""

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_56_product_template
msgid "Cheese 250gr"
msgstr ""

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_2
msgid "Cleaning"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Contact us"
msgstr "Kontaktujte nás"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Discover more about Odoo by diving into our"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Discover the basics of this package and explore all the possibilities Odoo "
"offers to improve your experience."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Do you want to go further?"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Do you want to test it and see how it looks on your website? Click on the "
"\"Preview\" button."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Don't forget to hit the \"Order\" button each time you change an order. This"
" will forward the order to the Kitchen Display."
msgstr ""

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_2
msgid "Done"
msgstr "Hotovo"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Fine Dining Restaurant"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "From the Point of Sale, open your register on the main dashboard."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Get visibility and share your menu with a great <font class=\"text-o-"
"color-1\"><strong>Website</strong></font>."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Go to your Purchase App to easily create Requests for Proposal or Purchase "
"Orders."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"Skvělé příběhy jsou <b>pro každého</b>, i když jsou psány pouze <b>pro jednu"
" osobu</b>. Pokud se pokusíte psát s ohledem na široké publikum, bude váš "
"příběh znít falešně a bez emocí. Nikdo nebude mít zájem. Pište pro jednu "
"osobu. Pokud je to pravé pro jednoho, je to pravé pro ostatní."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"Velké příběhy mají <b>osobnost</b>. Zvažte vyprávění skvělého příběhu, který"
" poskytuje osobnost. Psaní příběhu s osobností pro potenciální klienty "
"pomůže při navazování vztahů. To se zobrazuje v malých vtípcích, jako jsou "
"volby slov nebo fráze. Pište ze svého úhlu pohledu, ne ze zkušeností někoho "
"jiného."

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Hello there!"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you navigate to your Purchase App, you can see a new Purchase Order that "
"is ready to be sent. You can choose a packaging if your product has an "
"existing one."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to assign them an Odoo user account, you can select a related "
"user in the \"HR Settings\" tab."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"If you want to execute a practical guided tour of this module, you should "
"Import demo data and try the Demo - Use Case. (In the next Knowledge "
"Article)"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Join us and make your company a better place."
msgstr "Přidejte se k nám a udělejte vaší firmu lepším místem."

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_57_product_template
msgid "Ketchup 500ml"
msgstr ""

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_3
msgid "Kitchen"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Let your customer book a table anytime with the <font class=\"text-o-"
"color-1\"><strong>Appointment App</strong></font>."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Main Course"
msgstr "Hlavní chod"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage all your communication channels in one place with Odoo <font "
"class=\"text-o-color-1\"><strong>Marketing Automation</strong></font>, <font"
" class=\"text-o-color-1\"><strong>Email Marketing</strong></font>, <font "
"class=\"text-o-color-1\"><strong>Social Media Marketing</strong></font>, and"
" <font class=\"text-o-color-1\"><strong>SMS Marketing</strong></font>."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Manage your accounting easier than ever with a completely integrated "
"environment. (<font class=\"text-o-color-1\"><strong>Accounting "
"App</strong></font>)"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Manage your booking using the Appointment App."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr ""

#. module: industry_restaurant
#: model:website.menu,name:industry_restaurant.website_menu_14
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu"
msgstr "Menu"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu One"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Menu Two"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Odoo is entirely integrated into an App. Download it to turn your phone into"
" an additional cashier (and much more)."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Odoo offers you infinite possibilities, such as :"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Of course, this is just an overview of the features included in this "
"package. Feel free to add new apps, delete/modify demo data, and test "
"everything around!"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Once the products are there, you can click the \"Receipt\" smart button to "
"validate your receipt and add these new products to your stock."
msgstr ""

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_2
msgid "Online Payment"
msgstr "Platba přes internet"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Organise your events and connect with your customers easily with the <font "
"class=\"text-o-color-1\"><strong>Events App</strong></font>."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Our Menus"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.homepage
msgid "Our menu&amp;nbsp;<span class=\"fa fa-angle-right ms-2\"/>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Plan your services easily using the Planning App."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Reach us"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Reordering rules are displayed in the smart buttons on top of your product. "
"You can see a minimum quantity, and a maximum. Each time the forecast stock "
"lowers beneath the minimum stock, it automatically creates a purchase order "
"to replenish to the maximum quantity."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Replenishment is essential but challenging for a bar owner to systematize."
msgstr ""

#. module: industry_restaurant
#: model:product.template,name:industry_restaurant.product_product_58_product_template
msgid "Sanitizer 250ml"
msgstr ""

#. module: industry_restaurant
#: model:planning.role,name:industry_restaurant.planning_role_1
msgid "Service"
msgstr "Služba"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Setting different starts for the same service allows you to give your "
"customer the possibility to book every 15 minutes in this setup."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Share a direct link with external services using the \"Share\" option."
msgstr ""

#. module: industry_restaurant
#: model:pos.payment.method,name:industry_restaurant.pos_payment_method_1
msgid "Sodexo Card Payment"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Start receiving orders directly in Odoo or go live with your <font "
"class=\"text-o-color-1\"><strong>E-shop</strong></font> in a few minutes."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Starter"
msgstr "Startér"

#. module: industry_restaurant
#: model:project.project,name:industry_restaurant.project_project_1
msgid "Suppliers Orders"
msgstr ""

#. module: industry_restaurant
#: model:project.project,label_tasks:industry_restaurant.project_project_1
#: model:project.project,label_tasks:industry_restaurant.project_project_2
msgid "Tasks"
msgstr "Úkoly"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The <font class=\"text-o-color-1\"><strong>Kitchen Display</strong></font> "
"will ensure a great follow-up of every order in your bar and kitchen."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"The stock is only updated when you close your register. Let's do it before "
"continuing to the next step."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"These all go free in your current subscription; feel free to explore! 🙃"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"This setup is made to provide availability from Monday to Friday, with 2 "
"services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM)."
msgstr ""

#. module: industry_restaurant
#: model:project.task.type,name:industry_restaurant.project_task_type_1
msgid "To Do"
msgstr "K udělání"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Use case:"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Inventory App</strong></font>"
" to manage your stock and receive products."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Planning App</strong></font> "
"to schedule and share your shifts with your employees."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Point of Sale</strong></font>"
" at the desk for your sales. You can also download the Odoo Mobile App on "
"any phone to take orders."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Project App</strong></font> "
"to never miss a reordering or a cleaning task."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Use the <font class=\"text-o-color-1\"><strong>Purchase App</strong></font> "
"to reorder your products."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.website_page_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr ""

#. module: industry_restaurant
#: model_terms:web_tour.tour,rainbow_man_message:industry_restaurant.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "Would you like to discuss your Odoo setup with us or go even further?"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can access every installed App in your Odoo database on your main "
"dashboard."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can discover a pre-configured Appointment Type named \"Book a table\". "
"Use it to schedule services, the duration of each booking, and every "
"communication you want to send to your customers when they place a booking."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can easily edit the floor map by clicking \"Edit plan\" in the top-right"
" menu when on the table selection step of your Point of Sale."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can now email your Request for Proposal to your vendor or confirm it "
"manually."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You can see that there is a reordering rule configured for this product if "
"you go to Inventory &gt; Products &gt; Blond Beer"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You completed that demo use case! There are millions of other ways to adapt "
"your Odoo setup to fit your business needs."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You currently have two steps in your Kitchen Display: one for the "
"kitchen/bar and one for the service. You can configure steps in the kitchen "
"display configuration menu."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You didn't import demo data? You can still do it. Go to Apps &gt; Industries"
" &gt; Upgrade your Fine Dining Restaurant package and check the related box."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You have two main possibilities in Odoo to ease your replenishment process."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You just installed the Odoo for Fine Dining Restaurant package. By doing so,"
" we have installed many necessary apps to run your restaurant efficiently."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"You should be able to execute the following flows to have an overview of "
"various flows you can quickly execute with this package using Odoo."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.industry_restaurant_appointment_confirmed
msgid "Your table has successfully been booked!"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid ""
"Your website management has never been so easy. Go to the \"Website App\" to"
" discover a sample website and explore millions of possibilities by clicking"
" on the \"Edit\" button."
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "academy"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "and"
msgstr "and"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "documentation"
msgstr "dokumentace"

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "if you need help! <br/>"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "request a demo"
msgstr ""

#. module: industry_restaurant
#: model_terms:ir.ui.view,arch_db:industry_restaurant.welcome_article_body
msgid "🎓 Website"
msgstr ""
