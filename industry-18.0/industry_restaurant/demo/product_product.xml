<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
        <record id="product_product_54" model="product.product">
            <field name="seller_ids" eval="[(6, 0, [ref('product_supplierinfo_1')])]"/>
        </record>
        <record id="product_product_55" model="product.product">
            <field name="seller_ids" eval="[(6, 0, [ref('product_supplierinfo_2')])]"/>
        </record>    
        <record id="product_product_56" model="product.product">
            <field name="seller_ids" eval="[(6, 0, [ref('product_supplierinfo_3')])]"/>
        </record>
        <record id="product_product_57" model="product.product">
            <field name="seller_ids" eval="[(6, 0, [ref('product_supplierinfo_4')])]"/>
        </record>
        <record id="product_product_58" model="product.product">
            <field name="seller_ids" eval="[(6, 0, [ref('product_supplierinfo_5')])]"/>
        </record>
        <record id="product_product_59" model="product.product">
            <field name="pos_categ_ids" model="pos.category" eval="[(6, 0, obj().search([('name', 'ilike', 'Drinks')]).ids)]"/>
            <field name="nbr_reordering_rules">1</field>
            <field name="show_forecasted_qty_status_button" eval="True"/>
            <field name="reordering_max_qty">48.0</field>
            <field name="reordering_min_qty">24.0</field>
            <field name="qty_available">24.0</field>
            <field name="route_ids" eval="[(6, 0, [ref('purchase_stock.route_warehouse0_buy')])]"/>
            <field name="seller_ids" eval="[(6, 0, [ref('product_supplierinfo_6')])]"/>
        </record>
</odoo>
