<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="planning_role_1" model="planning.role">
        <field name="name">Service</field>
    </record>
    <record id="planning_role_2" model="planning.role">
        <field name="name">Bar</field>
    </record>
    <record id="planning_role_3" model="planning.role">
        <field name="name">Kitchen</field>
    </record>
    <record id="hr_employee_4" model="hr.employee">
        <field name="planning_role_ids" eval="[(4, ref('planning_role_3'))]"/>
    </record>
    <record id="hr_employee_2" model="hr.employee">
        <field name="planning_role_ids" eval="[(4, ref('planning_role_1'))]"/>
    </record>
</odoo>
