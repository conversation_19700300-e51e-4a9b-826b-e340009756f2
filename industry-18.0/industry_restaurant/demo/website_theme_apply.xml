<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="ir.ui.view" name="write">
        <value model="ir.ui.view" eval="obj().env['website'].with_context(website_id=obj().env.ref('website.default_website').id).viewref('website.homepage').id"/>
        <value model="ir.ui.view" eval="{'arch': obj().env.ref('industry_restaurant.homepage').arch}"/>
    </function>
    <function model="web_editor.assets" name="make_scss_customization">
        <value eval="'/website/static/src/scss/options/colors/user_color_palette.scss'"/>
        <value eval="{'o-color-1': '#861D3D', 'o-color-2': '#003163','o-theme-font': 'Poppins', 'menu': 2, 'footer': 2, 'copyright': 2}"/>
    </function>
</odoo>
