<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="planning_slot_1" model="planning.slot">
        <field name="role_id" ref="planning_role_2"/>
        <field name="start_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=11)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="end_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=14)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="state">published</field>
        <field name="repeat" eval="True"/>
        <field name="repeat_interval">1</field>
        <field name="repeat_unit">day</field>
    </record>

    <record id="planning_slot_2" model="planning.slot">
        <field name="role_id" ref="planning_role_2"/>
        <field name="start_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=18)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="end_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=23)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="state">published</field>
        <field name="repeat" eval="True"/>
        <field name="repeat_interval">1</field>
        <field name="repeat_unit">day</field>
    </record>

    <record id="planning_slot_3" model="planning.slot">
        <field name="role_id" ref="planning_role_1"/>
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('industry_restaurant.hr_employee_2').resource_id.id"/>
        <field name="start_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=18)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="end_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=22)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="state">published</field>
        <field name="repeat" eval="True"/>
        <field name="repeat_interval">1</field>
        <field name="repeat_unit">day</field>
    </record>
    <record id="planning_slot_4" model="planning.slot">
        <field name="role_id" ref="planning_role_3"/>
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('industry_restaurant.hr_employee_4').resource_id.id"/>
        <field name="start_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=10)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="end_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=14)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="state">published</field>
        <field name="repeat" eval="True"/>
        <field name="repeat_interval">1</field>
        <field name="repeat_unit">day</field>
    </record>
    <record id="planning_slot_5" model="planning.slot">
        <field name="role_id" ref="planning_role_3"/>
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('industry_restaurant.hr_employee_4').resource_id.id"/>
        <field name="start_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=17)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="end_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=21)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="state">published</field>
        <field name="repeat" eval="True"/>
        <field name="repeat_interval">1</field>
        <field name="repeat_unit">day</field>
    </record>
    <record id="planning_slot_6" model="planning.slot">
        <field name="role_id" ref="planning_role_1"/>
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('industry_restaurant.hr_employee_2').resource_id.id"/>
        <field name="start_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=11)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="end_datetime" model="hr.employee" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.today().date() + relativedelta(weekday=0, hour=15)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="state">published</field>
        <field name="repeat" eval="True"/>
        <field name="repeat_interval">1</field>
        <field name="repeat_unit">day</field>
    </record>
</odoo>
