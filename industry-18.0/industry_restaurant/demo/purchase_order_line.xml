<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="purchase_order_line_1" model="purchase.order.line">
        <field name="product_id" ref="product_product_56"/>
        <field name="product_qty">6.0</field>
        <field name="order_id" ref="purchase_order_1"/>
    </record>
    <record id="purchase_order_line_2" model="purchase.order.line">
        <field name="product_id" ref="product_product_57"/>
        <field name="product_qty">5.0</field>
        <field name="order_id" ref="purchase_order_1"/>
    </record>
    <record id="purchase_order_line_3" model="purchase.order.line">
        <field name="product_id" ref="product_product_54"/>
        <field name="product_qty">10.0</field>
        <field name="order_id" ref="purchase_order_2"/>
    </record>
    <record id="purchase_order_line_4" model="purchase.order.line">
        <field name="product_id" ref="product_product_55"/>
        <field name="product_qty">10.0</field>
        <field name="order_id" ref="purchase_order_2"/>
    </record>
    <record id="purchase_order_line_5" model="purchase.order.line">
        <field name="product_id" ref="product_product_58"/>
        <field name="product_qty">3.0</field>
        <field name="order_id" ref="purchase_order_2"/>
    </record>
    <record id="purchase_order_line_6" model="purchase.order.line">
        <field name="product_id" ref="product_product_59"/>
        <field name="product_qty">24</field>
        <field name="order_id" ref="purchase_order_2"/>
    </record>
</odoo>
