<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">

    <record id="website_image_01" model="ir.attachment">
        <field name="name">website.library_image_02</field>
        <field name="key">industry_restaurant.library_image_02</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_1.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_02" model="ir.attachment">
        <field name="name">website.library_image_03</field>
        <field name="key">industry_restaurant.library_image_03</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_2.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_03" model="ir.attachment">
        <field name="name">website.library_image_05</field>
        <field name="key">industry_restaurant.library_image_05</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_3.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_04" model="ir.attachment">
        <field name="name">website.library_image_08</field>
        <field name="key">industry_restaurant.library_image_08</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_4.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_05" model="ir.attachment">
        <field name="name">website.library_image_10</field>
        <field name="key">industry_restaurant.library_image_10</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_5.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_06" model="ir.attachment">
        <field name="name">website.library_image_13</field>
        <field name="key">industry_restaurant.library_image_13</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_6.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_07" model="ir.attachment">
        <field name="name">website.library_image_14</field>
        <field name="key">industry_restaurant.library_image_14</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_7.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_08" model="ir.attachment">
        <field name="name">website.library_image_16</field>
        <field name="key">industry_restaurant.library_image_16</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_8.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_09" model="ir.attachment">
        <field name="name">website.s_banner_default_image</field>
        <field name="key">industry_restaurant.s_banner_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_9.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="website_image_10" model="ir.attachment">
        <field name="name">website.s_banner_default_image</field>
        <field name="key">industry_restaurant.s_banner_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="industry_restaurant/static/src/binary/ir_attachment/website_10.jpg" />
        <field name="public" eval="True"/>
    </record>
</odoo>
