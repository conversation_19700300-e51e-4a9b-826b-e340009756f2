<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_task_10" model="project.task">
        <field name="name">Order Ecodys</field>
        <field name="project_id" ref="project_project_1"/>
        <field name="stage_id" ref="project_task_type_1"/>
        <field name="display_in_project" eval="True"/>
        <field name="recurring_task" eval="True"/>
    </record>
    <record id="project_task_1" model="project.task">
        <field name="name">Order VDS</field>
        <field name="project_id" ref="project_project_1"/>
        <field name="stage_id" ref="project_task_type_1"/>
        <field name="display_in_project" eval="True"/>
        <field name="recurring_task" eval="True"/>
    </record>
    <record id="project_task_2" model="project.task">
        <field name="name">Floors</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_1"/>
        <field name="display_in_project" eval="True"/>
        <field name="recurring_task" eval="True"/>
    </record>
    <record id="project_task_3" model="project.task">
        <field name="name">Oven</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_1"/>
        <field name="display_in_project" eval="True"/>
        <field name="recurring_task" eval="True"/>
    </record>
    <record id="project_task_4" model="project.task">
        <field name="name">Dishwasher</field>
        <field name="project_id" ref="project_project_2"/>
        <field name="stage_id" ref="project_task_type_1"/>
        <field name="display_in_project" eval="True"/>
        <field name="recurring_task" eval="True"/>
    </record>
</odoo>
