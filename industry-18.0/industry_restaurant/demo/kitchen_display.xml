<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="preparation_display_main_restaurant_bar" model="pos_preparation_display.display">
        <field name="name">Bar Display</field>
        <field name="pos_config_ids" eval="[(6, 0, [ref('pos_restaurant.pos_config_main_restaurant')])]" />
        <field name="category_ids" model="pos.category" search="[('name', 'ilike', 'Drinks')]"/>
    </record>
</odoo>
