<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="website_menu_14" model="website.menu">
        <field name="url">/menu</field>
        <field name="parent_id" model="website.menu" eval="obj().search([('url', '=', '/default-main-menu'), ('website_id', '=', obj().env.ref('website.default_website').id)], limit=1).id"/>
        <field name="website_id" ref="website.default_website"/>
        <field name="name">Menu</field>
        <field name="sequence">11</field>
        <field name="page_id" ref="website_page_1"/>
    </record>
</odoo>
