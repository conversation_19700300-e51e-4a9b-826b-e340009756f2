<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="pos_payment_method_2" model="pos.payment.method">
        <field name="name">Online Payment</field>
        <field name="is_online_payment" eval="1"/>
        <field name="online_payment_provider_ids" eval="[(6, 0, [ref('payment.payment_provider_demo')])]"/>
    </record>
    <record id="cash" model="account.journal">
        <field name="name">Cash (Restaurant)</field>
        <field name="type">cash</field>
    </record>
    <record id="pos_payment_method_3" model="pos.payment.method">
        <field name="name">Cash</field>
        <field name="journal_id" ref="cash"/>
    </record>
</odoo>
