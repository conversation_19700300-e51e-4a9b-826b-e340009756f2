<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_supplierinfo_1" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_12"/>
        <field name="price">24.0</field>
        <field name="product_tmpl_id" ref="product_product_54"/>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_2" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_12"/>
        <field name="price">2.0</field>
        <field name="product_tmpl_id" ref="product_product_55"/>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_3" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_11"/>
        <field name="price">6.5</field>
        <field name="product_tmpl_id" ref="product_product_56"/>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_4" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_11"/>
        <field name="price">2.5</field>
        <field name="product_tmpl_id" ref="product_product_57"/>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_5" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_12"/>
        <field name="price">3</field>
        <field name="product_tmpl_id" ref="product_product_58"/>
        <field name="min_qty">1.0</field>
    </record>
    <record id="product_supplierinfo_6" model="product.supplierinfo">
        <field name="min_qty">24</field>
        <field name="price">1.5</field>
        <field name="product_tmpl_id" ref="product_product_59"/>
        <field name="partner_id" ref="res_partner_12"/>
    </record>
</odoo>
