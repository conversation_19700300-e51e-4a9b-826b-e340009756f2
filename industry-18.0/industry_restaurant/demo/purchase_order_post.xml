<?xml version='1.0' encoding='UTF-8'?>
<odoo>

    <function model="purchase.order" name="button_confirm">
        <value eval="[
            ref('purchase_order_1'),
            ref('purchase_order_2'),
            ]"
        />
    </function>
    <function name="button_validate" model="stock.picking">
        <value model="stock.picking" eval="
            (
                obj().env.ref('industry_restaurant.purchase_order_1') +
                obj().env.ref('industry_restaurant.purchase_order_2')
            ).picking_ids.ids
        "/>
    </function>
</odoo>
