<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="appointment_type_1" model="appointment.type">
        <field name="name">Book a table</field>
        <field name="assign_method">time_auto_assign</field>
        <field name="category">recurring</field>
        <field name="image_1920" type="base64" file="industry_restaurant/static/src/binary/appointment_resource/table1.jpg"/>
        <field name="appointment_tz" model="res.users" eval="obj().env.ref('base.user_admin').tz or 'Europe/Brussels'"/>
        <field name="schedule_based_on">resources</field>
        <field name="min_cancellation_hours">1</field>
        <field name="min_schedule_hours">1</field>
        <field name="location_id" ref="base.main_partner"/>
        <field name="appointment_duration">2</field>
        <field name="avatars_display">show</field>
        <field name="slot_ids" eval="[
        (0, 0, {'weekday': '1', 'start_hour': 12.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '1', 'start_hour': 12.25, 'end_hour': 15.0}),
        (0, 0, {'weekday': '1', 'start_hour': 12.5, 'end_hour': 15.0}),
        (0, 0, {'weekday': '1', 'start_hour': 12.75, 'end_hour': 15.0}),
        (0, 0, {'weekday': '1', 'start_hour': 13.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '1', 'start_hour': 18.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '1', 'start_hour': 18.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '1', 'start_hour': 18.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '1', 'start_hour': 18.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '1', 'start_hour': 19.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '1', 'start_hour': 19.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '1', 'start_hour': 19.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '1', 'start_hour': 19.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 12.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '2', 'start_hour': 12.25, 'end_hour': 15.0}),
        (0, 0, {'weekday': '2', 'start_hour': 12.5, 'end_hour': 15.0}),
        (0, 0, {'weekday': '2', 'start_hour': 12.75, 'end_hour': 15.0}),
        (0, 0, {'weekday': '2', 'start_hour': 13.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '2', 'start_hour': 18.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 18.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 18.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 18.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 19.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 19.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 19.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '2', 'start_hour': 19.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 12.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '3', 'start_hour': 12.25, 'end_hour': 15.0}),
        (0, 0, {'weekday': '3', 'start_hour': 12.5, 'end_hour': 15.0}),
        (0, 0, {'weekday': '3', 'start_hour': 12.75, 'end_hour': 15.0}),
        (0, 0, {'weekday': '3', 'start_hour': 13.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '3', 'start_hour': 18.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 18.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 18.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 18.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 19.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 19.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 19.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '3', 'start_hour': 19.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 12.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '4', 'start_hour': 12.25, 'end_hour': 15.0}),
        (0, 0, {'weekday': '4', 'start_hour': 12.5, 'end_hour': 15.0}),
        (0, 0, {'weekday': '4', 'start_hour': 12.75, 'end_hour': 15.0}),
        (0, 0, {'weekday': '4', 'start_hour': 13.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '4', 'start_hour': 18.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 18.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 18.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 18.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 19.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 19.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 19.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '4', 'start_hour': 19.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 12.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '5', 'start_hour': 12.25, 'end_hour': 15.0}),
        (0, 0, {'weekday': '5', 'start_hour': 12.5, 'end_hour': 15.0}),
        (0, 0, {'weekday': '5', 'start_hour': 12.75, 'end_hour': 15.0}),
        (0, 0, {'weekday': '5', 'start_hour': 13.0, 'end_hour': 15.0}),
        (0, 0, {'weekday': '5', 'start_hour': 18.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 18.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 18.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 18.75, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 19.0, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 19.25, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 19.5, 'end_hour': 23.0}),
        (0, 0, {'weekday': '5', 'start_hour': 19.75, 'end_hour': 23.0}),
    ]"/>    
    </record>
</odoo>
