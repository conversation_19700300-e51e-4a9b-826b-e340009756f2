<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_category_5" model="product.category">
        <field name="name">Dessert</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_6" model="product.category">
        <field name="name">Drinks</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_7" model="product.category">
        <field name="name">Hot Coffees</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_8" model="product.category">
        <field name="name">Meal</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_9" model="product.category">
        <field name="name">Raw Material</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_10" model="product.category">
        <field name="name">Tea</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_11" model="product.category">
        <field name="name">Water</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_14" model="product.category">
        <field name="name">Combo Offers</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
    <record id="product_category_16" model="product.category">
        <field name="name">Purchase</field>
        <field name="parent_id" ref="product.product_category_all"/>
    </record>
</odoo>
