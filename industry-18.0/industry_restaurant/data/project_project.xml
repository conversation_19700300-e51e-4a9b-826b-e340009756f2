<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_project_1" model="project.project">
        <field name="name">Suppliers Orders</field>
        <field name="stage_id" ref="project.project_project_stage_0"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_1'), ref('project_task_type_2')])]"/>
    </record>
    <record id="project_project_2" model="project.project">
        <field name="name">Cleaning</field>
        <field name="stage_id" ref="project.project_project_stage_0"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_1'), ref('project_task_type_2')])]"/>
    </record>
</odoo>
