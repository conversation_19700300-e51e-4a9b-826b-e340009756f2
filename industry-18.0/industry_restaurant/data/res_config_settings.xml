<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="res_config_settings_enable_restaurant" model="res.config.settings">
        <field name="qr_code" eval="1"/>
        <field name="group_product_variant" eval="1"/>
        <field name="group_project_recurring_tasks" eval="1"/>
        <field name="pos_use_pricelist" eval="1"/>
        <field name="group_stock_packaging" eval="1"/>
        <field name="pos_self_ordering_mode" eval="'mobile'"/>
    </record>

    <function name="execute" model="res.config.settings">
        <value eval="[ref('res_config_settings_enable_restaurant')]"/>
    </function>
</odoo>
