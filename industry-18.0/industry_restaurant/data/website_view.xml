<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="industry_restaurant_appointment" inherit_id="appointment.appointment_meeting_details">
        <xpath expr="//small[hasclass('text-uppercase')]" position="replace">
            <small class="text-uppercase text-muted">Table Booking Details</small>
        </xpath>
    </template>
    <template id="industry_restaurant_appointment_confirmed" inherit_id="appointment.appointment_validated">
        <xpath expr="//div[hasclass('o_appointment')]//span" position="replace">
            <span t-if="attendee_status == 'accepted'">Your table has successfully been booked!</span>
        </xpath>
    </template>
</odoo>
