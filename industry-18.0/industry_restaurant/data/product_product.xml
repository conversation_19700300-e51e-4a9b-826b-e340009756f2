<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_54" model="product.product">
        <field name="name">Beef 1kg</field>
        <field name="sale_ok" eval="False"/>
        <field name="categ_id" ref="product_category_16"/>
        <field name="image_1920" type="base64" file="industry_restaurant/static/src/binary/product_template/54-image_1920.png"/>
    </record>
    <record id="product_product_55" model="product.product">
        <field name="name">Butter</field>
        <field name="sale_ok" eval="False"/>
        <field name="categ_id" ref="product_category_16"/>
        <field name="image_1920" type="base64" file="industry_restaurant/static/src/binary/product_template/55-image_1920.png"/>
    </record>    
    <record id="product_product_56" model="product.product">
        <field name="name">Cheese 250gr</field>
        <field name="sale_ok" eval="False"/>
        <field name="categ_id" ref="product_category_16"/>
        <field name="image_1920" type="base64" file="industry_restaurant/static/src/binary/product_template/56-image_1920.png"/>
    </record>
    <record id="product_product_57" model="product.product">
        <field name="name">Ketchup 500ml</field>
        <field name="sale_ok" eval="False"/>
        <field name="categ_id" ref="product_category_16"/>
        <field name="image_1920" type="base64" file="industry_restaurant/static/src/binary/product_template/57-image_1920.png"/>
    </record>
    <record id="product_product_58" model="product.product">
        <field name="name">Sanitizer 250ml</field>
        <field name="sale_ok" eval="False"/>
        <field name="categ_id" ref="product_category_16"/>
        <field name="image_1920" type="base64" file="industry_restaurant/static/src/binary/product_template/58-image_1920.png"/>
    </record>
    <record id="product_product_59" model="product.product">
        <field name="image_1920" type="base64" file="industry_restaurant/static/src/binary/product_template/56-image_1920"/>
        <field name="available_in_pos" eval="True"/>
        <field name="name">Blond Beer</field>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="is_storable" eval="True"/>
        <field name="list_price">4.5</field>
        <field name="purchase_method">receive</field>
        <field name="standard_price">2.5</field>
    </record>
</odoo>
