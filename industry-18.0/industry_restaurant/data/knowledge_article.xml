<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="welcome_article_body">
        
<p style="font-weight: bolder; color: black; font-size: 56px;">Fine Dining Restaurant</p>
<p>Hello there! </p>
<p>You just installed the Odoo for Fine Dining Restaurant package. By doing so, we have installed many necessary apps to run your restaurant efficiently.</p>
<p>Discover the basics of this package and explore all the possibilities Odoo offers to improve your experience.</p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">💡</i>
    <div class="ms-3">
        <p>If you want to execute a practical guided tour of this module, you should Import demo data and try the Demo - Use Case. (In the next Knowledge Article)</p>
    </div>
</div>
<div data-oe-protected="true" class="o_knowledge_behavior_anchor o_knowledge_behavior_type_toc" tabindex="-1">
                <div class="o_knowledge_toc_content">
                        <a href="#" data-oe-nodeid="0" class="o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Basics:</a>
                        <a href="#" data-oe-nodeid="1" class="o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Use case:</a>
                        <a href="#" data-oe-nodeid="2" class="o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">1. Point of sale</a>
                        <a href="#" data-oe-nodeid="3" class="o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">2. Replenishment</a>
                        <a href="#" data-oe-nodeid="4" class="o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">3. Plan your services</a>
                        <a href="#" data-oe-nodeid="5" class="o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Do you want to go further?</a>
                </div>
                </div>
<p> <br/></p>
<h2 style="font-size: 34px;"><strong>Basics:</strong></h2>
 <hr/>
<p>You can access every installed App in your Odoo database on your main dashboard.</p>
<ul>
    <li>Use the <font class="text-o-color-1"><strong>Point of Sale</strong></font> at the desk for your sales. You can also download the Odoo Mobile App on any phone to take orders.</li>
    <li>The <font class="text-o-color-1"><strong>Kitchen Display</strong></font> will ensure a great follow-up of every order in your bar and kitchen.</li>
    <li>Let your customer book a table anytime with the <font class="text-o-color-1"><strong>Appointment App</strong></font>.</li>
    <li>Use the <font class="text-o-color-1"><strong>Project App</strong></font> to never miss a reordering or a cleaning task.</li>
    <li>Use the <font class="text-o-color-1"><strong>Planning App</strong></font> to schedule and share your shifts with your employees.</li>
    <li>Use the <font class="text-o-color-1"><strong>Purchase App</strong></font> to reorder your products.</li>
    <li>Use the <font class="text-o-color-1"><strong>Inventory App</strong></font> to manage your stock and receive products.</li>
    <li>Get visibility and share your menu with a great <font class="text-o-color-1"><strong>Website</strong></font>.</li>
</ul>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">💡</i>
    <div class="ms-3">
        <p>Odoo is entirely integrated into an App. Download it to turn your phone into an additional cashier (and much more).</p>
    </div>
</div>
<p> <br/></p>
<h2 style="font-size: 34px;"><strong>Use Case:</strong></h2>
 <hr/>
<p>By uploading this module's demo data, your database has been filled with a few products, an appointment type, a website, and a sample planning.</p>
<p>You should be able to execute the following flows to have an overview of various flows you can quickly execute with this package using Odoo.</p>
<p>Of course, this is just an overview of the features included in this package. Feel free to add new apps, delete/modify demo data, and test everything around!</p>

<div class="alert alert-warning pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">⚠️</i>
    <div class="ms-3">
        <p>You didn't import demo data? You can still do it. Go to Apps &gt; Industries &gt; Upgrade your Fine Dining Restaurant package and check the related box. </p>
    </div>
</div>
<p> <br/></p>
<h3>1. Point of sale</h3>
 <hr/>
<p>From the Point of Sale, open your register on the main dashboard. </p>
<p>1. You are ready to welcome your first customers. To do so, select a table on the floor map and select the products they asked for from the list. </p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">💡</i>
    <div class="px-3">
        <p>You can easily edit the floor map by clicking "Edit plan" in the top-right menu when on the table selection step of your Point of Sale.</p>
    </div>
</div>

<p>2. To make it more interesting, feel free to create a second order. For this one, select the "Burger Menu Combo" product. You can choose 2 burger options and 4 different drinks. This "Combo" feature will allow you to create a menu with several courses and choices for each.</p>

<div class="alert alert-warning pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">⚠️</i>
    <div class="px-3">
        <p>Don't forget to hit the "Order" button each time you change an order. This will forward the order to the Kitchen Display.</p>
    </div>
</div>

<p>3. After placing your orders, look at our Kitchen Display. Click the "Backend" menu in the right corner to return to your backend, then switch to the Kitchen Display App. </p>
<p>4. Open your preparation screen. Your orders are there. You can now mark them as done either line by line or the entire order by clicking on the top.</p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">🚀</i>
    <div class="px-3">
        <p>You currently have two steps in your Kitchen Display: one for the kitchen/bar and one for the service. You can configure steps in the kitchen display configuration menu.</p>
    </div>
</div>

<p>At the end of your service, remember to close your register. To do so, in the Point of Sale App, select "Close Register" in the top-right menu. You can then precise your cash amount and validate the closing.</p>
<p> <br/></p>
<h3>2. Table booking</h3>
 <hr/>
<p>Manage your booking using the Appointment App. </p>
<p>You can discover a pre-configured Appointment Type named "Book a table". Use it to schedule services, the duration of each booking, and every communication you want to send to your customers when they place a booking. </p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">💡</i>
    <div class="px-3">
        <p>This setup is made to provide availability from Monday to Friday, with 2 services a day (12:00 AM to 15:00 PM and 18:00 PM to 23:00 PM).</p>
        <p>Setting different starts for the same service allows you to give your customer the possibility to book every 15 minutes in this setup. </p>
    </div>
</div>

<p>Do you want to test it and see how it looks on your website? Click on the "Preview" button. </p>
<p>Share a direct link with external services using the "Share" option.</p>
<p> <br/></p>
<h3>3. Website and online menu</h3>
 <hr/>
<p>Your website management has never been so easy. Go to the "Website App" to discover a sample website and explore millions of possibilities by clicking on the "Edit" button.</p>
<p><a class="btn btn-primary" href="https://www.odoo.com/documentation/latest/applications/websites/website.html">🎓 Website</a></p>
<p> <br/></p>
<h3>4. Replenishment</h3>
 <hr/>
<p>Replenishment is essential but challenging for a bar owner to systematize.</p>
<p>You have two main possibilities in Odoo to ease your replenishment process.</p>
<p><strong>1. Flow 1:</strong> Most products need a human assessment as the remaining quantity cannot be automatically computed. For those, a Recurring Task is created in the Project App. This task reappears every week, and you can personalize it with a checklist to avoid forgetting anything. </p>
<p>Go to your Purchase App to easily create Requests for Proposal or Purchase Orders.</p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">💡</i>
    <div class="px-3">
        <p>By the way, you can see another project with recurring tasks. This one is a demo for cleaning tasks.</p>
    </div>
</div>

<p><strong>2. Flow 2:</strong> For products that can be tracked, such as bottled beers and sodas, you can use automated purchases. To try it, navigate to your Point of Sale and sell a Blond Beer to a customer. </p>

<div class="alert alert-warning pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">⚠️</i>
    <div class="px-3">
        <p>The stock is only updated when you close your register. Let's do it before continuing to the next step.</p>
    </div>
</div>

<p>By doing so, you have only 23 beers left in your stock.</p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">🔁</i>
    <div class="px-3">
        <p>You can see that there is a reordering rule configured for this product if you go to Inventory &gt; Products &gt; Blond Beer</p>
        <p>Reordering rules are displayed in the smart buttons on top of your product. You can see a minimum quantity, and a maximum. Each time the forecast stock lowers beneath the minimum stock, it automatically creates a purchase order to replenish to the maximum quantity. </p>
    </div>
</div>

<p>If you navigate to your Purchase App, you can see a new Purchase Order that is ready to be sent. You can choose a packaging if your product has an existing one.</p>
<p>You can now email your Request for Proposal to your vendor or confirm it manually. </p>
<p>Once the products are there, you can click the "Receipt" smart button to validate your receipt and add these new products to your stock. </p>
<p> <br/></p>
<h3>5. Plan your services</h3>
 <hr/>
<p>Plan your services easily using the Planning App. </p>
<p>1. First, create all your employees in the database. Navigate to the Employee App to do so. </p>
<p>2. Create your employees' roles under the "Work Information" tab. Some are already created for this demo.</p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">🚀</i>
    <div class="px-3">
        <p>If you want to assign them an Odoo user account, you can select a related user in the "HR Settings" tab.</p>
    </div>
</div>

<p>3. Return to the Planning App. You can see a few shifts ready to be planned. Select the down arrow next to the publish button and click "Auto Plan." Your shifts will be automatically assigned based on your employees' availability.</p>
<p>4. You can still reassign any shift to adapt the planning.</p>
<p>5. Once you are happy with it, send it to your co-workers by smashing the publish button.</p>
<p> <br/></p>

<div class="alert alert-success pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">🎉</i>
    <div class="ms-3">
        <p>You completed that demo use case! There are millions of other ways to adapt your Odoo setup to fit your business needs. </p>
        <p>Discover more about Odoo by diving into our <a href="https://www.odoo.com/documentation/latest/">documentation</a> and <a href="https://www.odoo.com/fr_FR/slides/getting-started-15">academy</a> , and feel free to <a href="https://www.odoo.com/contactus">request a demo</a> if you need help! <br/></p>
    </div>
</div>
<p> <br/></p>

<h1 style="font-size: 34px;"><strong>Do you want to go further?</strong></h1>
 <hr/>
<p>Odoo offers you infinite possibilities, such as : </p>
<ul>
    <li>Start receiving orders directly in Odoo or go live with your <font class="text-o-color-1"><strong>E-shop</strong></font> in a few minutes.</li>
    <li>Manage your accounting easier than ever with a completely integrated environment. (<font class="text-o-color-1"><strong>Accounting App</strong></font>)</li>
    <li>Manage all your communication channels in one place with Odoo <font class="text-o-color-1"><strong>Marketing Automation</strong></font>, <font class="text-o-color-1"><strong>Email Marketing</strong></font>, <font class="text-o-color-1"><strong>Social Media Marketing</strong></font>, and <font class="text-o-color-1"><strong>SMS Marketing</strong></font>.</li>
    <li>Organise your events and connect with your customers easily with the <font class="text-o-color-1"><strong>Events App</strong></font>.</li>
</ul>
<p>These all go free in your current subscription; feel free to explore! 🙃</p>
<p> <br/></p>

<div class="alert alert-info pb-0 pt-3 d-flex align-items-center">
    <i class="mb-3 fst-normal">🚀</i>
    <div class="ms-3">
        <p>Would you like to discuss your Odoo setup with us or go even further? </p>
        <p><a class="btn btn-secondary" href="https://www.odoo.com/contactus">Reach us</a></p>
    </div>
</div>
      
    </template>
    <record id="welcome_article" model="knowledge.article">
        <field name="name">Fine Dining Restaurant</field>
        <field name="icon">🍽️</field>
        <field name="internal_permission">write</field>
        <field name="cover_image_id" ref="knowledge_cover_1"/>
        <field name="is_article_visible_by_everyone" eval="True"/>
        <field name="is_locked" eval="True"/>
        <field name="body"><![CDATA[]]>
</field>
    </record>
</odoo>
