# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* software_reseller
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:52+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_3
#: model:uom.uom,name:software_reseller.uom_uom_30
msgid "3 Years"
msgstr "3年"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_4
msgid "5 Years"
msgstr "5年"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: inherit;\">Create a purchase order to buy "
"the Odoo license to Odoo S.A.</font>"
msgstr ""
"<font style=\"background-color: inherit;\">Odoo "
"S.A.へのOdooライセンスの購買オーダを作成</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 231, 206);\">A studio automated "
"action turn tasks in Changes Requested when the license key expires in the "
"15 days or less.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 231, "
"206);\">スタジオの自動化アクションにより、ライセンスキーの使用期限が15日以内に切れる場合の変更依頼のタスクが実行されます。</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 239, 198);\">From the \"Optional "
"Products\" tab, you can also add development services ine one click.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 239, 198);\">\"オプションプロダクト\" "
"タブから、ワンクリックで開発サービスを追加することもできます。</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">On "
"this order, the way to manage licenses is similar to the above section. So, "
"we'll mostly focus on the delivery of the extra services.</font><br/>"
msgstr ""
"<font style=\"color: inherit; background-color: rgb(255, 239, "
"198);\">このオーダでは、ライセンスの管理方法は上記のセクションと同様です。そのため、ここでは主に追加サービス提供に焦点を当てます。</font><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                This setup if for IT companies reselling software licenses, and consulting services. The typical sale is a 1 year Oracle Database license that is purchased to Oracle, and resold to client at a margin, with extra services to\n"
"                setup the database.\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                本設定は、ソフトウェアライセンスやコンサルティングサービスを再販するIT企業向けです。典型的な販売例は、Oracleで購買した1年間のOracleデータベースライセンスを、マージンを上乗せして顧客に再販し、データベースのセットアップに追加サービスを\n"
"                提供するものです。\n"
"            </span>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\">Software Resellers, IT Services</span><br/>"
msgstr "<span style=\"font-size: 36px;\">ソフトウェア販売代理店、ITサービス</span><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Licenses</u></strong>"
msgstr "<strong><u>ライセンスの請求</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Projects</u></strong>"
msgstr "<strong><u>プロジェクトの請求</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Plan Consultants</u></strong>"
msgstr "<strong><u>計画コンサルタント</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Purchase the licenses</u></strong>"
msgstr "<strong><u>ライセンス購入</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell Oracle Licenses</u></strong>"
msgstr "<strong><u>Oracleライセンス販売</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell a project</u></strong>"
msgstr "<strong><u>プロジェクトを販売</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Timesheet Work done</u></strong>"
msgstr "<strong><u>タイムシート作業完了済</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A purchase order to Oracle; to buy the license for this client"
msgstr "Oracleへの購買オーダ; この顧客用にライセンスを購入"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A task to manage the license, in stage \"New Requests\""
msgstr "\"新規リクエスト\" 段階におけるライセンスの管理業務"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.all_licenses_ce314e8b-2439-473a-b7a7-493af7707108
#: model:ir.ui.menu,name:software_reseller.licenses_all_license_17e01089-b423-4963-bf8e-1eb6e86152b8
msgid "All Licenses"
msgstr "全ライセンス"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "All Licenses (default to list)"
msgstr "全ライセンス (リストに対するデフォルト)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "As you confirm the order, it will:"
msgstr "オーダを確認すると、"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"As you confirm the order, the license line is blue as you can already invoice it. The consulting services are black as there is nothing to invoice; you'll be able to invoice at the end of the month, based on the time spent on the\n"
"            project."
msgstr ""
"オーダを確認すると、請求書を作成できるため、ライセンス明細は青で表示されます。コンサルティングサービスは請求書を作成する必要がないため黒で表示されます。月末に、プロジェクトに費やした時間に基づいて請求書を\n"
"            作成できます。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Assign these services to the right person."
msgstr "サービスを適切な担当者に割当てます。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"At the end of the month, salespeople go to the menu \"Orders to Invoice\" in"
" Sales app. From there they can select an order (or select all), and invoice"
" what has been delivered on the order."
msgstr ""
"月末に、販売担当者は販売アプリの\"オーダから請求書\"メニューに移動します。そこから、オーダを選択(または全てを選択)し、そのオーダで納品されたものについて顧客に請求することができます。"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_21
msgid "Backlog"
msgstr "バックログ"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flow: Selling Services"
msgstr "ビジネスフロー: サービスの販売"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flows: Licenses Management"
msgstr "ビジネスフロー: ライセンス管理"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_2
msgid "Business Needs analysis"
msgstr "ビジネスニーズ分析"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "By software"
msgstr "ソフトウェアごと"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_1
msgid "Consultant"
msgstr "コンサルタント"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_1
#: model:product.template,name:software_reseller.product_product_7_product_template
#: model:project.project,name:software_reseller.project_project_4
msgid "Consulting Services"
msgstr "コンサルティングサービス"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 200 hours to plan, in the planning"
msgstr "計画から計画用に200時間を作成"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 3 tasks to track services"
msgstr "サービスを追跡する3タスクを作成"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Create a <strong><font class=\"text-o-color-2\">subscription​</font></strong> to any customer with the <strong><font class=\"text-o-color-2\">product \"Oracle Database\"</font></strong><font class=\"text-o-color-2\">,</font> for 5 users for\n"
"            one year. Once you confirm this order, two documents will be created:"
msgstr ""
"<strong><font class=\"text-o-color-2\">サブスクリプション​</font></strong> を <strong><font class=\"text-o-color-2\">プロダクト \"Oracle データベース\"</font></strong><font class=\"text-o-color-2\">を利用している任意の顧客に</font> 1年5ユーザで\n"
"            作成します。このオーダを確認すると2つのドキュメントが作成されます。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create a task to track the license"
msgstr "ライセンスを追跡するタスクを作成"

#. module: software_reseller
#: model:account.analytic.plan,name:software_reseller.account_analytic_plan_1
msgid "Default"
msgstr "デフォルト"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_27
msgid "Deployed"
msgstr "展開済"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_4
msgid "Deprecated"
msgstr "廃止"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_2
msgid "Developer"
msgstr "開発者"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_25
msgid "Development"
msgstr "開発"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_2
#: model:product.template,name:software_reseller.product_product_8_product_template
#: model:project.project,name:software_reseller.project_project_5
msgid "Development Services"
msgstr "開発サービス"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_23
msgid "Done"
msgstr "完了"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Environement: Production"
msgstr "環境: 本番"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 1: Sell Oracle Licenses"
msgstr "フロー1: Oracleライセンスを販売"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 2: Manage your licenses"
msgstr "フロー2: ライセンスを管理"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Renew a license"
msgstr "フロー3: ライセンス更新"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Selling Odoo with Services"
msgstr "フロー3: サービスとOdooを販売"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_3
msgid "Free to Use"
msgstr "引当なし"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From a license key:"
msgstr "ライセンスキーから:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the <strong><font class=\"text-o-color-2\">app "
"\"Licenses\"</font></strong>, you an have an overview of all your software "
"licenses. Click on one specific license to track license keys belonging to "
"each customer."
msgstr ""
"<strong><font class=\"text-o-color-2\">\"ライセンス\"アプリ</font></strong>から、 "
"ソフトウェアライセンスの概要を把握できます。特定のライセンスをクリックすると、各顧客に属するライセンスキーを追跡できます。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From the planning, click on the \"Plan Existing\" icon;"
msgstr "計画から、\"既存プラン\" アイコンをクリックします;"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the subscription, using the top buttons, jump to the purchase order to buy these licenses to Oracle. You can send by email your request for quotation, then confirm the order. At that point, Oracle will send you the license\n"
"            number."
msgstr ""
"サブスクリプションから、上部のボタンを使用して、Oracleのライセンス購買オーダにジャンプします。見積依頼をEメールで送信し、その後オーダを確定します。その時点で、Oracleからライセンス番号が\n"
"            送られてきます。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the timesheet app, or on the task, consultants can timesheet hours on the different phases of the project: Business Need Analysis, Odoo Configuration, or Training &amp; Support. This will be reflected as \"Delivered Quantity\" on\n"
"            the sale order lines."
msgstr ""
"タイムシートアプリから、またはタスク上で、コンサルタントはプロジェクトの異なるフェーズ (ビジネスニーズ分析、Odoo設定、トレーニング＆サポート) のタイムシート時間を記録できます。これは販売オーダ明細の\"納品数量\"として\n"
"            反映されます。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Go back to the sale order, then jump to the task. On the task, set the "
"different informations based on what oracle sends you:"
msgstr "販売オーダに戻り、タスクに移動します。タスク上で、Oracleから送信された内容に基づいて、さまざまな情報を設定します。"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_1
msgid "Implementation Services"
msgstr "導入サービス"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_22
msgid "In Progress"
msgstr "進行中"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_2
msgid "In Use"
msgstr "使用中"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_6
#: model:project.project,name:software_reseller.project_project_6
msgid "Internal"
msgstr "内部"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_2_product_template
msgid "Java EE License"
msgstr "Java EE ライセンス"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_3
#: model:project.project,name:software_reseller.project_project_1
msgid "Java Licenses"
msgstr "Java ライセンス"

#. module: software_reseller
#: model:ir.model.fields,field_description:software_reseller.x_is_license_field
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "License"
msgstr "ライセンス"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "License Key: XYZ"
msgstr "ライセンスキー: XYZ"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_8482a8f5-a077-46b1-8007-6efe437a9245
#: model:ir.ui.menu,name:software_reseller.licenses_by_software_597c3ae4-8ff8-4785-9768-957e61596e95
#: model:project.project,label_tasks:software_reseller.project_project_1
#: model:project.project,label_tasks:software_reseller.project_project_2
#: model:project.project,label_tasks:software_reseller.project_project_3
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_5
#: model:uom.category,name:software_reseller.uom_category_8
msgid "Licenses"
msgstr "ライセンス"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Licenses as managed as tasks in the project management app, with custom properties; license key, number of users, software version, ... these properties depend on the type of software sold (i.e. Odoo is sold based on the number of\n"
"            users periodically, but Oracle requires a lot of information: number of CPUs, number of developer seats, size of DB, etc)"
msgstr ""
"プロジェクト管理アプリのタスクとして管理されるライセンス、カスタムプロパティ付き。ライセンスキー、ユーザ数、ソフトウェアバージョンなど、これらのプロパティは販売するソフトウェアの種類によって異なります(例えば、Odooはユーザ数に基づいて販売されますが、\n"
"            OracleではCPU数、開発者数、DBのサイズなど、多くの情報が必要です)。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses by Software (in kanban)"
msgstr "ソフトウェア別ライセンス (かんばん)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses to Renew: things you have to check periodically"
msgstr "更新が必要なライセンス: 定期的に確認すべき事項"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "Make the project visible in the Licences app"
msgstr "ライセンスアプリでプロジェクトを可視化します。"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_31
msgid "Month"
msgstr "月"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_20
msgid "New"
msgstr "新規"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_1
msgid "New Requests"
msgstr "新規リクエスト"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_3
msgid "Odoo Configuration"
msgstr "Odoo設定"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_3_product_template
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_6
msgid "Odoo EE License"
msgstr "Odoo EE ライセンス"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_4
#: model:project.project,name:software_reseller.project_project_2
msgid "Odoo Licenses"
msgstr "Odoo ライセンス"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_1
msgid "Odoo Standard Implementation"
msgstr "Odoo 標準導入"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_1_product_template
msgid "Oracle Database License"
msgstr "Oracle データベースライセンス"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_5
#: model:project.project,name:software_reseller.project_project_3
msgid "Oracle Database Licenses"
msgstr "Oracle データベースライセンス"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Record all information related to the license (version, type of license, "
"...). These information are different from one license type to another "
"(Oracle vs Odoo)"
msgstr ""
"ライセンスに関連する全ての情報(バージョン、ライセンスの種類など)を記録します。これらの情報は、ライセンスの種類によって異なります(OracleとOdoo)。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Responsible: your account manager / user"
msgstr "担当: アカウントマネジャー / ユーザ"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Select the services to plan \"Business Need Analysis\" and \"Odoo "
"Configuration\" (training will be planned later on, on phase 2)"
msgstr "サービスを選択して\"ビジネスニーズ分析\" と \"Odoo設定\" (レーニングは後ほどフェーズ2で計画)を計画します。"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_licenese_01632710-57f0-4ec5-9e2e-e975cfbc0406
msgid "Software"
msgstr "ソフトウェア"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Software Resellers, IT Services"
msgstr "ソフトウェア販売代理店、ITサービス"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_24
msgid "Specification"
msgstr "仕様設計"

#. module: software_reseller
#: model:project.project,label_tasks:software_reseller.project_project_4
#: model:project.project,label_tasks:software_reseller.project_project_5
#: model:project.project,label_tasks:software_reseller.project_project_6
msgid "Tasks"
msgstr "タスク"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_26
msgid "Testing"
msgstr "テスト"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "The <strong>Licenses</strong> app has 3 menus:"
msgstr "<strong>ライセンス</strong> アプリには3メニューあります:"

#. module: software_reseller
#: model_terms:ir.actions.act_window,help:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "This is where you manage your software licenses."
msgstr "ここでソフトウェアライセンスを管理します。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"To renew a license, you should just renew the subscription; the task remains"
" the same."
msgstr "ライセンスを更新するには、サブスクリプションを更新するだけでよく、タスクは変わりません。"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_4
msgid "Training & Support"
msgstr "トレーニング & サポート"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_28
msgid "User / Month"
msgstr "ユーザ / 月"

#. module: software_reseller
#: model:uom.category,name:software_reseller.uom_category_7
msgid "User Licenses"
msgstr "ユーザライセンス"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_27
msgid "Users / Year"
msgstr "ユーザ / 年"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Usually, you sell the software licenses with additional services. To test this flow, as you create a quotation, use the quotation template\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">\"Odoo Standard Implementation\"</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">. That will add the services billed on timesheets (default setup: sell days, but timesheets per hour).</span></font>\n"
"            </strong>"
msgstr ""
"通常、追加サービスとともにソフトウェアライセンスを販売します。この流れをテストするには、見積作成時に\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">\"Odoo 標準導入\"</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">見積テンプレートを使用します。 タイムシートで請求されたサービスが追加されます(デフォルト設定: 日単位で販売、タイムシートは時間単位)。</span></font>\n"
"            </strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Version: 20.0.1"
msgstr "バージョン: 20.0.1"

#. module: software_reseller
#: model_terms:web_tour.tour,rainbow_man_message:software_reseller.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ようこそ！"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_29
msgid "Year"
msgstr "年"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_2
msgid "Yearly"
msgstr "年次"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"You can communicate with customers on the chatter, to keep an history of the"
" discussions"
msgstr "顧客とチャター上でコミュニケーションをとり、やり取りの履歴を残すことができます。"
