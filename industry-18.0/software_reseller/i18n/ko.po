# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* software_reseller
# 
# Translators:
# Sarah Park, 2024
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:52+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_3
#: model:uom.uom,name:software_reseller.uom_uom_30
msgid "3 Years"
msgstr "3년"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_4
msgid "5 Years"
msgstr "5년"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: inherit;\">Create a purchase order to buy "
"the Odoo license to Odoo S.A.</font>"
msgstr ""
"<font style=\"background-color: inherit;\">Odoo S.A.에서 Odoo 라이선스 구매용 발주서를 "
"생성합니다..</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 231, 206);\">A studio automated "
"action turn tasks in Changes Requested when the license key expires in the "
"15 days or less.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 231, 206);\">라이선스 키의 만료 기간이 15일 이하가"
" 되는 경우 스튜디오의 자동화 작업을 통해 작업을 변경을 요청한 작업으로 전환합니다.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 239, 198);\">From the \"Optional "
"Products\" tab, you can also add development services ine one click.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 239, 198);\">\"선택 제품\" 탭에서 클릭 한 번으로"
" 개발 서비스도 추가할 수 있습니다.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">On "
"this order, the way to manage licenses is similar to the above section. So, "
"we'll mostly focus on the delivery of the extra services.</font><br/>"
msgstr ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">이 "
"주문서에서 라이선스를 관리하는 방법은 위 섹션 내용과 비슷합니다. 따라서 여기에서는 추가 서비스를 제공하는 것을 위주로 "
"설명합니다.</font><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                This setup if for IT companies reselling software licenses, and consulting services. The typical sale is a 1 year Oracle Database license that is purchased to Oracle, and resold to client at a margin, with extra services to\n"
"                setup the database.\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                이 설정은 소프트웨어 라이선스 및 컨설팅 서비스를 재판매하는 IT 회사를 위해 설계되었습니다. 일반적인 판매는 오라클에서 구매한 1년짜리 오라클 데이터베이스 라이선스를 데이터베이스 설정을 위한 추가 서비스와 함께 마크업하여 고객에게\n"
"                재판매하는 방식입니다.\n"
"            </span>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\">Software Resellers, IT Services</span><br/>"
msgstr "<span style=\"font-size: 36px;\">소프트웨어 리셀러, IT 서비스</span><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Licenses</u></strong>"
msgstr "<strong><u>라이선스 청구서 발행</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Projects</u></strong>"
msgstr "<strong><u>프로젝트 청구서 발행</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Plan Consultants</u></strong>"
msgstr "<strong><u>컨설턴트 계획</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Purchase the licenses</u></strong>"
msgstr "<strong><u>라이선스 구매</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell Oracle Licenses</u></strong>"
msgstr "<strong><u>Oracle 라이선스 판매</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell a project</u></strong>"
msgstr "<strong><u>프로젝트 판매</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Timesheet Work done</u></strong>"
msgstr "<strong><u>작업시간표 작업 완료</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A purchase order to Oracle; to buy the license for this client"
msgstr "해당 클라이언트가 사용할 라이선스를 구매하기 위한 Oracle 구매용 발주서"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A task to manage the license, in stage \"New Requests\""
msgstr "\"새 요청\" 단계에서 라이선스를 관리하는 작업"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.all_licenses_ce314e8b-2439-473a-b7a7-493af7707108
#: model:ir.ui.menu,name:software_reseller.licenses_all_license_17e01089-b423-4963-bf8e-1eb6e86152b8
msgid "All Licenses"
msgstr "모든 라이선스"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "All Licenses (default to list)"
msgstr "모든 라이선스 (기본적으로 표시됨)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "As you confirm the order, it will:"
msgstr "주문을 확인하면 다음과 같이 진행됩니다:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"As you confirm the order, the license line is blue as you can already invoice it. The consulting services are black as there is nothing to invoice; you'll be able to invoice at the end of the month, based on the time spent on the\n"
"            project."
msgstr ""
"주문을 확인하면 라이선스 라인이 파란색으로 표시되어 즉시 청구할 수 있음을 나타냅니다. 반면 컨설팅 서비스는 현재 청구할 항목이 없으므로 검은색으로 표시되며, 프로젝트에 소요된 시간을 기준으로 월말에 청구가\n"
"            이루어집니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Assign these services to the right person."
msgstr "해당 서비스를 담당자에게 배정합니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"At the end of the month, salespeople go to the menu \"Orders to Invoice\" in"
" Sales app. From there they can select an order (or select all), and invoice"
" what has been delivered on the order."
msgstr ""
"매월 말이 되면 영업 담당자는 영업 앱에 있는 \"청구서 발행 대상 주문\" 메뉴를 확인합니다. 여기에서 일부 주문을 선택 (또는 모두 "
"선택)한 후, 주문 배송품에 대해 청구서를 발행할 수 있습니다."

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_21
msgid "Backlog"
msgstr "기록"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flow: Selling Services"
msgstr "비즈니스 흐름: 서비스 판매"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flows: Licenses Management"
msgstr "비즈니스 흐름: 라이선스 관리"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_2
msgid "Business Needs analysis"
msgstr "비즈니스 요구 사항 분석"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "By software"
msgstr "소프트웨어별"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_1
msgid "Consultant"
msgstr "컨설턴트"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_1
#: model:product.template,name:software_reseller.product_product_7_product_template
#: model:project.project,name:software_reseller.project_project_4
msgid "Consulting Services"
msgstr "자문 서비스"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 200 hours to plan, in the planning"
msgstr "일정 수립에서 계획에 200시간을 생성"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 3 tasks to track services"
msgstr "서비스를 추적하기 위한 3가지 작업 생성"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Create a <strong><font class=\"text-o-color-2\">subscription​</font></strong> to any customer with the <strong><font class=\"text-o-color-2\">product \"Oracle Database\"</font></strong><font class=\"text-o-color-2\">,</font> for 5 users for\n"
"            one year. Once you confirm this order, two documents will be created:"
msgstr ""
"모든 <strong><font class=\"text-o-color-2\">\"Oracle 데이터베이스\" "
"제품</font></strong><font class=\"text-o-color-2\"></font> 고객에게 사용자 5명용으로 "
"<strong><font class=\"text-o-color-2\">구독</font></strong>을 생성합니다. 해당 주문서를 "
"확정하면 두 개의 문서가 생성됩니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create a task to track the license"
msgstr "라이선스 추적 작업 생성"

#. module: software_reseller
#: model:account.analytic.plan,name:software_reseller.account_analytic_plan_1
msgid "Default"
msgstr "기본"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_27
msgid "Deployed"
msgstr "배포됨"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_4
msgid "Deprecated"
msgstr "감가상각됨"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_2
msgid "Developer"
msgstr "개발자"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_25
msgid "Development"
msgstr "개발"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_2
#: model:product.template,name:software_reseller.product_product_8_product_template
#: model:project.project,name:software_reseller.project_project_5
msgid "Development Services"
msgstr "개발 서비스"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_23
msgid "Done"
msgstr "완료"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Environement: Production"
msgstr "환경: 프로덕션"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 1: Sell Oracle Licenses"
msgstr "흐름 1: Oracle 라이선스 판매"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 2: Manage your licenses"
msgstr "흐름 2: 라이선스 관리"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Renew a license"
msgstr "흐름 3: 라이선스 갱신"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Selling Odoo with Services"
msgstr "흐름 3: 서비스와 함께 Odoo 판매"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_3
msgid "Free to Use"
msgstr "사용 가능"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From a license key:"
msgstr "라이선스 키에서:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the <strong><font class=\"text-o-color-2\">app "
"\"Licenses\"</font></strong>, you an have an overview of all your software "
"licenses. Click on one specific license to track license keys belonging to "
"each customer."
msgstr ""
"<strong><font class=\"text-o-color-2\">\"라이선스\" 앱</font></strong>에서 모든 소프트웨어"
" 라이선스에 대한 전체보기를 확인할 수 있습니다. 각 고객이 가지고 있는 라이선스 키를 추적하려면 라이선스를 클릭하면 됩니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From the planning, click on the \"Plan Existing\" icon;"
msgstr "계획에서 \"기존 계획\" 아이콘을 클릭합니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the subscription, using the top buttons, jump to the purchase order to buy these licenses to Oracle. You can send by email your request for quotation, then confirm the order. At that point, Oracle will send you the license\n"
"            number."
msgstr ""
"구독에서 상단 버튼을 사용하여 Oracle에서 해당 라이선스를 구매하기 위한 구매 주문서로 이동합니다. 견적 요청을 이메일로 보낸 다음 주문을 확인할 수 있습니다. 주문이 확인되면 Oracle에서 라이선스 번호를\n"
"            제공합니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the timesheet app, or on the task, consultants can timesheet hours on the different phases of the project: Business Need Analysis, Odoo Configuration, or Training &amp; Support. This will be reflected as \"Delivered Quantity\" on\n"
"            the sale order lines."
msgstr ""
"작업 기록 앱을 통해 혹은 작업에서 컨설턴트는 다양한 프로젝트 단계에 대한 작업 시간을 기록할 수 있으며, 비즈니스 요구 분석, Odoo 환경 설정 또는 교육 및 지원과 같은 프로젝트에 활용할 수 있습니다. 이 내용은 판매주문서 내역에는\n"
"            \"배송된 수량\" 으로 반영됩니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Go back to the sale order, then jump to the task. On the task, set the "
"different informations based on what oracle sends you:"
msgstr "판매주문서로 돌아가서 작업으로 이동합니다. Oracle에서 전송된 내용에 따라 작업에서 다양한 정보를 설정합니다."

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_1
msgid "Implementation Services"
msgstr "구현 서비스"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_22
msgid "In Progress"
msgstr "진행 중"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_2
msgid "In Use"
msgstr "사용 중"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_6
#: model:project.project,name:software_reseller.project_project_6
msgid "Internal"
msgstr "내부"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_2_product_template
msgid "Java EE License"
msgstr "Java EE 라이선스"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_3
#: model:project.project,name:software_reseller.project_project_1
msgid "Java Licenses"
msgstr "Java 라이선스"

#. module: software_reseller
#: model:ir.model.fields,field_description:software_reseller.x_is_license_field
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "License"
msgstr "라이센스"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "License Key: XYZ"
msgstr "라이선스 키: XYZ"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_8482a8f5-a077-46b1-8007-6efe437a9245
#: model:ir.ui.menu,name:software_reseller.licenses_by_software_597c3ae4-8ff8-4785-9768-957e61596e95
#: model:project.project,label_tasks:software_reseller.project_project_1
#: model:project.project,label_tasks:software_reseller.project_project_2
#: model:project.project,label_tasks:software_reseller.project_project_3
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_5
#: model:uom.category,name:software_reseller.uom_category_8
msgid "Licenses"
msgstr "라이센스"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Licenses as managed as tasks in the project management app, with custom properties; license key, number of users, software version, ... these properties depend on the type of software sold (i.e. Odoo is sold based on the number of\n"
"            users periodically, but Oracle requires a lot of information: number of CPUs, number of developer seats, size of DB, etc)"
msgstr ""
"라이선스는 프로젝트 관리 앱에서 작업으로 관리되며, 라이선스 키, 사용자 수, 소프트웨어 버전 등과 같은 사용자 지정 속성이 있습니다. 이러한 속성은 판매되는 소프트웨어 유형에 따라 다릅니다. (예: Odoo는 주기적으로 사용자 수를 기준으로 판매되는 반면,\n"
"            Oracle은 CPU 수, 개발자 좌석 수, 데이터베이스 크기 등 광범위한 정보를 필요로 합니다)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses by Software (in kanban)"
msgstr "소프트웨어별 라이선스 (칸반)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses to Renew: things you have to check periodically"
msgstr "라이선스 갱신: 주기적으로 확인해야 할 사항"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "Make the project visible in the Licences app"
msgstr "라이선스 앱에서 프로젝트가 표시되도록 설정"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_31
msgid "Month"
msgstr "월"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_20
msgid "New"
msgstr "신규"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_1
msgid "New Requests"
msgstr "새 요청"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_3
msgid "Odoo Configuration"
msgstr "Odoo 환경 설정"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_3_product_template
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_6
msgid "Odoo EE License"
msgstr "Odoo EE 라이선스"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_4
#: model:project.project,name:software_reseller.project_project_2
msgid "Odoo Licenses"
msgstr "Odoo 라이선스"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_1
msgid "Odoo Standard Implementation"
msgstr "Odoo 표준 구현"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_1_product_template
msgid "Oracle Database License"
msgstr "Oracle 데이터베이스 라이선스"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_5
#: model:project.project,name:software_reseller.project_project_3
msgid "Oracle Database Licenses"
msgstr "Oracle 데이터베이스 라이선스"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Record all information related to the license (version, type of license, "
"...). These information are different from one license type to another "
"(Oracle vs Odoo)"
msgstr ""
"라이선스와 관련된 모든 정보 (버전, 라이선스 유형 등)를 기록합니다. 이러한 정보는 라이선스 유형마다 다릅니다 (Oracle과 Odoo"
" 차이 등)."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Responsible: your account manager / user"
msgstr "담당자: 계정 관리자 / 사용자"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Select the services to plan \"Business Need Analysis\" and \"Odoo "
"Configuration\" (training will be planned later on, on phase 2)"
msgstr "비즈니스 요구 분석 및 \"Odoo 환경 설정\"을 계획할 서비스를 선택합니다 (교육은 나중에 2단계에서 나중에 계획)"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_licenese_01632710-57f0-4ec5-9e2e-e975cfbc0406
msgid "Software"
msgstr "소프트웨어"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Software Resellers, IT Services"
msgstr "소프트웨어 리셀러, IT 서비스"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_24
msgid "Specification"
msgstr "사양"

#. module: software_reseller
#: model:project.project,label_tasks:software_reseller.project_project_4
#: model:project.project,label_tasks:software_reseller.project_project_5
#: model:project.project,label_tasks:software_reseller.project_project_6
msgid "Tasks"
msgstr "작업"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_26
msgid "Testing"
msgstr "테스트중"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "The <strong>Licenses</strong> app has 3 menus:"
msgstr "<strong>라이선스</strong> 앱에는 3개의 메뉴가 있습니다."

#. module: software_reseller
#: model_terms:ir.actions.act_window,help:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "This is where you manage your software licenses."
msgstr "여기에서 소프트웨어 라이선스를 관리할 수 있습니다."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"To renew a license, you should just renew the subscription; the task remains"
" the same."
msgstr "라이선스를 갱신하려면 구독만 갱신하면 됩니다. 작업은 동일하게 유지됩니다."

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_4
msgid "Training & Support"
msgstr "교육 및 지원"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_28
msgid "User / Month"
msgstr "사용자 / 월"

#. module: software_reseller
#: model:uom.category,name:software_reseller.uom_category_7
msgid "User Licenses"
msgstr "사용자 라이선스"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_27
msgid "Users / Year"
msgstr "사용자 / 년"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Usually, you sell the software licenses with additional services. To test this flow, as you create a quotation, use the quotation template\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">\"Odoo Standard Implementation\"</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">. That will add the services billed on timesheets (default setup: sell days, but timesheets per hour).</span></font>\n"
"            </strong>"
msgstr ""
"일반적으로 소프트웨어 라이선스를 추가 서비스와 함께 판매합니다. 이 흐름을 테스트하려면 견적서를 생성할 때 견적서 템플릿\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">\"Odoo 표준 구현\"</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">을 사용합니다. 여기에는 작업표에 청구되는 서비스가 포함됩니다(기본 설정: 판매 일수이지만 작업표는 시간당 청구할 수 있음).</span></font>\n"
"            </strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Version: 20.0.1"
msgstr "버전: 20.0.1"

#. module: software_reseller
#: model_terms:web_tour.tour,rainbow_man_message:software_reseller.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "환영합니다! 마음껏 둘러 보세요."

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_29
msgid "Year"
msgstr "년"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_2
msgid "Yearly"
msgstr "매년"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"You can communicate with customers on the chatter, to keep an history of the"
" discussions"
msgstr "메시지창을 통해 고객과 소통하여 논의한 내역을 보관할 수 있습니다."
