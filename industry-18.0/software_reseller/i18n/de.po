# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* software_reseller
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:52+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_3
#: model:uom.uom,name:software_reseller.uom_uom_30
msgid "3 Years"
msgstr "3 Jahre"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_4
msgid "5 Years"
msgstr "5 Jahre"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: inherit;\">Create a purchase order to buy "
"the Odoo license to Odoo S.A.</font>"
msgstr ""
"<font style=\"background-color: inherit;\">Eine eine Bestellung wird "
"erstellt, um die Odoo-Lizenz für Odoo S. A. zu kaufen.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 231, 206);\">A studio automated "
"action turn tasks in Changes Requested when the license key expires in the "
"15 days or less.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 231, 206);\">Eine automatisierte "
"Studio-Aktion verwandelt Aufgaben in angefragte Änderungen, wenn der "
"Lizenzschlüssel innerhalb der nächsten 2 Wochen abläuft.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 239, 198);\">From the \"Optional "
"Products\" tab, you can also add development services ine one click.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 239, 198);\">Über den Reite "
"„Optionale Produkte“ können Sie auch Entwicklungsdienstleistungen in einem "
"Klick hinzufügen.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">On "
"this order, the way to manage licenses is similar to the above section. So, "
"we'll mostly focus on the delivery of the extra services.</font><br/>"
msgstr ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">Bei "
"diesem Auftrag ist die Verwaltung der Lizenzen ähnlich wie im obigen "
"Abschnitt. Wir konzentrieren uns also hauptsächlich auf die Bereitstellung "
"der zusätzlichen Dienste.</font><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                This setup if for IT companies reselling software licenses, and consulting services. The typical sale is a 1 year Oracle Database license that is purchased to Oracle, and resold to client at a margin, with extra services to\n"
"                setup the database.\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                Diese Einrichtung ist für IT-Unternehmen gedacht, die Softwarelizenzen und Beratungsdienstleistungen weiterverkaufen.  Der typische Verkauf ist eine 1-Jahres-Lizenz für die Oracle-Datenbank, die bei Oracle erworben und mit einer Marge an den Kunden weiterverkauft wird, mit zusätzlichen Dienstleistungen\n"
"                zur Einrichtung der Datenbank.\n"
"            </span>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\">Software Resellers, IT Services</span><br/>"
msgstr ""
"<span style=\"font-size: 36px;\">Software-Händler, IT-Services</span><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Licenses</u></strong>"
msgstr "<strong><u>Lizenzen abrechnen</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Projects</u></strong>"
msgstr "<strong><u>Projekte abrechnen</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Plan Consultants</u></strong>"
msgstr "<strong><u>Berater einplanen</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Purchase the licenses</u></strong>"
msgstr "<strong><u>Die Lizenzen erwerben</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell Oracle Licenses</u></strong>"
msgstr "<strong><u>Oracle-Lizenzen verkaufen</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell a project</u></strong>"
msgstr "<strong><u>Ein Projekt verkaufen</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Timesheet Work done</u></strong>"
msgstr "<strong><u>Zeit für Arbeit erfassen</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A purchase order to Oracle; to buy the license for this client"
msgstr ""
"Eine Bestellung bei Oracle, um die Lizenz für diesen Kunden zu kaufen."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A task to manage the license, in stage \"New Requests\""
msgstr "Eine Aufgabe zur Verwaltung der Lizenz in der Phase „Neue Anfragen“."

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.all_licenses_ce314e8b-2439-473a-b7a7-493af7707108
#: model:ir.ui.menu,name:software_reseller.licenses_all_license_17e01089-b423-4963-bf8e-1eb6e86152b8
msgid "All Licenses"
msgstr "Alle Lizenzen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "All Licenses (default to list)"
msgstr "Alle Lizenzen (standardmäßig Liste)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "As you confirm the order, it will:"
msgstr "Bei Bestätigung des Auftrags geschieht Folgendes:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"As you confirm the order, the license line is blue as you can already invoice it. The consulting services are black as there is nothing to invoice; you'll be able to invoice at the end of the month, based on the time spent on the\n"
"            project."
msgstr ""
"Wenn Sie den Auftrag bestätigen, ist die Lizenzzeile blau, da Sie sie "
"bereits in Rechnung stellen können. Die Beratungsservices sind schwarz, da "
"es nichts in Rechnung zu stellen gibt. Sie können am Ende des Monats eine "
"Rechnung stellen, basierend auf der für das Projekt aufgewendeten Zeit."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Assign these services to the right person."
msgstr "Weisen Sie diese Services der richtigen Person zu."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"At the end of the month, salespeople go to the menu \"Orders to Invoice\" in"
" Sales app. From there they can select an order (or select all), and invoice"
" what has been delivered on the order."
msgstr ""
"Am Ende des Monats gehen die Vertriebsmitarbeiter in der Verkaufsapp zum "
"Menü „Abzurechnende Aufträge“. Von dort aus können sie einen Auftrag (oder "
"alle) auswählen und die gelieferte Ware in Rechnung stellen."

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_21
msgid "Backlog"
msgstr "Rückstand"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flow: Selling Services"
msgstr "Geschäftsablauf: Services verkaufen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flows: Licenses Management"
msgstr "Geschäftsabläufe: Lizenzverwaltung"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_2
msgid "Business Needs analysis"
msgstr "Analyse der Geschäftsanforderungen"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "By software"
msgstr "Je Software"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_1
msgid "Consultant"
msgstr "Berater"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_1
#: model:product.template,name:software_reseller.product_product_7_product_template
#: model:project.project,name:software_reseller.project_project_4
msgid "Consulting Services"
msgstr "Beratungsleistungen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 200 hours to plan, in the planning"
msgstr "200 zu planenden STunden werden in der Planung erstellt."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 3 tasks to track services"
msgstr "3 Aufgaben zur Verfolgung der Dienstleistungen werden erstellt."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Create a <strong><font class=\"text-o-color-2\">subscription​</font></strong> to any customer with the <strong><font class=\"text-o-color-2\">product \"Oracle Database\"</font></strong><font class=\"text-o-color-2\">,</font> for 5 users for\n"
"            one year. Once you confirm this order, two documents will be created:"
msgstr ""
"Erstellen Sie ein <strong><font class=\"text-o-color-2\">Abonnement </font></strong>für einen Kunden mit dem <strong><font class=\"text-o-color-2\">Produkt „Oracle-Datenbank“</font></strong> <font class=\"text-o-color-2\"></font>für 5 Benutzer\n"
"            für ein Jahr. Sobald Sie diesen Auftrag bestätigen, werden zwei Dokumente erstellt:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create a task to track the license"
msgstr "Eine Aufgabe zur Verfolgung der Lizenz wird erstellt."

#. module: software_reseller
#: model:account.analytic.plan,name:software_reseller.account_analytic_plan_1
msgid "Default"
msgstr "Standard"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_27
msgid "Deployed"
msgstr "Implementiert"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_4
msgid "Deprecated"
msgstr "Nicht mehr verwendet"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_2
msgid "Developer"
msgstr "Entwickler"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_25
msgid "Development"
msgstr "Entwicklung"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_2
#: model:product.template,name:software_reseller.product_product_8_product_template
#: model:project.project,name:software_reseller.project_project_5
msgid "Development Services"
msgstr "Entwicklungsservices"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_23
msgid "Done"
msgstr "Erledigt"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Environement: Production"
msgstr "Umgebung: Produktion"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 1: Sell Oracle Licenses"
msgstr "Ablauf 1: Oracle-Lizenzen verkaufen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 2: Manage your licenses"
msgstr "Ablauf: Ihre Lizenzen verwalten"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Renew a license"
msgstr "Ablauf 3: eine Lizenz erneuern"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Selling Odoo with Services"
msgstr "Ablauf 3: Odoo mit Services verkaufen"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_3
msgid "Free to Use"
msgstr "Verfügbar"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From a license key:"
msgstr "Über einen Lizenzschlüssel:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the <strong><font class=\"text-o-color-2\">app "
"\"Licenses\"</font></strong>, you an have an overview of all your software "
"licenses. Click on one specific license to track license keys belonging to "
"each customer."
msgstr ""
"Über die <strong><font class=\"text-o-color-2\">Lizenzenapp</font></strong> "
"haben Sie einen Überblick über alle Software-Lizenzen. Klicken Sie auf eine "
"spezifische Lizenz, um die Lizenzschlüssel jedes Kunden zu verfolgen."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From the planning, click on the \"Plan Existing\" icon;"
msgstr "Klicken Sie in der Planung auf das Symbol „Bestehende planen“."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the subscription, using the top buttons, jump to the purchase order to buy these licenses to Oracle. You can send by email your request for quotation, then confirm the order. At that point, Oracle will send you the license\n"
"            number."
msgstr ""
"Gehen Sie im Abonnement über die oberen Schaltflächen zur Bestellung, um "
"diese Lizenzen bei Oracle zu kaufen. Sie können Ihre Angebotsanfrage per "
"E-Mail senden und dann die Bestellung bestätigen. Oracle wird ihnen dann die"
" Lizenznummer zusenden."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the timesheet app, or on the task, consultants can timesheet hours on the different phases of the project: Business Need Analysis, Odoo Configuration, or Training &amp; Support. This will be reflected as \"Delivered Quantity\" on\n"
"            the sale order lines."
msgstr ""
"Über die Zeiterfassungsapp oder direkt in der Aufgabe können Berater die "
"Stunden für die verschiedenen Phasen des Projekts erfassen: Bedarfsanalyse, "
"Odoo-Konfiguration oder Schulung und Support. Dies wird als „gelieferte "
"Menge“ in den Auftragszeilen angezeigt."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Go back to the sale order, then jump to the task. On the task, set the "
"different informations based on what oracle sends you:"
msgstr ""
"Gehen Sie zurück zum Verkaufsauftrag und dann zur Aufgabe. Legen Sie in der "
"Aufgabe die verschiedenen Informationen basierend auf dem fest, was Oracle "
"Ihnen sendet:"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_1
msgid "Implementation Services"
msgstr "Implementierungsservices"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_22
msgid "In Progress"
msgstr "In Bearbeitung"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_2
msgid "In Use"
msgstr "In Verwendung"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_6
#: model:project.project,name:software_reseller.project_project_6
msgid "Internal"
msgstr "Intern"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_2_product_template
msgid "Java EE License"
msgstr "Java-EE-Lizenz"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_3
#: model:project.project,name:software_reseller.project_project_1
msgid "Java Licenses"
msgstr "Java-Lizenzen"

#. module: software_reseller
#: model:ir.model.fields,field_description:software_reseller.x_is_license_field
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "License"
msgstr "Lizenz"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "License Key: XYZ"
msgstr "Lizenzschlüssel: XYZ"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_8482a8f5-a077-46b1-8007-6efe437a9245
#: model:ir.ui.menu,name:software_reseller.licenses_by_software_597c3ae4-8ff8-4785-9768-957e61596e95
#: model:project.project,label_tasks:software_reseller.project_project_1
#: model:project.project,label_tasks:software_reseller.project_project_2
#: model:project.project,label_tasks:software_reseller.project_project_3
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_5
#: model:uom.category,name:software_reseller.uom_category_8
msgid "Licenses"
msgstr "Lizenzen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Licenses as managed as tasks in the project management app, with custom properties; license key, number of users, software version, ... these properties depend on the type of software sold (i.e. Odoo is sold based on the number of\n"
"            users periodically, but Oracle requires a lot of information: number of CPUs, number of developer seats, size of DB, etc)"
msgstr ""
"Lizenzen werden in der Projektmanagementapp als Aufgaben verwaltet, mit "
"benutzerdefinierten Eigenschaften; Lizenzschlüssel, Anzahl der Benutzer, "
"Softwareversion ... diese Eigenschaften hängen von der Art der verkauften "
"Software ab (d. h. Odoo wird auf der Grundlage der Anzahl der Benutzer in "
"regelmäßigen Abständen verkauft, aber Oracle erfordert eine Vielzahl von "
"Informationen: Anzahl der CPUs, Anzahl der Entwickler, Größe der Datenbank "
"usw.)."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses by Software (in kanban)"
msgstr "Lizenzen je Software (in Kanban)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses to Renew: things you have to check periodically"
msgstr "Zu erneuernde Lizenzen: Dinge, die Sie regemäßig prüfen müssen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "Make the project visible in the Licences app"
msgstr "Zeigen Sie das Projekt in der Lizenzen-App an"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_31
msgid "Month"
msgstr "Monat"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_20
msgid "New"
msgstr "Neu"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_1
msgid "New Requests"
msgstr "Neue Anfragen"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_3
msgid "Odoo Configuration"
msgstr "Odoo-Konfiguration"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_3_product_template
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_6
msgid "Odoo EE License"
msgstr "Odoo-EE-Lizenz"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_4
#: model:project.project,name:software_reseller.project_project_2
msgid "Odoo Licenses"
msgstr "Odoo-Lizenzen"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_1
msgid "Odoo Standard Implementation"
msgstr "Odoo-Standardimplementierung"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_1_product_template
msgid "Oracle Database License"
msgstr "Oracle-Datenbanklizenz"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_5
#: model:project.project,name:software_reseller.project_project_3
msgid "Oracle Database Licenses"
msgstr "Oracle-Datenbanklizenzen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Record all information related to the license (version, type of license, "
"...). These information are different from one license type to another "
"(Oracle vs Odoo)"
msgstr ""
"Notieren Sie alle Informationen im Zusammenhang mit der Lizenz (Version, "
"Lizenztyp usw.). Diese Informationen unterscheiden sich je nach Lizenztyp "
"(Oracle vs. Odoo)."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Responsible: your account manager / user"
msgstr "Verantwortlich: Ihr Kundenbetreuer/Benutzer"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Select the services to plan \"Business Need Analysis\" and \"Odoo "
"Configuration\" (training will be planned later on, on phase 2)"
msgstr ""
"Wählen Sie die Dienstleistungen aus, um „Analyse der Geschäftsbedürfnisse“ "
"und „Odoo-Konfiguration“ zu planen (die Schulung wird später in Phase 2 "
"geplant)."

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_licenese_01632710-57f0-4ec5-9e2e-e975cfbc0406
msgid "Software"
msgstr "Software"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Software Resellers, IT Services"
msgstr "Software-Händler, IT-Services"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_24
msgid "Specification"
msgstr "Spezifikation"

#. module: software_reseller
#: model:project.project,label_tasks:software_reseller.project_project_4
#: model:project.project,label_tasks:software_reseller.project_project_5
#: model:project.project,label_tasks:software_reseller.project_project_6
msgid "Tasks"
msgstr "Aufgaben"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_26
msgid "Testing"
msgstr "Testen"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "The <strong>Licenses</strong> app has 3 menus:"
msgstr "Die <strong>Lizenzen</strong>-App hat 3 Menüs:"

#. module: software_reseller
#: model_terms:ir.actions.act_window,help:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "This is where you manage your software licenses."
msgstr "Dort verwalten Sie alle Ihre Software-Lizenzen."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"To renew a license, you should just renew the subscription; the task remains"
" the same."
msgstr ""
"Um eine Lizenz zu erneuern, sollten Sie einfach das Abonnement erneuern. Die"
" Aufgabe bleibt dieselbe."

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_4
msgid "Training & Support"
msgstr "Schulung & Support"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_28
msgid "User / Month"
msgstr "Benutzer/Monat"

#. module: software_reseller
#: model:uom.category,name:software_reseller.uom_category_7
msgid "User Licenses"
msgstr "Benutzerlizenzen"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_27
msgid "Users / Year"
msgstr "Benutzer/Jahr"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Usually, you sell the software licenses with additional services. To test this flow, as you create a quotation, use the quotation template\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">\"Odoo Standard Implementation\"</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">. That will add the services billed on timesheets (default setup: sell days, but timesheets per hour).</span></font>\n"
"            </strong>"
msgstr ""
"Normalerweise verkaufen Sie die Softwarelizenzen zusammen mit zusätzlichen Dienstleistungen. Um diesen Ablauf zu testen, verwenden Sie beim Erstellen eines Angebots die Angebotsvorlage\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">„Odoo-Standardimplementierung“</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">. Dadurch werden die in Rechnung gestellten Dienstleistungen auf Zeiterfassungsbögen hinzugefügt (Standardeinstellung: Verkauf von Tagen, aber Zeiterfassung pro Stunde).</span></font>\n"
"            </strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Version: 20.0.1"
msgstr "Version: 20.0.1"

#. module: software_reseller
#: model_terms:web_tour.tour,rainbow_man_message:software_reseller.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Willkommen! Viel Spaß beim Erkunden!"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_29
msgid "Year"
msgstr "Jahr"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_2
msgid "Yearly"
msgstr "Jährlich"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"You can communicate with customers on the chatter, to keep an history of the"
" discussions"
msgstr ""
"Sie können im Chatter mit Kunden kommunizieren, um einen Verlauf der "
"Unterhaltung zu erhalten."
