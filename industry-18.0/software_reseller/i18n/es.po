# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* software_reseller
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:52+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_3
#: model:uom.uom,name:software_reseller.uom_uom_30
msgid "3 Years"
msgstr "3 años"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_4
msgid "5 Years"
msgstr "5 años"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: inherit;\">Create a purchase order to buy "
"the Odoo license to Odoo S.A.</font>"
msgstr ""
"<font style=\"background-color: inherit;\">Se crea un pedido de compra para "
"comprar la licencia de Odoo a Odoo S.A.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 231, 206);\">A studio automated "
"action turn tasks in Changes Requested when the license key expires in the "
"15 days or less.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 231, 206);\">La acción automatizada"
" de Studio convierte las tareas en cambios solicitados cuando la clave de la"
" licencia vence en 15 días o menos.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 239, 198);\">From the \"Optional "
"Products\" tab, you can also add development services ine one click.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 239, 198);\">En la pestaña "
"“Productos opcionales” también puede añadir servicios de desarrollo con un "
"solo clic.</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">On "
"this order, the way to manage licenses is similar to the above section. So, "
"we'll mostly focus on the delivery of the extra services.</font><br/>"
msgstr ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">La "
"forma de gestionar las licencias en este pedido es similar a la sección "
"anterior. Aquí nos enfocaremos en la entrega de los servicios "
"adicionales.</font><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                This setup if for IT companies reselling software licenses, and consulting services. The typical sale is a 1 year Oracle Database license that is purchased to Oracle, and resold to client at a margin, with extra services to\n"
"                setup the database.\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"               Esta configuración es para empresas de TI que venden licencias de software y servicios de consultoría. Una venta común es una licencia de una base de datos de Oracle con una duración de 1 año, esta se compra a Oracle para vendérsela a un cliente y obtener un margen de ganancia, además de otros servicios para\n"
"                configurar la base de datos.\n"
"            </span>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\">Software Resellers, IT Services</span><br/>"
msgstr ""
"<span style=\"font-size: 36px;\">Distribuidores de software, servicios de "
"TI</span><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Licenses</u></strong>"
msgstr "<strong><u>Facturar licencias</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Projects</u></strong>"
msgstr "<strong><u>Facturar proyectos</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Plan Consultants</u></strong>"
msgstr "<strong><u>Planificar consultores</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Purchase the licenses</u></strong>"
msgstr "<strong><u>Comprar las licencias</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell Oracle Licenses</u></strong>"
msgstr "<strong><u>Vender las licencias de Oracle</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell a project</u></strong>"
msgstr "<strong><u>Vender un proyecto</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Timesheet Work done</u></strong>"
msgstr "<strong><u>Registrar el trabajo realizado</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A purchase order to Oracle; to buy the license for this client"
msgstr ""
"Un pedido de compra a Oracle, para comprar la licencia para este cliente"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A task to manage the license, in stage \"New Requests\""
msgstr "Una tarea en la etapa “Nuevas solicitudes” para gestionar la licencia"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.all_licenses_ce314e8b-2439-473a-b7a7-493af7707108
#: model:ir.ui.menu,name:software_reseller.licenses_all_license_17e01089-b423-4963-bf8e-1eb6e86152b8
msgid "All Licenses"
msgstr "Todas las licencias"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "All Licenses (default to list)"
msgstr "Todas las licencias (por defecto en lista)."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "As you confirm the order, it will:"
msgstr "Al confirmar el pedido:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"As you confirm the order, the license line is blue as you can already invoice it. The consulting services are black as there is nothing to invoice; you'll be able to invoice at the end of the month, based on the time spent on the\n"
"            project."
msgstr ""
"Al confirmar el pedido, la línea de la licencia aparecerá en azul porque puede facturarla. Los servicios de consultoría aparecerán en negro porque no hay nada que facturar, pero podrá facturarlos al final del mes, según el tiempo dedicado al\n"
"            proyecto."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Assign these services to the right person."
msgstr "Asigne estos servicios a la persona adecuada."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"At the end of the month, salespeople go to the menu \"Orders to Invoice\" in"
" Sales app. From there they can select an order (or select all), and invoice"
" what has been delivered on the order."
msgstr ""
"Al final del mes, los comerciales deben acceder al menú “Pedidos a "
"facturar\" en la aplicación Ventas. Desde ahí pueden seleccionar un pedido "
"(o seleccionar todos) y facturar lo que entregaron en el pedido."

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_21
msgid "Backlog"
msgstr "Pendientes"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flow: Selling Services"
msgstr "Flujo empresarial: venta de servicios"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flows: Licenses Management"
msgstr "Flujo empresarial: gestión de licencias"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_2
msgid "Business Needs analysis"
msgstr "Análisis de las necesidades empresariales"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "By software"
msgstr "Por software"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_1
msgid "Consultant"
msgstr "Consultor"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_1
#: model:product.template,name:software_reseller.product_product_7_product_template
#: model:project.project,name:software_reseller.project_project_4
msgid "Consulting Services"
msgstr "Servicios de consultoría"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 200 hours to plan, in the planning"
msgstr "Se crean 200 horas a planificar en la planificación."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 3 tasks to track services"
msgstr "Se crean 3 tareas para llevar seguimiento de los servicios."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Create a <strong><font class=\"text-o-color-2\">subscription​</font></strong> to any customer with the <strong><font class=\"text-o-color-2\">product \"Oracle Database\"</font></strong><font class=\"text-o-color-2\">,</font> for 5 users for\n"
"            one year. Once you confirm this order, two documents will be created:"
msgstr ""
"Cree una <strong><font class=\"text-o-color-2\">suscripción​</font></strong> para cualquier cliente con el <strong><font class=\"text-o-color-2\">producto “Base de datos de Oracle”</font></strong><font class=\"text-o-color-2\">,</font> para 5 usuarios durante\n"
"            un año. Una vez que confirme este pedido se crearán dos documentos:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create a task to track the license"
msgstr "Se crea una tarea para llevar seguimiento de la licencia."

#. module: software_reseller
#: model:account.analytic.plan,name:software_reseller.account_analytic_plan_1
msgid "Default"
msgstr "Por defecto"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_27
msgid "Deployed"
msgstr "Desplegado"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_4
msgid "Deprecated"
msgstr "Obsoleto"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_2
msgid "Developer"
msgstr "Desarrollador"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_25
msgid "Development"
msgstr "Desarrollo"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_2
#: model:product.template,name:software_reseller.product_product_8_product_template
#: model:project.project,name:software_reseller.project_project_5
msgid "Development Services"
msgstr "Servicios de desarrollo"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_23
msgid "Done"
msgstr "Hecho"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Environement: Production"
msgstr "Entorno: producción"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 1: Sell Oracle Licenses"
msgstr "Flujo 1: vender las licencias de Oracle"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 2: Manage your licenses"
msgstr "Flujo 2: gestionar sus licencias"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Renew a license"
msgstr "Flujo 3: renovar una licencia"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Selling Odoo with Services"
msgstr "Flujo 3: vender Odoo con servicios"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_3
msgid "Free to Use"
msgstr "Disponible"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From a license key:"
msgstr "Desde una clave de licencia:"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the <strong><font class=\"text-o-color-2\">app "
"\"Licenses\"</font></strong>, you an have an overview of all your software "
"licenses. Click on one specific license to track license keys belonging to "
"each customer."
msgstr ""
"Desde la aplicación <strong><font class=\"text-o-"
"color-2\">Licencias</font></strong> puede consultar la información general "
"de todas sus licencias de software. Haga clic en una para rastrear las "
"claves de licencia que pertenecen a cada cliente."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From the planning, click on the \"Plan Existing\" icon;"
msgstr "Haga clic en el icono “Planificar existente” de la planificación."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the subscription, using the top buttons, jump to the purchase order to buy these licenses to Oracle. You can send by email your request for quotation, then confirm the order. At that point, Oracle will send you the license\n"
"            number."
msgstr ""
"Vaya a la suscripción y con los botones en la parte superior acceda al pedido de compra para comprar estas licencias a Oracle. Puede enviar su solicitud de presupuesto por correo electrónico, luego confirme el pedido. Oracle le enviará el número de\n"
"            licencia."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the timesheet app, or on the task, consultants can timesheet hours on the different phases of the project: Business Need Analysis, Odoo Configuration, or Training &amp; Support. This will be reflected as \"Delivered Quantity\" on\n"
"            the sale order lines."
msgstr ""
"En Partes de horas o en la tarea, los consultores pueden registrar las horas trabajadas en las distintas fases del proyecto, como análisis de las necesidades empresariales, configuración de Odoo o formación y soporte. Este tiempo aparecerá como “Cantidad entregada” en\n"
"            las líneas del pedido de venta."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Go back to the sale order, then jump to the task. On the task, set the "
"different informations based on what oracle sends you:"
msgstr ""
"Regrese al pedido de venta y luego diríjase a la tarea. Agregue la "
"información a la tarea según lo que Oracle le envíe:"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_1
msgid "Implementation Services"
msgstr "Servicios de implementación"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_22
msgid "In Progress"
msgstr "En proceso"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_2
msgid "In Use"
msgstr "En uso"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_6
#: model:project.project,name:software_reseller.project_project_6
msgid "Internal"
msgstr "Interno"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_2_product_template
msgid "Java EE License"
msgstr "Licencia Java EE"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_3
#: model:project.project,name:software_reseller.project_project_1
msgid "Java Licenses"
msgstr "Licencias de Java"

#. module: software_reseller
#: model:ir.model.fields,field_description:software_reseller.x_is_license_field
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "License"
msgstr "Licencia"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "License Key: XYZ"
msgstr "Clave de licencia: XYZ"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_8482a8f5-a077-46b1-8007-6efe437a9245
#: model:ir.ui.menu,name:software_reseller.licenses_by_software_597c3ae4-8ff8-4785-9768-957e61596e95
#: model:project.project,label_tasks:software_reseller.project_project_1
#: model:project.project,label_tasks:software_reseller.project_project_2
#: model:project.project,label_tasks:software_reseller.project_project_3
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_5
#: model:uom.category,name:software_reseller.uom_category_8
msgid "Licenses"
msgstr "Licencias"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Licenses as managed as tasks in the project management app, with custom properties; license key, number of users, software version, ... these properties depend on the type of software sold (i.e. Odoo is sold based on the number of\n"
"            users periodically, but Oracle requires a lot of information: number of CPUs, number of developer seats, size of DB, etc)"
msgstr ""
"Las licencias se gestionan como tareas en la aplicación de gestión de proyectos, con propiedades personalizadas como la clave de licencia, el número de usuarios, la versión del software, etc. Estas propiedades dependen del tipo de software vendido. (Por ejemplo, Odoo se vende según el número de usuarios periódicamente,\n"
"             mientras que Oracle requiere mucha más información, como el número de CPU, el número de desarrolladores, el tamaño de la base de datos, etc.)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses by Software (in kanban)"
msgstr "Licencias por software (en kanban)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses to Renew: things you have to check periodically"
msgstr "Licencias a renovar: cosas que debe revisar de vez en cuando"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "Make the project visible in the Licences app"
msgstr "Muestre el proyecto en la aplicación Licencias"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_31
msgid "Month"
msgstr "Mes"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_20
msgid "New"
msgstr "Nuevo"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_1
msgid "New Requests"
msgstr "Nuevas solicitudes"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_3
msgid "Odoo Configuration"
msgstr "Configuración de Odoo"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_3_product_template
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_6
msgid "Odoo EE License"
msgstr "Licencia Odoo EE"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_4
#: model:project.project,name:software_reseller.project_project_2
msgid "Odoo Licenses"
msgstr "Licencias de Odoo"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_1
msgid "Odoo Standard Implementation"
msgstr "Implementación estándar de Odoo"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_1_product_template
msgid "Oracle Database License"
msgstr "Licencia de la base de datos de Oracle"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_5
#: model:project.project,name:software_reseller.project_project_3
msgid "Oracle Database Licenses"
msgstr "Licencias de la base de datos de Oracle"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Record all information related to the license (version, type of license, "
"...). These information are different from one license type to another "
"(Oracle vs Odoo)"
msgstr ""
"Registre toda la información relacionada con la licencia (versión, tipo de "
"licencia, etc.). Esta información es distinta entre un tipo de licencia y "
"otro (Oracle y Odoo)"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Responsible: your account manager / user"
msgstr "Responsable: su gerente de cuenta/usuario"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Select the services to plan \"Business Need Analysis\" and \"Odoo "
"Configuration\" (training will be planned later on, on phase 2)"
msgstr ""
"Seleccione los servicios a planificar: “Análisis de las necesidades "
"empresariales” y “Configuración de Odoo” (la formación se llevará a cabo en "
"la fase 2)."

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_licenese_01632710-57f0-4ec5-9e2e-e975cfbc0406
msgid "Software"
msgstr "Software"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Software Resellers, IT Services"
msgstr "Distribuidores de software, servicios de TI"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_24
msgid "Specification"
msgstr "Especificación"

#. module: software_reseller
#: model:project.project,label_tasks:software_reseller.project_project_4
#: model:project.project,label_tasks:software_reseller.project_project_5
#: model:project.project,label_tasks:software_reseller.project_project_6
msgid "Tasks"
msgstr "Tareas"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_26
msgid "Testing"
msgstr "Prueba"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "The <strong>Licenses</strong> app has 3 menus:"
msgstr "La aplicación <strong>Licencias</strong> tiene 3 menús:"

#. module: software_reseller
#: model_terms:ir.actions.act_window,help:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "This is where you manage your software licenses."
msgstr "Aquí gestionará sus licencias de software."

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"To renew a license, you should just renew the subscription; the task remains"
" the same."
msgstr ""
"Para renovar una licencia solo debe renovar la suscripción, la tarea sigue "
"siendo la misma."

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_4
msgid "Training & Support"
msgstr "Formación y soporte"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_28
msgid "User / Month"
msgstr "Usuario / Mes"

#. module: software_reseller
#: model:uom.category,name:software_reseller.uom_category_7
msgid "User Licenses"
msgstr "Licencias de usuario"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_27
msgid "Users / Year"
msgstr "Usuarios / Año"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Usually, you sell the software licenses with additional services. To test this flow, as you create a quotation, use the quotation template\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">\"Odoo Standard Implementation\"</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">. That will add the services billed on timesheets (default setup: sell days, but timesheets per hour).</span></font>\n"
"            </strong>"
msgstr ""
"Por lo general, cuando vende las licencias de software también vende servicios adicionales. Para probar este flujo, al crear un presupuesto, use la plantilla de presupuesto\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">“Implementación estándar de Odoo”</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">, esta añadirá los servicios facturados en los partes de horas (configuración por defecto: se venden días, pero las hojas se registran por hora).</span></font>\n"
"            </strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Version: 20.0.1"
msgstr "Versión: 20.0.1"

#. module: software_reseller
#: model_terms:web_tour.tour,rainbow_man_message:software_reseller.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "¡Bienvenido! Disfrute del sitio."

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_29
msgid "Year"
msgstr "Año"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_2
msgid "Yearly"
msgstr "Anualmente"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"You can communicate with customers on the chatter, to keep an history of the"
" discussions"
msgstr ""
"Puede comunicarse con los clientes desde el chatter para mantener un "
"historial de sus conversaciones."
