# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* software_reseller
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:52+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_3
#: model:uom.uom,name:software_reseller.uom_uom_30
msgid "3 Years"
msgstr "3 年"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_4
msgid "5 Years"
msgstr "5 年"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: inherit;\">Create a purchase order to buy "
"the Odoo license to Odoo S.A.</font>"
msgstr ""
"<font style=\"background-color: inherit;\">建立採購單，以向 Odoo S.A. 購買 Odoo "
"使用許可證</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 231, 206);\">A studio automated "
"action turn tasks in Changes Requested when the license key expires in the "
"15 days or less.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 231, 206);\">利用 Studio "
"自動化操作，在許可證密鑰將於 15 天內到期時，自動將任務轉入「請求變更」中。</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"background-color: rgb(255, 239, 198);\">From the \"Optional "
"Products\" tab, you can also add development services ine one click.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 239, "
"198);\">在「可選購產品」分頁中，你也可一鍵加入開發服務。</font>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<font style=\"color: inherit; background-color: rgb(255, 239, 198);\">On "
"this order, the way to manage licenses is similar to the above section. So, "
"we'll mostly focus on the delivery of the extra services.</font><br/>"
msgstr ""
"<font style=\"color: inherit; background-color: rgb(255, 239, "
"198);\">在此訂單上，管理許可證的方式與上一章節所述類似。因此，我們將重點主要放在如何提供額外服務。</font><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                This setup if for IT companies reselling software licenses, and consulting services. The typical sale is a 1 year Oracle Database license that is purchased to Oracle, and resold to client at a margin, with extra services to\n"
"                setup the database.\n"
"            </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                此設定適用於轉售軟件使用許可證，以及提供諮詢顧問服務的 IT 公司。常見的銷售會是向 Oracle 購買 1 年期 Oracle 資料庫使用許可證，然後以一定的利潤率轉售給客戶，並額外提供\n"
"                配置及設定資料庫的服務。\n"
"            </span>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\">Software Resellers, IT Services</span><br/>"
msgstr "<span style=\"font-size: 36px;\">軟件經銷商，IT 資訊科技服務</span><br/>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Licenses</u></strong>"
msgstr "<strong><u>發票許可證</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Invoice Projects</u></strong>"
msgstr "<strong><u>發票專案項目</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Plan Consultants</u></strong>"
msgstr "<strong><u>服務計劃顧問</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Purchase the licenses</u></strong>"
msgstr "<strong><u>購買許可證</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell Oracle Licenses</u></strong>"
msgstr "<strong><u>銷售 Oracle 許可證</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Sell a project</u></strong>"
msgstr "<strong><u>銷售專案</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "<strong><u>Timesheet Work done</u></strong>"
msgstr "<strong><u>將已完成工作記入工時表</u></strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A purchase order to Oracle; to buy the license for this client"
msgstr "向 Oracle 公司發出採購單，以為這名客戶購買許可證"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "A task to manage the license, in stage \"New Requests\""
msgstr "在「新請求」階段，建立管理許可證的任務"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.all_licenses_ce314e8b-2439-473a-b7a7-493af7707108
#: model:ir.ui.menu,name:software_reseller.licenses_all_license_17e01089-b423-4963-bf8e-1eb6e86152b8
msgid "All Licenses"
msgstr "所有許可證"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "All Licenses (default to list)"
msgstr "所有許可證（預設為列表）"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "As you confirm the order, it will:"
msgstr "當你確認訂單時，系統將會："

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"As you confirm the order, the license line is blue as you can already invoice it. The consulting services are black as there is nothing to invoice; you'll be able to invoice at the end of the month, based on the time spent on the\n"
"            project."
msgstr ""
"確認訂單後，許可證資料行會變成藍色，因為已經可以為它開立發票。顧問服務資料行則仍是黑色，因為沒有可開立發票的資料。你可在月底，根據專案所花費的工時，\n"
"            才開立發票。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Assign these services to the right person."
msgstr "將這些服務分配至合適的人員。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"At the end of the month, salespeople go to the menu \"Orders to Invoice\" in"
" Sales app. From there they can select an order (or select all), and invoice"
" what has been delivered on the order."
msgstr ""
"至月底時，銷售人員會前往銷售應用程式，點選「訂單至發票」選單。他們可在程式內選擇一張訂單（或選擇所有訂單），並為訂單上已完成送貨的商品開立發票。"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_21
msgid "Backlog"
msgstr "積壓工作"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flow: Selling Services"
msgstr "業務流程：銷售服務"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Business Flows: Licenses Management"
msgstr "業務流程：管理許可證"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_2
msgid "Business Needs analysis"
msgstr "業務需求分析"

#. module: software_reseller
#: model:ir.actions.act_window,name:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "By software"
msgstr "透過軟體"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_1
msgid "Consultant"
msgstr "顧問"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_1
#: model:product.template,name:software_reseller.product_product_7_product_template
#: model:project.project,name:software_reseller.project_project_4
msgid "Consulting Services"
msgstr "顧問服務"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 200 hours to plan, in the planning"
msgstr "在規劃中，建立 200 小時的待規劃工時"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create 3 tasks to track services"
msgstr "建立 3 個任務，以追蹤服務"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Create a <strong><font class=\"text-o-color-2\">subscription​</font></strong> to any customer with the <strong><font class=\"text-o-color-2\">product \"Oracle Database\"</font></strong><font class=\"text-o-color-2\">,</font> for 5 users for\n"
"            one year. Once you confirm this order, two documents will be created:"
msgstr ""
"為所有使用<strong><font class=\"text-o-color-2\">「Oracle 資料庫」產品</font></strong>的客戶，建立一個<strong><font class=\"text-o-color-2\">訂閱</font></strong>服務計劃<font class=\"text-o-color-2\"></font>，人數為 5 名使用者，\n"
"            為期 1 年。當你確認此訂單後，系統將建立兩份文件："

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Create a task to track the license"
msgstr "建立一個任務，以追蹤許可證"

#. module: software_reseller
#: model:account.analytic.plan,name:software_reseller.account_analytic_plan_1
msgid "Default"
msgstr "預設"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_27
msgid "Deployed"
msgstr "已部署"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_4
msgid "Deprecated"
msgstr "已棄用"

#. module: software_reseller
#: model:planning.role,name:software_reseller.planning_role_2
msgid "Developer"
msgstr "開發人員"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_25
msgid "Development"
msgstr "開發"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_2
#: model:product.template,name:software_reseller.product_product_8_product_template
#: model:project.project,name:software_reseller.project_project_5
msgid "Development Services"
msgstr "開發服務"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_23
msgid "Done"
msgstr "完成"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Environement: Production"
msgstr "環境：正式運行"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 1: Sell Oracle Licenses"
msgstr "流程 1：銷售 Oracle 許可證"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 2: Manage your licenses"
msgstr "流程 2：管理你的許可證"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Renew a license"
msgstr "流程 3：許可證續期"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Flow 3: Selling Odoo with Services"
msgstr "流程 3：銷售 Odoo 系統及服務"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_3
msgid "Free to Use"
msgstr "可用"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From a license key:"
msgstr "使用許可證密鑰："

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the <strong><font class=\"text-o-color-2\">app "
"\"Licenses\"</font></strong>, you an have an overview of all your software "
"licenses. Click on one specific license to track license keys belonging to "
"each customer."
msgstr ""
"在<strong><font class=\"text-o-"
"color-2\">「許可證」應用程式</font></strong>中，你可概覽全部軟體許可證。按一下某個許可證，便可追蹤屬於每名客戶的許可證密鑰。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "From the planning, click on the \"Plan Existing\" icon;"
msgstr "在規劃中，按一下「現有規劃」圖示；"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the subscription, using the top buttons, jump to the purchase order to buy these licenses to Oracle. You can send by email your request for quotation, then confirm the order. At that point, Oracle will send you the license\n"
"            number."
msgstr ""
"在訂閱資料頁面，使用頂部按鈕跳至採購單，以向 Oracle 購買使用許可證。你可透過電郵發送報價請求，然後確認訂單。\n"
"            屆時，Oracle 將向你發送許可證號碼。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"From the timesheet app, or on the task, consultants can timesheet hours on the different phases of the project: Business Need Analysis, Odoo Configuration, or Training &amp; Support. This will be reflected as \"Delivered Quantity\" on\n"
"            the sale order lines."
msgstr ""
"在工時表應用程式或任務中，顧問可為專案項目不同階段所花費的工時，編製工時表並用作收費，例如：業務需求分析、配置 Odoo 系統、培訓及支援等。相關工時會在銷售單資料行上，\n"
"            顯示為「已交付數量」。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Go back to the sale order, then jump to the task. On the task, set the "
"different informations based on what oracle sends you:"
msgstr "返回銷售單，然後前往該任務。在任務中，根據 Oracle 發送給你的資料，設定不同資訊："

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_1
msgid "Implementation Services"
msgstr "實施服務"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_22
msgid "In Progress"
msgstr "進行中"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_2
msgid "In Use"
msgstr "使用中"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_6
#: model:project.project,name:software_reseller.project_project_6
msgid "Internal"
msgstr "內部"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_2_product_template
msgid "Java EE License"
msgstr "Java 企業版許可證"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_3
#: model:project.project,name:software_reseller.project_project_1
msgid "Java Licenses"
msgstr "Java 許可證"

#. module: software_reseller
#: model:ir.model.fields,field_description:software_reseller.x_is_license_field
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "License"
msgstr "許可"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "License Key: XYZ"
msgstr "許可證密鑰：XYZ"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_8482a8f5-a077-46b1-8007-6efe437a9245
#: model:ir.ui.menu,name:software_reseller.licenses_by_software_597c3ae4-8ff8-4785-9768-957e61596e95
#: model:project.project,label_tasks:software_reseller.project_project_1
#: model:project.project,label_tasks:software_reseller.project_project_2
#: model:project.project,label_tasks:software_reseller.project_project_3
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_5
#: model:uom.category,name:software_reseller.uom_category_8
msgid "Licenses"
msgstr "許可證"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Licenses as managed as tasks in the project management app, with custom properties; license key, number of users, software version, ... these properties depend on the type of software sold (i.e. Odoo is sold based on the number of\n"
"            users periodically, but Oracle requires a lot of information: number of CPUs, number of developer seats, size of DB, etc)"
msgstr ""
"許可證在專案管理應用程式中，以「任務」形式管理，其屬性可自訂。許可證金鑰、使用者數目、軟體版本等屬性，視乎所銷售的軟體類型而有所不同。例如，Odoo 的銷售是根據\n"
"            使用者數目進行定期收費，而 Oracle 則需要提供大量資訊，包括中央處理器（CPU）數目、開發人員席位數目、資料庫容量大小等。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses by Software (in kanban)"
msgstr "許可證，按軟件劃分（在看板內）"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Licenses to Renew: things you have to check periodically"
msgstr "待續期許可證：須定期檢查的事項"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.project_project_form_customization
msgid "Make the project visible in the Licences app"
msgstr "在許可證應用程式中顯示專案"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_31
msgid "Month"
msgstr "月"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_20
msgid "New"
msgstr "新增"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_1
msgid "New Requests"
msgstr "新請求"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_3
msgid "Odoo Configuration"
msgstr "Odoo 配置"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_3_product_template
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_6
msgid "Odoo EE License"
msgstr "Odoo 企業版許可證"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_4
#: model:project.project,name:software_reseller.project_project_2
msgid "Odoo Licenses"
msgstr "Odoo 許可證"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_1
msgid "Odoo Standard Implementation"
msgstr "Odoo 標準實施"

#. module: software_reseller
#: model:product.template,name:software_reseller.product_product_1_product_template
msgid "Oracle Database License"
msgstr "Oracle 資料庫許可證"

#. module: software_reseller
#: model:account.analytic.account,name:software_reseller.account_analytic_account_5
#: model:project.project,name:software_reseller.project_project_3
msgid "Oracle Database Licenses"
msgstr "Oracle 資料庫許可證"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Record all information related to the license (version, type of license, "
"...). These information are different from one license type to another "
"(Oracle vs Odoo)"
msgstr "記錄與許可證相關的所有資訊（例如：版本、許可證類型等）。這些資訊會因應許可證類型，而有所不同（例如：Oracle 對比 Odoo）。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Responsible: your account manager / user"
msgstr "負責人：你的客戶經理/使用者"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Select the services to plan \"Business Need Analysis\" and \"Odoo "
"Configuration\" (training will be planned later on, on phase 2)"
msgstr "選擇適用服務，以規劃「業務需求分析」及「Odoo 配置」（培訓將在稍後的第 2 階段才規劃）"

#. module: software_reseller
#: model:ir.ui.menu,name:software_reseller.licenses_licenese_01632710-57f0-4ec5-9e2e-e975cfbc0406
msgid "Software"
msgstr "軟體"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Software Resellers, IT Services"
msgstr "軟件經銷商，IT 資訊科技服務"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_24
msgid "Specification"
msgstr "規格"

#. module: software_reseller
#: model:project.project,label_tasks:software_reseller.project_project_4
#: model:project.project,label_tasks:software_reseller.project_project_5
#: model:project.project,label_tasks:software_reseller.project_project_6
msgid "Tasks"
msgstr "任務"

#. module: software_reseller
#: model:project.task.type,name:software_reseller.project_task_type_26
msgid "Testing"
msgstr "測試"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "The <strong>Licenses</strong> app has 3 menus:"
msgstr "<strong>許可證</strong>應用程式有 3 個選單："

#. module: software_reseller
#: model_terms:ir.actions.act_window,help:software_reseller.licenese_50da6821-c954-4104-b2bf-1a1a498da4de
msgid "This is where you manage your software licenses."
msgstr "此處讓你管理軟體許可證。"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"To renew a license, you should just renew the subscription; the task remains"
" the same."
msgstr "要為許可證續期，只需續訂即可，相關任務應保持不變。"

#. module: software_reseller
#: model:sale.order.template.line,name:software_reseller.sale_order_template_line_4
msgid "Training & Support"
msgstr "培訓及支援"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_28
msgid "User / Month"
msgstr "使用者 / 每月"

#. module: software_reseller
#: model:uom.category,name:software_reseller.uom_category_7
msgid "User Licenses"
msgstr "使用者許可證"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_27
msgid "Users / Year"
msgstr "使用者 / 每年"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"Usually, you sell the software licenses with additional services. To test this flow, as you create a quotation, use the quotation template\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">\"Odoo Standard Implementation\"</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">. That will add the services billed on timesheets (default setup: sell days, but timesheets per hour).</span></font>\n"
"            </strong>"
msgstr ""
"銷售軟件使用許可證時，通常會提供附加服務。要測試此流程，請在建立報價單時，使用\n"
"            <strong>\n"
"                <font class=\"text-o-color-2\">「Odoo 標準安裝」</font>\n"
"                <font style=\"color: inherit;\"><span style=\"font-weight: normal;\">報價單範本。此操作會加入按工時表收費的服務（預設設定：以日數為單位銷售，但按每小時工作記錄工時表）。</span></font>\n"
"            </strong>"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid "Version: 20.0.1"
msgstr "版本：20.0.1"

#. module: software_reseller
#: model_terms:web_tour.tour,rainbow_man_message:software_reseller.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "歡迎！請享受探索過程。"

#. module: software_reseller
#: model:uom.uom,name:software_reseller.uom_uom_29
msgid "Year"
msgstr "年"

#. module: software_reseller
#: model:sale.subscription.plan,name:software_reseller.sale_subscription_plan_2
msgid "Yearly"
msgstr "每年"

#. module: software_reseller
#: model_terms:ir.ui.view,arch_db:software_reseller.welcome_article_body
msgid ""
"You can communicate with customers on the chatter, to keep an history of the"
" discussions"
msgstr "你可使用聊天視窗，與客戶溝通交流，並保留對話內容記錄"
