<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="purchase.order" name="button_confirm" eval="[
        (
            obj().env.ref('software_reseller.sale_order_1') + obj().env.ref('software_reseller.sale_order_5')
        ).order_line.purchase_line_ids.order_id.ids]"/>

    <function model="purchase.order" name="action_create_invoice" eval="[
        obj().env.ref('software_reseller.sale_order_1').order_line.purchase_line_ids.order_id.ids]"/>

</odoo>
