<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_1" model="sale.order">
        <field name="partner_id" ref="res_partner_12" />
        <field name="require_signature" eval="True" />
        <field name="plan_id" ref="sale_subscription_plan_2"/>
    </record>
    <record id="sale_order_2" model="sale.order">
        <field name="partner_id" ref="res_partner_12" />
        <field name="sale_order_template_id" ref="sale_order_template_1" />
        <field name="plan_id" ref="sale_subscription_plan_2"/>
    </record>
    <record id="sale_order_3" model="sale.order">
        <field name="partner_id" ref="res_partner_12" />
        <field name="sale_order_template_id" ref="sale_order_template_1" />
        <field name="plan_id" ref="sale_subscription_plan_2"/>
    </record>
    <record id="sale_order_4" model="sale.order">
        <field name="partner_id" ref="res_partner_11" />
        <field name="require_signature" eval="True" />
    </record>
    <record id="sale_order_5" model="sale.order">
        <field name="partner_id" ref="res_partner_12" />
        <field name="require_signature" eval="True" />
        <field name="plan_id" ref="sale_subscription_plan_2"/>
    </record>
    <record id="sale_order_6" model="sale.order">
        <field name="partner_id" ref="res_partner_12" />
        <field name="sale_order_template_id" ref="sale_order_template_1" />
        <field name="plan_id" ref="sale_subscription_plan_2"/>
    </record>
    <record id="sale_order_7" model="sale.order">
        <field name="partner_id" ref="res_partner_11" />
        <field name="require_signature" eval="True" />
        <field name="subscription_state">1_draft</field>
        <field name="plan_id" ref="sale_subscription_plan_2"/>
    </record>
    <record id="sale_order_8" model="sale.order">
        <field name="partner_id" ref="res_partner_11" />
        <field name="require_signature" eval="True" />
        <field name="next_invoice_date" eval="DateTime.today().date()"/>
        <field name="first_contract_date" eval="DateTime.today().date()"/>
        <field name="start_date" eval="DateTime.today().date()"/>
        <field name="end_date" eval="DateTime.today().date() + relativedelta(months=2)"/>
        <field name="close_reason_id" ref="sale_subscription.close_reason_auto_close_limit_reached" />
        <field name="subscription_state">6_churn</field>
        <field name="sale_order_template_id" ref="sale_order_template_2" />
    </record>
</odoo>
