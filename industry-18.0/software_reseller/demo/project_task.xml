<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_task_1" model="project.task">
        <field name="name">Wavestone v16</field>
        <field name="stage_id" ref="project_task_type_2" />
        <field name="user_ids" eval="[Command.set([ref('base.user_admin')])]"/>
        <field name="project_id" ref="project_project_2" />
        <field name="date_assign" eval="DateTime.today()"/>
        <field name="date_last_stage_update" eval="DateTime.today()"/>
        <field name="date_deadline" eval="DateTime.today().date() - relativedelta(days=1)"/>
        <field name="state">02_changes_requested</field>
        <field name="task_properties" eval="{'14e63001c39f0a7c': 100, '19460db267e8e4c3': '0acee79ccc0c1c8c', '33ae81dcafa5961b': 'OM0210210210201201', '3b2b9964284a7f63': 7, '5238d6796e605ce9': '280c6b03bf099390'}" />
    </record>
    <record id="project_task_10" model="project.task">
        <field name="name">Training</field>
        <field name="project_id" ref="project_project_6" />
        <field name="date_last_stage_update" eval="DateTime.today()"/>
    </record>
    <record id="project_task_11" model="project.task">
        <field name="name">Meeting</field>
        <field name="project_id" ref="project_project_6" />
        <field name="date_last_stage_update" eval="DateTime.today()"/>
        <field name="effective_hours">8.0</field>
        <field name="remaining_hours">-8.0</field>
        <field name="total_hours_spent">8.0</field>
    </record>
    <record id="project_task_2" model="project.task">
        <field name="name">Wavestone SE 20</field>
        <field name="user_ids" eval="[Command.set([ref('base.user_admin')])]"/>
        <field name="stage_id" ref="project_task_type_1" />
        <field name="project_id" ref="project_project_1" />
        <field name="date_assign" eval="DateTime.today() - relativedelta(days=1)"/>
        <field name="date_last_stage_update" eval="DateTime.today() - relativedelta(days=1)"/>
        <field name="task_properties" eval="{'0199a697eee37d67': False, '92523a6947203790': '20', 'b2e92295f220408e': False}" />
    </record>
    <record id="project_task_3" model="project.task">
        <field name="name">Wavestone</field>
        <field name="sequence">11</field>
        <field name="stage_id" ref="project_task_type_2" />
        <field name="user_ids" eval="[Command.set([ref('base.user_admin')])]"/>
        <field name="project_id" ref="project_project_3" />
        <field name="date_assign" eval="DateTime.today() - relativedelta(days=1)"/>
        <field name="date_last_stage_update" eval="DateTime.today()"/>
        <field name="date_deadline" eval="DateTime.today().date() + relativedelta(years=1)"/>
        <field name="task_properties" eval="{'2c8f32560151873d': 7, '3e2f0ae0a826c70f': 'f5bd74aad416df9b', 'c02448c8c029a4a9': False, 'cf426b7354e473b8': '6.5.1'}" />
    </record>
    <record id="project_task_4" model="project.task">
        <field name="name">Camptocamp</field>
        <field name="user_ids" eval="[Command.set([ref('base.user_admin')])]"/>
        <field name="stage_id" ref="project_task_type_2" />
        <field name="project_id" ref="project_project_2" />
        <field name="date_assign" eval="DateTime.today()"/>
        <field name="date_last_stage_update" eval="DateTime.today()"/>
        <field name="date_deadline" eval="DateTime.today().date() + relativedelta(years=1)"/>
        <field name="task_properties" eval="{'14e63001c39f0a7c': 80, '19460db267e8e4c3': '0acee79ccc0c1c8c', '33ae81dcafa5961b': '210820180218210', '3b2b9964284a7f63': 11, '5238d6796e605ce9': 'd876b1557301d0ef'}" />
    </record>
</odoo>
