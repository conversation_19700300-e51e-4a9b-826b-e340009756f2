<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_line_1" model="sale.order.line">
        <field name="product_id" ref="product_product_1" />
        <field name="price_unit">7600.0</field>
        <field name="product_uom_qty">20.0</field>
        <field name="product_uom" ref="uom_uom_27" />
        <field name="order_id" ref="sale_order_1" />
    </record>
    <record id="sale_order_line_10" model="sale.order.line">
        <field name="name">Odoo Configuration</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">750.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_3" />
    </record>
    <record id="sale_order_line_11" model="sale.order.line">
        <field name="name">Training &amp; Support</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">750.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_3" />
    </record>
    <record id="sale_order_line_12" model="sale.order.line">
        <field name="name">Licenses</field>
        <field name="display_type">line_section</field>
        <field name="order_id" ref="sale_order_3" />
    </record>
    <record id="sale_order_line_13" model="sale.order.line">
        <field name="product_id" ref="product_product_3" />
        <field name="price_unit">228.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom_uom_27" />
        <field name="order_id" ref="sale_order_3" />
    </record>
    <record id="sale_order_line_14" model="sale.order.line">
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">750.0</field>
        <field name="product_uom_qty">5.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_4" />
    </record>
    <record id="sale_order_line_15" model="sale.order.line">
        <field name="product_id" ref="product_product_1" />
        <field name="price_unit">8000.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom_uom_27" />
        <field name="order_id" ref="sale_order_5" />
    </record>
    <record id="sale_order_line_16" model="sale.order.line">
        <field name="name">Implementation Services</field>
        <field name="sequence">-99</field>
        <field name="display_type">line_section</field>
        <field name="order_id" ref="sale_order_6" />
    </record>
    <record id="sale_order_line_17" model="sale.order.line">
        <field name="name">Business Needs analysis</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">6000.0</field>
        <field name="product_uom_qty">5.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_6" />
    </record>
    <record id="sale_order_line_18" model="sale.order.line">
        <field name="name">Odoo Configuration</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">6000.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_6" />
    </record>
    <record id="sale_order_line_19" model="sale.order.line">
        <field name="name">Training &amp; Support</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">6000.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_6" />
    </record>
    <record id="sale_order_line_2" model="sale.order.line">
        <field name="name">Implementation Services</field>
        <field name="sequence">-99</field>
        <field name="display_type">line_section</field>
        <field name="order_id" ref="sale_order_2" />
    </record>
    <record id="sale_order_line_20" model="sale.order.line">
        <field name="name">Licenses</field>
        <field name="display_type">line_section</field>
        <field name="order_id" ref="sale_order_6" />
    </record>
    <record id="sale_order_line_21" model="sale.order.line">
        <field name="product_id" ref="product_product_3" />
        <field name="price_unit">228.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom_uom_27" />
        <field name="order_id" ref="sale_order_6" />
    </record>
    <record id="sale_order_line_22" model="sale.order.line">
        <field name="product_id" ref="product_product_2" />
        <field name="price_unit">400.0</field>
        <field name="product_uom" ref="uom_uom_27" />
        <field name="order_id" ref="sale_order_7" />
    </record>
    <record id="sale_order_line_23" model="sale.order.line">
        <field name="price_unit">190.0</field>
        <field name="product_id" ref="product_product_3" />
        <field name="product_uom" ref="uom_uom_27" />
        <field name="order_id" ref="sale_order_8" />
    </record>
    <record id="sale_order_line_3" model="sale.order.line">
        <field name="name">Business Needs analysis</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">750.0</field>
        <field name="product_uom_qty">5.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_2" />
    </record>
    <record id="sale_order_line_4" model="sale.order.line">
        <field name="name">Odoo Configuration</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">750.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_2" />
    </record>
    <record id="sale_order_line_5" model="sale.order.line">
        <field name="name">Training &amp; Support</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">750.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_2" />
    </record>
    <record id="sale_order_line_6" model="sale.order.line">
        <field name="name">Licenses</field>
        <field name="display_type">line_section</field>
        <field name="order_id" ref="sale_order_2" />
    </record>
    <record id="sale_order_line_7" model="sale.order.line">
        <field name="product_id" ref="product_product_3" />
        <field name="price_unit">228.0</field>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom" ref="uom_uom_27" />
        <field name="order_id" ref="sale_order_2" />
    </record>
    <record id="sale_order_line_8" model="sale.order.line">
        <field name="name">Implementation Services</field>
        <field name="sequence">-99</field>
        <field name="display_type">line_section</field>
        <field name="order_id" ref="sale_order_3" />
    </record>
    <record id="sale_order_line_9" model="sale.order.line">
        <field name="name">Business Needs analysis</field>
        <field name="product_id" ref="product_product_7" />
        <field name="price_unit">750.0</field>
        <field name="product_uom_qty">5.0</field>
        <field name="product_uom" ref="uom.product_uom_day" />
        <field name="order_id" ref="sale_order_3" />
    </record>
</odoo>
