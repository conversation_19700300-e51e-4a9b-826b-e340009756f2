<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_supplierinfo_1" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_9"/>
        <field name="product_tmpl_id" model="product.template" eval="obj().env.ref('software_reseller.product_product_1').product_tmpl_id.id"/>
        <field name="price">500.0</field>
    </record>
    <record id="product_supplierinfo_2" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_9"/>
        <field name="product_tmpl_id" model="product.template" eval="obj().env.ref('software_reseller.product_product_2').product_tmpl_id.id"/>
        <field name="price">200.0</field>
    </record>
    <record id="product_supplierinfo_3" model="product.supplierinfo">
        <field name="partner_id" ref="res_partner_10"/>
        <field name="product_tmpl_id" model="product.template" eval="obj().env.ref('software_reseller.product_product_3').product_tmpl_id.id"/>
        <field name="price">238.0</field>
    </record>
</odoo>
