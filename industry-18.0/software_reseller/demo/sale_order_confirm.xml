<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="sale.order" name="action_confirm" eval="[[
        ref('sale_order_1'),
        ref('sale_order_2'),
        ref('sale_order_3'),
        ref('sale_order_4'),
        ref('sale_order_5'),
        ref('sale_order_6'),
        ref('sale_order_8')]]"/>
    <record id="sale_advance_payment_inv_1" model="sale.advance.payment.inv">
        <field name="advance_payment_method" >delivered</field>
        <field name="sale_order_ids" eval="[Command.set([ref('sale_order_6')])]"/>
    </record>

    <function model="sale.advance.payment.inv" name="create_invoices"
            eval="[ref('sale_advance_payment_inv_1')]"/>

    <function model="project.task" name="write">
        <value model="project.task" eval="obj().env.ref('software_reseller.sale_order_line_15').task_id.ids"/>
        <value eval="{'user_ids': [Command.set([ref('base.user_admin')])]}"/>
    </function>
    <function model="project.task" name="write">
        <value model="project.task" eval="
            (
                obj().env.ref('software_reseller.sale_order_line_19') +
                obj().env.ref('software_reseller.sale_order_line_3') +
                obj().env.ref('software_reseller.sale_order_line_17')
            ).task_id.ids"/>
        <value eval="{'stage_id': ref('project_task_type_22')}"/>
    </function>

    <function model="project.task" name="write">
        <value model="project.task" eval="
            (
                obj().env.ref('software_reseller.sale_order_line_11') +
                obj().env.ref('software_reseller.sale_order_line_5') +
                obj().env.ref('software_reseller.sale_order_line_9')
            ).task_id.ids"/>
        <value eval="{'stage_id': ref('project_task_type_23')}"/>
    </function>

    <function model="project.task" name="write">
        <value model="project.task" eval="
            (
                obj().env.ref('software_reseller.sale_order_line_15') +
                obj().env.ref('software_reseller.sale_order_line_1')
            ).task_id.ids"/>
        <value eval="{'stage_id': ref('project_task_type_2')}"/>
    </function>

    <function model="project.task" name="write">
        <value model="project.task" eval="
            (
                obj().env.ref('software_reseller.sale_order_line_18') +
                obj().env.ref('software_reseller.sale_order_line_11') +
                obj().env.ref('software_reseller.sale_order_line_17')
            ).task_id.ids"/>
        <value eval="{'state': '03_approved'}"/>
    </function>

</odoo>
