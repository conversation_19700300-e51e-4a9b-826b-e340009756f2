<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="planning_slot_1" model="planning.slot">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="role_id" ref="planning_role_1" />
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('hr.employee_admin').resource_id.id" />
        <field name="start_datetime" eval="DateTime.today()"/>
        <field name="end_datetime" eval="DateTime.today() + relativedelta(days=4)"/>
        <field name="sale_line_id" ref="sale_order_line_3" />
    </record>
    <record id="planning_slot_10" model="planning.slot">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="role_id" ref="planning_role_1" />
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('hr.employee_admin').resource_id.id" />
        <field name="start_datetime" eval="DateTime.today()"/>
        <field name="end_datetime" eval="DateTime.today() + relativedelta(days=4)"/>
        <field name="sale_line_id" ref="sale_order_line_18" />
    </record>
    <record id="planning_slot_12" model="planning.slot">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="role_id" ref="planning_role_1" />
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('hr.employee_admin').resource_id.id" />
        <field name="start_datetime" eval="DateTime.today()"/>
        <field name="end_datetime" eval="DateTime.today() + relativedelta(days=3)"/>
        <field name="sale_line_id" ref="sale_order_line_18" />
    </record>
    <record id="planning_slot_2" model="planning.slot">
        <field name="employee_id" ref="hr.employee_admin" />
        <field name="role_id" ref="planning_role_1" />
        <field name="resource_id" model="resource.resource" eval="obj().env.ref('hr.employee_admin').resource_id.id" />
        <field name="start_datetime" eval="DateTime.today()"/>
        <field name="end_datetime" eval="DateTime.today() + relativedelta(days=4)"/>
        <field name="sale_line_id" ref="sale_order_line_4" />
    </record>
</odoo>
