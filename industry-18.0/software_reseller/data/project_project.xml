<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_project_1" model="project.project">
        <field name="name">Java Licenses</field>
        <field name="label_tasks">Licenses</field>
        <field name="stage_id" ref="project.project_project_stage_0" />
        <field name="allow_timesheets" eval="False" />
        <field name="x_is_license" eval="True"/>
        <field name="is_favorite" eval="False"/>
        <field name="account_id" ref="account_analytic_account_3"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_1'), ref('project_task_type_2'), ref('project_task_type_3'), ref('project_task_type_4')])]" />
        <field name="task_properties_definition" eval="[{'name': '0199a697eee37d67', 'type': 'many2one', 'domain': False, 'string': 'Client', 'comodel': 'res.partner', 'default': False}, {'name': '92523a6947203790', 'type': 'char', 'string': 'Java Version', 'default': '20'}, {'name': 'b2e92295f220408e', 'type': 'char', 'string': 'License Key', 'default': ''}]" />
    </record>
    <record id="project_project_2" model="project.project">
        <field name="name">Odoo Licenses</field>
        <field name="label_tasks">Licenses</field>
        <field name="stage_id" ref="project.project_project_stage_0" />
        <field name="allow_timesheets" eval="False" />
        <field name="x_is_license" eval="True"/>
        <field name="is_favorite" eval="False"/>
        <field name="account_id" ref="account_analytic_account_4"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_1'), ref('project_task_type_2'), ref('project_task_type_3'), ref('project_task_type_4')])]" />
        <field name="task_properties_definition" eval="[{'name': '3b2b9964284a7f63', 'type': 'many2one', 'domain': False, 'string': 'Client', 'comodel': 'res.partner', 'default': False}, {'name': '14e63001c39f0a7c', 'type': 'integer', 'string': 'Number of Users', 'default': 10}, {'name': '5238d6796e605ce9', 'type': 'selection', 'string': 'Odoo Version', 'default': False, 'selection': [['854ed77adce74cd6', 'Odoo 14'], ['5e030d963127e2d2', 'Odoo 15'], ['280c6b03bf099390', 'Odoo 16'], ['d876b1557301d0ef', 'Odoo 17']]}, {'name': '19460db267e8e4c3', 'type': 'selection', 'string': 'Hosted On', 'default': False, 'selection': [['7fc38e587064fa82', 'On Premise'], ['0acee79ccc0c1c8c', 'Odoo.sh'], ['bd1d12f1e743ece6', 'Odoo Online']]}, {'name': '33ae81dcafa5961b', 'type': 'char', 'string': 'License Key', 'default': ''}]" />
    </record>
    <record id="project_project_3" model="project.project">
        <field name="name">Oracle Database Licenses</field>
        <field name="label_tasks">Licenses</field>
        <field name="stage_id" ref="project.project_project_stage_0" />
        <field name="allow_timesheets" eval="False" />
        <field name="x_is_license" eval="True"/>
        <field name="is_favorite" eval="False"/>
        <field name="account_id" ref="account_analytic_account_5"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_1'), ref('project_task_type_2'), ref('project_task_type_3'), ref('project_task_type_4')])]" />
        <field name="task_properties_definition" eval="[{'name': '2c8f32560151873d', 'type': 'many2one', 'domain': False, 'string': 'Client', 'comodel': 'res.partner', 'default': False}, {'name': 'cf426b7354e473b8', 'type': 'char', 'string': 'Version', 'default': ''}, {'name': '3e2f0ae0a826c70f', 'type': 'selection', 'string': 'Environment', 'default': '6f5ecfb88e6de484', 'selection': [['f5bd74aad416df9b', 'Production'], ['d5a484bbfb529530', 'Test'], ['6f5ecfb88e6de484', 'Development']]}, {'name': 'c02448c8c029a4a9', 'type': 'char', 'string': 'License Key', 'default': ''}]" />
    </record>
    <record id="project_project_4" model="project.project">
        <field name="name">Consulting Services</field>
        <field name="stage_id" ref="project.project_project_stage_0" />
        <field name="allow_billable" eval="True" />
        <field name="allow_timesheets" eval="False" />
        <field name="x_is_license" eval="True"/>
        <field name="account_id" ref="account_analytic_account_1"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_20'), ref('project_task_type_22'), ref('project_task_type_23')])]" />
    </record>
    <record id="project_project_5" model="project.project">
        <field name="name">Development Services</field>
        <field name="stage_id" ref="project.project_project_stage_0" />
        <field name="allow_billable" eval="True" />
        <field name="allow_timesheets" eval="False" />
        <field name="x_is_license" eval="True"/>
        <field name="account_id" ref="account_analytic_account_2"/>
        <field name="type_ids" eval="[(6, 0, [ref('project_task_type_21'), ref('project_task_type_24'), ref('project_task_type_25'), ref('project_task_type_26'), ref('project_task_type_27')])]" />
    </record>
    <record id="project_project_6" model="project.project">
        <field name="name">Internal</field>
        <field name="stage_id" ref="project.project_project_stage_0" />
        <field name="allow_timesheets" eval="False" />
        <field name="x_is_license" eval="True"/>
        <field name="is_favorite" eval="False"/>
        <field name="account_id" ref="account_analytic_account_6"/>
        <field name="type_ids" eval="[(6, 0, [ref('hr_timesheet.internal_project_default_stage')])]" />
    </record>
</odoo>
