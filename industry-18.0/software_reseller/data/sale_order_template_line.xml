<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="sale_order_template_line_1" model="sale.order.template.line">
        <field name="name">Implementation Services</field>
        <field name="sale_order_template_id" ref="sale_order_template_1"/>
        <field name="display_type">line_section</field>
        <field name="product_uom_qty" eval="False"/>
    </record>
    <record id="sale_order_template_line_2" model="sale.order.template.line">
        <field name="name">Business Needs analysis</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="sale_order_template_id" ref="sale_order_template_1"/>
        <field name="product_uom_qty">5.0</field>
        <field name="product_uom_id" ref="uom.product_uom_day"/>
    </record>
    <record id="sale_order_template_line_3" model="sale.order.template.line">
        <field name="name">Odoo Configuration</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="sale_order_template_id" ref="sale_order_template_1"/>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom_id" ref="uom.product_uom_day"/>
    </record>
    <record id="sale_order_template_line_4" model="sale.order.template.line">
        <field name="name">Training &amp; Support</field>
        <field name="product_id" ref="product_product_7"/>
        <field name="sale_order_template_id" ref="sale_order_template_1"/>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom_id" ref="uom.product_uom_day"/>
    </record>
    <record id="sale_order_template_line_5" model="sale.order.template.line">
        <field name="name">Licenses</field>
        <field name="sale_order_template_id" ref="sale_order_template_1"/>
        <field name="display_type">line_section</field>
        <field name="product_uom_qty" eval="False"/>
    </record>
    <record id="sale_order_template_line_6" model="sale.order.template.line">
        <field name="name">Odoo EE License</field>
        <field name="product_id" ref="product_product_3"/>
        <field name="sale_order_template_id" ref="sale_order_template_1"/>
        <field name="product_uom_qty">10.0</field>
        <field name="product_uom_id" ref="uom_uom_27"/>
    </record>
</odoo>
