<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_1" model="product.product">
        <field name="name">Oracle Database License</field>
        <field name="project_id" ref="project_project_3" />
        <field name="categ_id" ref="product_category_4" />
        <field name="service_type">timesheet</field>
        <field name="type">service</field>
        <field name="purchase_method">purchase</field>
        <field name="uom_id" ref="uom_uom_27" />
        <field name="uom_po_id" ref="uom_uom_27" />
        <field name="service_tracking">task_global_project</field>
        <field name="recurring_invoice" eval="True" />
        <field name="list_price">8000.0</field>
        <field name="standard_price">4400.0</field>
        <field name="image_1920" type="base64" file="software_reseller/static/src/binary/product_template/1-image_1920" />
    </record>
    <record id="product_product_2" model="product.product">
        <field name="name">Java EE License</field>
        <field name="project_id" ref="project_project_1" />
        <field name="categ_id" ref="product_category_4" />
        <field name="service_type">timesheet</field>
        <field name="type">service</field>
        <field name="purchase_method">purchase</field>
        <field name="uom_id" ref="uom_uom_27" />
        <field name="uom_po_id" ref="uom_uom_27" />
        <field name="service_tracking">task_global_project</field>
        <field name="recurring_invoice" eval="True" />
        <field name="list_price">500.0</field>
        <field name="standard_price">400.0</field>
        <field name="image_1920" type="base64" file="software_reseller/static/src/binary/product_template/2-image_1920" />
    </record>
    <record id="product_product_3" model="product.product">
        <field name="name">Odoo EE License</field>
        <field name="project_id" ref="project_project_2" />
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">timesheet</field>
        <field name="type">service</field>
        <field name="purchase_method">purchase</field>
        <field name="uom_id" ref="uom_uom_27" />
        <field name="uom_po_id" ref="uom_uom_27" />
        <field name="service_tracking">task_global_project</field>
        <field name="recurring_invoice" eval="True" />
        <field name="list_price">228.0</field>
        <field name="standard_price">190.0</field>
        <field name="image_1920" type="base64" file="software_reseller/static/src/binary/product_template/3-image_1920" />
        <field name="can_image_1024_be_zoomed" eval="True" />
    </record>
    <record id="product_product_7" model="product.product">
        <field name="name">Consulting Services</field>
        <field name="project_id" ref="project_project_4" />
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">timesheet</field>
        <field name="type">service</field>
        <field name="purchase_method">purchase</field>
        <field name="uom_id" ref="uom.product_uom_hour" />
        <field name="uom_po_id" ref="uom.product_uom_hour" />
        <field name="service_tracking">task_global_project</field>
        <field name="list_price">750.0</field>
        <field name="standard_price">350.0</field>
        <field name="planning_enabled" eval="True" />
        <field name="planning_role_id" ref="planning_role_1" />
    </record>
    <record id="product_product_8" model="product.product">
        <field name="name">Development Services</field>
        <field name="project_id" ref="project_project_5" />
        <field name="categ_id" ref="product.product_category_all" />
        <field name="service_type">timesheet</field>
        <field name="type">service</field>
        <field name="purchase_method">purchase</field>
        <field name="uom_id" ref="uom.product_uom_hour" />
        <field name="uom_po_id" ref="uom.product_uom_hour" />
        <field name="service_tracking">task_global_project</field>
        <field name="list_price">750.0</field>
        <field name="standard_price">500.0</field>
        <field name="planning_enabled" eval="True" />
        <field name="planning_role_id" ref="planning_role_2" />
    </record>
</odoo>
