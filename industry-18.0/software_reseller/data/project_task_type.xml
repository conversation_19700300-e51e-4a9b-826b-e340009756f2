<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="project_task_type_1" model="project.task.type">
        <field name="name">New Requests</field>
        <field name="sequence" eval="False"/>
    </record>
    <record id="project_task_type_2" model="project.task.type">
        <field name="name">In Use</field>
    </record>
    <record id="project_task_type_20" model="project.task.type">
        <field name="name">New</field>
        <field name="sequence" eval="False"/>
    </record>
    <record id="project_task_type_21" model="project.task.type">
        <field name="name">Backlog</field>
        <field name="sequence" eval="False"/>
    </record>
    <record id="project_task_type_22" model="project.task.type">
        <field name="name">In Progress</field>
    </record>
    <record id="project_task_type_23" model="project.task.type">
        <field name="name">Done</field>
        <field name="sequence">2</field>
    </record>
    <record id="project_task_type_24" model="project.task.type">
        <field name="name">Specification</field>
    </record>
    <record id="project_task_type_25" model="project.task.type">
        <field name="name">Development</field>
        <field name="sequence">2</field>
    </record>
    <record id="project_task_type_26" model="project.task.type">
        <field name="name">Testing</field>
        <field name="sequence">3</field>
    </record>
    <record id="project_task_type_27" model="project.task.type">
        <field name="name">Deployed</field>
        <field name="sequence">4</field>
    </record>
    <record id="project_task_type_3" model="project.task.type">
        <field name="name">Free to Use</field>
        <field name="sequence">2</field>
    </record>
    <record id="project_task_type_4" model="project.task.type">
        <field name="name">Deprecated</field>
        <field name="sequence">3</field>
    </record>
</odoo>
