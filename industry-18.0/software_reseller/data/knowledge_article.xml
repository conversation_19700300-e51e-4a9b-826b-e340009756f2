<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="welcome_article_body">
        <h1><span style="font-size: 36px;">Software Resellers, IT Services</span><br /></h1>
    
        <p>
            <span style="font-size: 14px;">
                This setup if for IT companies reselling software licenses, and consulting services. The typical sale is a 1 year Oracle Database license that is purchased to Oracle, and resold to client at a margin, with extra services to
                setup the database.
            </span>
        </p>
        <p>
            Licenses as managed as tasks in the project management app, with custom properties; license key, number of users, software version, ... these properties depend on the type of software sold (i.e. Odoo is sold based on the number of
            users periodically, but Oracle requires a lot of information: number of CPUs, number of developer seats, size of DB, etc)
        </p>
        <div class="o_knowledge_behavior_anchor o_knowledge_behavior_type_toc" data-oe-protected="true">
            <div class="o_knowledge_toc_content">
                <a href="#" data-oe-nodeid="0" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Software Resellers, IT Services </a>
                <a href="#" data-oe-nodeid="1" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Business Flows: Licenses Management</a>
                <a href="#" data-oe-nodeid="2" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 1: Sell Oracle Licenses</a>
                <a href="#" data-oe-nodeid="3" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 2: Manage your licenses</a>
                <a href="#" data-oe-nodeid="4" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 3: Renew a license</a>
                <a href="#" data-oe-nodeid="5" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Business Flow: Selling Services</a>
                <a href="#" data-oe-nodeid="6" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 3: Selling Odoo with Services</a>
            </div>
        </div>
        <p><br /></p>
        <h1>Business Flows: Licenses Management</h1>
        <hr />
        <h2>Flow 1: Sell Oracle Licenses</h2>
        <p>
            <strong><u>Sell Oracle Licenses</u></strong>
        </p>
        <p>
            Create a <strong><font class="text-o-color-2">subscription​</font></strong> to any customer with the <strong><font class="text-o-color-2">product "Oracle Database"</font></strong><font class="text-o-color-2">,</font> for 5 users for
            one year. Once you confirm this order, two documents will be created:
        </p>
        <ul>
            <li>A purchase order to Oracle; to buy the license for this client</li>
            <li>A task to manage the license, in stage "New Requests"</li>
        </ul>
        <p>
            <strong><u>Purchase the licenses</u></strong>
        </p>
        <p>
            From the subscription, using the top buttons, jump to the purchase order to buy these licenses to Oracle. You can send by email your request for quotation, then confirm the order. At that point, Oracle will send you the license
            number.
        </p>
        <p>Go back to the sale order, then jump to the task. On the task, set the different informations based on what oracle sends you:</p>
        <ul>
            <li>Environement: Production</li>
            <li>Version: 20.0.1</li>
            <li>License Key: XYZ</li>
            <li>Responsible: your account manager / user</li>
        </ul>
        <h2>Flow 2: Manage your licenses</h2>
        <p>
            From the <strong><font class="text-o-color-2">app "Licenses"</font></strong>, you an have an overview of all your software licenses. Click on one specific license to track license keys belonging to each customer.
        </p>
        <p>From a license key:</p>
        <ul>
            <li>Record all information related to the license (version, type of license, ...). These information are different from one license type to another (Oracle vs Odoo)</li>
            <li>You can communicate with customers on the chatter, to keep an history of the discussions</li>
        </ul>
        <p>The <strong>Licenses</strong> app has 3 menus:</p>
        <ul>
            <li>Licenses by Software (in kanban)</li>
            <li>All Licenses (default to list)</li>
            <li>Licenses to Renew: things you have to check periodically</li>
        </ul>
        <blockquote data-o-mail-quote-node="1" data-o-mail-quote="1">
            <font style="background-color: rgb(255, 231, 206);">A studio automated action turn tasks in Changes Requested when the license key expires in the 15 days or less.</font>
        </blockquote>
        <h2 data-o-mail-quote-node="1" data-o-mail-quote="1">Flow 3: Renew a license</h2>
        <p data-o-mail-quote-node="1" data-o-mail-quote="1">To renew a license, you should just renew the subscription; the task remains the same.</p>
        <p data-o-mail-quote-node="1" data-o-mail-quote="1"><br /></p>
        <p></p>
        <h1>Business Flow: Selling Services</h1>
        <hr />
        <h3>Flow 3: Selling Odoo with Services</h3>
        <p>
            <strong><u>Sell a project</u></strong>
        </p>
        <p>
            Usually, you sell the software licenses with additional services. To test this flow, as you create a quotation, use the quotation template
            <strong>
                <font class="text-o-color-2">"Odoo Standard Implementation"</font>
                <font style="color: inherit;"><span style="font-weight: normal;">. That will add the services billed on timesheets (default setup: sell days, but timesheets per hour).</span></font>
            </strong>
        </p>
        <blockquote data-o-mail-quote-node="1" data-o-mail-quote="1"><font style="background-color: rgb(255, 239, 198);">From the "Optional Products" tab, you can also add development services ine one click.</font></blockquote>
        <p>As you confirm the order, it will:</p>
        <ul>
            <li><font style="background-color: inherit;">Create a purchase order to buy the Odoo license to Odoo S.A.</font></li>
            <li>Create a task to track the license</li>
            <li>Create 3 tasks to track services</li>
            <li>Create 200 hours to plan, in the planning</li>
        </ul>
        <p>
            <strong><u>Invoice Licenses</u></strong>
        </p>
        <p>
            As you confirm the order, the license line is blue as you can already invoice it. The consulting services are black as there is nothing to invoice; you'll be able to invoice at the end of the month, based on the time spent on the
            project.
        </p>
        <blockquote data-o-mail-quote-node="1" data-o-mail-quote="1">
            <font style="color: inherit; background-color: rgb(255, 239, 198);">On this order, the way to manage licenses is similar to the above section. So, we'll mostly focus on the delivery of the extra services.</font><br />
        </blockquote>
        <p>
            <strong><u>Plan Consultants</u></strong> 
        </p>
        <p>From the planning, click on the "Plan Existing" icon;</p>
        <ul>
            <li>Select the services to plan "Business Need Analysis" and "Odoo Configuration" (training will be planned later on, on phase 2)</li>
            <li>Assign these services to the right person.</li>
        </ul>
        <p>
            <strong><u>Timesheet Work done</u></strong>
        </p>
        <p>
            From the timesheet app, or on the task, consultants can timesheet hours on the different phases of the project: Business Need Analysis, Odoo Configuration, or Training &amp; Support. This will be reflected as "Delivered Quantity" on
            the sale order lines.
        </p>
        <p>
            <strong><u>Invoice Projects</u></strong>
        </p>
        <p>At the end of the month, salespeople go to the menu "Orders to Invoice" in Sales app. From there they can select an order (or select all), and invoice what has been delivered on the order.</p>
    </template>    
    <record id="welcome_article" model="knowledge.article">
        <field name="name">software Resellers</field>
        <field name="sequence">1</field>
        <field name="category">workspace</field>
        <field name="internal_permission">write</field>
        <field name="cover_image_id" ref="knowledge_cover_3" />
        <field name="is_locked" eval="True" />
        <field name="is_article_visible_by_everyone" eval="True" />
        <field name="body">
            <![CDATA[]]>
        </field>
    </record>
</odoo>
