<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="uom_uom_27" model="uom.uom">
        <field name="name">Users / Year</field>
        <field name="rounding">1.0</field>
        <field name="category_id" ref="uom_category_7"/>
    </record>
    <record id="uom_uom_28" model="uom.uom">
        <field name="name">User / Month</field>
        <field name="uom_type">smaller</field>
        <field name="rounding">1.0</field>
        <field name="factor">12.0</field>
        <field name="category_id" ref="uom_category_7"/>
    </record>
    <record id="uom_uom_29" model="uom.uom">
        <field name="name">Year</field>
        <field name="rounding">1.0</field>
        <field name="category_id" ref="uom_category_8"/>
    </record>
    <record id="uom_uom_30" model="uom.uom">
        <field name="name">3 Years</field>
        <field name="uom_type">bigger</field>
        <field name="rounding">1.0</field>
        <field name="factor">0.3333333333333333</field>
        <field name="category_id" ref="uom_category_8"/>
    </record>
    <record id="uom_uom_31" model="uom.uom">
        <field name="name">Month</field>
        <field name="uom_type">smaller</field>
        <field name="rounding">1.0</field>
        <field name="factor">12.0</field>
        <field name="category_id" ref="uom_category_8"/>
    </record>
</odoo>
