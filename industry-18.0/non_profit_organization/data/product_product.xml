<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="product_product_1" model="product.product">
        <field name="name">Membership</field>
        <field name="type">service</field>
        <field name="list_price">100.0</field>
        <field name="invoice_policy">order</field>
        <field name="purchase_ok" eval="False"/>
        <field name="categ_id" ref="product.product_category_all"/>
        <field name="recurring_invoice" eval="True"/>
        <field name="service_type">manual</field>
        <field name="website_sequence">10010</field>
    </record>
</odoo>
