<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">

    <record id="filter_default_crm_pipeline" model="ir.filters" forcecreate="0">
        <field name="name">Pipeline</field>
        <field name="model_id">crm.lead</field>
        <field name="user_id" eval="False"/>
        <field name="is_default" eval="True"/>
        <field name="action_id" ref="crm.crm_lead_action_pipeline"/>
        <field name="domain">[]</field>
        <field name="context">{'group_by': []}</field>
        <field name="sort">[]</field>
    </record>

    <record id="filter_domation_payments" model="ir.filters" forcecreate="0">
        <field name="name">Donations</field>
        <field name="model_id">account.payment</field>
        <field name="user_id" eval="False"/>
        <field name="action_id" ref="account.action_account_payments"/>
        <field name="domain">[("is_donation", "=", True)]</field>
        <field name="context">{'group_by': []}</field>
        <field name="sort">[]</field>
    </record>

    <record id="mailing_filter_payming_members" model="mailing.filter" forcecreate="0">
        <field name="name">Payming Members</field>
        <field name="mailing_model_id" ref="sale.model_sale_order"/>
        <field name="mailing_domain">["&amp;", "&amp;", ("state", "!=", "cancel"), "|", ("subscription_state", "=", "3_progress"), ("subscription_state", "=", "4_paused"), ("order_line.product_id.name", "ilike", "Member")]</field>
    </record>

</odoo>
