<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <template id="welcome_article_body">
        <p>
            <span style="font-size: 36px;"><span style="font-size: 36px;">Non Profit Organization</span></span>
        </p>
        <p>
            This setup is for association organizing events and offering membership program.<br />
            They have a website where people can:
        </p>
        <ol>
            <li>Fill a form to request membership. Or buy membership directly on the eCommerce.</li>
            <li>See planned event and by registration ticket.</li>
            <li>Make a donation of their preferred amount.</li>
        </ol>
        <p>In addition, the organization use the email marketing application to send news about upcoming events.</p>
        <div class="o_knowledge_behavior_anchor o_knowledge_behavior_type_toc" data-oe-protected="true">
            <div class="o_knowledge_toc_content">
                <a href="#" data-oe-nodeid="0" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_0">Business Flows</a>
                <a href="#" data-oe-nodeid="1" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription</a>
                <a href="#" data-oe-nodeid="2" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">Website: Request membership program</a>
                <a href="#" data-oe-nodeid="3" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">CRM: Manage opportunity &amp; create a quotation</a>
                <a href="#" data-oe-nodeid="4" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">Portal: Pay the quotation online</a>
                <a href="#" data-oe-nodeid="5" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">Subscription: Track progress and renew subscription</a>
                <a href="#" data-oe-nodeid="6" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 2:  Event -&gt; Website</a>
                <a href="#" data-oe-nodeid="7" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">Event App: Create event</a>
                <a href="#" data-oe-nodeid="8" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">Website: Design &amp; Sell Ticket</a>
                <a href="#" data-oe-nodeid="9" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 3: Website -&gt; Donation</a>
                <a href="#" data-oe-nodeid="10" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">Website: Receive donation</a>
                <a href="#" data-oe-nodeid="11" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_2">Accounting: See donations</a>
                <a href="#" data-oe-nodeid="12" class="oe_unremovable o_no_link_popover d-block o_knowledge_toc_link o_knowledge_toc_link_depth_1">Flow 4: Email Marketing -&gt; Mailing</a>
            </div>
        </div>
        <p><br /></p>
        <h1><span style="font-size: 36px;">Business Flows</span></h1>
        <h2>Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription</h2>
        <h3>Website: Request membership program</h3>
        <p>
            On the website page "[...].odoo.com/contactus", visitor can fill a form.<br />
            When the form is completed, a CRM opportunity will be created and the admin will get a mail notification.<br />
            <br />
        </p>
        <h3>CRM: Manage opportunity &amp; create a quotation</h3>
        <p>
            On the CRM Pipeline, you can see the created opportunity.  You can communicate with prospect and change the opportunity stage.<br />
            Eventually, you can create a quotation for the customer by clicking on "New Quotation".
        </p>
        <ul>
            <li>"Recurrence" should be set as Yearly.</li>
            <li>Product "Membership" should be added as product</li>
        </ul>
        <p>
            Then, you can click on button <strong>"Send by mail".<br /></strong><span>Be careful not to click on "confirm", as it would start the subscription even though the customer hasn't stat paying.</span>
        </p>
        <div class="o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-info pb-0 pt-3" data-oe-protected="true">
            <i class="fs-4 fa fa-info-circle mb-3"></i>
            <div class="o_editable o_editable_no_shadow w-100 ms-3" data-oe-protected="false">
                <p>
                    When sending via mail, user can load template "Sales: Send Quotation (membership)".<br />
                    That way, the mail body will tell the customer to create an account on the website.
                </p>
                <p><img class="img-fluid img-thumbnail" data-file-name="image.png" src="/web/image/non_profit_organization.ir_attachment_505" style="width: 100%;" /><br /></p>
            </div>
        </div>
        <p><br /></p>
        <h3>Portal: Pay the quotation online</h3>
        <p>
            Customer will receive a quotation email with a link to the customer portal. On the portal, he can pay.<br />
            His payment details will be saved, the order will be confirmed and the subscription will start.
        </p>
        <p>Before or after paying, customer should also create an account by going to page [...].odoo.com/web/signup</p>
        <div class="o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-warning pb-0 pt-3" data-oe-protected="true">
            <i class="fs-4 fa fa-exclamation-triangle mb-3"></i>
            <div class="o_editable o_editable_no_shadow w-100 ms-3" data-oe-protected="false">
                <p>
                    Don't forget to enable payment provider, in the video we used the "demo" provider that accepts any data.<br />
                    You can do that in Sales -&gt; Configuration -&gt; Payment Provider.<br />
                </p>
            </div>
        </div>
        <div class="o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-success pb-0 pt-3" data-oe-protected="true">
            <i class="fs-4 fa fa-check-circle mb-3"></i>
            <div class="o_editable o_editable_no_shadow w-100 ms-3" data-oe-protected="false">
                <p>
                    As soon as the payment is done, the order will be confirmed and the subscription start.<br />
                    Also, thanks to an automated actions, the customer pricelist will be updated to "Member"<br />
                </p>
            </div>
        </div>
        <h3>Subscription: Track progress and renew subscription</h3>
        <p>You can look at ongoing plans in the subscription app. There you can easily communicate with customers and renew soon to expire membership.</p>
        <p><br /></p>
        <h2>Flow 2:  Event -&gt; Website</h2>
        <h3>Event App: Create event</h3>
        <p>Using the event app, you can create events that will be visible on the website. There is one already created to show you how it looks.</p>
        <p>
            The product "Event Registration" can be used as ticket. It is configured to have a default price of 30$.<br />
            The "Member" pricelist will overwrite the sale price to 10$. Remember that this pricelist is automatically applied to customer with ongoing membership subscription.
        </p>
        <p>The button "Go to website" will allow you to publish your event and to design your event website page.</p>
        <h3>Website: Design &amp; Sell Ticket</h3>
        <p>Your customers can purchase event ticket. If they are logged in, the pricelist assigned to their customers will automatically apply and the ticket will be cheaper.</p>
        <p>Later on, in the event application, you can communicate to participant &amp; track attendance.</p>
        <p><br /></p>
        <h2>Flow 3: Website -&gt; Donation</h2>
        <h3>Website: Receive donation</h3>
        <p>You can easily add a donation form on your website by using our building block.  We created a page that you can access on yourdatabase.odoo.com/donation  .</p>
        <p>Online visitors will be able to get money to your association in just a few steps.</p>
        <h3>Accounting: See donations</h3>
        <p>Later, you can see all payments received in Accounting-&gt;Customers-&gt;Payments.</p>
        <p>You can use the filter "Donations" in the favourite to filter only donations payments.</p>
        <p><img class="img-fluid" data-file-name="image.png" src="/web/image/non_profit_organization.ir_attachment_518/image.png" /></p>
        <p><br /></p>
        <h2>Flow 4: Email Marketing -&gt; Mailing</h2>
        <p>
            Using the email marketing, you can easily communicate with stakeholders.<br />
            For example, you can use the filter we created to communicate with paying members.
        </p>
        <p><img class="img-fluid img-thumbnail" data-file-name="image.png" src="/web/image/non_profit_organization.ir_attachment_525/image.png" /><br /></p>
    </template>    

    <record id="welcome_article" model="knowledge.article">
        <field name="name">Nonprofit Organization</field>
        <field name="internal_permission">write</field>
        <field name="category">workspace</field>
        <field name="is_locked" eval="True"/>
        <field name="is_article_visible_by_everyone" eval="True"/>
        <field name="body"><![CDATA[]]></field>
    </record>
</odoo>
