<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record model="res.config.settings" id="res_config_settings_enable_npo">
        <field name="portal_confirmation_pay" eval="1"/>
        <field name="group_product_pricelist" eval="1"/>
        <field name="portal_confirmation_sign" eval="0"/>
    </record>
    <function model="res.config.settings" name="execute">
        <value eval="[ref('res_config_settings_enable_npo')]"/>
    </function>
</odoo>
