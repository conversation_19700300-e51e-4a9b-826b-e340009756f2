<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="mail_template_1" model="mail.template">
        <field name="name">Sales: Send Quotation (membership)</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="email_from">{{ (object.user_id.email_formatted or object.company_id.email_formatted or user.email_formatted) }}</field>
        <field name="lang">{{ object.partner_id.lang }}</field>
        <field name="partner_to">{{ object.partner_id.id }}</field>
        <field name="report_template_ids" eval="[(6, 0, [ref('sale.action_report_saleorder')])]"/>
        <field name="subject">{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and (ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ object.name or 'n/a' }})</field>
        <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
        <p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;">
                <t t-set="doc_name" t-value="'quotation' if object.state in ('draft', 'sent') else 'order'"></t>
                Hello,
                <br/></p><p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;"><br/></p><p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;">Thanks for your interest in our program.<br/>
                Your
                <t t-if="ctx.get('proforma')">
                        Pro forma invoice for <t t-out="doc_name or ''">quotation</t> <span style="font-weight: bold;" t-out="object.name or ''">S00052</span>
                        <t t-if="object.origin">
                                (with reference: <t t-out="object.origin or ''"></t> )
                        </t>
                        amounting in <span style="font-weight: bold;" t-out="format_amount(object.amount_total, object.currency_id) or ''">$ 10.00</span> is available.
                </t>
                <t t-else="">
                        <t t-out="doc_name or ''">quotation</t> <span style="font-weight: bold;" t-out="object.name or ''"></span>
                        <t t-if="object.origin">
                                (with reference: <t t-out="object.origin or ''">S00052</t> )
                        </t>
                        amounting in <span style="font-weight: bold;" t-out="format_amount(object.amount_total, object.currency_id) or ''">$ 10.00</span> is ready for review.
                </t>
                <br/></p>
                <p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;"><br/></p>
                <p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;">Once paid, your membership will be automatically enabled.<br/>Don't forget to create an account on our website.<br/>You will need to login later on to access discounted price.</p>
                <p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;"><br/></p>
                <p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;">Do not hesitate to contact us if you have any questions.</p>
                <p style="box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;">
                <t t-if="not is_html_empty(object.user_id.signature)">
                        <br/><br/>
                        <t t-out="object.user_id.signature or ''">--<br/>Mitchell Admin</t>
                </t>
                <br/><br/>
        </p>
</div>
        </field>
    </record>
</odoo>
