<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <record id="base_automation_1" model="base.automation">
        <field name="name">Update pricelist of customer with ongoing membership subscription</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="action_server_ids" eval="[(6, 0, [ref('ir_act_server_711')])]"/>
        <field name="filter_domain">["|", ("subscription_state", "=", "3_progress"), ("subscription_state", "=", "5_renewed")]</field>
        <field name="filter_pre_domain" eval="['&amp;', ('plan_id', '!=', False), ('order_line.product_id', '=', ref('product_product_1'))]"/>
        <field name="trigger">on_create_or_write</field>
        <field name="trigger_field_ids" eval="[(6, 0, [ref('sale_subscription.field_sale_order__subscription_state')])]"/>
    </record>
    <record id="base_automation_2" model="base.automation">
        <field name="name">Update pricelist of customer with closing subscription</field>
        <field name="model_id" ref="sale.model_sale_order"/>
        <field name="action_server_ids" eval="[(6, 0, [ref('ir_act_server_712')])]"/>
        <field name="filter_domain">["|", "|", ("subscription_state", "=", "4_paused"), ("subscription_state", "=", "6_churn"), ("subscription_state", "=", "1_draft")]</field>
        <field name="filter_pre_domain" eval="['&amp;', ('plan_id', '!=', False), ('order_line.product_id', '=', ref('product_product_1'))]"/>
        <field name="trigger">on_create_or_write</field>
        <field name="trigger_field_ids" eval="[(6, 0, [ref('sale_subscription.field_sale_order__subscription_state')])]"/>
    </record>
</odoo>
