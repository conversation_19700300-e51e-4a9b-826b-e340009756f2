# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* non_profit_organization
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:48+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: Rasar<PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "\"Recurrence\" should be set as Yearly."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "10"
msgstr "10"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "100"
msgstr "100"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "25"
msgstr "25"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "50"
msgstr "50"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "<b>ABOUT US</b>"
msgstr "<b>เกี่ยวกับเรา</b>"

#. module: non_profit_organization
#: model:mail.template,body_html:non_profit_organization.mail_template_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Hello,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Thanks for your interest in our program.<br>\n"
"                Your\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Once paid, your membership will be automatically enabled.<br>Don't forget to create an account on our website.<br>You will need to login later on to access discounted price.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Do not hesitate to contact us if you have any questions.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                เรียน\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">ขอขอบคุณที่ให้ความสนใจกับโปรแกรมของเรา<br>\n"
"                       \n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        ใบแจ้งหนี้สำหรับ <t t-out=\"doc_name or ''\">ใบเสนอราคา</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>ของคุณ\n"
"                        <t t-if=\"object.origin\">\n"
"                                (การอ้างอิง: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        เป็นจำนวน <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> \n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">ใบเสนอราคา</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (การอ้างอิง: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        เป็นจำนวน <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> พร้อมสำหรับการตรวจสอบแล้ว\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">เมื่อชำระเงินแล้ว ความเป็นสมาชิกของคุณจะถูกเปิดใช้งานโดยอัตโนมัติ<br> โปรดอย่าลืมสร้างบัญชีบนเว็บไซต์ของเรา<br>คุณจะต้องเข้าสู่ระบบในภายหลังเพื่อเข้าถึงราคาส่วนลด</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">โปรดอย่าลังเลที่จะติดต่อเราหากคุณมีคำถาม</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Comment</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Phone</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Non Profit"
" Organization</span></span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "<span style=\"font-size: 36px;\">Business Flows</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "A year of cultural awakening."
msgstr "ปีแห่งการตื่นตัวทางวัฒนธรรม"

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_13
msgid "About Us"
msgstr "เกี่ยวกับเรา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "About us"
msgstr "เกี่ยวกับเรา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Accounting: See donations"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline "
"เป็นหนึ่งในบุคคลต้นแบบสำคัญของผู้คนที่สามารถพูดได้ว่าเธอรักในสิ่งที่เธอทำอย่างแท้จริง"
" เธอให้คำปรึกษานักพัฒนาภายในองค์กรมากกว่า 100 "
"รายและดูแลชุมชนนักพัฒนาหลายพันคน"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"แม้ว่าเว็บไซต์นี้อาจเชื่อมโยงกับเว็บไซต์อื่น ๆ แต่ไม่ได้หมายความว่าเรายอมรับ"
" เกี่ยวข้อง สนับสนุน รับรอง หรือมีความสัมพันธ์ใด ๆ "
"กับเว็บไซต์ที่มีการเชื่อมโยงโดยตรงหรือโดยอ้อม "
"เว้นแต่จะระบุไว้โดยเฉพาะในที่นี้"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Amount"
msgstr "จำนวน"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"As soon as the payment is done, the order will be confirmed and the subscription start.<br/>\n"
"                    Also, thanks to an automated actions, the customer pricelist will be updated to \"Member\"<br/>"
msgstr ""
"เมื่อชำระเงินเรียบร้อยแล้วคำสั่งซื้อจะได้รับการยืนยันและเริ่มการสมัครสมาชิก<br/>\n"
"                    นอกจากนี้ ด้วยการดำเนินการอัตโนมัติ รายการราคาลูกค้าจะได้รับการอัปเดตเป็น \"สมาชิก\"<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_header
msgid "Become a member"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Before or after paying, customer should also create an account by going to "
"page [...].odoo.com/web/signup"
msgstr ""
"ก่อนหรือหลังจากชำระเงิน ลูกค้าจะต้องสร้างบัญชีโดยไปที่หน้า "
"[...].odoo.com/web/signup"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Business Flows"
msgstr "กระแสธุรกิจ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "CRM: Manage opportunity &amp; create a quotation"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Caring for a baby for 1 month."
msgstr "เลี้ยงทารกได้ 1 เดือน"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr "การเปลี่ยนโลกก็เป็นไปได้ <br/> เราเคยทำมาแล้ว"

#. module: non_profit_organization
#: model:event.event,name:non_profit_organization.event_event_1
msgid "Conference - Our Annual program"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Contact us"
msgstr "ติดต่อเรา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Custom Amount"
msgstr "จำนวนที่กำหนดเอง"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Customer will receive a quotation email with a link to the customer portal. On the portal, he can pay.<br/>\n"
"            His payment details will be saved, the order will be confirmed and the subscription will start."
msgstr ""
"ลูกค้าจะได้รับอีเมลใบเสนอราคาพร้อมลิงก์ไปยังพอร์ทัลลูกค้า โดยสามารถชำระเงินผ่านพอร์ทัลได้<br/>\n"
"            รายละเอียดการชำระเงินของเขาจะได้รับการบันทึกไว้ การสั่งซื้อจะได้รับการยืนยันและการสมัครสมาชิกจะเริ่มต้นขึ้น"

#. module: non_profit_organization
#: model:account.analytic.plan,name:non_profit_organization.account_analytic_plan_1
msgid "Default"
msgstr "เริ่มต้น"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Discover more"
msgstr "ค้นพบเพิ่มขึ้น"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Don't forget to enable payment provider, in the video we used the \"demo\" provider that accepts any data.<br/>\n"
"                    You can do that in Sales -&gt; Configuration -&gt; Payment Provider.<br/>"
msgstr ""
"อย่าลืมเปิดใช้งานผู้ให้บริการการชำระเงิน ในวิดีโอ เราใช้ผู้ให้บริการ \"สาธิต\" ที่ยอมรับข้อมูลทุกประเภท<br/>\n"
"                    คุณสามารถทำได้ในการขาย -&gt; การกำหนดค่า -&gt; ผู้ให้บริการชำระเงิน<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Donate Now"
msgstr "บริจาคตอนนี้"

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_14
msgid "Donation"
msgstr "การบริจาค"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Event App: Create event"
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"    This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"ทุกปี เราเชิญคอมมูนิตี้ พาร์ทเนอร์ และผู้ใช้ปลายทางมาพบกับเรา นับเป็นกิจกรรมที่เหมาะในการมาพูดคุยและนำเสนอฟีเจอร์ใหม่ แผนงานสำหรับเวอร์ชันในอนาคต ความสำเร็จของซอฟต์แวร์ เวิร์กช็อป เซสชันการฝึกอบรม เป็นต้น...\n"
"    นอกจากนี้ งานนี้ยังเป็นโอกาสให้คุณได้นำเสนอกรณีศึกษา วิธีการ หรือการพัฒนาของพาร์ทเนอร์ของเรา เข้าร่วมงานและรับชมฟีเจอร์ของเวอร์ชันใหม่โดยตรงจากแหล่งที่มา!"

#. module: non_profit_organization
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_711
msgid "Execute Code"
msgstr "ดำเนินการรหัส"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Fill a form to request membership. Or buy membership directly on the "
"eCommerce."
msgstr "กรอกแบบฟอร์มเพื่อขอสมัครสมาชิก หรือซื้อสมาชิกโดยตรงบนอีคอมเมิร์ซ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Fill this form and we'll back to you as soon as possible."
msgstr "กรอกแบบฟอร์มนี้แล้วเราจะติดต่อคุณกลับโดยเร็วที่สุด"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 2:  Event -&gt; Website"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 3: Website -&gt; Donation"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 4: Email Marketing -&gt; Mailing"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"ผู้ก่อตั้งและผู้นำที่มีวิสัยทัศน์ Tony เป็นพลังขับเคลื่อนเบื้องหลังบริษัท\n"
"                                                                เขาทำงานหนักที่จะมีส่วนร่วมในการพัฒนาซอฟต์แวร์\n"
"                                                                การตลาด และกลยุทธ์ประสบการณ์ลูกค้า"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Get involved"
msgstr "มีส่วนเกี่ยวข้อง"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Help us protect and preserve for future generations"
msgstr "ช่วยให้เราปกป้องและอนุรักษ์ไว้สำหรับคนรุ่นอนาคต"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"In addition, the organization use the email marketing application to send "
"news about upcoming events."
msgstr ""
"นอกจากนี้ "
"องค์กรยังใช้แอปอีเมลมาร์เกตติ้งเพื่อส่งข่าวสารเกี่ยวกับกิจกรรมที่จะเกิดขึ้น"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Interested by becoming a member?"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"ด้วยประสบการณ์ระดับนานาชาติของ Iris "
"ช่วยให้เราเข้าใจตัวเลขและปรับปรุงตัวเลขเหล่านี้ได้อย่างง่ายดาย "
"เธอมุ่งมั่นที่จะขับเคลื่อนความสำเร็จและมอบความเฉียบแหลมในอาชีพของเธอเพื่อนำพาบริษัทให้ก้าวไปอีกขั้น"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Join us and make the planet a better place."
msgstr "เข้าร่วมกับเราและทำให้โลกเป็นสถานที่ที่ดีขึ้น"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Join us for this 24 hours Event"
msgstr "เข้าร่วมกับเราสำหรับอีเวนต์ 24 ชั่วโมงนี้"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later on, in the event application, you can communicate to participant &amp;"
" track attendance."
msgstr ""
"ในภายหลังในแอปกิจกรรม "
"คุณสามารถติดต่อสื่อสารกับผู้เข้าร่วมและติดตามการเข้าร่วมได้"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later, you can see all payments received in "
"Accounting-&gt;Customers-&gt;Payments."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Learn more"
msgstr "เรียนรู้เพิ่มเติม"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Links to other Websites"
msgstr "ลิงก์ไปยังเว็บไซต์อื่น ๆ "

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"MEMBERS PRICE - 10€ per ticket<br><br>\n"
"                Enjoy a discounted price if you subscribe to our yearly membership.<br>\n"
"                If you are already subscribed,"
msgstr ""
"ราคาสมาชิก - 10 ยูโร ต่อตั๋ว<br><br>\n"
"                เพลิดเพลินไปกับราคาลดพิเศษหากคุณสมัครสมาชิกรายปีของเรา<br>\n"
"                หากคุณสมัครสมาชิกแล้ว"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Make a Donation"
msgstr "โดเนท"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Make a donation of their preferred amount."
msgstr ""

#. module: non_profit_organization
#: model:product.template,name:non_profit_organization.product_product_1_product_template
msgid "Membership"
msgstr "สมาชิก"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich ชอบความท้าทาย "
"และด้วยประสบการณ์หลายปีในตำแหน่งผู้อำนวยการฝ่ายการค้าในอุตสาหกรรมซอฟต์แวร์ "
"เขาได้ช่วยให้บริษัทไปถึงจุดที่เป็นอยู่ในปัจจุบัน Mich "
"เป็นหนึ่งในแรงขับเคลื่อนที่ดีที่สุด"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "My Company"
msgstr "My Company"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the CRM Pipeline, you can see the created opportunity.  You can communicate with prospect and change the opportunity stage.<br/>\n"
"            Eventually, you can create a quotation for the customer by clicking on \"New Quotation\"."
msgstr ""
"ในไปป์ไลน์ CRM คุณสามารถดูโอกาสทางการขายที่สร้างขึ้นได้ คุณสามารถสื่อสารกับลูกค้าเป้าหมายและเปลี่ยนแปลงขั้นตอนโอกาสทางการขายได้<br/>\n"
"            คุณก็สามารถสร้างใบเสนอราคาให้กับลูกค้าได้โดยการคลิกที่ \"ใบเสนอราคาใหม่\""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the website page \"[...].odoo.com/contactus\", visitor can fill a form.<br/>\n"
"            When the form is completed, a CRM opportunity will be created and the admin will get a mail notification.<br/>\n"
"            <br/>"
msgstr ""
"ที่หน้าเว็บไซต์ \"[...].odoo.com/contactus\" ผู้เยี่ยมชมสามารถกรอกแบบฟอร์มได้<br/>\n"
"            เมื่อกรอกแบบฟอร์มเรียบร้อยแล้ว ระบบจะสร้างโอกาสทางการขายบน CRM และผู้ดูแลระบบจะได้รับการแจ้งเตือนทางอีเมล<br/>\n"
"            <br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in elementary school."
msgstr "หนึ่งปีในโรงเรียนประถม"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in high school."
msgstr "หนึ่งปีในโรงเรียนมัธยม"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Online visitors will be able to get money to your association in just a few "
"steps."
msgstr "ผู้เยี่ยมชมออนไลน์จะสามารถรับเงินให้กับสมาคมของคุณได้ในไม่กี่ขั้นตอน"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Mission"
msgstr "ภารกิจของเรา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Values"
msgstr "คุณค่าของเรา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"Our mission is to create a shared plan<br/> for saving the planet’s most "
"exceptional wild places."
msgstr ""
"ภารกิจของเราคือการสร้างแผนร่วมกัน<br/>ในการอนุรักษ์สถานที่ป่าที่โดดเด่นที่สุดในโลก"

#. module: non_profit_organization
#: model:product.pricelist,name:non_profit_organization.product_pricelist_1
msgid "Paying member"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Portal: Pay the quotation online"
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Price for members = 10 €<br>Price for non member = 30 €<br>"
msgstr ""
"ราคาสำหรับสมาชิก = 10 ยูโร <br>ราคาสำหรับผู้ที่ไม่ใช่สมาชิก = 30 ยูโร<br>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Privacy Policy"
msgstr "นโยบายความเป็นส่วนตัว"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Product \"Membership\" should be added as product"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Programs and Initiatives"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Read more"
msgstr "อ่านเพิ่มเติม"

#. module: non_profit_organization
#: model:event.event.ticket,name:non_profit_organization.event_event_ticket_1
msgid "Registration"
msgstr "การลงทะเบียน"

#. module: non_profit_organization
#: model:mail.template,name:non_profit_organization.mail_template_1
msgid "Sales: Send Quotation (membership)"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Section Subtitle"
msgstr "คำบรรยายส่วน"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "See planned event and by registration ticket."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Shaping our future"
msgstr "สร้างอนาคตของเรา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Small or large, your contribution is essential."
msgstr "ไม่ว่าจะเล็กหรือใหญ่ การมีส่วนร่วมของคุณเป็นสิ่งสำคัญ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Start with the customer – find out what they want and give it to them."
msgstr "เริ่มที่ลูกค้า ค้นหาสิ่งที่พวกเขาต้องการและมอบให้พวกเขา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Submit"
msgstr "ส่ง"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Subscription: Track progress and renew subscription"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Terms of service"
msgstr "ข้อกำหนดการให้บริการ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The button \"Go to website\" will allow you to publish your event and to "
"design your event website page."
msgstr ""
"ปุ่ม \"ไปที่เว็บไซต์\" "
"จะช่วยให้คุณเผยแพร่กิจกรรมและออกแบบหน้าเว็บไซต์กิจกรรมของคุณได้"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The product \"Event Registration\" can be used as ticket. It is configured to have a default price of 30$.<br/>\n"
"            The \"Member\" pricelist will overwrite the sale price to 10$. Remember that this pricelist is automatically applied to customer with ongoing membership subscription."
msgstr ""
"ผลิตภัณฑ์ \"การลงทะเบียนกิจกรรม\" สามารถใช้เป็นตั๋วได้ โดยกำหนดค่าราคาเริ่มต้นไว้ที่ 30 ดอลลาร์<br/>\n"
"            รายการราคา \"สมาชิก\" จะเขียนทับราคาขายเป็น 10 ดอลลาร์ โปรดจำไว้ว่ารายการราคานี้จะถูกนำไปใช้กับลูกค้าที่สมัครสมาชิกอย่างต่อเนื่องโดยอัตโนมัติ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Then, you can click on button <strong>\"Send by "
"mail\".<br/></strong><span>Be careful not to click on \"confirm\", as it "
"would start the subscription even though the customer hasn't stat "
"paying.</span>"
msgstr ""
"จากนั้นคุณสามารถคลิกที่ปุ่ม <strong>\"ส่งทางไปรษณีย์\" "
"ได้<br/></strong><span> โปรดระวัง อย่าคลิก \"ยืนยัน\" "
"เพราะจะทำให้การสมัครสมาชิกเริ่มต้นขึ้นแม้ว่าลูกค้าจะยังไม่ได้ชำระเงินก็ตาม"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"ข้อกำหนดในการให้บริการเหล่านี้ (\"ข้อกำหนด\"และ\"ข้อตกลง\") "
"เป็นข้อตกลงระหว่างเว็บไซต์ (\"ผู้ให้บริการเว็บไซต์\" \"เรา\" หรือ "
"\"ของเรา\") และคุณ (\"ผู้ใช้\"  \"คุณ\" หรือ \"ของคุณ \") "
"ข้อตกลงนี้กำหนดข้อกำหนดและเงื่อนไขทั่วไปของการใช้งานเว็บไซต์นี้และผลิตภัณฑ์หรือบริการใด"
" ๆ ของคุณ (เรียกรวมกันว่า \"เว็บไซต์\" หรือ \"บริการ\")"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"This setup is for association organizing events and offering membership program.<br/>\n"
"            They have a website where people can:"
msgstr ""
"การตั้งค่านี้มีไว้สำหรับการจัดกิจกรรมสมาคมและการเสนอโปรแกรมสมาชิก<br/>\n"
"            พวกเขามีเว็บไซต์ที่ผู้คนสามารถ:"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_2
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_712
msgid "Update pricelist of customer with closing subscription"
msgstr ""

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_1
msgid "Update pricelist of customer with ongoing membership subscription"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Use of Cookies"
msgstr "การใช้คุกกี้"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the email marketing, you can easily communicate with stakeholders.<br/>\n"
"            For example, you can use the filter we created to communicate with paying members."
msgstr ""
"การใช้การตลาดแบบอีเมลช่วยให้คุณสามารถสื่อสารกับผู้มีส่วนได้ส่วนเสียได้อย่างง่ายดาย<br/>\n"
"            ตัวอย่างเช่น คุณสามารถใช้ตัวกรองที่เราสร้างเพื่อสื่อสารกับสมาชิกแบบชำระเงินได้"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the event app, you can create events that will be visible on the "
"website. There is one already created to show you how it looks."
msgstr ""
"คุณสามารถสร้างกิจกรรมที่จะมองเห็นได้บนเว็บไซต์ด้วยแอปกิจกรรม "
"ซึ่งมีการสร้างกิจกรรมไว้แล้วเพื่อแสดงให้คุณเห็นว่ามีลักษณะอย่างไร"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"เว็บไซต์อาจใช้คุกกี้เพื่อปรับแต่งและอำนวยความสะดวกในการนำทางสูงสุดของผู้ใช้โดยเว็บไซต์นี้"
" "
"ผู้ใช้อาจกำหนดค่าเบราว์เซอร์ของตนเพื่อแจ้งและปฏิเสธการติดตั้งคุกกี้ที่เราส่งมา"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Design &amp; Sell Ticket"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Receive donation"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Request membership program"
msgstr ""

#. module: non_profit_organization
#: model_terms:web_tour.tour,rainbow_man_message:non_profit_organization.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ยินดีต้อนรับ! ขอให้สนุกกับการค้นพบ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"When sending via mail, user can load template \"Sales: Send Quotation (membership)\".<br/>\n"
"                    That way, the mail body will tell the customer to create an account on the website."
msgstr ""
"เมื่อส่งผ่านอีเมล ผู้ใช้สามารถโหลดเทมเพลต \"การขาย: ส่งใบเสนอราคา (สมาชิก)\" ได้<br/>\n"
"                    ด้วยวิธีนี้ เนื้อหาของอีเมลจะแจ้งให้ลูกค้าสร้างบัญชีบนเว็บไซต์"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"ด้วยปัญหาทั่วโลกที่โลกของเราเผชิญอยู่ทุกวันนี้<br/> "
"คอมมูนิตี้ของผู้ที่เกี่ยวข้องกับพวกเขากำลังเติบโต<br/> "
"เพื่อป้องกันผลกระทบด้านลบ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"เขียนหนึ่งหรือสองย่อหน้าเพื่ออธิบายผลิตภัณฑ์หรือบริการ "
"เพื่อให้คุณประสบความสำเร็จเนื้อหาจะต้องเป็นประโยชน์ต่อผู้อ่านของคุณ"

#. module: non_profit_organization
#: model:sale.subscription.plan,name:non_profit_organization.sale_subscription_plan_1
msgid "Yearly Membership"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can easily add a donation form on your website by using our building "
"block.  We created a page that you can access on "
"yourdatabase.odoo.com/donation  ."
msgstr ""
"คุณสามารถเพิ่มแบบฟอร์มบริจาคบนเว็บไซต์ของคุณได้อย่างง่ายดายโดยใช้บล็อกการสร้างของเรา"
" เราได้สร้างหน้าที่คุณสามารถเข้าถึงได้ที่ yourdatabase.odoo.com/donation"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can look at ongoing plans in the subscription app. There you can easily "
"communicate with customers and renew soon to expire membership."
msgstr ""
"คุณสามารถดูแผนบริการที่กำลังดำเนินการได้ในแอป subscription "
"ซึ่งคุณจะสามารถติดต่อสื่อสารกับลูกค้าและต่ออายุสมาชิกภาพเพื่อหมดอายุได้อย่างง่ายดาย"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can use the filter \"Donations\" in the favourite to filter only "
"donations payments."
msgstr ""
"คุณสามารถใช้ตัวกรอง \"การบริจาค\" ในรายการโปรด "
"เพื่อกรองเฉพาะการบริจาคและการชำระเงินเท่านั้น"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"คุณควรตรวจสอบข้อความทางกฎหมายและเงื่อนไขอื่น ๆ ในการใช้งานเว็บไซต์ใด ๆ "
"ที่คุณเข้าถึงผ่านลิงก์จากเว็บไซต์นี้อย่างรอบคอบ การเชื่อมโยงไปยังเพจนอกอื่น "
"ๆ หรือเว็บไซต์อื่น ๆ ถือเป็นความเสี่ยงของคุณเอง"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Your customers can purchase event ticket. If they are logged in, the "
"pricelist assigned to their customers will automatically apply and the "
"ticket will be cheaper."
msgstr ""
"ลูกค้าของคุณสามารถซื้อตั๋วเข้าร่วมงานได้ หากเข้าสู่ระบบ "
"รายการราคาที่กำหนดให้กับลูกค้าจะนำไปใช้โดยอัตโนมัติ และตั๋วจะมีราคาถูกลง"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "remember to signin to see discounted price."
msgstr ""

#. module: non_profit_organization
#: model:mail.template,subject:non_profit_organization.mail_template_1
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
