# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* non_profit_organization
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# KeyVillage, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <al<PERSON><PERSON>_v<PERSON>@abv.bg>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:48+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "\"Recurrence\" should be set as Yearly."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "10"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "100"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "25"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "50"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "<b>ABOUT US</b>"
msgstr ""

#. module: non_profit_organization
#: model:mail.template,body_html:non_profit_organization.mail_template_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Hello,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Thanks for your interest in our program.<br>\n"
"                Your\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Once paid, your membership will be automatically enabled.<br>Don't forget to create an account on our website.<br>You will need to login later on to access discounted price.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Do not hesitate to contact us if you have any questions.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Comment</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Phone</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Non Profit"
" Organization</span></span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "<span style=\"font-size: 36px;\">Business Flows</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "A year of cultural awakening."
msgstr ""

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_13
msgid "About Us"
msgstr "За нас"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "About us"
msgstr "За нас"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Accounting: See donations"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Aline Turner, CTO"
msgstr "Алин Търнър, главен технически директор"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Amount"
msgstr "Количество"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"As soon as the payment is done, the order will be confirmed and the subscription start.<br/>\n"
"                    Also, thanks to an automated actions, the customer pricelist will be updated to \"Member\"<br/>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_header
msgid "Become a member"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Before or after paying, customer should also create an account by going to "
"page [...].odoo.com/web/signup"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Business Flows"
msgstr "Бизнес Процеси"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "CRM: Manage opportunity &amp; create a quotation"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Caring for a baby for 1 month."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr ""

#. module: non_profit_organization
#: model:event.event,name:non_profit_organization.event_event_1
msgid "Conference - Our Annual program"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Contact us"
msgstr "Свържете се с нас"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Custom Amount"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Customer will receive a quotation email with a link to the customer portal. On the portal, he can pay.<br/>\n"
"            His payment details will be saved, the order will be confirmed and the subscription will start."
msgstr ""

#. module: non_profit_organization
#: model:account.analytic.plan,name:non_profit_organization.account_analytic_plan_1
msgid "Default"
msgstr "Стандартен"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Discover more"
msgstr "Открийте повече"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Don't forget to enable payment provider, in the video we used the \"demo\" provider that accepts any data.<br/>\n"
"                    You can do that in Sales -&gt; Configuration -&gt; Payment Provider.<br/>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Donate Now"
msgstr ""

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_14
msgid "Donation"
msgstr "Дарение"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Event App: Create event"
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"    This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""

#. module: non_profit_organization
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_711
msgid "Execute Code"
msgstr "Изпълнение на кода"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Fill a form to request membership. Or buy membership directly on the "
"eCommerce."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Fill this form and we'll back to you as soon as possible."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 2:  Event -&gt; Website"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 3: Website -&gt; Donation"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 4: Email Marketing -&gt; Mailing"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Get involved"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Help us protect and preserve for future generations"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"In addition, the organization use the email marketing application to send "
"news about upcoming events."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Interested by becoming a member?"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Iris Joe, CFO"
msgstr "Айрис Джо, финансов директор"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Join us and make the planet a better place."
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Join us for this 24 hours Event"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later on, in the event application, you can communicate to participant &amp;"
" track attendance."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later, you can see all payments received in "
"Accounting-&gt;Customers-&gt;Payments."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Learn more"
msgstr "Научете повече"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Links to other Websites"
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"MEMBERS PRICE - 10€ per ticket<br><br>\n"
"                Enjoy a discounted price if you subscribe to our yearly membership.<br>\n"
"                If you are already subscribed,"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Make a Donation"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Make a donation of their preferred amount."
msgstr ""

#. module: non_profit_organization
#: model:product.template,name:non_profit_organization.product_product_1_product_template
msgid "Membership"
msgstr "Членство"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Mich Stark, COO"
msgstr "Мич Старк, главен оперативен директор"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "My Company"
msgstr "Моята компания"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the CRM Pipeline, you can see the created opportunity.  You can communicate with prospect and change the opportunity stage.<br/>\n"
"            Eventually, you can create a quotation for the customer by clicking on \"New Quotation\"."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the website page \"[...].odoo.com/contactus\", visitor can fill a form.<br/>\n"
"            When the form is completed, a CRM opportunity will be created and the admin will get a mail notification.<br/>\n"
"            <br/>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in elementary school."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in high school."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Online visitors will be able to get money to your association in just a few "
"steps."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Mission"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Values"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"Our mission is to create a shared plan<br/> for saving the planet’s most "
"exceptional wild places."
msgstr ""

#. module: non_profit_organization
#: model:product.pricelist,name:non_profit_organization.product_pricelist_1
msgid "Paying member"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Portal: Pay the quotation online"
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Price for members = 10 €<br>Price for non member = 30 €<br>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Privacy Policy"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Product \"Membership\" should be added as product"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Programs and Initiatives"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Read more"
msgstr ""

#. module: non_profit_organization
#: model:event.event.ticket,name:non_profit_organization.event_event_ticket_1
msgid "Registration"
msgstr "Регистрация"

#. module: non_profit_organization
#: model:mail.template,name:non_profit_organization.mail_template_1
msgid "Sales: Send Quotation (membership)"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Section Subtitle"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "See planned event and by registration ticket."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Shaping our future"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Small or large, your contribution is essential."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Start with the customer – find out what they want and give it to them."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Submit"
msgstr "Изпращане"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Subscription: Track progress and renew subscription"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Terms of service"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The button \"Go to website\" will allow you to publish your event and to "
"design your event website page."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The product \"Event Registration\" can be used as ticket. It is configured to have a default price of 30$.<br/>\n"
"            The \"Member\" pricelist will overwrite the sale price to 10$. Remember that this pricelist is automatically applied to customer with ongoing membership subscription."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Then, you can click on button <strong>\"Send by "
"mail\".<br/></strong><span>Be careful not to click on \"confirm\", as it "
"would start the subscription even though the customer hasn't stat "
"paying.</span>"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"This setup is for association organizing events and offering membership program.<br/>\n"
"            They have a website where people can:"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Tony Fred, CEO"
msgstr "Тони Фред, главен изпълнителен директор"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_2
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_712
msgid "Update pricelist of customer with closing subscription"
msgstr ""

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_1
msgid "Update pricelist of customer with ongoing membership subscription"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Use of Cookies"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the email marketing, you can easily communicate with stakeholders.<br/>\n"
"            For example, you can use the filter we created to communicate with paying members."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the event app, you can create events that will be visible on the "
"website. There is one already created to show you how it looks."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Design &amp; Sell Ticket"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Receive donation"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Request membership program"
msgstr ""

#. module: non_profit_organization
#: model_terms:web_tour.tour,rainbow_man_message:non_profit_organization.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Добре дошли! Приятно разглеждане!"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"When sending via mail, user can load template \"Sales: Send Quotation (membership)\".<br/>\n"
"                    That way, the mail body will tell the customer to create an account on the website."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""

#. module: non_profit_organization
#: model:sale.subscription.plan,name:non_profit_organization.sale_subscription_plan_1
msgid "Yearly Membership"
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can easily add a donation form on your website by using our building "
"block.  We created a page that you can access on "
"yourdatabase.odoo.com/donation  ."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can look at ongoing plans in the subscription app. There you can easily "
"communicate with customers and renew soon to expire membership."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can use the filter \"Donations\" in the favourite to filter only "
"donations payments."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Your customers can purchase event ticket. If they are logged in, the "
"pricelist assigned to their customers will automatically apply and the "
"ticket will be cheaper."
msgstr ""

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "remember to signin to see discounted price."
msgstr ""

#. module: non_profit_organization
#: model:mail.template,subject:non_profit_organization.mail_template_1
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Проформа' or 'Оферта') or 'Поръчка' }} (Ref {{ "
"object.name or 'n/a' }})"
