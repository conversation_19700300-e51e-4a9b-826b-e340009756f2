# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* non_profit_organization
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:48+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "\"Recurrence\" should be set as Yearly."
msgstr "\"Thời gian đ<PERSON>nh kỳ\" nên được đặt là Hàng năm."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "10"
msgstr "10"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "100"
msgstr "100"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "25"
msgstr "25"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "50"
msgstr "50"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "<b>ABOUT US</b>"
msgstr "<b>GIỚI THIỆU</b>"

#. module: non_profit_organization
#: model:mail.template,body_html:non_profit_organization.mail_template_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Hello,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Thanks for your interest in our program.<br>\n"
"                Your\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Once paid, your membership will be automatically enabled.<br>Don't forget to create an account on our website.<br>You will need to login later on to access discounted price.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Do not hesitate to contact us if you have any questions.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Xin chào,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Cảm ơn bạn đã quan tâm đến chương trình của chúng tôi!<br>\n"
"                Hoá đơn\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        chiếu lệ cho <t t-out=\"doc_name or ''\">báo giá</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span> của bạn\n"
"                        <t t-if=\"object.origin\">\n"
"                                (với mã tham chiếu: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        có giá trị là <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> đã có.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">báo giá</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (với mã tham chiếu: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        có giá trị là <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> đã sẵn sàng để bạn kiểm tra.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Sau khi thanh toán, thì tư cách thành viên của bạn sẽ được tự động kích hoạt.<br>Đừng quên tạo tài khoản trên trang web của chúng tôi.<br>Sau này, bạn sẽ cần đăng nhập để được hưởng mức giá ưu đãi.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Đừng ngần ngại liên hệ với chúng tôi nếu bạn có câu hỏi cần được giải đáp.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Cuộc sống trên Trái Đất —</b>\n"
"                                    </font>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"
msgstr "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Thông tin banner\"></i>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Comment</span>"
msgstr "<span class=\"s_website_form_label_content\">Bình luận</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Phone</span>"
msgstr "<span class=\"s_website_form_label_content\">Số điện thoại</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Tên</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Non Profit"
" Organization</span></span>"
msgstr ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Tổ Chức "
"Phi Chính Phủ</span></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "<span style=\"font-size: 36px;\">Business Flows</span>"
msgstr "<span style=\"font-size: 36px;\">Chu trình kinh doanh</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "A year of cultural awakening."
msgstr "Một năm khám phá văn hóa. "

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_13
msgid "About Us"
msgstr "Giới thiệu"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "About us"
msgstr "Về chúng tôi"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Accounting: See donations"
msgstr "Kế toán: Xem các khoản quyên góp"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline là một trong những hình mẫu yêu mến công việc tiêu biểu trong cuộc "
"sống. Cô ấy cố vấn cho hơn 100 lập trình viên nội bộ và hỗ trợ cộng đồng "
"hàng nghìn lập trình viên."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Mặc dù trang web này có thể được liên kết với các trang web khác, nhưng "
"chúng tôi không, trực tiếp hoặc gián tiếp, ngụ ý bất kỳ sự chấp thuận, liên "
"kết, tài trợ, chứng thực hoặc liên kết với bất kỳ trang web được liên kết "
"nào, trừ khi được quy định cụ thể ở đây."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Amount"
msgstr "Số tiền"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"As soon as the payment is done, the order will be confirmed and the subscription start.<br/>\n"
"                    Also, thanks to an automated actions, the customer pricelist will be updated to \"Member\"<br/>"
msgstr ""
"Ngay sau khi thanh toán xong, đơn hàng sẽ được xác nhận và đăng ký bắt đầu.<br/>\n"
"Ngoài ra, nhờ vào hành động tự động, bảng giá của khách hàng sẽ được cập nhật thành \"Thành viên\"<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_header
msgid "Become a member"
msgstr "Trở thành thành viên"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Before or after paying, customer should also create an account by going to "
"page [...].odoo.com/web/signup"
msgstr ""
"Trước hoặc sau khi thanh toán, khách hàng cũng nên tạo một tài khoản bằng "
"cách truy cập trang [...].odoo.com/web/signup"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Business Flows"
msgstr "Chu trình kinh doanh"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "CRM: Manage opportunity &amp; create a quotation"
msgstr "CRM: Quản lý cơ hội và tạo báo giá"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Caring for a baby for 1 month."
msgstr "Chăm sóc em bé trong 1 tháng. "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr ""
"Thay đổi thế giới là điều có thể,<br/> vì chúng tôi đã từng làm được điều "
"đó."

#. module: non_profit_organization
#: model:event.event,name:non_profit_organization.event_event_1
msgid "Conference - Our Annual program"
msgstr "Hội nghị - Chương trình thường niên của chúng tôi"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Contact us"
msgstr "Liên hệ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Custom Amount"
msgstr "Khoản tùy chỉnh"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Customer will receive a quotation email with a link to the customer portal. On the portal, he can pay.<br/>\n"
"            His payment details will be saved, the order will be confirmed and the subscription will start."
msgstr ""
"Khách hàng sẽ nhận được email báo giá có liên kết đến cổng thông tin khách hàng. Trên cổng thông tin, khách hàng có thể thanh toán.<br/>\n"
"Thông tin thanh toán của khách hàng sẽ được lưu, đơn hàng sẽ được xác nhận và đăng ký sẽ bắt đầu."

#. module: non_profit_organization
#: model:account.analytic.plan,name:non_profit_organization.account_analytic_plan_1
msgid "Default"
msgstr "Mặc định"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Discover more"
msgstr "Khám phá thêm"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Don't forget to enable payment provider, in the video we used the \"demo\" provider that accepts any data.<br/>\n"
"                    You can do that in Sales -&gt; Configuration -&gt; Payment Provider.<br/>"
msgstr ""
"Đừng quên bật tính năng nhà cung cấp dịch vụ thanh toán. Trong video, chúng tôi đã sử dụng nhà cung cấp \"demo\" chấp nhận mọi dữ liệu.<br/>\n"
"Bạn có thể thực hiện việc này trong Bán hàng -&gt; Cấu hình -&gt; Nhà cung cấp dịch vụ thanh toán.<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Donate Now"
msgstr "Quyên góp "

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_14
msgid "Donation"
msgstr "Quyên góp"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Event App: Create event"
msgstr "Ứng dụng Sự kiện: Tạo sự kiện"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"    This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"Hằng năm, chúng tôi mời cộng đồng, đối tác, người dùng cuối cùng tới giao lưu với chúng tôi! Đây là sự kiện lý tưởng để gặp gỡ và giới thiệu những tính năng mới, định hướng các phiên bản trong tương lai, điểm lại thành tựu của phần mềm với các buổi workshop, tập huấn,...\n"
"            Sự kiện này cũng là cơ hội để vinh danh thành tựu, phương pháp và sự phát triển của các đối tác. Hãy tới tham dự và tận mắt chứng kiến các tính năng thú vị của phiên bản mới!"

#. module: non_profit_organization
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_711
msgid "Execute Code"
msgstr "Thực thi mã"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Fill a form to request membership. Or buy membership directly on the "
"eCommerce."
msgstr ""
"Điền vào mẫu để đăng ký thành viên. Hoặc mua gói thành viên trực tiếp trên "
"Thương mại điện tử."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Fill this form and we'll back to you as soon as possible."
msgstr "Hãy điền vào mẫu này và chúng tôi sẽ phản hồi bạn sớm nhất có thể."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription"
msgstr ""
"Chu trình 1: Trang web -&gt; CRM -&gt; Cổng thông tin (Báo giá) -&gt; Đăng "
"ký"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 2:  Event -&gt; Website"
msgstr "Chu trình 2: Sự kiện -&gt; Trang web"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 3: Website -&gt; Donation"
msgstr "Chu trình 3: Trang web -&gt; Quyên góp"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 4: Email Marketing -&gt; Mailing"
msgstr "Chu trình 4: Marketing qua Email -&gt; Thư"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"Là nhà sáng lập và có tầm nhìn xa trông rộng, Tony là động lực phát triển của công ty. Anh\n"
"                              thích tạo sự bận rộn cho bản thân bằng cách tham gia phát triển phần mềm,\n"
"                              marketing, và các chiến lược về trải nghiệm khách hàng. "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Get involved"
msgstr "Get involved"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Help us protect and preserve for future generations"
msgstr "Giúp chúng tôi bảo vệ và gìn giữ cho các thế hệ tương lai"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"In addition, the organization use the email marketing application to send "
"news about upcoming events."
msgstr ""
"Ngoài ra, tổ chức còn sử dụng ứng dụng Marketing qua Email để gửi tin tức về"
" các sự kiện sắp tới."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Interested by becoming a member?"
msgstr "Bạn muốn trở thành thành viên?"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Iris, với kinh nghiệm quốc tế của mình, giúp chúng tôi dễ dàng hiểu các con "
"số và cải thiện chúng. Cô quyết tâm thúc đẩy thành công và thể hiện sự nhạy "
"bén trong nghề nghiệp của mình để đưa công ty phát triển lên một tầm cao "
"mới."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Join us and make the planet a better place."
msgstr ""
"Hãy tham gia cùng chúng tôi và biến hành tinh này thành một nơi tốt đẹp hơn."

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Join us for this 24 hours Event"
msgstr "Hãy tham dự sự kiện 24 giờ này cùng chúng tôi"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later on, in the event application, you can communicate to participant &amp;"
" track attendance."
msgstr ""
"Sau đó, trong ứng dụng sự kiện, bạn có thể liên lạc với người tham gia và "
"theo dõi xem họ có tham dự hay không."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later, you can see all payments received in "
"Accounting-&gt;Customers-&gt;Payments."
msgstr ""
"Sau đó, bạn có thể xem tất cả các khoản thanh toán đã nhận trong Kế "
"toán-&gt;Khách hàng-&gt;Thanh toán."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Learn more"
msgstr "Tìm hiểu thêm"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Links to other Websites"
msgstr "Liên kết đến các trang web khác"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"MEMBERS PRICE - 10€ per ticket<br><br>\n"
"                Enjoy a discounted price if you subscribe to our yearly membership.<br>\n"
"                If you are already subscribed,"
msgstr ""
"GIÁ THÀNH VIÊN - 10€ cho mỗi vé<br><br>\n"
"Tận hưởng mức giá ưu đãi nếu bạn đăng ký thành viên hàng năm của chúng tôi.<br>\n"
"Nếu bạn đã đăng ký,"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Make a Donation"
msgstr "Quyên góp "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Make a donation of their preferred amount."
msgstr "Quyên góp số tiền mà họ muốn."

#. module: non_profit_organization
#: model:product.template,name:non_profit_organization.product_product_1_product_template
msgid "Membership"
msgstr "Tư cách thành viên"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich rất thích thử thách. Với kinh nghiệm nhiều năm làm Giám đốc Thương mại "
"trong ngành phần mềm, Mich đã giúp công ty có được vị trí như ngày hôm nay. "
"Mich là một trong số những người tài giỏi nhất."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "My Company"
msgstr "My Company"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the CRM Pipeline, you can see the created opportunity.  You can communicate with prospect and change the opportunity stage.<br/>\n"
"            Eventually, you can create a quotation for the customer by clicking on \"New Quotation\"."
msgstr ""
"Trên Chu trình CRM, bạn có thể thấy cơ hội đã tạo. Bạn có thể trao đổi với khách hàng tiềm năng và thay đổi giai đoạn của cơ hội.<br/>\n"
"Cuối cùng, bạn có thể tạo báo giá cho khách hàng bằng cách nhấp vào \"Báo giá mới\"."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the website page \"[...].odoo.com/contactus\", visitor can fill a form.<br/>\n"
"            When the form is completed, a CRM opportunity will be created and the admin will get a mail notification.<br/>\n"
"            <br/>"
msgstr ""
"Trên trang web \"[...].odoo.com/contactus\", khách truy cập có thể điền vào biểu mẫu.<br/>\n"
"Khi biểu mẫu hoàn tất, một cơ hội CRM sẽ được tạo và quản trị viên sẽ nhận được thông báo qua email.<br/>\n"
"            <br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in elementary school."
msgstr "Một năm học tiểu học."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in high school."
msgstr "Một năm học trung học. "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Online visitors will be able to get money to your association in just a few "
"steps."
msgstr ""
"Khách truy cập online có thể chuyển tiền vào hiệp hội của bạn chỉ sau vài "
"bước."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Mission"
msgstr "Sứ mệnh của chúng tôi"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Values"
msgstr "Giá trị của chúng tôi"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"Our mission is to create a shared plan<br/> for saving the planet’s most "
"exceptional wild places."
msgstr ""
"Our mission is to create a shared plan<br/> for saving the planet’s most "
"exceptional wild places."

#. module: non_profit_organization
#: model:product.pricelist,name:non_profit_organization.product_pricelist_1
msgid "Paying member"
msgstr "Thành viên trả phí"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Portal: Pay the quotation online"
msgstr "Cổng thông tin: Thanh toán đơn hàng online"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Price for members = 10 €<br>Price for non member = 30 €<br>"
msgstr ""
"Giá cho thành viên = 10€ <br>Giá cho khách không phải thành viên = 30€<br>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Privacy Policy"
msgstr "Chính sách quyền riêng tư"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Product \"Membership\" should be added as product"
msgstr "Sản phẩm \"Thành viên\" nên được thêm vào làm sản phẩm"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Programs and Initiatives"
msgstr "Chương trình và sáng kiến"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Read more"
msgstr "Xem thêm"

#. module: non_profit_organization
#: model:event.event.ticket,name:non_profit_organization.event_event_ticket_1
msgid "Registration"
msgstr "Đăng ký"

#. module: non_profit_organization
#: model:mail.template,name:non_profit_organization.mail_template_1
msgid "Sales: Send Quotation (membership)"
msgstr "Bán hàng: Gửi báo giá (thành viên)"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Section Subtitle"
msgstr "Mục phụ đề"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "See planned event and by registration ticket."
msgstr "Xem sự kiện đã lên kế hoạch và theo vé đăng ký."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Shaping our future"
msgstr "Định hình tương lai"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Small or large, your contribution is essential."
msgstr "Dù ít hay nhiều, đóng góp của bạn đều đáng quý. "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Start with the customer – find out what they want and give it to them."
msgstr ""
"Bắt đầu với khách hàng – tìm hiểu những gì họ muốn và mang đến cho họ điều "
"đó."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Submit"
msgstr "Gửi"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Subscription: Track progress and renew subscription"
msgstr "Đăng ký: Theo dõi tiến trình và gia hạn đăng ký"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Terms of service"
msgstr "Điều khoản dịch vụ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The button \"Go to website\" will allow you to publish your event and to "
"design your event website page."
msgstr ""
"Nút \"Đi đến trang web\" sẽ cho phép bạn đăng sự kiện và thiết kế trang web "
"sự kiện."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The product \"Event Registration\" can be used as ticket. It is configured to have a default price of 30$.<br/>\n"
"            The \"Member\" pricelist will overwrite the sale price to 10$. Remember that this pricelist is automatically applied to customer with ongoing membership subscription."
msgstr ""
"Sản phẩm \"Đăng ký sự kiện\" có thể được sử dụng làm vé. Sản phẩm này được cấu hình với giá mặc định là 30$.<br/>\n"
"Bảng giá \"Thành viên\" sẽ ghi đè giá bán thành 10$. Hãy nhớ rằng bảng giá này được tự động áp dụng cho khách hàng có đăng ký thành viên còn hiệu lực."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Then, you can click on button <strong>\"Send by "
"mail\".<br/></strong><span>Be careful not to click on \"confirm\", as it "
"would start the subscription even though the customer hasn't stat "
"paying.</span>"
msgstr ""
"Sau đó, bạn có thể nhấp vào nút <strong>\"Gửi qua email\".<br/></strong> "
"<span>Lưu ý không nhấp vào \"xác nhận\" vì như vậy thì đăng ký sẽ bắt đầu "
"ngay cả khi khách hàng chưa thanh toán.</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Các điều khoản dịch vụ này (\"Điều khoản\", \"Thỏa thuận\") là một thỏa "
"thuận giữa trang web (\"Nhà điều hành trang web\", \"chúng tôi\", \"chúng "
"tôi\" hoặc \"của chúng tôi\") và bạn (\"Người dùng\", \"bạn\" hoặc \"của bạn"
" \"). Thỏa thuận này nêu ra các điều khoản và điều kiện chung về việc bạn sử"
" dụng trang web này và bất kỳ sản phẩm hoặc dịch vụ nào của nó (gọi chung là"
" \"Trang web\" hoặc \"Dịch vụ\")."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"This setup is for association organizing events and offering membership program.<br/>\n"
"            They have a website where people can:"
msgstr ""
"Thiết lập ngành này dành cho các hiệp hội tổ chức sự kiện và cung cấp chương trình thành viên.<br/>\n"
"Họ có một trang web nơi mọi người có thể:"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_2
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_712
msgid "Update pricelist of customer with closing subscription"
msgstr "Cập nhật bảng giá của khách hàng có đăng ký đang đóng"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_1
msgid "Update pricelist of customer with ongoing membership subscription"
msgstr "Cập nhật bảng giá của khách hàng có đăng ký đang hoạt động"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Use of Cookies"
msgstr "Sử dụng cookie"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the email marketing, you can easily communicate with stakeholders.<br/>\n"
"            For example, you can use the filter we created to communicate with paying members."
msgstr ""
"Sử dụng marketing qua email, bạn có thể dễ dàng trao đổi với các bên liên quan.<br/>\n"
"Ví dụ, bạn có thể sử dụng bộ lọc chúng tôi đã tạo để trao đổi với các thành viên trả phí."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the event app, you can create events that will be visible on the "
"website. There is one already created to show you how it looks."
msgstr ""
"Bằng cách sử dụng ứng dụng sự kiện, bạn có thể tạo các sự kiện sẽ hiển thị "
"trên trang web. Một sự kiện đã được tạo sẵn để cho bạn thấy sự kiện đó sẽ "
"trông như thế nào."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Trang web có thể sử dụng cookie để cá nhân hóa và hỗ trợ điều hướng tối đa "
"cho người dùng bởi trang web này. Người dùng có thể định cấu hình trình "
"duyệt của mình để thông báo và từ chối cài đặt cookie do chúng tôi gửi."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Design &amp; Sell Ticket"
msgstr "Trang web: Thiết kế &amp; bán vé"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Receive donation"
msgstr "Trang web: Nhận quyên góp"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Request membership program"
msgstr "Trang web: Yêu cầu chương trình thành viên"

#. module: non_profit_organization
#: model_terms:web_tour.tour,rainbow_man_message:non_profit_organization.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Chào mừng bạn! Chúc bạn một chuyến khám phá bổ ích!"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"When sending via mail, user can load template \"Sales: Send Quotation (membership)\".<br/>\n"
"                    That way, the mail body will tell the customer to create an account on the website."
msgstr ""
"Khi gửi qua email, người dùng có thể tải mẫu \"Bán hàng: Gửi báo giá (thành viên)\".<br/>\n"
"Như vậy, nội dung email sẽ báo khách hàng tạo tài khoản trên trang web."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Viết một hoặc hai đoạn văn mô tả sản phẩm hoặc dịch vụ của bạn. Để thành "
"công, nội dung của bạn cần phải hữu ích cho người đọc."

#. module: non_profit_organization
#: model:sale.subscription.plan,name:non_profit_organization.sale_subscription_plan_1
msgid "Yearly Membership"
msgstr "Tư cách thành viên theo năm"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can easily add a donation form on your website by using our building "
"block.  We created a page that you can access on "
"yourdatabase.odoo.com/donation  ."
msgstr ""
"Bạn có thể dễ dàng thêm biểu mẫu quyên góp vào trang web của mình bằng cách "
"sử dụng khối dựng của chúng tôi. Chúng tôi đã tạo một trang mà bạn có thể "
"truy cập trên yourdatabase.odoo.com/donation."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can look at ongoing plans in the subscription app. There you can easily "
"communicate with customers and renew soon to expire membership."
msgstr ""
"Bạn có thể xem các gói đang hoạt động trong ứng dụng đăng ký. Tại đó, bạn có"
" thể dễ dàng trao đổi với khách hàng và gia hạn các gói thành viên sắp hết "
"hạn."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can use the filter \"Donations\" in the favourite to filter only "
"donations payments."
msgstr ""
"Bạn có thể sử dụng bộ lọc \"Quyên góp\" trong mục yêu thích để chỉ lọc các "
"khoản thanh toán quyên góp."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"Bạn nên xem xét cẩn thận các tuyên bố pháp lý và các điều kiện sử dụng khác "
"của bất kỳ trang web nào bạn truy cập thông qua một liên kết từ trang web "
"này. Liên kết của bạn đến bất kỳ trang ngoài trang web hoặc các trang web "
"khác là nguy cơ của riêng bạn."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Your customers can purchase event ticket. If they are logged in, the "
"pricelist assigned to their customers will automatically apply and the "
"ticket will be cheaper."
msgstr ""
"Khách hàng của bạn có thể mua vé sự kiện. Nếu họ đã đăng nhập, bảng giá cho "
"khách hàng sẽ tự động được áp dụng và giá vé sẽ rẻ hơn."

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "remember to signin to see discounted price."
msgstr "nhớ đăng nhập để xem giá ưu đãi."

#. module: non_profit_organization
#: model:mail.template,subject:non_profit_organization.mail_template_1
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Hóa đơn chiếu lệ' or 'Báo giá') or 'Đơn hàng' }} "
"(Mã {{ object.name or 'không có' }})"
