# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* non_profit_organization
# 
# Translators:
# Wil O<PERSON>o, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:48+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "\"Recurrence\" should be set as Yearly."
msgstr "\"繰返し\" は1年ごとに設定して下さい。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "10"
msgstr "10"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "100"
msgstr "100"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "25"
msgstr "25"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "50"
msgstr "50"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "<b>ABOUT US</b>"
msgstr "<b>当社について</b>"

#. module: non_profit_organization
#: model:mail.template,body_html:non_profit_organization.mail_template_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Hello,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Thanks for your interest in our program.<br>\n"
"                Your\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Once paid, your membership will be automatically enabled.<br>Don't forget to create an account on our website.<br>You will need to login later on to access discounted price.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Do not hesitate to contact us if you have any questions.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                こんにちは。\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">プログラムにご興味をお持ちいただきありがとうございます。<br>\n"
"                お客様の\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        見積送状 <t t-out=\"doc_name or ''\">見積</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (参照番号: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        金額 <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> のご用意ができました。\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">見積書</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (参照番号: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        金額 <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> のご確認の用意ができました。\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">お支払い頂けると、メンバーシップが自動的に有効になります。<br>ウェブサイトでアカウントを作成して下さい。<br>割引価格にアクセスするためにログインが必要です。</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">ご質問がございましたら、お気軽にお問合せ下さい。</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— 地球上の生き物 —</b>\n"
"                                    </font>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"
msgstr "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Comment</span>"
msgstr "<span class=\"s_website_form_label_content\">コメント</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Phone</span>"
msgstr "<span class=\"s_website_form_label_content\">電話</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Eメール</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">名前</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Non Profit"
" Organization</span></span>"
msgstr ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: "
"36px;\">非営利団体</span></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "<span style=\"font-size: 36px;\">Business Flows</span>"
msgstr "<span style=\"font-size: 36px;\">ビジネスフロー</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "A year of cultural awakening."
msgstr "文化的覚醒の1年。"

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_13
msgid "About Us"
msgstr "会社概要"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "About us"
msgstr "会社概要"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Accounting: See donations"
msgstr "会計: 寄付金を見る"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Alineは自分の仕事が大好きだと言える、人生の象徴的な人々の1人です。彼女は100人以上の社内開発者を指導し、何千人もの開発者のコ​​ミュニティーの世話をしています。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"このサイトは他のサイトにリンクされている場合はありますが、ここに特に明記されていない限り、リンクされているサイトの承認、関連付け、スポンサーシップ、承認又は提携を暗示することはありません。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Amount"
msgstr "金額"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"As soon as the payment is done, the order will be confirmed and the subscription start.<br/>\n"
"                    Also, thanks to an automated actions, the customer pricelist will be updated to \"Member\"<br/>"
msgstr ""
"支払が完了次第、オーダが確定し、サブスクリプションが開始されます。<br/>\n"
"                    また、自動化されたアクションにより、顧客価格表は\"会員\"に更新されます。<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_header
msgid "Become a member"
msgstr "メンバーになる"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Before or after paying, customer should also create an account by going to "
"page [...].odoo.com/web/signup"
msgstr "支払前でも後でも、顧客はページ[...].odoo.com/web/signupにアクセスしてアカウントを作成する必要があります。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Business Flows"
msgstr "ビジネスフロー"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "CRM: Manage opportunity &amp; create a quotation"
msgstr "CRM: 案件管理 &amp; 見積作成"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Caring for a baby for 1 month."
msgstr "1か月の赤ちゃんの世話。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr "世界を変えることは可能です。<br/>当社はこれまでにも行ってきました。"

#. module: non_profit_organization
#: model:event.event,name:non_profit_organization.event_event_1
msgid "Conference - Our Annual program"
msgstr "コンファレンス - 年間プログラム"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Contact us"
msgstr "お問い合わせ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Custom Amount"
msgstr "金額をカスタム"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Customer will receive a quotation email with a link to the customer portal. On the portal, he can pay.<br/>\n"
"            His payment details will be saved, the order will be confirmed and the subscription will start."
msgstr ""
"顧客は、顧客ポータルへのリンクが記載された見積Eメールを受取ります。ポータル上で支払いを行うことができます。<br/>\n"
"            顧客の支払情報が保存され、オーダが確定し、サブスクリプションが開始されます。"

#. module: non_profit_organization
#: model:account.analytic.plan,name:non_profit_organization.account_analytic_plan_1
msgid "Default"
msgstr "デフォルト"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Discover more"
msgstr "詳細をご覧ください"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Don't forget to enable payment provider, in the video we used the \"demo\" provider that accepts any data.<br/>\n"
"                    You can do that in Sales -&gt; Configuration -&gt; Payment Provider.<br/>"
msgstr ""
"決済プロバイダーを有効にすることをお忘れなく。動画では、あらゆるデータを受け入れる\"デモ\" プロバイダーを使用しました。<br/>\n"
"                    販売 -&gt; 設定 -&gt; 決済プロバイダーで行うことができます。<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Donate Now"
msgstr "今すぐ寄付する"

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_14
msgid "Donation"
msgstr "寄付"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Event App: Create event"
msgstr "イベントアプリ: イベント作成"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"    This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"毎年、コミュニティ、パートナー、エンドユーザーを特別なイベントに招待しています！新機能を紹介し、将来のバージョンのロードマップについて話し合い、ソフトウェアの成果をプレゼンし、ワークショップやトレーニングセッションを提供する絶好の機会です。\n"
"また、イベントでは、パートナーのケーススタディ、方法論、開発状況を紹介する機会もあります。新バージョンの機能を直接確認する機会をお見逃しなく！"

#. module: non_profit_organization
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_711
msgid "Execute Code"
msgstr "コードを実行"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Fill a form to request membership. Or buy membership directly on the "
"eCommerce."
msgstr "会員資格を申請するには、フォームに記入して下さい。または、eコマースサイトで直接会員資格を購入して下さい。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Fill this form and we'll back to you as soon as possible."
msgstr "フォームにご記入頂ければ、できるだけ早くご連絡いたします。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription"
msgstr "フロー1: ウェブサイト -&gt; CRM -&gt; ポータル (見積) -&gt; サブスクリプション"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 2:  Event -&gt; Website"
msgstr "フロー2:  イベント -&gt; ウェブサイト"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 3: Website -&gt; Donation"
msgstr "フロー3: ウェブサイト -&gt; 寄付"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 4: Email Marketing -&gt; Mailing"
msgstr "フロー4: Eメールマーケティング -&gt; メール配信"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"創業者で最高責任者であるTonyは会社の原動力です。彼は\n"
"                                                                ソフトウェア、マーケティング、カスタマー・エクスペリエンス戦略の開発\n"
"                                                                に関わり、忙しく働くことが大好きです。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Get involved"
msgstr "参加する"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Help us protect and preserve for future generations"
msgstr "未来の世代のために、保護と保全にご協力を"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"In addition, the organization use the email marketing application to send "
"news about upcoming events."
msgstr "また、この団体はEメールマーケティングアプリケーションを使用して、今後のイベントに関するニュースを配信しています。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Interested by becoming a member?"
msgstr "メンバー加入をお考えですか？"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Irisは国際的な経験を生かして、私たちが数字への理解を深めや改善策を立案するのをサポートしています。成功を確信し、会社を次のレベルに引き上げるべく、プロとしての洞察力を発揮しています。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Join us and make the planet a better place."
msgstr "私たちと一緒に、地球をより良い場所にしましょう。"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Join us for this 24 hours Event"
msgstr "この24時間イベントに参加しましょう！"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later on, in the event application, you can communicate to participant &amp;"
" track attendance."
msgstr "後ほど、イベントアプリケーションで参加者に連絡したり、出席状況を確認したりすることができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later, you can see all payments received in "
"Accounting-&gt;Customers-&gt;Payments."
msgstr "後で、会計-&gt;顧客-&gt;支払で受取った全ての支払を見ることができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Learn more"
msgstr "もっと知る"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Links to other Websites"
msgstr "他のサイトへのリンク"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"MEMBERS PRICE - 10€ per ticket<br><br>\n"
"                Enjoy a discounted price if you subscribe to our yearly membership.<br>\n"
"                If you are already subscribed,"
msgstr ""
"メンバー価格 - チケット1枚10€<br><br>\n"
"                年間会員にご登録いただくと、割引価格でお楽しみ頂けます。\n"
"                すでにご登録の場合は、"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Make a Donation"
msgstr "寄付をする"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Make a donation of their preferred amount."
msgstr "希望の金額で寄付金を払う。"

#. module: non_profit_organization
#: model:product.template,name:non_profit_organization.product_product_1_product_template
msgid "Membership"
msgstr "会員資格"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Michは常にチャレンジし続けています。ソフトウェア業界で、コマーシャルディレクターとしての長年の経験があるこそ、会社をここまで成長させました。Michは間違いなくトップクラスの人材です。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "My Company"
msgstr "My Company"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the CRM Pipeline, you can see the created opportunity.  You can communicate with prospect and change the opportunity stage.<br/>\n"
"            Eventually, you can create a quotation for the customer by clicking on \"New Quotation\"."
msgstr ""
"CRMパイプラインでは、作成された案件を確認できます。見込み客とコミュニケーションをとり、案件のステージを変更することができます。<br/>\n"
"            最終的に、\"新規見積\" をクリックすることで、顧客に見積を作成することができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the website page \"[...].odoo.com/contactus\", visitor can fill a form.<br/>\n"
"            When the form is completed, a CRM opportunity will be created and the admin will get a mail notification.<br/>\n"
"            <br/>"
msgstr ""
"ウェブサイトページ \"[...].odoo.com/contactus\" で訪問者はフォームに入力することができます。<br/>\n"
"            フォームが完了すると、CRM案件が作成され、管理者にメール通知が送信されます。<br/>\n"
"            <br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in elementary school."
msgstr "小学校一年間。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in high school."
msgstr "高校一年間。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Online visitors will be able to get money to your association in just a few "
"steps."
msgstr "オンライン訪問者は、わずか数ステップであなたの団体に寄付を行うことができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Mission"
msgstr "当社の使命"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Values"
msgstr "当社の価値観"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"Our mission is to create a shared plan<br/> for saving the planet’s most "
"exceptional wild places."
msgstr "私たちの使命は、地球上で最も優れた野生の場所を守るための共有計画<br/>を作ることです。"

#. module: non_profit_organization
#: model:product.pricelist,name:non_profit_organization.product_pricelist_1
msgid "Paying member"
msgstr "有料メンバ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Portal: Pay the quotation online"
msgstr "ポータル: オンライン見積を支払う"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Price for members = 10 €<br>Price for non member = 30 €<br>"
msgstr "メンバー価格 = 10 €<br>非メンバー価格 = 30 €<br>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Privacy Policy"
msgstr "プライバシーポリシー"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Product \"Membership\" should be added as product"
msgstr "プロダクト \"会員資格\" をプロダクトとして追加する必要があります"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Programs and Initiatives"
msgstr "プログラムと取組み"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Read more"
msgstr "続きを読む"

#. module: non_profit_organization
#: model:event.event.ticket,name:non_profit_organization.event_event_ticket_1
msgid "Registration"
msgstr "登録"

#. module: non_profit_organization
#: model:mail.template,name:non_profit_organization.mail_template_1
msgid "Sales: Send Quotation (membership)"
msgstr "販売: 見積送付 (メンバーシップ)"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Section Subtitle"
msgstr "セクションサブタイトル"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "See planned event and by registration ticket."
msgstr "予定されているイベントとチケットの登録内容をご確認下さい。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Shaping our future"
msgstr "私たちの未来を創る"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Small or large, your contribution is essential."
msgstr "大小にかかわらず、あなたの貢献は非常に重要です。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Start with the customer – find out what they want and give it to them."
msgstr "お客様を第一に – 顧客が望むものを見極めて提供しましょう。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Submit"
msgstr "提出"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Subscription: Track progress and renew subscription"
msgstr "サブスクリプション: 進捗を追跡し、サブスクリプションを更新"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Terms of service"
msgstr "利用規約"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The button \"Go to website\" will allow you to publish your event and to "
"design your event website page."
msgstr "\"ウェブサイトへ\"ボタンをクリックすると、イベントを公開し、イベントウェブサイトページをデザインすることができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The product \"Event Registration\" can be used as ticket. It is configured to have a default price of 30$.<br/>\n"
"            The \"Member\" pricelist will overwrite the sale price to 10$. Remember that this pricelist is automatically applied to customer with ongoing membership subscription."
msgstr ""
"プロダクト\"イベント登録\" はチケットとして使用できます。デフォルト価格は30$に設定されています。<br/>\n"
"            \"会員\"価格リストは販売価格を10$に上書きします。この価格リストは、会員資格のサブスクリプションを継続中の顧客に自動的に適用されることにご注意下さい。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Then, you can click on button <strong>\"Send by "
"mail\".<br/></strong><span>Be careful not to click on \"confirm\", as it "
"would start the subscription even though the customer hasn't stat "
"paying.</span>"
msgstr ""
"すると、 "
"<strong>\"メールで送信\"ボタンをクリックできます。<br/></strong><span>\"確認\"ボタンをクリックしないようにご注意下さい。顧客が支払いを開始していないにもかかわらず、サブスクリプションが開始されてしまうためです。</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"これらの利用規約 (「条件」、「契約」)はサイト (「サイト運営者」、又は「私たち」)とあなた "
"(「ユーザー」、又は「あなた」)の間の合意です。この契約は、あなたのこのサイト及びサイトのプロダクト・サービス "
"(以下、「サイト」、又は「サービス」と総称します)の使用の一般取引条件を定めます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"This setup is for association organizing events and offering membership program.<br/>\n"
"            They have a website where people can:"
msgstr ""
"この設定は、団体がイベントを企画し、会員資格プログラムを提供するためのものです。<br/>\n"
"            ウェブサイトで訪問者は以下を行うことができます:"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_2
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_712
msgid "Update pricelist of customer with closing subscription"
msgstr "サブスクリプションを終了した顧客の価格リストを更新"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_1
msgid "Update pricelist of customer with ongoing membership subscription"
msgstr "サブスクリプションを継続中の顧客の価格リストを更新"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Use of Cookies"
msgstr "クッキーの使用"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the email marketing, you can easily communicate with stakeholders.<br/>\n"
"            For example, you can use the filter we created to communicate with paying members."
msgstr ""
"Eメールマーケティングを活用すれば、関係者と簡単にコミュニケーションを取ることができます。<br/>\n"
"            例えば、私たちが作成したフィルタを使用して、有料会員とコミュニケーションを取ることができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the event app, you can create events that will be visible on the "
"website. There is one already created to show you how it looks."
msgstr ""
"イベントアプリを使用すると、ウェブサイト上に表示されるイベントを作成することができます。例としてご覧頂けるよう、すでに作成済のイベントが1つあります。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"サイトがクッキーを使ってカスタマイズすることで、ユーザのサイト体験を向上させることがあります。ユーザはブラウザを設定して、受信したクッキーのインストールを通知・拒否することができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Design &amp; Sell Ticket"
msgstr "ウェブサイト: デザイン &amp; チケット販売"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Receive donation"
msgstr "ウェブサイト: 寄付金を受取る"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Request membership program"
msgstr "ウェブサイト: 会員プログラムを請求"

#. module: non_profit_organization
#: model_terms:web_tour.tour,rainbow_man_message:non_profit_organization.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "ようこそ！"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"When sending via mail, user can load template \"Sales: Send Quotation (membership)\".<br/>\n"
"                    That way, the mail body will tell the customer to create an account on the website."
msgstr ""
"メール経由で送る際、ユーザは \"販売: 見積を送信 (会員資格)\"をロードできます。<br/>\n"
"                    それにより、メール本文で顧客にウェブサイト上でアカウントを作成するよう促すことができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"今日、地球が直面しているあらゆる地球規模の問題に対して、<br/>その問題に関心を持つ人々のコミュニティは、悪影響を防ぐために拡大<br/>しています。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr "製品やサービスについて軽く説明しましょう。"

#. module: non_profit_organization
#: model:sale.subscription.plan,name:non_profit_organization.sale_subscription_plan_1
msgid "Yearly Membership"
msgstr "年間メンバーシップ"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can easily add a donation form on your website by using our building "
"block.  We created a page that you can access on "
"yourdatabase.odoo.com/donation  ."
msgstr ""
"弊社のビルディングブロックを使用すれば、ウェブサイトに簡単に寄付フォームを追加することができます。こちらにアクセスできるページを作成しました。yourdatabase.odoo.com/donation"
"  ."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can look at ongoing plans in the subscription app. There you can easily "
"communicate with customers and renew soon to expire membership."
msgstr ""
"サブスクリプションアプリでは、進行中のプランを確認できます。 "
"また、顧客とのコミュニケーションも簡単に行え、使用期限が迫った会員資格の更新もすぐに行うことができます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can use the filter \"Donations\" in the favourite to filter only "
"donations payments."
msgstr "お気に入りで\"寄付\"フィルタを使用すると、寄付の支払のみをフィルタリングできます。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"このサイトのリンクを通してアクセスする全てのサイトの法的条項及びその他の利用条件を必ず確認してください。オフサイトページや他のサイトにリンクする責任はユーザ本人に所在します。"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Your customers can purchase event ticket. If they are logged in, the "
"pricelist assigned to their customers will automatically apply and the "
"ticket will be cheaper."
msgstr "顧客はイベントチケットを購買できます。ログインしている場合、顧客に割当てられた価格表が自動的に適用され、チケットが安くなります。"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "remember to signin to see discounted price."
msgstr "割引価格をご覧になるには、サインインをお忘れなく。"

#. module: non_profit_organization
#: model:mail.template,subject:non_profit_organization.mail_template_1
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
