# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* non_profit_organization
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:48+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "\"Recurrence\" should be set as Yearly."
msgstr "\"Récurrence\" doit être défini sur Annuel."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "10"
msgstr "10"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "100"
msgstr "100"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "25"
msgstr "25"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "50"
msgstr "50"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "<b>ABOUT US</b>"
msgstr "<b>À PROPOS DE NOUS</b>"

#. module: non_profit_organization
#: model:mail.template,body_html:non_profit_organization.mail_template_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Hello,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Thanks for your interest in our program.<br>\n"
"                Your\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Once paid, your membership will be automatically enabled.<br>Don't forget to create an account on our website.<br>You will need to login later on to access discounted price.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Do not hesitate to contact us if you have any questions.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Bonjour,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Merci de l'intérêt que vous portez à notre programme.<br>\n"
"                Votre\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        facture pro forma pour <t t-out=\"doc_name or ''\">le devis</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (avec référence : <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        d'un montant de <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">10.00 $</span> est disponible.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">le devis</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (avec référence : <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        d'un montant de <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">10.00 $</span> attend votre validation.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Une fois le paiement effectué, votre adhésion sera automatiquement activée.<br>N'oubliez pas de créer un compte sur notre site web.<br>Vous devrez vous connecter par la suite pour accéder au prix réduit.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">N'hésitez pas à nous contacter si vous avez des questions.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"
msgstr "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Info bannière\"></i>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Comment</span>"
msgstr "<span class=\"s_website_form_label_content\">Commentaire</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Phone</span>"
msgstr "<span class=\"s_website_form_label_content\">Numéro de téléphone</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Adresse e-mail</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Votre nom</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Non Profit"
" Organization</span></span>"
msgstr ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: "
"36px;\">Organisation à but non lucratif</span></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "<span style=\"font-size: 36px;\">Business Flows</span>"
msgstr "<span style=\"font-size: 36px;\">Flux d'activités</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "A year of cultural awakening."
msgstr "Une année d'éveil culturel."

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_13
msgid "About Us"
msgstr "À propos"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "About us"
msgstr "À propos de nous"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Accounting: See donations"
msgstr "Comptabilité : Voir les dons"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline est une personne emblématique. On peut dire qu'elle aime ce qu'elle "
"fait. Elle encadre plus de 100 développeurs internes et s'occupe de la "
"communauté de milliers de développeurs."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Bien que ce site web puisse être lié à d'autres sites web, nous ne sommes "
"pas impliqués, directement ou indirectement, dans aucune validation, "
"association, parrainage, adhésion ou affiliation avec un site web lié, sauf "
"indication contraire dans la présente."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Amount"
msgstr "Montant"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"As soon as the payment is done, the order will be confirmed and the subscription start.<br/>\n"
"                    Also, thanks to an automated actions, the customer pricelist will be updated to \"Member\"<br/>"
msgstr ""
"Dès que le paiement est effectué, la commande est confirmée et l'abonnement démarre.<br/>\n"
"                    Une action automatisée mettra également à jour la liste de prix du client à \"Membre\".<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_header
msgid "Become a member"
msgstr "Devenez membre"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Before or after paying, customer should also create an account by going to "
"page [...].odoo.com/web/signup"
msgstr ""
"Avant ou après le paiement, le client doit également créer un compte sur la "
"page [...].odoo.com/web/signup"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Business Flows"
msgstr "Flux d'activités"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "CRM: Manage opportunity &amp; create a quotation"
msgstr "CRM : Gérer les opportunités et créer un devis"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Caring for a baby for 1 month."
msgstr "Prendre soin d'un bébé pendant 1 mois."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr "Il est possible de changer le monde.<br/> Nous l'avons déjà fait."

#. module: non_profit_organization
#: model:event.event,name:non_profit_organization.event_event_1
msgid "Conference - Our Annual program"
msgstr "Conférences - Notre programme annuel"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Contact us"
msgstr "Contactez-nous"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Custom Amount"
msgstr "Montant personnalisé"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Customer will receive a quotation email with a link to the customer portal. On the portal, he can pay.<br/>\n"
"            His payment details will be saved, the order will be confirmed and the subscription will start."
msgstr ""
"Le client recevra un e-mail de devis avec un lien vers le portail client. Sur le portail, il pourra payer.<br/>\n"
"            Ses coordonnées de paiement seront enregistrées, la commande sera confirmée et l'abonnement commencera."

#. module: non_profit_organization
#: model:account.analytic.plan,name:non_profit_organization.account_analytic_plan_1
msgid "Default"
msgstr "Par défaut"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Discover more"
msgstr "Découvrir plus"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Don't forget to enable payment provider, in the video we used the \"demo\" provider that accepts any data.<br/>\n"
"                    You can do that in Sales -&gt; Configuration -&gt; Payment Provider.<br/>"
msgstr ""
"N'oubliez pas d'activer le fournisseur de paiement, dans la vidéo nous avons utilisé le fournisseur \"démo\" qui accepte toutes les données.<br/>\n"
"                    Vous pouvez faire cela dans Ventes -> Configuration -> Fournisseur de paiement.<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Donate Now"
msgstr "Faire un don maintenant"

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_14
msgid "Donation"
msgstr "Don"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Event App: Create event"
msgstr "Application Événement : Créer un événement"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"    This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"Chaque année, nous invitons notre communauté, nos partenaires et nos utilisateurs à venir nous rencontrer ! C'est l'événement idéal pour se retrouver et présenter les nouvelles fonctionnalités, la roadmap des futures versions, le logiciel, mais aussi participer à des ateliers, sessions de formation, etc...\n"
"    Cet événement est également l'occasion de présenter les études de cas de nos partenaires, la méthodologie ou des développements. Rejoignez-nous et découvrez à la source les fonctionnalités de la nouvelle version !"

#. module: non_profit_organization
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_711
msgid "Execute Code"
msgstr "Exécuter le code"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Fill a form to request membership. Or buy membership directly on the "
"eCommerce."
msgstr ""
"Remplissez un formulaire pour demander un abonnement. Ou achetez un "
"abonnement directement sur l'eCommerce."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Fill this form and we'll back to you as soon as possible."
msgstr ""
"Remplissez ce formulaire et nous vous répondrons dans les plus brefs délais."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription"
msgstr "Flux 1 : Site Web -> CRM -> Portail (Devis) -> Abonnement"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 2:  Event -&gt; Website"
msgstr "Flux 2 : Événement -> Site Web"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 3: Website -&gt; Donation"
msgstr "Flux 3 : Site Web -> Don"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 4: Email Marketing -&gt; Mailing"
msgstr "Flux 4 : E-mail Marketing -> Mailing"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"Fondateur et visionnaire en chef, Tony est le moteur de l'entreprise. Il aime\n"
"                                                                rester occupé en participant au développement des stratégies logicielles,\n"
"                                                                de marketing et d'expérience client."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Get involved"
msgstr "Engagez-vous"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Help us protect and preserve for future generations"
msgstr "Aidez-nous à protéger et à préserver pour les générations futures"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"In addition, the organization use the email marketing application to send "
"news about upcoming events."
msgstr ""
"De plus, l'organisation utilise l'application de marketing par e-mail pour "
"envoyer des nouvelles sur les événements à venir."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Interested by becoming a member?"
msgstr "Envie de devenir membre ?"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Grâce à son expérience internationale, Iris nous aide à comprendre les "
"chiffres et à les améliorer. Elle est déterminée à réussir et met ses "
"compétences professionnelles au service de l'entreprise pour la faire "
"progresser."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Join us and make the planet a better place."
msgstr "Rejoignez-nous et faisons de la planète un endroit meilleur."

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Join us for this 24 hours Event"
msgstr "Rejoignez-nous pour cet événement de 24 heures"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later on, in the event application, you can communicate to participant &amp;"
" track attendance."
msgstr ""
"Ensuite, dans l'application Événements, vous pourrez communiquer avec les "
"participants et suivre leur présence."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later, you can see all payments received in "
"Accounting-&gt;Customers-&gt;Payments."
msgstr ""
"Vous pourrez voir tous les paiements reçus dans Comptabilité -> Clients -> "
"Paiements."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Learn more"
msgstr "En savoir plus"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Links to other Websites"
msgstr "Liens vers d'autres sites web"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"MEMBERS PRICE - 10€ per ticket<br><br>\n"
"                Enjoy a discounted price if you subscribe to our yearly membership.<br>\n"
"                If you are already subscribed,"
msgstr ""
"PRIX POUR LES MEMBRES - 10€ par ticket<br><br>\n"
"                Bénéficiez d'un tarif préférentiel en souscrivant à notre adhésion annuelle.<br>\n"
"                Si vous êtes déjà inscrit,"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Make a Donation"
msgstr "Faire un don"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Make a donation of their preferred amount."
msgstr "Faites un don du montant de votre choix."

#. module: non_profit_organization
#: model:product.template,name:non_profit_organization.product_product_1_product_template
msgid "Membership"
msgstr "Adhésion"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich adore relever des défis. Fort de son expérience de plusieurs années en "
"tant que directeur commercial dans l'industrie du logiciel, Mich a aidé "
"l'entreprise à en arriver là où elle en est aujourd'hui. Mich fait partie "
"des meilleurs esprits."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "My Company"
msgstr "Ma société"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the CRM Pipeline, you can see the created opportunity.  You can communicate with prospect and change the opportunity stage.<br/>\n"
"            Eventually, you can create a quotation for the customer by clicking on \"New Quotation\"."
msgstr ""
"Sur le Pipeline CRM, vous pouvez voir l'opportunité créée. Vous pouvez communiquer avec le prospect et changer l'étape de l'opportunité.<br/>\n"
"            Finalement, vous pouvez créer un devis pour le client en cliquant sur \"Nouveau Devis\"."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the website page \"[...].odoo.com/contactus\", visitor can fill a form.<br/>\n"
"            When the form is completed, a CRM opportunity will be created and the admin will get a mail notification.<br/>\n"
"            <br/>"
msgstr ""
"Sur la page du site Web \"[...].odoo.com/contactus\", le visiteur peut remplir un formulaire.<br/>\n"
"            Lorsque le formulaire est complété, une opportunité CRM sera créée et l'administrateur recevra une notification par e-mail.<br/>\n"
"            <br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in elementary school."
msgstr "Un an à l'école primaire."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in high school."
msgstr "Un an au lycée."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Online visitors will be able to get money to your association in just a few "
"steps."
msgstr ""
"Les visiteurs en ligne pourront faire un don à votre association en quelques"
" étapes seulement."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Mission"
msgstr "Notre mission"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Values"
msgstr "Nos valeurs"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"Our mission is to create a shared plan<br/> for saving the planet’s most "
"exceptional wild places."
msgstr ""
"Notre mission consiste à créer une stratégie commune<br/> pour sauver les "
"espaces sauvages les plus exceptionnels de la planète."

#. module: non_profit_organization
#: model:product.pricelist,name:non_profit_organization.product_pricelist_1
msgid "Paying member"
msgstr "Membre payant"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Portal: Pay the quotation online"
msgstr "Portail : Payer le devis en ligne"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Price for members = 10 €<br>Price for non member = 30 €<br>"
msgstr "Prix pour les membres = 10 €<br>Prix pour les non-membres = 30 €<br>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Privacy Policy"
msgstr "Politique vie privée"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Product \"Membership\" should be added as product"
msgstr "Le produit \"Abonnement\" doit être ajouté en tant que produit"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Programs and Initiatives"
msgstr "Programmes et initiatives"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Read more"
msgstr "En savoir plus"

#. module: non_profit_organization
#: model:event.event.ticket,name:non_profit_organization.event_event_ticket_1
msgid "Registration"
msgstr "Inscription"

#. module: non_profit_organization
#: model:mail.template,name:non_profit_organization.mail_template_1
msgid "Sales: Send Quotation (membership)"
msgstr "Ventes : Envoyer le devis (adhésion)"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Section Subtitle"
msgstr "Sous-titre de section"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "See planned event and by registration ticket."
msgstr "Voir l'événement planifié et acheter un ticket d'inscription."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Shaping our future"
msgstr "Façonner notre avenir"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Small or large, your contribution is essential."
msgstr "Petite ou grande, votre contribution est essentielle."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Commencez par le client : trouvez ce qu'il veut et donnez-le lui."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Submit"
msgstr "Soumettre"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Subscription: Track progress and renew subscription"
msgstr "Abonnement : Suivre la progression et renouveler l'abonnement"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Terms of service"
msgstr "Conditions d'utilisation"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The button \"Go to website\" will allow you to publish your event and to "
"design your event website page."
msgstr ""
"Le bouton \"Aller au site web\" vous permettra de publier votre événement et"
" de concevoir la page de votre site d'événements."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The product \"Event Registration\" can be used as ticket. It is configured to have a default price of 30$.<br/>\n"
"            The \"Member\" pricelist will overwrite the sale price to 10$. Remember that this pricelist is automatically applied to customer with ongoing membership subscription."
msgstr ""
"Le produit \"Inscription à l'événement\" peut être utilisé comme billet. Il est configuré avec un prix par défaut de 30 €.<br/>\n"
"            La liste de prix \"Membre\" remplacera le prix de vente par 10 €. N'oubliez pas que cette liste de prix est automatiquement appliquée aux clients ayant un abonnement en cours."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Then, you can click on button <strong>\"Send by "
"mail\".<br/></strong><span>Be careful not to click on \"confirm\", as it "
"would start the subscription even though the customer hasn't stat "
"paying.</span>"
msgstr ""
"Ensuite, cliquez sur le bouton <strong>\"Envoyer par "
"e-mail\"<br/></strong><span>. Faites attention de ne pas cliquer sur "
"\"Confirmer\", car cela lance l'abonnement même si le client n'a pas encore "
"payé.</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Ces conditions de service (\"Conditions\", \"Accord\") sont un accord entre "
"le site Web (\"Exploitant du site Web\", \"nous\", \"nous\" ou \"nos\") et "
"vous (\"Utilisateur\", \"vous\" ou \"votre \"). Le présent accord définit "
"les conditions générales de votre utilisation de ce site Web et de l'un de "
"ses produits ou services (collectivement, \"site Web\" ou \"services\")."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"This setup is for association organizing events and offering membership program.<br/>\n"
"            They have a website where people can:"
msgstr ""
"Cette configuration est destinée aux associations organisant des événements et offrant un programme d'abonnement.<br/>\n"
"            Elles ont un site web où les gens peuvent :"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_2
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_712
msgid "Update pricelist of customer with closing subscription"
msgstr ""
"Mettre à jour la liste de prix pour les clients dont l'abonnement arrive à "
"échéance."

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_1
msgid "Update pricelist of customer with ongoing membership subscription"
msgstr "Mettre à jour la liste de prix pour les membres"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Use of Cookies"
msgstr "Utilisation des cookies"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the email marketing, you can easily communicate with stakeholders.<br/>\n"
"            For example, you can use the filter we created to communicate with paying members."
msgstr ""
"En utilisant le marketing par e-mail, vous pouvez facilement communiquer avec les parties prenantes.<br/>\n"
"            Par exemple, vous pouvez utiliser le filtre que nous avons créé pour communiquer avec les membres payants."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the event app, you can create events that will be visible on the "
"website. There is one already created to show you how it looks."
msgstr ""
"En utilisant l'application Événements, vous pouvez créer des événements qui "
"seront visibles sur le site web. Il y en a déjà un créé pour vous montrer à "
"quoi cela ressemble."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Le site web peut utiliser des cookies afin de personnaliser et de faciliter "
"la navigation maximale de l'utilisateur sur ce site. L'Utilisateur peut "
"configurer son navigateur pour être notifié et refuser l'installation des "
"cookies que nous lui envoyons."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Design &amp; Sell Ticket"
msgstr "Site Web : Concevoir et vendre des billets"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Receive donation"
msgstr "Site Web : Recevoir des dons"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Request membership program"
msgstr "Site Web : Demander un programme d'abonnement"

#. module: non_profit_organization
#: model_terms:web_tour.tour,rainbow_man_message:non_profit_organization.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Bienvenue ! Amusez-vous en explorant."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"When sending via mail, user can load template \"Sales: Send Quotation (membership)\".<br/>\n"
"                    That way, the mail body will tell the customer to create an account on the website."
msgstr ""
"Lors de l'envoi par e-mail, l'utilisateur peut charger le modèle \"Ventes : Envoyer le devis (abonnement)\".<br/>\n"
"                    Ainsi, le corps du mail indiquera au client de créer un compte sur le site web."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"Face à tous les problèmes mondiaux auxquels notre planète est confrontée ce "
"jour,<br/> les communautés de personnes qui s'y intéressent se "
"multiplient<br/> pour en prévenir les effets négatifs."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Écrivez un ou deux paragraphes décrivant votre produit ou vos services. Pour"
" réussir, votre contenu doit être utile à vos lecteurs."

#. module: non_profit_organization
#: model:sale.subscription.plan,name:non_profit_organization.sale_subscription_plan_1
msgid "Yearly Membership"
msgstr "Adhésion annuelle"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can easily add a donation form on your website by using our building "
"block.  We created a page that you can access on "
"yourdatabase.odoo.com/donation  ."
msgstr ""
"Vous pouvez facilement ajouter un formulaire de don sur votre site Web en "
"utilisant notre bloc de construction. Nous avons créé une page à laquelle "
"vous pouvez accéder sur votrebasededonnées.odoo.com/don."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can look at ongoing plans in the subscription app. There you can easily "
"communicate with customers and renew soon to expire membership."
msgstr ""
"Vous pouvez consulter les plans en cours dans l'application d'abonnement. "
"Là, vous pouvez facilement communiquer avec les clients et renouveler les "
"abonnements sur le point d'expirer."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can use the filter \"Donations\" in the favourite to filter only "
"donations payments."
msgstr ""
"Vous pouvez utiliser le filtre \"Dons\" dans les favoris pour filtrer "
"uniquement les paiements de dons."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"Vous devez lire attentivement les mentions légales et les autres conditions "
"d'utilisation de tout site web auquel vous accédez via un lien à partir de "
"ce site web. Votre lien vers d'autres pages hors site ou d'autres sites web "
"est à vos propres risques."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Your customers can purchase event ticket. If they are logged in, the "
"pricelist assigned to their customers will automatically apply and the "
"ticket will be cheaper."
msgstr ""
"Vos clients peuvent acheter un ticket pour l'événement. S'ils sont "
"connectés, la liste de prix attribuée à leur compte client sera "
"automatiquement appliquée et le ticket sera moins cher."

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "remember to signin to see discounted price."
msgstr "Connectez-vous pour voir les réductions."

#. module: non_profit_organization
#: model:mail.template,subject:non_profit_organization.mail_template_1
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Pro forma' or 'Devis') or 'Commande' }} (Ref {{ "
"object.name or 'n/a' }})"
