# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* non_profit_organization
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-08 10:48+0000\n"
"PO-Revision-Date: 2024-09-27 12:51+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "\"Recurrence\" should be set as Yearly."
msgstr "\"Herhaling\" moet worden ingesteld op Jaarlijks."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "10"
msgstr "10"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "100"
msgstr "100"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "25"
msgstr "25"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "50"
msgstr "50"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "<b>ABOUT US</b>"
msgstr "<b>OVER ONS</b>"

#. module: non_profit_organization
#: model:mail.template,body_html:non_profit_organization.mail_template_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Hello,\n"
"                <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Thanks for your interest in our program.<br>\n"
"                Your\n"
"                <t t-if=\"ctx.get('proforma')\">\n"
"                        Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                        <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                        <t t-if=\"object.origin\">\n"
"                                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                        </t>\n"
"                        amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"                </t>\n"
"                <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Once paid, your membership will be automatically enabled.<br>Don't forget to create an account on our website.<br>You will need to login later on to access discounted price.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Do not hesitate to contact us if you have any questions.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"        <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"               <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"                Hallo,\n"
"               <br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p><p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Bedankt voor je interesse in ons programma.<br>\n"
"                Je\n"
"               <t t-if=\"ctx.get('proforma')\">\n"
"                        Pro forma factuur voor <t t-out=\"doc_name or ''\">offerte</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"                       <t t-if=\"object.origin\">\n"
"                                (met referentie: <t t-out=\"object.origin or ''\"></t> )\n"
"                       </t>\n"
"                        voor een bedrag van <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10,00</span> is beschikbaar.\n"
"               </t>\n"
"               <t t-else=\"\">\n"
"                       <t t-out=\"doc_name or ''\">offerte</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"                       <t t-if=\"object.origin\">\n"
"                                (met referentie: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"                       </t>\n"
"                        ten bedrage van <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10,00</span> is klaar voor beoordeling.\n"
"               </t>\n"
"               <br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Na betaling wordt je lidmaatschap automatisch geactiveerd.<br>Vergeet niet om een account aan te maken op onze website.<br>Je moet later inloggen om toegang te krijgen tot de korting.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\"><br></p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">Aarzel niet om contact met ons op te nemen als je vragen hebt.</p>\n"
"                <p style=\"box-sizing:border-box;margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"                        <br><br>\n"
"                        <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                </t>\n"
"                <br><br>\n"
"        </p>\n"
"</div>\n"
"        "

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"
msgstr ""
"<font style=\"font-size: 62px;\">\n"
"                                        <b>— Life on Earth —</b>\n"
"                                    </font>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope me-2\"/>\n"
"                                            <span><EMAIL></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">3575 Fake Buena Vista Avenue</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/>\n"
"                                            <span class=\"o_force_ltr\">+****************</span>"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"
msgstr "<i class=\"fs-4 fa fa-info-circle mb-3\" aria-label=\"Banner Info\"></i>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Comment</span>"
msgstr "<span class=\"s_website_form_label_content\">Opmerking</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "<span class=\"s_website_form_label_content\">Phone</span>"
msgstr "<span class=\"s_website_form_label_content\">Telefoon</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Je E-mail</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Je naam</span>\n"
"                                                            <span class=\"s_website_form_mark\">*</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Non Profit"
" Organization</span></span>"
msgstr ""
"<span style=\"font-size: 36px;\"><span style=\"font-size: 36px;\">Non-profit"
" organisatie</span></span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "<span style=\"font-size: 36px;\">Business Flows</span>"
msgstr "<span style=\"font-size: 36px;\">Bedrijfsstromen</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "A year of cultural awakening."
msgstr "Een jaar van culturele bewustwording."

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_13
msgid "About Us"
msgstr "Over ons"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "About us"
msgstr "Over ons"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Accounting: See donations"
msgstr "Boekhouding: Zie donaties"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Aline Turner, CTO"
msgstr "Aline Turner, CTO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr ""
"Aline is een van de iconische mensen in het leven die kunnen zeggen dat ze "
"houden van wat ze doen. Ze begeleidt meer dan 100 interne ontwikkelaars en "
"zorgt voor de community van duizenden ontwikkelaars."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr ""
"Hoewel deze website mogelijk gekoppeld is aan andere websites, impliceren "
"wij geen directe of indirecte goedkeuring, associatie, sponsoring, "
"instemming of aansluiting bij een gekoppelde website, tenzij dit specified "
"wordt vermeld."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Amount"
msgstr "Bedrag"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"As soon as the payment is done, the order will be confirmed and the subscription start.<br/>\n"
"                    Also, thanks to an automated actions, the customer pricelist will be updated to \"Member\"<br/>"
msgstr ""
"Zodra de betaling is gedaan, wordt de bestelling bevestigd en start het abonnement.<br/>\n"
"                    Dankzij een geautomatiseerde actie wordt de prijslijst van de klant bijgewerkt naar \"Lid\"<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_header
msgid "Become a member"
msgstr "Word lid"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Before or after paying, customer should also create an account by going to "
"page [...].odoo.com/web/signup"
msgstr ""
"Voor of na het betalen moet de klant ook een account aanmaken door naar "
"pagina [...].odoo.com/web/signup te gaan"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Business Flows"
msgstr "Bedrijfsstromen"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "CRM: Manage opportunity &amp; create a quotation"
msgstr "CRM: Beheer verkoopkans &amp; maak een offerte"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Caring for a baby for 1 month."
msgstr "1 maand voor een baby zorgen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Changing the world is possible.<br/> We’ve done it before."
msgstr ""
"De wereld veranderen is mogelijk.<br/> We hebben het al eerder gedaan."

#. module: non_profit_organization
#: model:event.event,name:non_profit_organization.event_event_1
msgid "Conference - Our Annual program"
msgstr "Conferentie - Ons jaarprogramma"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Contact us"
msgstr "Contact"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Custom Amount"
msgstr "Aangepast bedrag"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Customer will receive a quotation email with a link to the customer portal. On the portal, he can pay.<br/>\n"
"            His payment details will be saved, the order will be confirmed and the subscription will start."
msgstr ""
"De klant ontvangt een e-mail met een offerte en een link naar het klantenportaal. Op het portaal kan hij betalen.<br/>\n"
"            Zijn betalingsgegevens worden opgeslagen, de bestelling wordt bevestigd en het abonnement gaat van start."

#. module: non_profit_organization
#: model:account.analytic.plan,name:non_profit_organization.account_analytic_plan_1
msgid "Default"
msgstr "Standaard"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Discover more"
msgstr "Ontdek meer"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Don't forget to enable payment provider, in the video we used the \"demo\" provider that accepts any data.<br/>\n"
"                    You can do that in Sales -&gt; Configuration -&gt; Payment Provider.<br/>"
msgstr ""
"Vergeet niet om de betalingsprovider in te schakelen, in de video gebruikten we de \"demo\" provider die alle gegevens accepteert.<br/>\n"
"                    Je kunt dat doen in Verkoop -&gt; Configuratie -&gt; Betalingsprovider.<br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Donate Now"
msgstr "Nu doneren"

#. module: non_profit_organization
#: model:website.menu,name:non_profit_organization.website_menu_14
msgid "Donation"
msgstr "Donatie"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Event App: Create event"
msgstr "Evenement App: Evenement maken"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...\n"
"    This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!"
msgstr ""
"Elk jaar nodigen we onze community, contacten en eindgebruikers uit om ons te komen ontmoeten! Het is het ideale evenement om samen te komen en nieuwe functies, de roadmap van toekomstige versies, prestaties van de software, workshops, trainingen, enz. te presenteren.\n"
"    Dit evenement is ook een gelegenheid om de case studies, methodologie of ontwikkelingen van onze contacten te laten zien. Wees erbij en bekijk de functies van de nieuwe versie direct vanaf de bron!"

#. module: non_profit_organization
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_711
msgid "Execute Code"
msgstr "Code uitvoeren"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Fill a form to request membership. Or buy membership directly on the "
"eCommerce."
msgstr ""
"Vul een formulier in om een lidmaatschap aan te vragen. Of koop direct een "
"lidmaatschap via de e-commerce."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Fill this form and we'll back to you as soon as possible."
msgstr "Vul dit formulier in en we nemen zo snel mogelijk contact met je op."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 1: Website -&gt; CRM -&gt; Portal (Quotation) -&gt; Subscription"
msgstr "Stroom 1: Website -&gt; CRM -&gt; Portaal (Offerte) -&gt; Abonnement"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 2:  Event -&gt; Website"
msgstr "Stroom 2: Evenement -&gt; Website"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 3: Website -&gt; Donation"
msgstr "Stroom 3: Website -&gt; Donatie"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Flow 4: Email Marketing -&gt; Mailing"
msgstr "Stroom 4: E-mailmarketing -&gt; Mailing"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                                                to keep his hands full by participating in the development of the software,\n"
"                                                                marketing, and customer experience strategies."
msgstr ""
"Oprichter en belangrijkste visionair Tony is de drijvende kracht achter het bedrijf. Hij houdt\n"
"                                                                zijn handen vol aan de ontwikkeling van de software,\n"
"                                                                marketing en klantervaringsstrategieën."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Get involved"
msgstr "Doe mee"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Help us protect and preserve for future generations"
msgstr "Help ons beschermen en behouden voor toekomstige generaties"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"In addition, the organization use the email marketing application to send "
"news about upcoming events."
msgstr ""
"Daarnaast gebruikt de organisatie de e-mailmarketingapplicatie om nieuws "
"over komende evenementen te versturen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Interested by becoming a member?"
msgstr "Heb je interesse om lid te worden?"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Iris Joe, CFO"
msgstr "Iris Joe, CFO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr ""
"Iris, met haar internationale ervaring, helpt ons de cijfers gemakkelijk te "
"begrijpen en te verbeteren. Ze is vastbesloten om succes te stimuleren en "
"levert haar professionele inzicht om het bedrijf naar een hoger niveau te "
"tillen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Join us and make the planet a better place."
msgstr "Doe mee en maak van de planeet een betere plek."

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Join us for this 24 hours Event"
msgstr "Doe mee met dit 24-uurs-evenement"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later on, in the event application, you can communicate to participant &amp;"
" track attendance."
msgstr ""
"Later, in de evenementtoepassing, kun je communiceren met deelnemer &amp; "
"aanwezigheid bijhouden."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Later, you can see all payments received in "
"Accounting-&gt;Customers-&gt;Payments."
msgstr ""
"Later kun je alle ontvangen betalingen bekijken in "
"Accounting-&gt;Klanten-&gt;Betalingen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Learn more"
msgstr "Leer meer"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Links to other Websites"
msgstr "Links naar andere websites"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid ""
"MEMBERS PRICE - 10€ per ticket<br><br>\n"
"                Enjoy a discounted price if you subscribe to our yearly membership.<br>\n"
"                If you are already subscribed,"
msgstr ""
"LEDENPRIJS - 10€ per kaartje<br><br>\n"
"                Geniet van een gereduceerde prijs als je je inschrijft voor ons jaarlijkse lidmaatschap.<br>\n"
"                Als je al lid bent,"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Make a Donation"
msgstr "Doe een donatie"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Make a donation of their preferred amount."
msgstr "Doe een donatie van het bedrag van hun voorkeur."

#. module: non_profit_organization
#: model:product.template,name:non_profit_organization.product_product_1_product_template
msgid "Membership"
msgstr "Lidmaatschap"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Mich Stark, COO"
msgstr "Mich Stark, COO"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr ""
"Mich gaat graag uitdagingen aan. Met zijn meerjarige ervaring als "
"commercieel directeur in de software-branche heeft Mich het bedrijf geholpen"
" om te komen waar het nu is. Mich is een van de knapste koppen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "My Company"
msgstr "Mijn bedrijf"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the CRM Pipeline, you can see the created opportunity.  You can communicate with prospect and change the opportunity stage.<br/>\n"
"            Eventually, you can create a quotation for the customer by clicking on \"New Quotation\"."
msgstr ""
"In de CRM-Pipeline kun je de aangemaakte verkoopkans zien.  Je kunt communiceren met de prospect en de fase van de verkoopkans wijzigen.<br/>\n"
"            Uiteindelijk kun je een offerte maken voor de klant door te klikken op \"Nieuwe offerte\"."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"On the website page \"[...].odoo.com/contactus\", visitor can fill a form.<br/>\n"
"            When the form is completed, a CRM opportunity will be created and the admin will get a mail notification.<br/>\n"
"            <br/>"
msgstr ""
"Op de website pagina \"[...].odoo.com/contactus\" kan de bezoeker een formulier invullen.<br/>\n"
"            Als het formulier is ingevuld, wordt er een CRM-verkoopkans aangemaakt en krijgt de beheerder een e-mailnotificatie.<br/>\n"
"            <br/>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in elementary school."
msgstr "Een jaar op de basisschool."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "One year in high school."
msgstr "Een jaar op de middelbare school."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Online visitors will be able to get money to your association in just a few "
"steps."
msgstr ""
"Online bezoekers kunnen in een paar stappen geld overmaken naar je "
"vereniging."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Mission"
msgstr "Onze missie"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Our Values"
msgstr "Onze waarden"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid ""
"Our mission is to create a shared plan<br/> for saving the planet’s most "
"exceptional wild places."
msgstr ""
"Het is onze missie om een gedeeld plan te maken<br/> voor het redden van de "
"meest uitzonderlijke plekken op aarde."

#. module: non_profit_organization
#: model:product.pricelist,name:non_profit_organization.product_pricelist_1
msgid "Paying member"
msgstr "Betalend lid"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Portal: Pay the quotation online"
msgstr "Portaal: Betaal de offerte online"

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "Price for members = 10 €<br>Price for non member = 30 €<br>"
msgstr "Prijs voor leden = 10 €<br>Prijs voor niet-leden = 30 €<br>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Privacy Policy"
msgstr "Privacybeleid"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Product \"Membership\" should be added as product"
msgstr "Product \"Lidmaatschap\" moet worden toegevoegd als product"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Programs and Initiatives"
msgstr "Programma's en initiatieven"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
msgid "Read more"
msgstr "Lees meer"

#. module: non_profit_organization
#: model:event.event.ticket,name:non_profit_organization.event_event_ticket_1
msgid "Registration"
msgstr "Registratie"

#. module: non_profit_organization
#: model:mail.template,name:non_profit_organization.mail_template_1
msgid "Sales: Send Quotation (membership)"
msgstr "Verkoop: Offerte versturen (lidmaatschap)"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Section Subtitle"
msgstr "Sectie subtitel"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "See planned event and by registration ticket."
msgstr "Zie gepland evenement en per registratiekaartje."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Shaping our future"
msgstr "Onze toekomst vormgeven"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_donation_view
msgid "Small or large, your contribution is essential."
msgstr "Klein of groot, je bijdrage is essentieel."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Start met de klant  – vind wat de klanten willen en geef het hen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.contactus
msgid "Submit"
msgstr "Indienen"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Subscription: Track progress and renew subscription"
msgstr "Abonnement: Voortgang bijhouden en abonnement verlengen"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Terms of service"
msgstr "Servicevoorwaarden"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The button \"Go to website\" will allow you to publish your event and to "
"design your event website page."
msgstr ""
"Met de knop \"Ga naar website\" kun je je evenement publiceren en de "
"websitepagina van je evenement ontwerpen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"The product \"Event Registration\" can be used as ticket. It is configured to have a default price of 30$.<br/>\n"
"            The \"Member\" pricelist will overwrite the sale price to 10$. Remember that this pricelist is automatically applied to customer with ongoing membership subscription."
msgstr ""
"Het product \"Evenementregistratie\" kan worden gebruikt als ticket. Het is geconfigureerd om een standaardprijs van 30$ te hebben.<br/>\n"
"            De prijslijst \"Lid\" zal de verkoopprijs overschrijven naar 10$. Vergeet niet dat deze prijslijst automatisch wordt toegepast op klanten met een doorlopend lidmaatschapsabonnement."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Then, you can click on button <strong>\"Send by "
"mail\".<br/></strong><span>Be careful not to click on \"confirm\", as it "
"would start the subscription even though the customer hasn't stat "
"paying.</span>"
msgstr ""
"Daarna kun je op de knop <strong>\"Verzenden per e-mail\" "
"klikken.<br/></strong><span>Pas op dat je niet op \"bevestigen\" klikt, want"
" dan start het abonnement ook al heeft de klant nog niet betaald.</span>"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"Deze servicevoorwaarden (\"Voorwaarden\", \"Overeenkomst\") vormen een "
"overeenkomst tussen de website (\"Website-operator\", \"ons\", \"wij\" of "
"\"onze\") en je (\"Gebruiker\", \"jij\" of \"je\"). Deze overeenkomst bevat "
"de algemene voorwaarden voor je gebruik van deze website en alle producten "
"of diensten (gezamenlijk, \"Website\" of \"Diensten\")."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"This setup is for association organizing events and offering membership program.<br/>\n"
"            They have a website where people can:"
msgstr ""
"Deze opzet is voor verenigingen die evenementen organiseren en lidmaatschapsprogramma's aanbieden.<br/>\n"
"            Ze hebben een website waar mensen kunnen:"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid "Tony Fred, CEO"
msgstr "Tony Fred, CEO"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_2
#: model:ir.actions.server,name:non_profit_organization.ir_act_server_712
msgid "Update pricelist of customer with closing subscription"
msgstr "Prijslijst van klant met afsluitend abonnement bijwerken"

#. module: non_profit_organization
#: model:base.automation,name:non_profit_organization.base_automation_1
msgid "Update pricelist of customer with ongoing membership subscription"
msgstr "Prijslijst bijwerken van klant met doorlopend lidmaatschapsabonnement"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid "Use of Cookies"
msgstr "Gebruik van cookies"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the email marketing, you can easily communicate with stakeholders.<br/>\n"
"            For example, you can use the filter we created to communicate with paying members."
msgstr ""
"Met e-mailmarketing kun je eenvoudig communiceren met belanghebbenden.<br/>\n"
"            Je kunt bijvoorbeeld de filter gebruiken die we hebben gemaakt om met betalende leden te communiceren."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Using the event app, you can create events that will be visible on the "
"website. There is one already created to show you how it looks."
msgstr ""
"Met de evenementen-app kun je evenementen maken die zichtbaar zijn op de "
"website. Er is er al een gemaakt om je te laten zien hoe het eruitziet."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr ""
"Website kan cookies gebruiken om de navigatie van de website te "
"personaliseren en te vergemakkelijken voor de gebruiker van deze site. De "
"gebruiker kan zijn / haar browser configureren om de kennisgeving en de "
"installatie van de cookies die door ons zijn verzonden af te wijzen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Design &amp; Sell Ticket"
msgstr "Website: Ontwerp & Verkoop Ticket"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Receive donation"
msgstr "Website: Donatie ontvangen"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid "Website: Request membership program"
msgstr "Website: Lidmaatschapsprogramma aanvragen"

#. module: non_profit_organization
#: model_terms:web_tour.tour,rainbow_man_message:non_profit_organization.knowledge_tour
msgid "Welcome! Happy exploring."
msgstr "Welkom! Veel plezier met verkennen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"When sending via mail, user can load template \"Sales: Send Quotation (membership)\".<br/>\n"
"                    That way, the mail body will tell the customer to create an account on the website."
msgstr ""
"Bij het verzenden via e-mail kan de gebruiker de sjabloon \"Verkoop: Offerte verzenden (lidmaatschap)\".<br/>\n"
"                    Op die manier zal de body van de e-mail de klant vertellen om een account aan te maken op de website."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.homepage
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"With all the global problems our planet faces today,<br/> communities of "
"people concerned with them are growing<br/> to prevent the negative impact."
msgstr ""
"Met alle wereldwijde problemen waarmee onze planeet vandaag geconfronteerd "
"wordt,<br/> groeien de gemeenschappen van mensen die hiervoor aandacht "
"hebben<br/> om de negatieve gevolgen te voorkomen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_about_us_view
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Schrijf een of twee paragrafen die je product of diensten beschrijven. Om "
"succesvol te zijn, moet je inhoud nuttig zijn voor je lezers."

#. module: non_profit_organization
#: model:sale.subscription.plan,name:non_profit_organization.sale_subscription_plan_1
msgid "Yearly Membership"
msgstr "Jaarlijks lidmaatschap"

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can easily add a donation form on your website by using our building "
"block.  We created a page that you can access on "
"yourdatabase.odoo.com/donation  ."
msgstr ""
"Je kunt eenvoudig een donatieformulier toevoegen aan je website door onze "
"bouwsteen te gebruiken.  We hebben een pagina gemaakt die je kunt openen op "
"yourdatabase.odoo.com/donation ."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can look at ongoing plans in the subscription app. There you can easily "
"communicate with customers and renew soon to expire membership."
msgstr ""
"Je kunt lopende plannen bekijken in de abonnementen-app. Daar kun je "
"eenvoudig communiceren met klanten en binnenkort aflopende abonnementen "
"verlengen."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"You can use the filter \"Donations\" in the favourite to filter only "
"donations payments."
msgstr ""
"Je kunt de filter \"Donaties\" in de favoriet gebruiken om alleen betalingen"
" van donaties te filteren."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.website_page_privacy_policy_view
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr ""
"Je moet de wettelijke verklaringen en andere gebruiksvoorwaarden van elke "
"website die je bezoekt via een link vanaf deze website goed doornemen. Jouw "
"link naar andere off-site pagina's of andere websites zijn op eigen risico."

#. module: non_profit_organization
#: model_terms:ir.ui.view,arch_db:non_profit_organization.welcome_article_body
msgid ""
"Your customers can purchase event ticket. If they are logged in, the "
"pricelist assigned to their customers will automatically apply and the "
"ticket will be cheaper."
msgstr ""
"Je klanten kunnen een ticket voor een evenement kopen. Als ze ingelogd zijn,"
" wordt de prijslijst die aan hun klanten is toegewezen automatisch toegepast"
" en is het ticket goedkoper."

#. module: non_profit_organization
#: model_terms:event.event,description:non_profit_organization.event_event_1
msgid "remember to signin to see discounted price."
msgstr "vergeet niet in te loggen om de kortingsprijs te zien."

#. module: non_profit_organization
#: model:mail.template,subject:non_profit_organization.mail_template_1
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Offerte') or 'Order' }} (Ref {{ "
"object.name or 'nvt' }})"
