<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="event_event_1" model="event.event">
        <field name="name">Conference - Our Annual program</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="address_inline">Industry-Non Profit Organization, Belgium</field>
        <field name="address_search" ref="base.main_partner"/>
        <field name="event_registrations_started" eval="True"/>
        <field name="stage_id" ref="event.event_stage_new"/>
        <field name="address_name">Industry-Non Profit Organization</field>
        <field name="address_id" ref="base.main_partner"/>
        <field name="date_begin" model="res.users" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.now().replace(hour=10, minute=30) + relativedelta(months=1, weekday=1)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="date_end" model="res.users" eval="
            pytz.timezone(obj().env.user.tz or 'UTC').localize(
                DateTime.now().replace(hour=10, minute=30) + relativedelta(months=1, weekday=1, days=1)
            ).astimezone(pytz.UTC).replace(tzinfo=None)
        "/>
        <field name="organizer_id" ref="base.main_partner"/>
        <field name="is_published" eval="True"/>
        <field name="event_registrations_open" eval="True"/>
        <field name="can_publish" eval="True"/>
        <field name="description" type="html">
<section class="s_text_block o_colored_level" style="background-image: none;" data-name="Text">
    <h5>Join us for this 24 hours Event</h5>
    <p>Every year we invite our community, partners and end-users to come and meet us! It's the ideal event to get together and present new features, roadmap of future versions, achievements of the software, workshops, training sessions, etc...
    This event is also an opportunity to showcase our partners' case studies, methodology or developments. Be there and see directly from the source the features of the new version!</p>
    <p>Price for members = 10 €<br/>Price for non member = 30 €<br/></p>
</section>
<section class="s_text_block o_colored_level pt0 pb8 o_snippet_invisible o_conditional_hidden" data-snippet="s_text_block" data-name="Text" style="background-image: none;" data-visibility="conditional" data-visibility-value-logged='[{"value":"false","id":2}]' data-visibility-selectors='html:not([data-logged="false"]) body:not(.editor_enable) [data-visibility-id="logged_o_2"]' data-visibility-id="logged_o_2">
    <div class="s_allow_columns container">
        <p><br/></p>
        <div class="o_editor_banner o_not_editable lh-1 d-flex align-items-center alert alert-info pb-0 pt-3" role="status" data-oe-protected="true">
            <i class="fs-4 fa fa-info-circle mb-3" aria-label="Banner Info"></i>
            <div class="o_editable_no_shadow w-100 ms-3" data-oe-protected="false">
                MEMBERS PRICE - 10€ per ticket<br/><br/>
                Enjoy a discounted price if you subscribe to our yearly membership.<br/>
                If you are already subscribed, <a href="/web/login" data-bs-original-title="" title="">remember to signin to see discounted price.</a><br/><br/>
            </div>
        </div>
        <p class="o_default_snippet_text"></p>
    </div>
</section>
        </field>
    </record>
</odoo>
