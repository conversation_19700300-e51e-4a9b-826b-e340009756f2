<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">

    <record id="configurator_1_library_image_02" model="ir.attachment">
        <field name="name">website.library_image_02</field>
        <field name="key">non_profit_organization.library_image_02</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/479-website.library_image_02" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_03" model="ir.attachment">
        <field name="name">website.library_image_03</field>
        <field name="key">non_profit_organization.library_image_03</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/472-website.library_image_03" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_05" model="ir.attachment">
        <field name="name">website.library_image_05</field>
        <field name="key">non_profit_organization.library_image_05</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/475-website.library_image_05" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_08" model="ir.attachment">
        <field name="name">website.library_image_08</field>
        <field name="key">non_profit_organization.library_image_08</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/478-website.library_image_08" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_10" model="ir.attachment">
        <field name="name">website.library_image_10</field>
        <field name="key">non_profit_organization.library_image_10</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/473-website.library_image_10" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_13" model="ir.attachment">
        <field name="name">website.library_image_13</field>
        <field name="key">non_profit_organization.library_image_13</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/474-website.library_image_13" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_14" model="ir.attachment">
        <field name="name">website.library_image_14</field>
        <field name="key">non_profit_organization.library_image_14</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/476-website.library_image_14" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_library_image_16" model="ir.attachment">
        <field name="name">website.library_image_16</field>
        <field name="key">non_profit_organization.library_image_16</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/477-website.library_image_16" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_banner_default_image" model="ir.attachment">
        <field name="name">website.s_banner_default_image</field>
        <field name="key">non_profit_organization.s_banner_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/456-website.s_banner_default_image" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_carousel_default_image_1" model="ir.attachment">
        <field name="name">website.s_carousel_default_image_1</field>
        <field name="key">non_profit_organization.s_carousel_default_image_1</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/453-website.s_carousel_default_image_1" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_carousel_default_image_2" model="ir.attachment">
        <field name="name">website.s_carousel_default_image_2</field>
        <field name="key">non_profit_organization.s_carousel_default_image_2</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/454-website.s_carousel_default_image_2" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_carousel_default_image_3" model="ir.attachment">
        <field name="name">website.s_carousel_default_image_3</field>
        <field name="key">non_profit_organization.s_carousel_default_image_3</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/455-website.s_carousel_default_image_3" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_cover_default_image" model="ir.attachment">
        <field name="name">website.s_cover_default_image</field>
        <field name="key">non_profit_organization.s_cover_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/446-website.s_cover_default_image" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_image_text_default_image" model="ir.attachment">
        <field name="name">website.s_image_text_default_image</field>
        <field name="key">non_profit_organization.s_image_text_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/466-website.s_image_text_default_image" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_masonry_block_default_image_1" model="ir.attachment">
        <field name="name">website.s_masonry_block_default_image_1</field>
        <field name="key">non_profit_organization.s_masonry_block_default_image_1</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/447-website.s_masonry_block_default_image_1" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_media_list_default_image_1" model="ir.attachment">
        <field name="name">website.s_media_list_default_image_1</field>
        <field name="key">non_profit_organization.s_media_list_default_image_1</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/448-website.s_media_list_default_image_1" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_media_list_default_image_2" model="ir.attachment">
        <field name="name">website.s_media_list_default_image_2</field>
        <field name="key">non_profit_organization.s_media_list_default_image_2</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/449-website.s_media_list_default_image_2" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_media_list_default_image_3" model="ir.attachment">
        <field name="name">website.s_media_list_default_image_3</field>
        <field name="key">non_profit_organization.s_media_list_default_image_3</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/450-website.s_media_list_default_image_3" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_parallax_default_image" model="ir.attachment">
        <field name="name">website.s_parallax_default_image</field>
        <field name="key">non_profit_organization.s_parallax_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/457-website.s_parallax_default_image" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_picture_default_image" model="ir.attachment">
        <field name="name">website.s_picture_default_image</field>
        <field name="key">non_profit_organization.s_picture_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/465-website.s_picture_default_image" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_catalog_default_image" model="ir.attachment">
        <field name="name">website.s_product_catalog_default_image</field>
        <field name="key">non_profit_organization.s_product_catalog_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/458-website.s_product_catalog_default_image" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_1" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_1</field>
        <field name="key">non_profit_organization.s_product_list_default_image_1</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/459-website.s_product_list_default_image_1" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_2" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_2</field>
        <field name="key">non_profit_organization.s_product_list_default_image_2</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/460-website.s_product_list_default_image_2" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_3" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_3</field>
        <field name="key">non_profit_organization.s_product_list_default_image_3</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/461-website.s_product_list_default_image_3" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_4" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_4</field>
        <field name="key">non_profit_organization.s_product_list_default_image_4</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/462-website.s_product_list_default_image_4" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_5" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_5</field>
        <field name="key">non_profit_organization.s_product_list_default_image_5</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/463-website.s_product_list_default_image_5" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_product_list_default_image_6" model="ir.attachment">
        <field name="name">website.s_product_list_default_image_6</field>
        <field name="key">non_profit_organization.s_product_list_default_image_6</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/464-website.s_product_list_default_image_6" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_quotes_carousel_demo_image_0" model="ir.attachment">
        <field name="name">website.s_quotes_carousel_demo_image_0</field>
        <field name="key">non_profit_organization.s_quotes_carousel_demo_image_0</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/471-website.s_quotes_carousel_demo_image_0" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_quotes_carousel_demo_image_1" model="ir.attachment">
        <field name="name">website.s_quotes_carousel_demo_image_1</field>
        <field name="key">non_profit_organization.s_quotes_carousel_demo_image_1</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/451-website.s_quotes_carousel_demo_image_1" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_quotes_carousel_demo_image_2" model="ir.attachment">
        <field name="name">website.s_quotes_carousel_demo_image_2</field>
        <field name="key">non_profit_organization.s_quotes_carousel_demo_image_2</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/452-website.s_quotes_carousel_demo_image_2" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_text_image_default_image" model="ir.attachment">
        <field name="name">website.s_text_image_default_image</field>
        <field name="key">non_profit_organization.s_text_image_default_image</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/467-website.s_text_image_default_image" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_three_columns_default_image_1" model="ir.attachment">
        <field name="name">website.s_three_columns_default_image_1</field>
        <field name="key">non_profit_organization.s_three_columns_default_image_1</field>
        <field name="website_id" ref="website.default_website"/>
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/468-website.s_three_columns_default_image_1" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_three_columns_default_image_2" model="ir.attachment">
        <field name="name">website.s_three_columns_default_image_2</field>
        <field name="key">non_profit_organization.s_three_columns_default_image_2</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/469-website.s_three_columns_default_image_2" />
        <field name="public" eval="True"/>
    </record>
    <record id="configurator_1_s_three_columns_default_image_3" model="ir.attachment">
        <field name="name">website.s_three_columns_default_image_3</field>
        <field name="key">non_profit_organization.s_three_columns_default_image_3</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/470-website.s_three_columns_default_image_3" />
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_601" model="ir.attachment">
        <field name="name">Team Member 1.jpg</field>
        <field name="key">non_profit_organization.s_company_team_image_1</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/601-team_team_member_1.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_602" model="ir.attachment">
        <field name="name">Team Member 2.jpg</field>
        <field name="key">non_profit_organization.s_company_team_image_2</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/602-team_team_member_2.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_603" model="ir.attachment">
        <field name="name">Team Member 3.jpg</field>
        <field name="key">non_profit_organization.s_company_team_image_3</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/603-team_team_member_3.jpg" />
        <field name="public" eval="True"/>
    </record>
    <record id="ir_attachment_604" model="ir.attachment">
        <field name="name">Team Member 4.jpg</field>
        <field name="key">non_profit_organization.s_company_team_image_4</field>
        <field name="website_id" ref="website.default_website" />
        <field name="datas" type="base64" file="non_profit_organization/static/src/binary/ir_attachment/604-team_team_member_4.jpg" />
        <field name="public" eval="True"/>
    </record>
</odoo>
