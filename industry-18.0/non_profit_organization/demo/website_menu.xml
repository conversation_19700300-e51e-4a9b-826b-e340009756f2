<?xml version='1.0' encoding='UTF-8'?>
<odoo noupdate="1">
    <record id="website_menu_13" model="website.menu">
        <field name="url">/about-us</field>
        <field name="parent_id" model="website.menu" eval="obj().search([('url', '=', '/default-main-menu'), ('website_id', '=', obj().env.ref('website.default_website').id)], limit=1).id"/>
        <field name="website_id" ref="website.default_website"/>
        <field name="name">About Us</field>
        <field name="sequence">59</field>
        <field name="page_id" ref="website_page_5"/>
    </record>
    <record id="website_menu_14" model="website.menu">
        <field name="url">/donation</field>
        <field name="parent_id" model="website.menu" eval="obj().search([('url', '=', '/default-main-menu'), ('website_id', '=', obj().env.ref('website.default_website').id)], limit=1).id"/>
        <field name="website_id" ref="website.default_website"/>
        <field name="name">Donation</field>
        <field name="sequence">11</field>
        <field name="page_id" ref="website_page_7"/>
    </record>
</odoo>
