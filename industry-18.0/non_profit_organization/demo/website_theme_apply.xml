<?xml version='1.0' encoding='UTF-8'?>
<odoo>
    <function model="ir.module.module" name="_theme_load" context="{'apply_new_theme': True}">
        <value model="ir.module.module" eval="obj().env['ir.module.module'].search([('name', '=', 'theme_treehouse')]).ids"/>
        <value model="ir.module.module" eval="obj().env.ref('website.default_website')"/>
    </function>
    <function model="ir.ui.view" name="write">
        <value model="ir.ui.view" eval="obj().env['website'].with_context(website_id=obj().env.ref('website.default_website').id).viewref('website.homepage').id"/>
        <value model="ir.ui.view" eval="{'arch': obj().env.ref('non_profit_organization.homepage').arch}"/>
    </function>
    <function model="ir.ui.view" name="write">
        <value model="ir.ui.view" eval="obj().env['website'].with_context(website_id=obj().env.ref('website.default_website').id).viewref('website.contactus').id"/>
        <value model="ir.ui.view" eval="{'arch': obj().env.ref('non_profit_organization.contactus').arch}"/>
    </function>
    <function model="web_editor.assets" name="make_scss_customization">
        <value eval="'/website/static/src/scss/options/colors/user_color_palette.scss'"/>
        <value eval="{'o-color-1': '#20C997', 'o-color-2': '#426A5A', 'o-color-3': '#f0f5f4', 'o-color-4': '#FFFFFF', 'o-color-5': '#142e26', 'o-theme-font': 'Poppins', 'menu': 2, 'footer': 2, 'copyright': 2}"/>
    </function>
</odoo>
