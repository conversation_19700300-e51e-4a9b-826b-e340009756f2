<?xml version="1.0"?>
<odoo><data noupdate="1">

<record id="forum_post_view_form" model="ir.ui.view">
    <field name="name">forum.post.view.form</field>
    <field name="model">forum.post</field>
    <field name="arch" type="xml">
        <form string="Forum Post">
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button type="object" class="oe_stat_button" icon="fa-globe" name="go_to_website">
                        <div class="o_form_field o_stat_info">
                            <span class="o_stat_text">Go to <br/>Website</span>
                        </div>
                    </button>
                </div>
                <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                <label for="name"/>
                <h1>
                    <field name="name" placeholder="e.g. When should I plant my tomatoes?"/>
                </h1>
                <group>
                    <group name="forum_details">
                        <field name="active" invisible="1"/>
                        <field name="forum_id"/>
                        <field name="website_id" groups="website.group_multi_website"/>
                        <field name="parent_id" invisible="not parent_id"/>
                    </group>
                    <group name="post_details">
                        <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                        <field name="state"/>
                        <field name="closed_reason_id" invisible="not closed_reason_id"/>
                        <field name="closed_uid" invisible="not closed_uid"/>
                        <field name="closed_date" invisible="not closed_date"/>
                    </group>
                    <group name="creation_details">
                        <field name="create_uid"/>
                        <field name="create_date"/>
                        <field name="write_uid"/>
                        <field name="write_date"/>
                    </group>
                    <group name="post_statistics">
                        <field name="is_correct" invisible="not parent_id"/>
                        <field name="views"/>
                        <field name="vote_count"/>
                        <field name="favourite_count"/>
                        <field name="child_count"/>
                        <field name="relevancy"/>
                    </group>
                </group>
                <group name="answers" string="Answers" invisible="parent_id">
                    <field name="child_ids" nolabel="1">
                        <list>
                            <field name="create_uid" string="Answered by"/>
                            <field name="vote_count"/>
                            <field name="state"/>
                            <field name="is_correct"/>
                        </list>
                    </field>
                </group>
            </sheet>
            <chatter/>
        </form>
    </field>
</record>

<record id="forum_post_view_search" model="ir.ui.view">
    <field name="name">forum.post.view.search</field>
    <field name="model">forum.post</field>
    <field name="arch" type="xml">
        <search string="Search in Post">
            <field name="name" string="Content" filter_domain="['|', ('name', 'ilike', self), ('content', 'ilike', self)]"/>
            <field name="create_uid"/>
            <field name="forum_id"/>
            <field name="tag_ids" string="Tag"/>
            <filter string="Posts" name="posts" domain="[('parent_id', '=', False)]" />
            <filter string="Answers" name="answers" domain="[('parent_id', '!=', False)]" />
            <filter string="Accepted Answer" name="accepted_answer" domain="[('is_correct' , '!=', False), ('parent_id', '!=', False)]" />
            <filter string="Answered Posts" name="answered_posts" domain="[('child_count', '!=', 0), ('parent_id', '=', False)]" />
            <separator/>
            <filter name="filter_create_date" date="create_date"/>
            <filter name="filter_write_date" date="write_date"/>
            <separator/>
            <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
            <group expand="0" string="Group By">
                <filter string="Forum" name="forum" domain="[]" context="{'group_by': 'forum_id'}"/>
                <filter string="Author" name="author" domain="[]" context="{'group_by': 'create_uid'}"/>
                <filter string="Post" name="post" domain="[]" context="{'group_by': 'parent_id'}"/>
            </group>
        </search>
    </field>
</record>

<record id="forum_post_view_graph" model="ir.ui.view">
    <field name="name">forum.post.view.graph</field>
    <field name="model">forum.post</field>
    <field name="arch" type="xml">
        <graph string="Graph of Posts" sample="1">
            <field name="write_date" interval="month"/>
            <field name="forum_id"/>
        </graph>
    </field>
</record>

<record id="forum_post_view_tree" model="ir.ui.view">
    <field name="name">forum.post.view.list</field>
    <field name="model">forum.post</field>
    <field name="priority">99</field>
    <field name="arch" type="xml">
        <list js_class="website_pages_list" create="false" type="object" action="go_to_website" multi_edit="1">
            <field name="active" column_invisible="True"/>
            <field name="name"/>
            <field name="website_url"/>

            <field name="forum_id" optional="show"/>
            <field name="views" string="# Views" sum="Total Views" optional="hide"/>
            <field name="child_count" string="# Answers" sum="Total Answers" optional="hide"/>
            <field name="state" widget="badge"
                decoration-success="state == 'active'"
                decoration-danger="state == 'close'"
                decoration-warning="state not in ('active', 'close')" optional="show"/>

            <field name="is_seo_optimized"/>

            <field name="website_id" groups="website.group_multi_website"/>
        </list>
    </field>
</record>

<record id="forum_post_view_kanban" model="ir.ui.view">
    <field name="name">Forum Post Pages Kanban</field>
    <field name="model">forum.post</field>
    <field name="priority">99</field>
    <field name="arch" type="xml">
        <kanban js_class="website_pages_kanban" create="false" action="go_to_website" type="object" sample="1">
            <field name="parent_id"/>
            <templates>
                <t t-name="card">
                    <field name="name" class="text-truncate fw-bold"/>
                    <div class="text-muted text-truncate fw-bold" t-if="record.website_id.value" groups="website.group_multi_website">
                        <i class="fa fa-globe me-1" title="Website"/>
                        <field name="website_id"/>
                    </div>
                    <div class="text-muted text-truncate fw-bold">
                        <i class="fa fa-comments-o me-1" title="Forum"/><field name="forum_id"/>
                        <span t-if="!record.parent_id.raw_value" class="ms-3"><field name="child_count"/> Answers</span>
                    </div>
                    <div class="row">
                        <div class="col-6 text-primary"><i class="fa fa-eye me-1" title="Views"/><field name="views"/></div>
                        <div class="col-6"><field name="create_uid" widget="many2one_avatar_user" class="float-end"/></div>
                    </div>
                </t>
            </templates>
        </kanban>
    </field>
</record>

<record id="forum_post_action" model="ir.actions.act_window">
    <field name="name">Forum Post Pages</field>
    <field name="res_model">forum.post</field>
    <field name="view_mode">list,kanban,graph</field>
    <field name="view_ids" eval="[(5, 0, 0),
        (0, 0, {'view_mode': 'list', 'view_id': ref('forum_post_view_tree')}),
        (0, 0, {'view_mode': 'kanban', 'view_id': ref('forum_post_view_kanban')}),
    ]"/>
    <field name="search_view_id" ref="forum_post_view_search"/>
    <field name="context">{'search_default_posts': 1, 'create_action': 'website_forum.forum_forum_action_add'}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create a new forum post
        </p>
    </field>
</record>

<record id="forum_post_action_favorites" model="ir.actions.act_window">
    <field name="name">Users favorite posts</field>
    <field name="res_model">forum.post</field>
    <field name="view_mode">list,form</field>
    <field name="domain">[('forum_id', '=', active_id), ('favourite_count', '>', 0), ('state', 'in', ('active', 'close'))]</field>
</record>

<record id="forum_post_action_forum_main" model="ir.actions.act_window">
    <field name="name">Posts</field>
    <field name="res_model">forum.post</field>
    <field name="view_mode">list,form</field>
    <field name="domain">[('forum_id', '=', active_id), ('parent_id', '=', False), ('state', 'in', ('active', 'close'))]</field>
</record>

</data></odoo>
