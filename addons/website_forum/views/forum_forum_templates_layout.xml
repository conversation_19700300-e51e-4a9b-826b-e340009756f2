<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

<!-- GLOBAL LAYOUT -->
<!-- ============================================================ -->

<!-- website_forum.layout removes the access right check for wysiwyg bundle -->
<template id="layout" inherit_id="website.layout" name="Forum Layout" primary="True">
    <xpath expr="//div[@id='wrapwrap']" position="before">
        <t t-set="pageName" t-value="'website_forum'"/>
    </xpath>
    <xpath expr="//div[@id='wrapwrap']" position="attributes">
        <attribute name="t-att-data-forum_id">forum and forum.id</attribute>
    </xpath>
    <xpath expr="//body" position="inside">
        <script t-if="seo_microdata" type="application/ld+json" t-out="seo_microdata"></script>
    </xpath>
</template>

<!-- PAGE INDEX -->
<!-- ============================================================ -->
<template id="forum_model_nav">
    <t t-set="_forum_slug" t-value="slug(forum) if forum else 'all'"/>
    <t t-set="_all_forums">All forums</t>
    <t t-set="_forum_name" t-value="forum.name if forum else _all_forums"/>
    <!-- allows to be overridden such as in website_slides_forum -->
    <t t-set="breadcrumb_kind" t-valuef="base"/>
    <nav id="o_wforum_nav" t-attf-class="navbar d-flex gap-2 #{ 'mw-xl-75 mw-xxl-100' if _page_name == 'single_question' else '' } px-0" aria-label="breadcrumb">
        <t t-if="_page_name == 'single_question'">
            <div class="flex-grow-1">
                <div class="o_wforum_breadcrumb_root_single row g-0">
                    <div t-if="breadcrumb_kind == 'base'" class="col-10">
                        <a class="btn btn-link px-0 pb-2 fs-5" t-attf-href="/forum/#{ _forum_slug }">
                            <i class="d-inline-block fa fa-angle-left me-1 small"/><t t-out="_forum_name"/>
                        </a>
                    </div>
                    <div class="d-lg-none col-2 text-end">
                        <button class="btn position-relative ms-auto fs-5" data-bs-toggle="offcanvas" data-bs-target="#o_wforum_offcanvas">
                            <i class="fa fa-navicon"/>
                        </button>
                    </div>
                </div>
                <div class="d-flex gap-2 align-items-baseline">
                    <h3 t-attf-class="col-lg-10 my-0" t-out="question.name"/>
                    <div class="col d-flex justify-content-end align-items-center">
                        <i t-if="question.state == 'close'" class="fa fa-lock ms-2 fs-4" title="Closed" data-bs-toggle="tooltip" data-bs-placement="top"/>
                        <span t-elif="not question.active" class="badge text-bg-danger">
                            <t t-if="question.state!='offensive'">Deleted</t>
                            <t t-if="question.state=='offensive'">Offensive</t>
                            <t t-if="question.state=='offensive' and question.closed_reason_id">
                                <t t-out="question.closed_reason_id.name.capitalize()"/>
                            </t>
                        </span>
                        <a t-elif="uid" type="button" aria-label="Favorite"
                            t-attf-data-href="/forum/#{slug(question.forum_id)}/question/#{slug(question)}/toggle_favourite"
                            t-attf-class="o_wforum_favourite_toggle btn btn-lg #{ 'opacity-50' if not question.user_favourite else '' } opacity-hover-100 p-0">
                            <i t-attf-class="position-relative fa #{'o_wforum_gold fa-star ' if question.user_favourite else 'fa-star-o '}"
                                data-bs-toggle="tooltip"
                                data-bs-placement="top"
                                title="Favorite"/>
                        </a>
                        <t t-if="question.state == 'active'" t-call="website_forum.follow">
                            <t t-set="object" t-value="question"/>
                            <t t-set="icons_design" t-value="True"/>
                            <t t-set="btn_classes" t-value="'opacity-50 opacity-100-hover' "/>
                        </t>
                    </div>
                </div>
            </div>
        </t>
        <t t-elif="_page_name == 'list_questions' or is_edit">
            <t t-set="target" t-value="post.parent_id if is_answer else post"/>
            <div class="o_wforum_breadcrumb_root_list_or_edit d-flex">
                <div t-if="breadcrumb_kind=='base'" class="col-10 col-lg flex-grow-1 fs-5">
                    <span t-if="not is_edit" class="fw-bold mb-0" t-out="_forum_name"/>
                    <span t-elif="not is_answer" class="fw-bold mb-0">Edit Question</span>
                    <span t-else="is_answer" class="fw-bold mb-0">Edit Answer</span>
                </div>
            </div>
        </t>
        <ol t-else="" class="breadcrumb col-10 col-lg flex-grow-1 flex-nowrap my-0 p-0 fs-5">
            <li class="o_wforum_breadcrumb_root breadcrumb-item text-nowrap text-truncatet">
                <a t-attf-href="/forum/#{ _forum_slug }" t-out="_forum_name"/>
            </li>
            <li t-if="queue_type" class="breadcrumb-item text-nowrap d-none d-lg-flex">
                <span>Moderation</span>
            </li>
            <li class="breadcrumb-item text-nowrap text-truncate">
                <span class="fw-bold text-truncate" t-out="_page_name_label"/>
            </li>
        </ol>
        <t t-if="_page_name != 'single_question'">
            <div class="d-lg-none text-end">
                <button class="btn position-relative ms-auto fs-5" data-bs-toggle="offcanvas" data-bs-target="#o_wforum_offcanvas">
                    <i class="fa fa-navicon"/>
                </button>
            </div>
            <div t-if="tag or tags or search or question_count or page_name or website_forum_action" t-attf-class="d-flex justify-content-lg-end gap-2 flex-grow-1 flex-wrap flex-md-nowrap w-100 w-lg-auto #{'mw-xl-75' if search else 'mw-xl-50'}">
                <span t-if="tag and not tags" class="btn btn-light rounded ps-2">
                    <span t-if="tag"><i class="fa fa-tag me-1 opacity-50"/><t t-out="tag.name"></t></span>
                    <a t-attf-href="#{ url_for('/forum') }/#{ _forum_slug }?#{ keep_query('search', 'sorting', 'my', 'create_uid') }"
                       class="p-1 text-decoration-none text-reset opacity-50"><i class="oi oi-close d-inline-block"/></a>
                </span>
                <span t-if="search" class="btn btn-light rounded ps-2">
                    <em t-if="search" class="text-muted">"<t t-out="search"/>"</em>
                    <a t-attf-href="?#{ keep_query('sorting', 'my') }" class="p-1 text-decoration-none text-reset opacity-50">
                        <i class="oi oi-close d-inline-block"/>
                    </a>
                </span>
                <div t-if="question_count or tags" class="dropdown ms-lg-auto">
                    <t t-if="_page_name == 'tags'" t-set="tag_filter" t-value="keep_query('filters').split('=')[1] if keep_query('filters') else ''"/>
                    <a href="#" class="btn btn-light dropdown-toggle" data-bs-toggle="dropdown">
                        <!-- Foreach tag_filter/ Foreach filters -->
                        <t t-if="filters == 'all' or tag_filter == 'all' or not tag_filter and not filters">All</t>
                        <t t-if="_page_name == 'tags'">
                            <t t-if="tag_filter == 'followed'">Followed Tags</t>
                            <t t-elif="tag_filter == 'unused'">Unused Tags</t>
                            <t t-elif="tag_filter == 'most_used'">Most used Tags</t>
                        </t>
                        <t t-else="">
                            <t t-if="filters == 'solved'">Solved</t>
                            <t t-elif="filters == 'unsolved'">Unsolved</t>
                            <t t-elif="filters == 'unanswered'">Unanswered</t>
                        </t>
                    </a>
                    <div class="dropdown-menu" role="menu">
                        <a t-attf-href="?#{ keep_query('search', 'sorting', 'create_uid', filters='all') }"
                            class="dropdown-item">
                            All
                        </a>
                        <div class="dropdown-divider"/>
                        <t t-if="_page_name == 'tags'">
                            <a t-if="not request.env.user._is_public()"
                                t-attf-href="?#{ keep_query('search', 'sorting', 'my', filters='followed') }"
                                class="dropdown-item">Followed Tags
                            </a>
                            <t t-if="forum and forum.can_moderate">
                                <a t-attf-href="?#{ keep_query('search', 'sorting', 'my', filters='unused') }"
                                    class="dropdown-item">Unused Tags
                                </a>
                            </t>
                            <a t-attf-href="?#{ keep_query('search', 'sorting', 'my', filters='most_used') }"
                                class="dropdown-item">Most Used Tags
                            </a>
                        </t>
                        <t t-else="">
                            <t t-if="not forum or (forum and forum.mode == 'questions')">
                                <a t-attf-href="?#{ keep_query('search', 'sorting', 'my', filters='solved') }"
                                    class="dropdown-item">Solved
                                </a>
                                <a t-attf-href="?#{ keep_query('search', 'sorting', 'my', filters='unsolved') }"
                                    class="dropdown-item">Unsolved
                                </a>
                            </t>
                            <a t-attf-href="?#{ keep_query('search', 'sorting', 'my', filters='unanswered') }"
                                class="dropdown-item">Unanswered
                            </a>
                        </t>
                    </div>
                </div>
                <!-- 'Search Box' -->
                <t t-if="question_count or tags" t-call="website.website_search_box_input">
                    <t t-if="_page_name == 'tags'">
                        <t t-set="search_type" t-value="'forum_tags_only'"/>
                        <t t-set="action" t-value="'/forum/%s/tag' % (_forum_slug)"/>
                    </t>
                    <t t-else="">
                        <t t-set="search_type" t-value="'forums'"/>
                        <t t-set="action" t-value="'/forum/%s' % (_forum_slug)"/>
                    </t>
                    <t t-set="display_description" t-valuef="true"/>
                    <t t-set="display_detail" t-valuef="true"/>
                    <t t-set="_form_classes" t-valuef="flex-grow-1"/>
                    <t t-set="_input_classes" t-valuef="border-0 bg-light"/>
                    <t t-set="_submit_classes" t-valuef="btn-light"/>
                    <input t-if="filters" type="hidden" name="filters" t-att-value="filters"/>
                    <input t-if="my" type="hidden" name="my" t-att-value="my"/>
                    <input t-if="sorting" type="hidden" name="sorting" t-att-value="sorting"/>
                </t>
                <t t-if="_page_name == 'list_questions'">
                    <t t-set="popover_title">You already have a pending post</t>
                    <t t-set="popover_content">Please wait for a moderator to validate your previous post before continuing.</t>
                    <div t-if="uid and forum and forum.has_pending_post"
                        data-bs-toggle="popover"
                        t-att-data-bs-title="popover_title"
                        t-att-data-bs-content="popover_content">
                        <a class="o_wforum_ask_btn disabled btn btn-primary w-100 w-md-auto mb-3 mb-md-0" t-attf-href="/forum/#{ slug(forum) }/ask">New Post</a>
                    </div>
                    <a t-elif="uid and forum" role="button" type="button" t-attf-class="o_wforum_ask_btn btn btn-primary w-100 w-md-auto #{ 'karma_required' if user.karma &lt; forum.karma_ask else '' }"
                        t-att-data-karma="forum.karma_ask" t-attf-href="/forum/#{slug(forum)}/ask">New Post</a>
                </t>
                <button t-if="website_forum_action and not queue_type == 'close' and posts_ids" type="button" class="o_wforum_btn_filter_tool btn btn-secondary"
                    data-bs-toggle="modal" data-bs-target="#markAllAsSpam">
                    <i class="fa fa-bug me-1"/>Filter Tool
                </button>
            </div>
        </t>
    </nav>
</template>

<template id="header" name="Forum Index">
    <t t-call="website_forum.layout">
        <t t-if="forum and not forum.active">
            <t t-set="head">
                <meta name="robots" content="noindex, nofollow" />
            </t>
            <div class="text-center text-muted">
                <p class="css_editable_hidden"><h2>This forum has been archived.</h2></p>
            </div>
        </t>
        <t t-else="">
            <div class="oe_structure" id="oe_structure_website_forum_header_1"/>
            <div id="wrap" t-attf-class="o_wforum_wrapper position-relative container row mx-auto px-0 #{ 'h-100' if forum_welcome_message else ''} #{website_forum_action}">
                <t t-set="_forum_slug" t-value="slug(forum) if forum else 'all'"/>
                <t t-set="_forum_path" t-value="url_for('/forum') + '/' + _forum_slug"/>
                <t t-call="website_forum.user_sidebar"/>
                <t t-call="website_forum.user_sidebar_mobile"/>
                <div class="o_wforum_content_wrapper col-lg-9">
                    <div class="o_wprofile_email_validation_container d-flex flex-column justify-content-center mb-3 mb-lg-5 pt-2 pt-lg-3">
                        <t t-call="website_profile.email_validation_banner">
                            <t t-set="redirect_url" t-value="'/forum/%s' % forum.id if forum else '/forum/all/'"/>
                            <t t-set="additional_validation_email_message"> and join this Forum</t>
                            <t t-set="additional_validated_email_message"> You may now participate in our forums.</t>
                        </t>
                        <t t-call="website_forum.forum_model_nav"/>
                        <t t-out="0"/>
                    </div>
                </div>
            </div>
        </t>
    </t>
</template>

<!-- User sidebar -->
<template id="user_sidebar">
    <aside class="o_wforum_sidebar col-3 d-none d-lg-flex flex-column z-1">
        <div class="nav d-block px-2">
            <t t-call="website_forum.user_sidebar_header"/>
            <t t-call="website_forum.user_sidebar_body"/>
        </div>
        <div t-if="forum" class="o_wforum_sidebar_footer mt-3 px-3 pb-2 text-center">
            <a class="btn btn-sm btn-link" t-attf-href="/forum/#{slug(forum)}/faq">
                <i class="fa fa-info-circle fa-fw"/> About this forum
            </a>
        </div>
    </aside>
</template>

<!-- Off canvas user sidebar on mobile -->
<template id="user_sidebar_mobile">
    <div id="o_wforum_offcanvas" class="o_website_offcanvas offcanvas offcanvas-end d-lg-none mw-75 p-0 overflow-visible">
        <button type="button" class="btn-close mt-3 ms-auto me-3" data-bs-dismiss="offcanvas" aria-label="Close"/>
        <div class="offcanvas-header align-items-start px-0" t-call="website_forum.user_sidebar_header"/>
        <div class="offcanvas-body d-flex flex-column py-0 nav">
            <t t-call="website_forum.user_sidebar_body"/>
            <div t-if="forum" class="mb-2 d-flex justify-content-center align-items-end flex-grow-1">
                <a class="btn btn-sm btn-link" t-att-href="_forum_path + '/faq'">
                    <i class="fa fa-info-circle fa-fw"/> About this forum
                </a>
            </div>
        </div>
    </div>
</template>

<!-- User sidebar elements -->
<template id="user_sidebar_header">
    <t t-set="_location" t-value="( ('/tag/' + slug(tag) + '/questions?') if tag else '?' )"/>

    <div t-if="not uid" class="o_wforum_sidebar_section mt-4 text-center mx-3 mx-lg-0">
        <div class="alert alert-info mb-2"><span>You need to be registered to interact with the community.</span>
            <a t-if="is_public_user and forum_welcome_message" href="/web/login" class="btn btn-primary mt-2">Sign up</a>
        </div>
    </div>
    <div t-if="uid" class="o_wforum_sidebar_section mt-4 mb-2">
        <a t-attf-href="/forum/user/#{ uid }?forum_id=#{ forum.id if forum else '' }&amp;forum_origin=#{ request.httprequest.path }"
            class="btn w-100 py-1 text-start d-flex align-items-center" data-bs-toggle="tooltip" data-trigger="hover"
            title="My profile">
            <img class="o_wforum_avatar rounded-circle o_object_fit_cover" t-att-src="request.website.image_url(user, 'avatar_128', '60x60')" alt="Avatar"/>
            <div class="d-flex flex-column justify-content-center ms-2">
                <h6 class="mt-0 mb-1" t-out="user_id.name"/>
                <span class="fs-6 text-reset opacity-50"><t t-out="user_id.karma"/> XP</span>
            </div>
        </a>
    </div>
</template>

<template id="user_sidebar_body">
    <div class="o_wforum_sidebar_section">
        <t t-set="location" t-valuef="#{ _forum_path }#{ ('/tag/' + slug(tag) + '/questions') if (tag and not tags) else '' }?"/>
        <!-- All Posts -->
        <a t-attf-class="nav-link my-1 py-1 #{ 'rounded text-bg-light disabled' if request.httprequest.path ==  _forum_path and no_filters and not any([my, queue_type, tags]) else 'text-reset' }" t-att-href="location">
            <i t-attf-class="fa fa-list fa-fw #{ 'opacity-50' if request.httprequest.path != _forum_path or my or queue_type else '' }"/> All Posts
        </a>
        <t t-if="uid">
            <!-- My Posts -->
            <a t-attf-class="nav-link my-1 py-1 #{ 'rounded text-bg-light disabled' if my == 'mine' else 'text-reset' }" t-att-href="location + keep_query('search', 'filters', 'sorting', my='mine')">
                <i t-attf-class="fa fa-user-circle fa-fw #{ 'opacity-50' if my != 'mine' else ''}"/> My Posts
            </a>

            <!-- My Favourites -->
            <a t-attf-class="nav-link my-1 py-1 #{ 'rounded text-bg-light disabled' if my == 'favourites' else 'text-reset' }" t-att-href="location + keep_query( 'search', 'filters', 'sorting', my='favourites')">
                <i t-attf-class="fa fa-star fa-fw #{ 'opacity-50' if my != 'favourites' else ''}"/> Favourites
            </a>
        </t>
        <!-- People -->
        <a t-attf-class="nav-link my-1 py-1 text-reset" t-attf-href="/profile/users?forum_origin=#{request.httprequest.path}">
            <i class="fa fa-users fa-fw opacity-50"/> People
        </a>

        <!-- Badges -->
        <a t-attf-class="nav-link my-1 py-1 text-reset" t-attf-href="/profile/ranks_badges?forum_origin=#{request.httprequest.path}">
            <i class="fa fa-shield fa-fw opacity-50"/> Badges
        </a>
    </div>
    <div t-if="forum and user.karma>=forum.karma_moderate" class="o_wforum_sidebar_section pt-3">
        <!-- Moderation Tools -->
        <div class="px-3 pb-1 fw-bold">Moderation tools</div>
        <a t-attf-class="nav-link my-1 py-1 #{ 'rounded text-bg-light disabled' if queue_type == 'validation' else 'text-reset'}" t-attf-href="/forum/#{_forum_slug}/validation_queue">
            <i t-attf-class="fa fa-check-square-o fa-fw #{ 'opacity-50' if queue_type != 'validation' else ''}"/> To Validate
            <span id="count_posts_queue_validation" t-attf-class="badge #{ 'text-bg-warning' if forum.count_posts_waiting_validation > 0 else 'd-none'}" t-out="forum.count_posts_waiting_validation"/>
        </a>
        <a t-attf-class="nav-link my-1 py-1 #{'rounded text-bg-light disabled' if queue_type == 'offensive' or queue_type == 'flagged' else 'text-reset'}" t-attf-href="/forum/#{_forum_slug}/flagged_queue">
            <i t-attf-class="fa fa-flag fa-fw #{ 'opacity-50' if queue_type != 'flagged' else ''}"/> Flagged
            <span id="count_posts_queue_flagged" t-attf-class="badge #{ 'text-bg-danger' if forum.count_flagged_posts > 0 else 'd-none'}" t-out="forum.count_flagged_posts"/>
        </a>
        <a t-attf-class="nav-link my-1 py-1 #{ 'rounded text-bg-light disabled' if queue_type == 'close' else 'text-reset'}" t-attf-href="/forum/#{_forum_slug}/closed_posts">
            <i t-attf-class="fa fa-window-close fa-fw #{ 'opacity-50' if queue_type != 'close' else '' }"/> Closed
        </a>
    </div>
    <div t-if="forum and forum.tag_most_used_ids" class="o_wforum_sidebar_section pt-3">
        <div class="d-flex align-items-center px-3 pb-1 fw-bold">Tags
            <a class="ms-2 px-0 fw-normal" t-att-href="_forum_path + '/tag'">
                <small>(View all)</small>
            </a>
        </div>
        <a t-foreach="forum.tag_most_used_ids" t-as="tag"
            t-attf-href="#{ _forum_path }/tag/#{ slug(tag) }/questions?#{ keep_query( 'search', 'sorting', 'my', 'filters') }"
            t-attf-class="nav-link my-1 py-1 text-reset">
            <i class="fa fa-tag fa-fw small opacity-50"/>
            <t t-out="tag.name"/>
        </a>
    </div>
    <div t-if="my_other_forums" class="o_wforum_sidebar_section pt-3">
        <div class="px-3 pb-1 fw-bold">My forums</div>
        <t t-foreach="my_other_forums.sorted(lambda f: f.name.casefold())" t-as="my_forum">
            <a class="nav-link my-1 py-1 text-reset" t-attf-href="/forum/#{slug(my_forum)}">
                <i class="fa fa-file-o fa-fw opacity-50"/>
                <t t-out="my_forum.name"/>
            </a>
        </t>
    </div>
</template>

<template id="header_welcome_message" inherit_id="website_forum.header" name="Forum Welcome Message (oe_structure_forum_top)">
    <xpath expr="//*[hasclass('oe_structure')][@id='oe_structure_website_forum_header_1']" position="replace">
        <div t-if="forum" class="oe_structure oe_empty" id="oe_structure_website_forum_header_1">
            <section t-if="editable or (is_public_user and not forum_welcome_message)" t-attf-class="s_cover parallax s_parallax_is_fixed bg-black-50 pt48 pb48 #{'css_non_editable_mode_hidden' if editable else 'forum_intro'}" data-scroll-background-ratio="1" data-snippet="s_cover">
                <span t-if="forum.image_1920" class="s_parallax_bg oe_img_bg" t-attf-style="background-image: url('#{request.website.image_url(forum, 'image_1920')}'); background-position: center;"/>
                <div t-if="forum.image_1920" class="o_we_bg_filter bg-black-50"/>
                <div class="container s_allow_columns">
                    <div class="row" data-row-count="5">
                        <div class="o_colored_level offset-lg-3 col-lg-6">
                            <div t-field="forum.welcome_message" class="container s_allow_columns"/>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </xpath>
</template>

<template id="no_results_message">
    <t t-if="queue_type">
        <t t-set="_no_results_title">You've Completely Caught&amp;nbsp;Up!</t>
        <t t-set="result_msg">Go enjoy a cup of coffee.</t>
        <t t-set="record_name_plural">posts</t>
        <t t-set="go_back_url" t-valuef="/forum/#{ slug(forum) }/"/>
    </t>
    <t t-else="" t-set="_no_results_title">Oops!</t>
    <div t-attf-class="#{ 'o_caught_up_alert' if queue_type else '' } row g-0 justify-content-center #{ 'd-none' if hide_alert else '' }">
        <div class="p-3 d-flex flex-column align-items-center">
            <img t-if="queue_type" src="/website_forum/static/src/img/tasks.svg" class="img img-fluid mb-3" width="200" alt="Animation of a pen checking a checkbox"/>
            <img t-else="" src="/website_forum/static/src/img/empty.svg" class="img img-fluid mb-3" width="150" alt="Empty box"/>
            <h5 t-out="_no_results_title"/>
            <span t-if="result_msg" t-out="result_msg"/>
            <span t-else="">
                <t t-if="filters == 'unanswered'">
                    Sorry, we could not find any <b>unanswered</b> results
                </t>
                <t t-elif="filters == 'solved'">
                    Sorry, we could not find any <b>solved</b> results
                </t>
                <t t-elif="filters == 'unsolved'">
                    Sorry, we could not find any <b>unsolved</b> results
                </t>
                <t t-else="">
                    Sorry, we could not find any results
                </t>
                <b t-if="my == 'favourites'"> in your favourites</b>
                <b t-elif="my == 'mine'"> in your posts</b>
                <span t-if="search">matching "<em class="fw-bold text-break" t-out="original_search or search"/>"</span>
                <span t-out="'and' if search and tag else ''"/>
                <t t-if="tag">
                    using the <span class="px-2 py-1 rounded bg-300" t-out="tag.name"/> tag
                </t>.
            </span>
            <span t-if="original_search">Showing results for <em class="fw-bold" t-out="search"/> instead.</span>
            <div t-if="question_count == 0 and (not forum or forum.total_posts != 0)" class="mt-3 text-start">
                <p><i class="fa fa-check fa-fw me-1"/>Check your spelling and try again.</p>
                <p><i class="fa fa-check fa-fw me-1"/>Try searching for one or two words.</p>
                <p><i class="fa fa-check fa-fw me-1"/>Be less specific in your wording for a wider search result.</p>
            </div>
            <span t-elif="forum and forum.total_posts == 0">Because there are no posts in this forum yet.</span>
            <div class="mt-3">
                <a t-if="uid and forum and forum.total_posts == 0" role="button" type="button" class="o_forum_ask_btn btn btn-primary ms-2" t-attf-href="/forum/#{_forum_slug}/ask">Start by creating a post</a>
                <a t-else="" role="button" type="button" class="btn btn-primary mt-2" t-att-href="go_back_url">Go back to the list of <t t-out="record_name_plural"/></a>
            </div>
        </div>
    </div>
</template>

<!-- ERROR MANAGEMENT -->
<!-- ============================================================ -->

<template id="404">
    <t t-call="website_forum.header">
        <div class="oe_structure oe_empty"/>
        <h1 class="mt-4">Question not found!</h1>
        <p>Sorry, this question is not available anymore.</p>
        <p>
            <a class="btn-link" t-attf-href="/forum"><i class="oi oi-arrow-right display-inline-block"/> Return to questions list</a>
        </p>
    </t>
</template>

    </data>
</odoo>
