<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="follow">
        <div t-attf-class="js_follow #{div_class}"
            t-att-data-id="object.id"
            t-att-data-object="object._name"
            t-att-data-follow="object.id and object.message_is_follower and 'on' or 'off'"
            t-att-data-unsubscribe="'unsubscribe' if 'unsubscribe' in request.params else None">
            <div t-if="icons_design and not request.env.user._is_public()" class="js_follow_icons_container">
                <button t-attf-class="btn js_unfollow_btn #{btn_classes}">
                    <i t-attf-class="fa fa-fw fa-minus-circle #{icons_classes}" data-bs-toggle="tooltip" data-bs-placement="top" title="Unfollow"/>
                </button>
                <button t-attf-class="btn js_follow_btn #{btn_classes} #{follow_btn_classes}">
                    <i t-attf-class="fa fa-fw fa-plus-circle #{icons_classes}" data-bs-toggle="tooltip" data-bs-placement="top" title="Follow"/>
                </button>
            </div>
            <span t-elif="request.env.user._is_public() and icons_design" class="js_follow_icons_container">
                <button t-attf-class="btn js_unfollow_btn #{btn_classes}">
                    <i t-attf-class="fa fa-fw fa-minus-circle #{icons_classes}" data-bs-toggle="tooltip" data-bs-placement="top" title="Unfollow"/>
                </button>
                <button t-attf-class="btn follow_btn #{btn_classes}" data-bs-toggle="modal" t-attf-data-bs-target="#o_wmail_follow_modal_#{object.id}">
                    <i t-attf-class="fa fa-fw fa-plus-circle #{icons_classes}" data-bs-toggle="tooltip" data-bs-placement="top" title="Follow"/>
                </button>
            </span>
            <t t-else="">
                <button href="#" t-attf-class="btn btn-primary js_unfollow_btn #{btn_classes}"><i class="fa fa-fw fa-check me-1"/>Following</button>
                <button href="#" t-attf-class="btn btn-outline-primary js_follow_btn #{btn_classes}">Follow</button>
            </t>
            <div role="dialog" class="modal fade" t-attf-id="o_wmail_follow_modal_#{object.id}" aria-hidden="True">
                <div class="modal-dialog mw-lg-25" role="document">
                    <div class="modal-content">
                        <header class="modal-header" role="status">
                            <h4 class="modal-title">Subscribe</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </header>
                        <main class="modal-body">
                            <p t-if="object == question">Get notified when there's activity on this post</p>
                            <p t-if="object == tag">Get notified when this tag is used</p>
                            <input type="email" name="email"
                                   class="js_follow_email form-control mb-2"
                                   placeholder="your email..."
                                   groups="base.group_public"/>
                            <button href="#" t-attf-class="btn btn-primary js_follow_btn">Subscribe</button>
                            <button href="#" t-attf-class="btn btn-secondary js_unfollow_btn"><i class="fa fa-fw fa-check me-1"/>Following</button>
                        </main>
                    </div>
                </div>
            </div>
        </div>
    </template>
</odoo>
