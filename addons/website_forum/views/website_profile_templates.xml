<?xml version="1.0" encoding="utf-8"?>
<odoo><data>
    <!--Access Denied - Profile Page-->
    <template id="profile_access_denied" inherit_id="website_profile.profile_access_denied">
        <xpath expr="//div[@id='profile_access_denied_return_link_container']" position="inside">
            <t t-if="request.params.get('forum_id')">
                <a t-attf-href="/forum/#{request.params.get('forum_id')}" class="btn btn-primary">Return to the forum</a>
            </t>
        </xpath>
    </template>

    <template id="user_profile_sub_nav" inherit_id="website_profile.user_profile_sub_nav">
        <xpath expr="//nav" position="before">
            <div t-if="request.params.get('forum_origin')" class="o_wprofile_all_users_nav_btn_container col pe-0 flex-grow-0">
                <a t-att-href="(request.website.domain or '') + '/' + request.params.get('forum_origin').lstrip('/')"
                    class="o_wprofile_all_users_nav_btn btn text-nowrap">
                    <i class="oi oi-chevron-left small"/> Back
                </a>
            </div>
        </xpath>
    </template>

    <template id="user_profile_content" inherit_id="website_profile.user_profile_content">
        <xpath expr="//table[@id='o_wprofile_sidebar_table']//tr[last()]" position="after">
            <t t-if="forum and (up_votes or down_votes)">
                <tr id="profile_abstract_info_company">
                    <th><small class="fw-bold">Votes</small></th>
                    <td>
                        <span>
                            <i class="fa fa-thumbs-up text-success" role="img" aria-label="Positive votes" title="Positive votes"/>
                            <span class="fw-bold" t-out="up_votes"/>
                            <i class="fa fa-thumbs-down text-danger ms-3" role="img" aria-label="Negative votes" title="Negative votes"/>
                            <span class="fw-bold" t-out="down_votes"/>
                        </span>
                    </td>
                </tr>
            </t>
        </xpath>
        <xpath expr="//ul[@id='profile_extra_info_tablist']" position="inside">
            <t t-if="forum">
                <li class="nav-item">
                    <a role="tab" aria-controls="questions" href="#questions" class="nav-link o_wprofile_navlink" data-bs-toggle="tab"><t t-out="count_questions"/> Questions</a>
                </li>
                <li class="nav-item">
                    <a role="tab" aria-controls="answers" href="#answers" class="nav-link o_wprofile_navlink" data-bs-toggle="tab"><t t-out="count_answers"/> Answers</a>
                </li>
                <li t-if="uid == user.id" class="nav-item">
                    <a role="tab" aria-controls="activity" href="#activity" class="nav-link o_wprofile_navlink" data-bs-toggle="tab">Activity</a>
                </li>
                <li t-if="uid == user.id" class="nav-item">
                    <a role="tab" aria-controls="votes" href="#votes" class="nav-link o_wprofile_navlink" data-bs-toggle="tab">Votes</a>
                </li>
            </t>
        </xpath>
        <xpath expr="//div[@id='profile_extra_info_tabcontent']" position="inside">
            <t t-if="forum">
                <t t-set="is_profile" t-value="true"/>
                <div role="tabpanel" class="o_wforum_profile_tab tab-pane" id="questions">
                    <div class="mb-4">
                        <h5 class="border-bottom pb-1 d-flex align-items-center justify-content-between">
                            Questions
                            <t t-if="count_questions &gt; 0" class="float-end" t-call="website.website_search_box_input">
                                <t t-set="_form_classes" t-valuef="d-flex flex-row align-items-center flex-wrap float-end"/>
                                <t t-set="search_type" t-valuef="forums"/>
                                <t t-if="forum_id" t-set="action" t-value="'/forum/%s%s' % (forum_id, ('/tag/%s/questions' % slug(tag)) if tag else '')"/>
                                <t t-else="" t-set="action" t-value="'/forum/all%s' % (('/tag/%s/questions' % slug(tag)) if tag else '')"/>
                                <t t-set="display_description" t-value="true"/>
                                <t t-set="display_detail" t-valuef="false"/>
                                <t t-set="placeholder" t-valuef="Search Questions..."/>
                                <input type="hidden" name="create_uid" t-att-value="user.id"/>
                                <input t-if="filters" type="hidden" name="filters" t-att-value="filters"/>
                                <input t-if="my_profile" type="hidden" name="my" t-att-value="mine"/>
                                <input t-if="sorting" type="hidden" name="sorting" t-att-value="sorting"/>
                            </t>
                        </h5>
                        <t t-call="website_forum.forum_filter_tag"/>
                        <t t-if="questions">
                            <div class="mb-1" t-foreach="questions" t-as="question">
                                <t t-call="website_forum.display_post"/>
                            </div>
                        </t>
                        <div t-elif="my_profile" class="text-muted">
                            You have not posted any questions yet. <br />
                            <a href="/forum/" class="btn btn-link px-0">
                                <i class="oi oi-arrow-right d-inline-block"/> Go to Forums
                            </a>
                        </div>
                        <t t-else="">
                            <div class="text-muted">This user hasn't posted any questions yet.<br/>
                                <a href="/forum/" class="btn btn-link px-0">
                                    <i class="oi oi-arrow-right d-inline-block"/> Go to Forums
                                </a>
                            </div>
                        </t>
                    </div>
                    <t t-if="favourite">
                        <div class="mb-4">
                            <h5 class="border-bottom pb-1">Favourite Questions</h5>
                            <div class="mb-1" t-foreach="favourite" t-as="question">
                                <t t-call="website_forum.display_post">
                                    <t t-set="hide_fav_icon" t-value="True"/>
                                </t>
                            </div>
                        </div>
                    </t>
                    <t t-if="followed">
                        <div class="mb-4">
                            <h5 class="border-bottom pb-1">Followed Questions</h5>
                            <div class="mb-1" t-foreach="followed" t-as="question">
                                <t t-call="website_forum.display_post"/>
                            </div>
                        </div>
                    </t>
                </div>
                <div role="tabpanel" class="o_wforum_profile_tab tab-pane" id="answers">
                    <div class="mb-4">
                        <h5 class="border-bottom pb-1 d-flex align-items-center justify-content-between">
                            Answers
                            <t t-if="count_questions &gt; 0" class="float-end" t-call="website.website_search_box_input">
                                <t t-set="_form_classes" t-valuef="d-flex flex-row align-items-center flex-wrap float-end"/>
                                <t t-set="search_type" t-valuef="forums"/>
                                <t t-if="forum_id" t-set="action" t-value="'/forum/%s%s' % (forum_id, '/tag/%s/questions' % slug(tag)) if tag else ''"/>
                                <t t-else="" t-set="action" t-value="'/forum/all%s' % (('/tag/%s/questions' % slug(tag)) if tag else '')"/>
                                <t t-set="display_description" t-value="true"/>
                                <t t-set="display_detail" t-valuef="false"/>
                                <t t-set="placeholder" t-valuef="Search Answers..."/>
                                <input type="hidden" name="author" t-att-value="user.id"/>
                                <input t-if="filters" type="hidden" name="filters" t-att-value="filters"/>
                                <input t-if="my_profile" type="hidden" name="my" t-att-value="mine"/>
                                <input t-if="sorting" type="hidden" name="sorting" t-att-value="sorting"/>
                                <input type="hidden" name="include_answers" t-att-value="True"/>
                            </t>
                        </h5>
                        <t t-call="website_forum.forum_filter_tag"/>

                        <t t-if="answers">
                            <div class="mb-1" t-foreach="answers" t-as="answer">
                                <t t-call="website_forum.display_post_answer"/>
                            </div>
                        </t>
                        <div t-elif="my_profile" class="text-muted">
                            You have not answered any questions yet. <br />
                            <a href="/forum/" class="btn btn-link px-0">
                                <i class="oi oi-arrow-right d-inline-block"/> Go to Forums
                            </a>
                        </div>
                        <div t-else="" class="text-muted">
                            This user hasn't answered any questions yet. <br/>
                            <a href="/forum/" class="btn btn-link px-0">
                                <i class="oi oi-arrow-right d-inline-block"/> Go to Forums
                            </a>
                        </div>
                    </div>
                </div>
                <div role="tabpanel" class="o_wforum_profile_tab tab-pane" id="votes" t-if="uid == user.id">
                    <div class="mb-4">
                        <h5 class="border-bottom pb-1 flex align-items-center justify-content-between" style="height:43px;">
                            Votes
                            <t t-if="my_profile" class="float-end" t-call="website.website_search_box_input">
                                <t t-set="_form_classes" t-valuef="d-flex flex-row align-items-center flex-wrap float-end"/>
                                <t t-set="search_type" t-valuef="forums"/>
                                <t t-set="action" t-value="'/forum/%s%s' % (forum_id or 'all', '/tag/%s/questions' % slug(tag) if tag else '')"/>
                                <t t-set="display_description" t-value="true"/>
                                <t t-set="display_detail" t-valuef="false"/>
                                <t t-set="placeholder" t-valuef="Search Posts..."/>
                                <input type="hidden" name="my" value="upvoted"/>
                                <input t-if="filters" type="hidden" name="filters" t-att-value="filters"/>
                                <input t-if="sorting" type="hidden" name="sorting" t-att-value="sorting"/>
                                <input type="hidden" name="include_answers" t-att-value="True"/>
                            </t>
                        </h5>
                        <t t-call="website_forum.forum_filter_tag"/>
                        <t t-call="website_forum.user_votes"/>
                    </div>
                </div>
                <div role="tabpanel" class="o_wforum_profile_tab tab-pane" id="activity" t-if="uid == user.id">
                    <div class="mb-4">
                        <h5 class="border-bottom pb-1 d-flex align-items-center justify-content-between" style="height:43px;">
                            Activities
                        </h5>
                        <t t-call="website_forum.forum_filter_tag"/>
                        <t t-call="website_forum.display_activities"/>
                    </div>
                </div>
            </t>
        </xpath>
    </template>

    <template id="forum_filter_tag" name="Filtering Forum Tag">
        <t t-if="forum_filtered">
            <span class="d-inline-flex align-items-center gap-2 rounded py-1 ps-2 pe-0 bg-200 small">
                <i class="fa fa-filter text-muted"/>
                <t t-out="forum_filtered"/>
                <a t-attf-href="/profile/user/#{user.id}" class="oi oi-close btn m-n1 text-muted"/>
            </span>
        </t>
    </template>

    <template id="display_activities" name="Forum Profile Activities">
        <t t-if="activities">
            <div t-foreach="activities" t-as="activity" class="card mb-2">
                <div class="card-body">
                    <span t-out="activity.subtype_id.name" class="badge text-bg-info me-2 mt-1"/>
                    <span t-field="activity.date" t-options='{"format": "short"}' class="me-2"/>
                    <t t-set="post" t-value="posts[activity.res_id]"/>
                    <span t-if="post[1]">
                        <a t-attf-href="/forum/#{ slug(post[0].forum_id) }/#{ slug(post[0]) }#answer-#{ str(post[1].id) }">
                            <span t-out="post[0].name"/>
                        </a>
                    </span>
                    <span t-if="not post[1]">
                        <a t-attf-href="/forum/#{ slug(post[0].forum_id) }/#{ slug(post[0]) }">
                            <span t-out="post[0].name"/>
                        </a>
                    </span>
                </div>
            </div>
        </t>
        <t t-else="">
            <span class="text-muted">There is no activity yet.</span>
        </t>
    </template>

    <template id="user_votes" name="Forum User Votes">
        <div t-foreach="vote_post" t-as="vote" class="card mb-2">
            <div class="card-body">
                <span t-if="vote.vote == '1'" class="fa fa-thumbs-up text-success me-2" role="img" aria-label="Positive vote" title="Positive vote"/>
                <span t-if="vote.vote == '-1'" class="fa fa-thumbs-down text-warning me-2" role="img" aria-label="Negative vote" title="Negative vote"/>
                <span t-field="vote.post_id.create_date"/>
                <a t-if="vote.post_id.parent_id" class="ms-2" t-attf-href="/forum/#{ slug(vote.post_id.forum_id) }/#{ vote.post_id.parent_id.id }/#answer-#{ vote.post_id.id }" t-out="vote.post_id.parent_id.name"/>
                <a t-else="" class="text-black ms-2" t-attf-href="/forum/#{ slug(vote.post_id.forum_id) }/#{ vote.post_id.id }" t-out="vote.post_id.name"/>
            </div>
        </div>
        <div class="mb16" t-if="not vote_post">
            <div t-if="my_profile" class="text-muted">
                Help moderating the forums by upvoting and downvoting posts. <br />
                <a href="/forum/" class="btn-link">
                    <i class="fa fa-arrow-right"></i> Go To Forums
                </a>
            </div>
            <p t-else="" class="text-muted">You haven't given any votes yet.</p>
        </div>
    </template>
</data></odoo>
