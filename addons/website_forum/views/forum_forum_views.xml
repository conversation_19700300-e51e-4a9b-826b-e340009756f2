<?xml version="1.0"?>
<odoo>
    <data>
        <!-- FORUM VIEWS -->
        <record id="forum_forum_view_tree" model="ir.ui.view">
            <field name="name">forum.forum.view.list</field>
            <field name="model">forum.forum</field>
            <field name="arch" type="xml">
                <list string="Forums">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="website_id" groups="website.group_multi_website"/>
                    <field name="total_posts" sum="Total Posts"/>
                    <field name="total_views" sum="Total Views"/>
                    <field name="total_answers" optional="hide"/>
                    <field name="total_favorites" optional="hide"/>
                    <field name="active" column_invisible="True"/>
                </list>
            </field>
        </record>

        <record id="forum_forum_view_form" model="ir.ui.view">
            <field name="name">forum.forum.view.form</field>
            <field name="model">forum.forum</field>
            <field name="arch" type="xml">
                <form string="Forum">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="%(forum_post_action_forum_main)d" type="action" class="oe_stat_button" icon="fa-comments">
                                <div class="o_form_field o_stat_info">
                                    <span class="o_stat_value">
                                        <field name="total_posts" />
                                    </span>
                                    <span class="o_stat_text">Posts</span>
                                </div>
                            </button>
                            <button name="%(forum_post_action_favorites)d" class="oe_stat_button" icon="fa-star" type="action">
                                <div class="o_form_field o_stat_info">
                                    <span class="o_stat_value">
                                        <field name="total_favorites" />
                                    </span>
                                    <span class="o_stat_text">Favorites</span>
                                </div>
                            </button>
                            <button type="object" class="oe_stat_button" icon="fa-globe" name="go_to_website">
                                <div class="o_form_field o_stat_info">
                                    <span class="o_stat_text">Go to <br/>Website</span>
                                </div>
                            </button>
                        </div>
                        <field name="active" invisible="1"/>
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                        <field name="image_1920" widget="image" options="{'preview_image': 'image_128'}" class="oe_avatar"/>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" placeholder="e.g. Help"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="mode" widget="radio" required="True"/>
                                <field name="website_id" options="{'no_create': True}" groups="website.group_multi_website"/>
                            </group>
                        </group>
                        <notebook>
                            <page name="options" string="Options">
                                <group>
                                    <group string="Order and Visibility" name="group_order">
                                        <field name="default_order" string="Default Sort"/>
                                        <field name="privacy" widget="radio" required="True"/>
                                        <field name="authorized_group_id" options="{'no_create': True}" invisible="privacy != 'private'" required="privacy == 'private'"/>
                                        <label for="relevancy_post_vote" string="Relevance Computation" groups="base.group_no_one" invisible="default_order != 'relevancy desc'"/>
                                        <div groups="base.group_no_one" class="o_row" invisible="default_order != 'relevancy desc'">
                                            (votes - 1) ** <field name="relevancy_post_vote"/> / (days + 2) ** <field name="relevancy_time_decay"/>
                                        </div>
                                    </group>
                                </group>

                                <field name="description" placeholder="Description visible on website"/>
                            </page>
                            <page name="karma_gains" string="Karma Gains">
                                <group name="karma_gain_details">
                                    <group>
                                        <field name="karma_gen_question_new"/>
                                        <field name="karma_gen_question_upvote"/>
                                        <field name="karma_gen_question_downvote"/>
                                        <field name="karma_gen_answer_upvote"/>
                                        <field name="karma_gen_answer_downvote"/>
                                        <field name="karma_gen_answer_accept"/>
                                        <field name="karma_gen_answer_accepted"/>
                                        <field name="karma_gen_answer_flagged"/>
                                    </group>
                                </group>
                            </page>
                            <page name="karma_rights" string="Karma Related Rights">
                                <group>
                                    <group name="karma_rights_left">
                                        <field name="karma_ask"/>
                                        <field name="karma_answer"/>
                                        <field name="karma_upvote"/>
                                        <field name="karma_downvote"/>
                                        <field name="karma_edit_own"/>
                                        <field name="karma_edit_all"/>
                                        <field name="karma_close_own"/>
                                        <field name="karma_close_all"/>
                                        <field name="karma_unlink_own"/>
                                        <field name="karma_unlink_all"/>
                                        <field name="karma_dofollow"/>
                                        <field name="karma_answer_accept_own"/>
                                        <field name="karma_answer_accept_all"/>
                                    </group>
                                    <group name="karma_rights_right">
                                        <field name="karma_editor"/>
                                        <field name="karma_comment_own"/>
                                        <field name="karma_comment_all"/>
                                        <field name="karma_comment_convert_own"/>
                                        <field name="karma_comment_convert_all"/>
                                        <field name="karma_comment_unlink_own"/>
                                        <field name="karma_comment_unlink_all"/>
                                        <field name="karma_post"/>
                                        <field name="karma_flag"/>
                                        <field name="karma_moderate"/>
                                        <field name="karma_edit_retag"/>
                                        <field name="karma_tag_create"/>
                                        <field name="karma_user_bio"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <record id="forum_forum_view_form_add" model="ir.ui.view">
            <field name="name">forum.forum.view.form.add</field>
            <field name="model">forum.forum</field>
            <field name="arch" type="xml">
                <form js_class="website_forum_add_form">
                    <group>
                        <field name="name" placeholder="e.g. Technical Assistance" string="Forum Name"/>
                        <field name="mode" widget="radio" required="True" string="Forum Mode"/>
                        <field name="privacy" widget="radio" required="True"/>
                        <field name="authorized_group_id" options="{'no_create': True}" invisible="privacy != 'private'" required="privacy == 'private'"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="forum_forum_view_search" model="ir.ui.view">
            <field name="name">forum.forum.view.search</field>
            <field name="model">forum.forum</field>
            <field name="arch" type="xml">
                <search string="Forum">
                    <field name="name"/>
                    <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
                </search>
            </field>
        </record>

        <record id="forum_forum_action" model="ir.actions.act_window">
            <field name="name">Forums</field>
            <field name="res_model">forum.forum</field>
            <field name="view_mode">list,form</field>
        </record>

        <record id="forum_forum_action_add" model="ir.actions.act_window">
            <field name="name">New Forum</field>
            <field name="res_model">forum.forum</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="forum_forum_view_form_add"/>
        </record>

    </data>
</odoo>
