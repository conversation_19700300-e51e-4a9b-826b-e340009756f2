<?xml version="1.0" encoding="utf-8"?>
<odoo><data>

<template id="contact" inherit_id="base.contact" name="Forum Contact Widget">
    <xpath expr="//div[@itemscope='itemscope']" position="after">
        <div>
             <div t-if="'karma' in fields" class='css_editable_mode_hidden'>
                <div t-if="options.get('UserBio')" class="mb-2">
                    <span t-field="object.company_name" class="o_forum_tooltip_line"/><br/>
                        <a t-att-href="object.website" t-if="object.website">
                            <span t-field="object.website" class="o_forum_tooltip_line"/>
                        </a>
                </div>
                <b class="mt-4"><i class="fa fa-diamond text-secondary"/> <t t-esc="object.karma"/></b>
                <div t-if="options.get('badges')" style="display: inline-block">
                    <t t-esc="separator"/>
                    <b>|</b>
                    <span class="fa fa-trophy o-text-gold ms-2" role="img" aria-label="Gold badge" title="Gold badge"/>
                    <t t-esc="object.gold_badge"/>
                    <span class="fa fa-trophy o-text-silver ms-2" role="img" aria-label="Silver badge" title="Silver badge"/>
                    <t t-esc="object.silver_badge"/>
                    <span class="fa fa-trophy o-text-bronze ms-2" role="img" aria-label="Bronze badge" title="Bronze badge"/>
                    <t t-esc="object.bronze_badge"/>
                </div>
                <t t-out="0"/>
                <div t-if="options.get('UserBio')" class="mt-2">
                    <div class="o_forum_tooltip_line" t-if="object.partner_id.country_id or object.partner_id.city">
                        <span t-field="object.partner_id.city"/><span t-if="object.partner_id.city and object.partner_id.country_id">, </span><span t-field="object.partner_id.country_id"/>
                    </div>
                </div>
            </div>
            <span t-if="options.get('website_description') and 'partner_id' in fields">
                <t t-if="object.partner_id.website_description">
                    <span t-field="object.partner_id.website_description"/>
                </t>
            </span>

        </div>
    </xpath>
</template>

</data></odoo>
