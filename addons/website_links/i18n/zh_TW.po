# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_links
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_charts.js:0
msgid "# of clicks"
msgstr "點擊次數"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_charts.js:0
msgid "%(clicks)s clicks"
msgstr "%(clicks)s 次點擊"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_charts.js:0
msgid "%(count)s countries"
msgstr "%(count)s 個國家/地區"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<i class=\"fa fa-copy me-2\"/>Copy"
msgstr "<i class=\"fa fa-copy me-2\"/> 複製"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "<span class=\"text-muted me-1\">Sort By:</span>"
msgstr "<span class=\"text-muted me-1\">排序依據：</span>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Campaign</strong>"
msgstr "<strong>宣傳活動</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Medium</strong>"
msgstr "<strong>媒介</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Name</strong>"
msgstr "<strong>姓名</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Original URL</strong>"
msgstr "<strong>原始網址</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Redirected URL</strong>"
msgstr "<strong>重新定向網址</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Source</strong>"
msgstr "<strong>來源</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "<strong>Tracked Link</strong>"
msgstr "<strong>追蹤連結</strong>"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "All Time"
msgstr "持續"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Campaign"
msgstr "行銷活動"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/xml/recent_link.xml:0
msgid "Copy"
msgstr "複製"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Create a Link tracker"
msgstr "建立一個連結追蹤器"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Create another Tracker"
msgstr "建立另一個追蹤器"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/xml/website_links_tags_wrapper.xml:0
msgid "Create option \""
msgstr "建立選項 \""

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Edit code"
msgstr "編輯代碼"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Generate Link Tracker"
msgstr "生成連結追蹤工具"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Last Clicks"
msgstr "最後點擊"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Month"
msgstr "上月"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Last Week"
msgstr "上週"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links.js:0
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
msgid "Link Copied!"
msgstr "連結已複製！"

#. module: website_links
#: model:ir.model,name:website_links.model_link_tracker
#: model:ir.ui.menu,name:website_links.menu_link_tracker
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "Link Tracker"
msgstr "連結追蹤"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Medium"
msgstr "媒體"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Name"
msgstr "名稱"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Newest"
msgstr "最新"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_charts.js:0
msgid "No data"
msgstr "沒有資料"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Number of Clicks"
msgstr "點選次數"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Read More"
msgstr "閱讀更多"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Share this page with a <strong>short link</strong> that includes "
"<strong>analytics trackers</strong>."
msgstr "使用包含<strong>分析追蹤器</strong>的<strong>短網址</strong> 來分享這個頁面。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Source"
msgstr "來源"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.graphs
#: model_terms:ir.ui.view,arch_db:website_links.link_tracker_view_tree
msgid "Statistics"
msgstr "統計資訊"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Target Link"
msgstr "目標連結"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
msgid "The code cannot be left empty"
msgstr "代碼不可空白"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_charts.js:0
msgid "There is no data to show"
msgstr "沒有顯示的資料"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_code_editor.js:0
msgid "This code is already taken"
msgstr "此代碼已被使用"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid ""
"Those trackers can be used in Google Analytics to track clicks and visitors,"
" or in Odoo reports to track opportunities and related revenues."
msgstr "這些追蹤器可以被Google分析去追蹤點選和訪問，或者在odoo的報表裡追蹤機會和相關收益。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Tracked Link"
msgstr "追蹤連結"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links.js:0
msgid "Unable to get recent links"
msgstr "無法獲取最近連結"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links_charts.js:0
msgid "Undefined"
msgstr "未定義的"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/xml/recent_link.xml:0
msgid "Visit Link"
msgstr "造訪連結"

#. module: website_links
#. odoo-python
#: code:addons/website_links/models/link_tracker.py:0
msgid "Visit Webpage Statistics"
msgstr "查看網頁統計資訊"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links.js:0
msgid "You don't have any recent links."
msgstr "您沒有任何最近的連結。"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "Your tracked links"
msgstr "您追蹤的連結"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "cancel"
msgstr "取消"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/xml/recent_link.xml:0
msgid "clicks"
msgstr "點擊次數"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. \"Black Friday Mailing Campaign\""
msgstr "例：新春郵寄推廣活動"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links.js:0
msgid "e.g. InMails, Ads, Social, ..."
msgstr "例：InMail、廣告、社交網絡⋯"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links.js:0
msgid "e.g. June Sale, Paris Roadshow, ..."
msgstr "例：六月大減價、巴黎路演⋯"

#. module: website_links
#. odoo-javascript
#: code:addons/website_links/static/src/js/website_links.js:0
msgid "e.g. LinkedIn, Facebook, Leads, ..."
msgstr "例：LinkedIn、Facebook、潛在客戶⋯"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
msgid "e.g. https://www.odoo.com/contactus"
msgstr "例：https://www.odoo.com/contactus"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "ok"
msgstr "確定"

#. module: website_links
#: model_terms:ir.ui.view,arch_db:website_links.create_shorten_url
#: model_terms:ir.ui.view,arch_db:website_links.graphs
msgid "or"
msgstr "或"
