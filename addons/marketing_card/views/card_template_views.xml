<?xml version="1.0"?>
<odoo>
    <record id="card_template_view_form" model="ir.ui.view">
        <field name="name">card.template.view.form</field>
        <field name="model">card.template</field>
        <field name="arch" type="xml">
            <form string="Share Template">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="default_background"/>
                        </group>
                        <group>
                            <field name="primary_color" widget="color"/>
                            <field name="secondary_color" widget="color"/>
                            <field name="primary_text_color" widget="color"/>
                            <field name="secondary_text_color" widget="color"/>
                        </group>
                        <field name="body" widget="code" options="{'mode': 'xml'}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="card_template_action" model="ir.actions.act_window">
        <field name="name">Card Template</field>
        <field name="res_model">card.template</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a design to use in Card Campaigns
            </p>
        </field>
    </record>

</odoo>
