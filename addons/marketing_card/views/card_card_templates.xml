<?xml version="1.0"?>
<odoo>
<!--Notable pitfalls-->
<!--Unpatched wkhtmltoimage uses an old version of webkit, meaning things like standard flexbox
    are not supported and -webkit flags should be used.-->
<!--Your local fonts may diverge from production fonts, make sure the designs work as intended
    in their final production environment.-->
<template id="common_template">
    <html>
        <head>
            <meta charset="utf-8"/>
            <style>
            body {
                @charset "UTF-8";
                padding: 35px;
                font-family: Robot<PERSON>, Ubuntu, "Noto Sans", <PERSON><PERSON>, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                font-size: 1em;
                background: <t t-out="card_campaign.card_template_id.primary_color"/>;
                color: <t t-out="card_campaign.card_template_id.primary_text_color"/>;
                background-size: cover;
                background-attachment: fixed;
                display: -webkit-box;
                display: flex;
                -webkit-box-orient: vertical;
                flex-direction: column;
                -webkit-box-pack: justify;
                justify-content: space-around;
            }
            .button_background {
                background: <t t-out="card_campaign.card_template_id.secondary_color"/>;
                color: <t t-out="card_campaign.card_template_id.secondary_text_color"/>;
            }
            .header {
                line-height: 1.2em;
            }
            .header, .subheader, .cta_button h1 {
                display: -webkit-box;
                -webkit-box-orient: vertical;
            }
            .header, .subheader, .cta_button h1, .cta_button h2 {
                margin: 0.1em;
            }
            .subheader, .cta_button h1{
                line-height: 1.2em;
            }
            .cta_button {
                text-align: center;
                width: fit-content;
                min-width: 30%;
                max-width: 70%;
                padding: 0.2rem 0.2rem;
            }
            .rounded_button {
                border-radius: 50px;
            }
            .rounded_rectangle_button {
                border-radius: 8px;
            }
            .footer {
                width: 100%;
                display: -webkit-box;
                display: flex;
                -webkit-box-orient: horizontal;
                flex-direction: row;
                -webkit-box-pack: justify;
                justify-content: space-between;
            }
            .footer &gt; div {
                height: 100%;
            }
            .profile_section {
                margin-right: 0.25rem;
                font-size: 1.25rem;
                display: -webkit-box;
                display: flex;
                -webkit-box-orient: horizontal;
                flex-direction: row;
                -webkit-box-align: center;
                align-items: center;
            }
            .text_section {
                display: -webkit-box;
                display: flex;
                -webkit-box-orient: vertical;
                flex-direction: column;
                -webkit-box-align: start;
                align-items: flex-start;
                align-content: flex-start;
                -webkit-box-pack: center;
                justify-content: center;
            }
            .profile_section .text_section .text_subsections {
                font-size: 1rem;
            }
            </style>
        </head>
        <t t-set="values" t-value="card_campaign._get_card_element_values(object)"/>
        <body t-attf-style="background-image: url('data:image/png;base64,{{card_campaign.content_background or card_campaign.card_template_id.default_background}}');">
        </body>
    </html>
</template>

<template id="template_1" inherit_id="common_template" primary="True">
    <xpath expr="//html/head/style" position="after">
    <style>
        body {
            -webkit-box-align: center;
            align-items: center;
        }
        .center_column {
            text-align: center;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: vertical;
            flex-direction: column;
            -webkit-box-align: center;
            align-items: center;
            -webkit-box-pack: justify;
            justify-content: space-around;
            line-height: 2rem;
            height: 60%;
            width: 100%;
        }
        .center_column .cta_button {
            min-width: 50%;
            max-width: 70%;
        }
        .footer {
            -webkit-box-align: center;
            align-items: center;
            height: 30%;
        }
        .profile_section img {
            object-fit: cover;
            border-radius: 50px;
        }
        .profile_section .text_section span {
            display: block;
        }
        .logo {
            max-height: 40px;
            width: auto;
        }
    </style>
    </xpath>
    <xpath expr="//html/body" position="inside">
        <div class="center_column">
            <div class="title">
                <h1 class="header" t-out="values['header']" t-att-style="'color: %s;' % card_campaign.content_header_color"></h1>
                <h2 class="subheader" t-out="values['sub_header']" t-att-style="'color: %s;' % card_campaign.content_sub_header_color"></h2>
            </div>
            <div class="button_background rounded_button cta_button" t-if="card_campaign.content_button">
                <h1 t-out="card_campaign.content_button">Button</h1>
            </div>
        </div>
        <div class="footer">
            <div class="profile_section">
                <img
                t-attf-src="data:image/png;base64,{{values['image1']}}"
                alt="Profile Picture"
                width="64px"
                height="64px"
                style="margin-right: 10px"
                t-if="values['image1']"
                />
                <div t-else=""></div>
                <div class="text_section">
                    <span t-out="values['section']"/>
                    <div class="text_subsections">
                        <span t-out="values['sub_section1']"/>
                        <span t-out="values['sub_section2']"/>
                    </div>
                </div>
            </div>
            <img
                t-attf-src="data:image/png;base64,{{values['image2']}}"
                alt="Profile Picture"
                height="auto"
                class="logo"
                t-if="values['image2']"
            />
        </div>
    </xpath>
</template>

<template id="template_2" inherit_id="common_template" primary="True">
    <xpath expr="//html/head/style" position="after">
    <style>
        body {
            -webkit-box-align: center;
            align-items: center;
            justify-content: space-between;
        }
        .title {
            line-height: 2rem;
            width: 100%;
        }
        .footer {
            -webkit-box-align: center;
            align-items: center;
            height: 30%;
        }
        .cta_button {
            text-align: center;
            width: fit-content;
            max-height: 3rem;
        }
        .profile_section img {
            object-fit: cover;
            border-radius: 50px;
        }
        .profile_section .text_section {
            margin-left: 0.25rem;
        }
        .profile_section .text_section span {
            display: block;
        }
        .logo_line {
            width: 100%;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: horizontal;
            flex-direction: row;
            -webkit-box-align: center;
            align-items: center;
            align-content: center;
            -webkit-box-pack: end;
            justify-content: flex-end;
        }
        .logo {
            margin: 0.1rem;
            align-self: flex-end;
            max-height: 40px;
            width: auto;
        }
    </style>
    </xpath>
    <xpath expr="//html/body" position="inside">
        <div class="logo_line">
            <img
                t-attf-src="data:image/png;base64,{{values['image2']}}"
                alt="Profile Picture"
                class="logo"
                t-if="values['image2']"
            />
        </div>
        <div class="title">
            <h1 class="header" t-out="values['header']" t-att-style="'color: %s;' % card_campaign.content_header_color"/>
            <h2 class="subheader" t-out="values['sub_header']" t-att-style="'color: %s;' % card_campaign.content_sub_header_color"/>
        </div>
        <div class="footer">
            <div class="profile_section">
                <img
                    t-attf-src="data:image/png;base64,{{values['image1']}}"
                    alt="Profile Picture"
                    width="64px"
                    height="64px"
                    t-if="values['image1']"
                />
                <div t-else=""></div>
                <div class="text_section">
                    <span t-out="values['section']"/>
                    <div class="text_subsections">
                        <span t-out="values['sub_section1']"/>
                        <span t-out="values['sub_section2']"/>
                    </div>
                </div>
            </div>
            <div class="button_background rounded_button cta_button" t-if="card_campaign.content_button">
                <h1 t-out="card_campaign.content_button">button text</h1>
            </div>
        </div>
    </xpath>
</template>

<template id="template_3" inherit_id="common_template" primary="True">
    <xpath expr="//html/head/style" position="after">
    <style>
        body {
            -webkit-box-pack: justify;
            justify-content: space-between;
        }
        .title {
            line-height: 2rem;
            width: 80%;
        }
        .footer {
            -webkit-box-align: end;
            align-items: flex-end;
            height: 50%;
        }
        .footer &gt; div {
            min-width: 50%;
        }
        .cta_button {
            margin-top: 20px;
        }
        .profile_image {
            object-fit: cover;
            border-radius: 50px;
        }
        .cta_section {
            max-width: 75%;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: vertical;
            flex-direction: column;
            -webkit-box-align: center;
            align-items: center;
            -webkit-box-pack: justify;
            justify-content: space-between;
        }
        .cta_section .text_section {
            width: 100%;
            font-size: 1.25rem;
        }
        .cta_section .text_subsections {
            font-size: 0.85rem;
        }
        .logo {
            position: absolute;
            right: 35px;
            top: 35px;
            max-height: 40px;
            width: auto;
        }
    </style>
    </xpath>
    <xpath expr="//html/body" position="inside">
        <img
            t-attf-src="data:image/png;base64,{{values['image2']}}"
            alt="Profile Picture"
            class="logo"
            t-if="values['image2']"
        />
        <div class="title">
            <h2 class="subheader" t-out="values['sub_header']" t-att-style="'color: %s;' % card_campaign.content_sub_header_color"></h2>
            <h1 class="header" t-out="values['header']" t-att-style="'color: %s;' % card_campaign.content_header_color"></h1>
        </div>
        <div class="footer">
            <div>
                <div class="text_section">
                    <span t-out="values['section']"/>
                    <div class="text_subsections">
                        <span t-out="values['sub_section1']"/>
                        <t t-if="values['sub_section1'] and values['sub_section2']">-</t>
                        <span t-out="values['sub_section2']"/>
                    </div>
                </div>
                <div class="button_background rounded_rectangle_button cta_button" t-if="card_campaign.content_button">
                    <h2 t-out="card_campaign.content_button">button text</h2>
                </div>
            </div>
            <img
                class="profile_image"
                t-attf-src="data:image/png;base64,{{values['image1']}}"
                alt="Profile Picture"
                width="106px"
                height="106px"
                t-if="values['image1']"
            />
        </div>
    </xpath>
</template>


<template id="template_4" inherit_id="common_template" primary="True">
    <xpath expr="//html/head/style" position="after">
    <style>
        body {
            justify-content: space-between;
        }
        .header {
            -webkit-line-clamp: 3;
            max-height: 4.5em;
            line-height: 1.2em;
        }
        .title {
            line-height: 2rem;
            width: 80%;
        }
        .footer {
            height: 50%;
        }
        .cta_button {
            text-align: center;
            max-height: 3rem;
        }
        .profile_image {
            object-fit: cover;
            border-radius: 50px;
        }
        .cta_section {
            max-width: 75%;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: vertical;
            flex-direction: column;
            -webkit-box-align: center;
            align-items: center;
        }
        .text_section {
            width: 55%;
            font-size: 1.25rem;
        }
        .text_subsections {
            font-size: 0.85rem;
        }
        .text_subsections span {
            text-wrap: nowrap;
            white-space: nowrap;
        }
        .cta_button {
            width: fit-content;
            margin-top: 30px;
        }
        .logo_line {
            width: 100%;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: horizontal;
            flex-direction: row;
            -webkit-box-align: center;
            align-items: center;
            align-content: center;
            -webkit-box-pack: start;
            justify-content: flex-start;
        }
        .logo {
            max-height: 40px;
            width: auto;
        }
        .profile_image {
            background-size: cover;
            position: absolute;
            right: 35px;
            top: 35px;
        }
    </style>
    </xpath>
    <xpath expr="//html/body" position="inside">
        <div class="logo_line">
            <img
                t-attf-src="data:image/png;base64,{{values['image2']}}"
                alt="Profile Picture"
                class="logo"
                t-if="values['image2']"
            />
        </div>
        <img
            class="profile_image"
            t-attf-src="data:image/png;base64,{{values['image1']}}"
            alt="Profile Picture"
            width="106px"
            height="106px"
            t-if="values['image1']"
        />
        <div class="title">
            <h2 class="subheader" t-out="values['sub_header']" t-att-style="'color: %s;' % card_campaign.content_sub_header_color"></h2>
            <h1 class="header" t-out="values['header']" t-att-style="'color: %s;' % card_campaign.content_header_color"></h1>
        </div>
        <div class="footer">
            <div class="text_section">
                <span t-out="values['section']"/>
                <div class="text_subsections">
                    <span t-out="values['sub_section1']"/>
                    <t t-if="values['sub_section1'] and values['sub_section2']">-</t>
                    <span t-out="values['sub_section2']"/>
                </div>
            </div>
            <div class="button_background rounded_button cta_button" t-if="card_campaign.content_button">
                <h1 t-out="card_campaign.content_button">button text</h1>
            </div>
        </div>
    </xpath>
</template>

<template id="template_5" inherit_id="common_template" primary="True">
    <xpath expr="//html/head/style" position="after">
    <style>
        body {
            height: 100%;
        }
        .footer {
            margin-top: 5%;
            height: 40%;
        }
        .cta_button {
            text-align: center;
        }
        .profile_image {
            object-fit: cover;
            border-radius: 50px;
        }
        .cta_section {
            max-width: 75%;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: vertical;
            flex-direction: column;
            -webkit-box-align: center;
            align-items: center;
        }
        .text_section {
            font-size: 1.25rem;
        }
        .text_subsections {
            height: 100%;
            width: 100%;
            font-size: 0.85rem;
        }
        .cta_button {
            width: fit-content;
            min-width: 30%;
            max-width: 50%;
        }
        .logo_line {
            width: 100%;
            display: -webkit-box;
            display: flex;
            -webkit-box-orient: horizontal;
            flex-direction: row;
            -webkit-box-align: center;
            align-items: center;
            align-content: center;
            -webkit-box-pack: justify;
            justify-content: space-between;
        }
        .logo {
            max-height: 40px;
            width: auto;
        }
    </style>
    </xpath>
    <xpath expr="//html/body" position="inside">
        <div class="logo_line">
            <img
                class="profile_image"
                t-attf-src="data:image/png;base64,{{values['image1']}}"
                alt="Profile Picture"
                width="60px"
                height="60px"
                t-if="values['image1']"
            />
            <!--spacing filler in case there is no image-->
            <div t-if="not values['image1']"></div>
            <div class="button_background rounded_button cta_button" t-if="card_campaign.content_button">
                <h1 t-out="card_campaign.content_button">button text</h1>
            </div>
        </div>
        <div class="title">
            <h2 class="subheader" t-out="values['sub_header']" t-att-style="'color: %s;' % card_campaign.content_sub_header_color"></h2>
            <h1 class="header" t-out="values['header']" t-att-style="'color: %s;' % card_campaign.content_header_color"></h1>
        </div>
        <div class="footer">
            <div class="text_section">
                <span t-out="values['section']"/>
                <div class="text_subsections">
                    <span t-out="values['sub_section1']"/>
                    <t t-if="values['sub_section1'] and values['sub_section2']">-</t>
                    <span t-out="values['sub_section2']"/>
                </div>
            </div>
            <img
                t-attf-src="data:image/png;base64,{{values['image2']}}"
                alt="Profile Picture"
                class="logo"
                t-if="values['image2']"
            />
        </div>
    </xpath>
</template>
</odoo>
