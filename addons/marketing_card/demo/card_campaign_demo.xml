<?xml version="1.0"?>
<odoo noupdate="1">
    <record id="card_campaign_tag_demo" model="card.campaign.tag">
        <field name="name">Exhibitor</field>
    </record>

    <record id="card_campaign_campaign_1" model="card.campaign">
        <field name="name">Employee Card</field>
        <field name="preview_record_ref" eval="'res.partner,%d' % ref('base.res_partner_1')"/>
        <field name="card_template_id" ref="marketing_card.card_template_drawings"/>
        <field name="post_suggestion">Checkout who joined Odoo.</field>
        <field name="tag_ids" eval="[Command.link(ref('card_campaign_tag_demo'))]"/>
        <field name="target_url">https://www.odoo.com/</field>
        <field name="reward_message" type="html">
            <p>You just leveled up your exposure!</p>
            <p>Check out odoo.com for more!</p>
        </field>
        <field name="reward_target_url">https://www.odoo.com/</field>
        <field name="user_id" ref="base.user_admin"/>

        <field name="request_title">Share the news with your community</field>
        <field name="request_description">That will help us</field>

        <field name="content_header_dyn" eval="True"/>
        <field name="content_header_path">name</field>
        <field name="content_header_color">#1F1881</field>
        <field name="content_sub_header">Welcome to Odoo</field>
        <field name="content_section">This section is static</field>
        <field name="content_sub_section1_dyn" eval="True"/>
        <field name="content_sub_section1_path">city</field>
        <field name="content_sub_section2_dyn" eval="True"/>
        <field name="content_sub_section2_path">country_id.name</field>
        <field name="content_button">join me</field>

    </record>
</odoo>
