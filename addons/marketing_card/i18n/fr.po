# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_card
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "%(card_campaign_name)s Mailings"
msgstr "%(card_campaign_name)s Listes de diffusion"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\" "
"title=\"Clicks\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clics\" role=\"img\" "
"title=\"Clics\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-share\" aria-label=\"Shares\" role=\"img\" title=\"Shares\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-share\" aria-label=\"Partages\" role=\"img\" "
"title=\"Partages\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "<small>Where does this link to?</small>"
msgstr "<small>Où mène ce lien ?</small>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Cards\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Cartes\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Clicks\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Clics\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Mailings\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Listes de diffusion\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Opened\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Ouvert\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Shared\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Partagé\n"
"                            </span>"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__active
#: model:ir.model.fields,field_description:marketing_card.field_card_card__active
msgid "Active"
msgstr "Actif"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_state
msgid "Activity State"
msgstr "Statut de l'activité"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Archived"
msgstr "Archivé"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Are you sure you want to update all cards of the campaign?"
msgstr ""
"Êtes-vous sûr de vouloir mettre à jour toutes les cartes de la campagne ?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_background
msgid "Background"
msgstr "Arrière-plan"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__body_html
#: model:ir.model.fields,field_description:marketing_card.field_card_template__body
msgid "Body"
msgstr "Corps"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_button
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
msgid "Button"
msgstr "Bouton"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Campaign"
msgstr "Campagne"

#. module: marketing_card
#: model:ir.ui.menu,name:marketing_card.card_campaign_menu
msgid "Campaigns"
msgstr "Campagnes"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.cards_card_action
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_ids
msgid "Card"
msgstr "Carte"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_campaign_action
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_campaign_id
msgid "Card Campaign"
msgstr "Campagne de cartes"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid "Card Campaign Mailing should target model %(model_name)s"
msgstr ""
"La liste de diffusion de la campagne de cartes doit cibler le "
"modèle%(model_name)s"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_click_count
msgid "Card Click Count"
msgstr "Nombre de clics sur les cartes"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_count
msgid "Card Count"
msgstr "Nombre de cartes"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Card Layout"
msgstr "Mise en page de la carte"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_requires_sync_count
msgid "Card Requires Sync Count"
msgstr "La carte nécessite une synchronisation"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_share_count
msgid "Card Share Count"
msgstr "Nombre de partages de la carte"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_template_action
#: model:ir.ui.menu,name:marketing_card.cards_template_menu
msgid "Card Template"
msgstr "Modèle de carte"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Cards"
msgstr "Cartes"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Click here for your reward!"
msgstr "Cliquez ici pour votre récompense !"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__color
msgid "Color"
msgstr "Couleur"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.cards_card_action
msgid "Create a Card Campaign to send cards to your partners"
msgstr ""
"Créez une campagne de cartes pour envoyer des cartes à vos partenaires"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid "Create a Sharing Campaign!"
msgstr "Créez une campagne de partage !"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_template_action
msgid "Create a design to use in Card Campaigns"
msgstr "Créer un modèle pour les campagnes de cartes"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_date
msgid "Created on"
msgstr "Créé le"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__default_background
msgid "Default Background"
msgstr "Arrière-plan par défaut"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__post_suggestion
msgid "Description below the card and default text when sharing on X"
msgstr "Description sous la carte et texte par défaut lors du partage sur X"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_template_id
msgid "Design"
msgstr "Conception"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_card__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Dynamic Field?"
msgstr "Champ dynamique ?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image1_path
msgid "Dynamic Image 1"
msgstr "Image dynamique 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image2_path
msgid "Dynamic Image 2"
msgstr "Image dynamique 2"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_card_campaign_record_unique
msgid "Each record should be unique for a campaign"
msgstr "Chaque enregistrement doit être unique dans une campagne"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Assistant de composition d'e-mails"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Filter By"
msgstr "Filtrer sur"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__has_message
msgid "Has Message"
msgstr "A un message"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header
msgid "Header"
msgstr "En-tête"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_color
msgid "Header Color"
msgstr "Couleur de l'en-tête"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_path
msgid "Header Path"
msgstr "Chemin d'accès vers l'en-tête"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Help us share the news"
msgstr "Aidez-nous à partager la nouvelle"

#. module: marketing_card
#: model:ir.module.category,description:marketing_card.module_category_marketing_card
msgid "Helps you manage marketing card campaigns."
msgstr "Permet de gérer les campagnes marketing de cartes."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__id
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__id
#: model:ir.model.fields,field_description:marketing_card.field_card_card__id
#: model:ir.model.fields,field_description:marketing_card.field_card_template__id
msgid "ID"
msgstr "ID"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__image
msgid "Image"
msgstr "Image"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__image_preview
msgid "Image Preview"
msgstr "Aperçu de l'image"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_dyn
msgid "Is Dynamic Header"
msgstr "Est un en-tête dynamique"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_dyn
msgid "Is Dynamic Section"
msgstr "Est une section dynamique"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_dyn
msgid "Is Dynamic Sub-Header"
msgstr "Est un sous-titre dynamique"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_dyn
msgid "Is Dynamic Sub-Section 1"
msgstr "Sous-section dynamique 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_dyn
msgid "Is Dynamic Sub-Section 2"
msgstr "Sous-section dynamique 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Join me at this event!"
msgstr "Rejoignez-moi à cet événement !"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__lang
msgid "Language"
msgstr "Langue"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__link_tracker_id
msgid "Link Tracker"
msgstr "Tracker de liens"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_ids
msgid "Mailing"
msgstr "Mailing"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_count
msgid "Mailing Count"
msgstr "Nombre de listes de diffusion"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_card
#: model:ir.module.category,name:marketing_card.module_category_marketing_card
#: model:ir.ui.menu,name:marketing_card.card_menu
#: model:ir.ui.menu,name:marketing_card.marketing_card_menu_technical
msgid "Marketing Card"
msgstr "Carte Marketing"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign
msgid "Marketing Card Campaign"
msgstr "Campagne marketing de cartes"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign_tag
msgid "Marketing Card Campaign Tag"
msgstr "Étiquette de campagne marketing de cartes"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_manager
msgid "Marketing Card Manager"
msgstr "Gestionnaire de carte marketing"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_template
msgid "Marketing Card Template"
msgstr "Modèle de carte marketing"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_user
msgid "Marketing Card User"
msgstr "Utilisateur de carte marketing"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mailing_mailing
msgid "Mass Mailing"
msgstr "E-mail Marketing"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_ids
msgid "Messages"
msgstr "Messages"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__res_model
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_model
msgid "Model Name"
msgstr "Nom de modèle"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid ""
"Model of campaign %(campaign)s may not be changed as it already has cards"
msgstr ""
"Le modèle de campagne %(campaign)s ne peut pas être modifié car il contient "
"déjà des cartes."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "My Campaigns"
msgstr "Mes campagnes"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__name
msgid "Name"
msgstr "Nom"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l'activité suivante"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "No button"
msgstr "Pas de bouton"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url_click_count
msgid "Number of Clicks"
msgstr "Nombre de clics"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Odoo"
msgstr "Odoo"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Langue de traduction facultative (code ISO) à sélectionner lors de l'envoi "
"d'un e-mail. Si aucune langue n'est définie, la version anglaise sera "
"utilisée. Il doit généralement s'agir d'une expression d'espace réservé qui "
"fournit le langage approprié, par ex. {{ object.partner_id.lang }}."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url
msgid "Post Link"
msgstr "Publier un lien"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__post_suggestion
msgid "Post Suggestion"
msgstr "Suggestion de publication"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Powered By"
msgstr "Généré par"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid ""
"Prepare a design and some content and let your community spread the word!"
msgstr ""
"Concevez un design et un contenu et laissez votre communauté le partager !"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview"
msgstr "Aperçu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__preview_record_ref
msgid "Preview On"
msgstr "Aperçu activé"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview on..."
msgstr "Aperçu..."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_color
msgid "Primary Color"
msgstr "Couleur primaire"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_text_color
msgid "Primary Text Color"
msgstr "Couleur du texte primaire"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "Profile Picture"
msgstr "Photo de profil"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipient Message"
msgstr "Message du destinataire"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipients"
msgstr "Destinataires"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "Modèle de destinaire"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_id
msgid "Record ID"
msgstr "ID de l'enregistrement"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__render_model
msgid "Rendering Model"
msgstr "Modèle de rendu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_title
msgid "Request"
msgstr "Demande"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_description
msgid "Request Description"
msgstr "Demander une description"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__requires_sync
msgid "Requires Sync"
msgstr "Nécessite une synchronisation"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__user_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Responsible"
msgstr "Responsable"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_target_url
msgid "Reward Link"
msgstr "Lien de récompense"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Search Card"
msgstr "Rechercher une carte"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Search Share Campaign"
msgstr "Rechercher une campagne de partage"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_color
msgid "Secondary Color"
msgstr "Couleur secondaire"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_text_color
msgid "Secondary Text Color"
msgstr "Couleur du texte secondaire"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section
msgid "Section"
msgstr "Section"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_path
msgid "Section Path"
msgstr "Chemin d'accès vers la section"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Select a field"
msgstr "Sélectionner un champ"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Select where to share"
msgstr "Sélectionner où partager"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Send"
msgstr "Envoyer"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Send Cards"
msgstr "Envoyer les cartes"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Share Campaign"
msgstr "Partager la campagne"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_list
msgid "Share Card"
msgstr "Partager la carte"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__share_status
msgid "Share Status"
msgstr "Partager le statut"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_template_view_form
msgid "Share Template"
msgstr "Partager le modèle"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Share with your community!"
msgstr "Partager avec votre communauté !"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__shared
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Shared"
msgstr "Partagé"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_color
msgid "Sub Header Color"
msgstr "Couleur du sous-titre"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header
msgid "Sub-Header"
msgstr "Sous-titre"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_path
msgid "Sub-Header Path"
msgstr "Chemin d'accès vers le sous-titre"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1
msgid "Sub-Section 1"
msgstr "Sous-Section 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_path
msgid "Sub-Section 1 Path"
msgstr "Chemin d'accès Sous-Section 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2
msgid "Sub-Section 2"
msgstr "Sous-Section 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_path
msgid "Sub-Section 2 Path"
msgstr "Chemin d'accès Sous-Section 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__tag_ids
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Tags"
msgstr "Étiquettes"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_campaign_tag_name_uniq
msgid "Tags may not reuse existing names."
msgstr "Les étiquettes ne peuvent pas reprendre des noms existants."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_message
msgid "Thank You Message"
msgstr "Message de remerciement"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/utm_source.py:0
msgid ""
"The UTM source '%s' cannot be deleted as it is used to promote marketing "
"cards campaigns."
msgstr ""
"La source UTM '%s' ne peut pas être supprimée, car elle est utilisée pour "
"promouvoir des campagnes marketing avec des cartes."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_utm_source
msgid "UTM Source"
msgstr "Source UTM"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update"
msgstr "Mettre à jour"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update Cards"
msgstr "Mettre à jour les cartes"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__visited
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Visited"
msgstr "Visité"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_card__requires_sync
msgid "Whether the image needs to be updated to match the campaign template."
msgstr ""
"Indique si l'image doit être mise à jour pour correspondre au modèle de "
"campagne."

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid ""
"You should update all the cards for %(mailing)s before scheduling a mailing."
msgstr ""
"Vous devez mettre à jour toutes les cartes de %(mailing)s avant de planifier"
" un envoi."

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Your Home Page"
msgstr "Votre page d'accueil"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "button text"
msgstr "texte du bouton"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"Thanks for sharing, here is your reward!\""
msgstr "par exemple : \"Merci pour le partage, voici votre récompense !\""

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"https://www.mycompany.com/reward\""
msgstr "par ex. \"https://www.mycompany.com/reward\""

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Aug 24, Brussels Expo"
msgstr "par ex. 24 août, Brussels Expo"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. By Lionel Messy"
msgstr "par ex. By Lionel Messy"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. CFO Chief Football Officer"
msgstr "par ex. CFO Chief Football Officer"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Join Odoo Experience 2024"
msgstr "par ex. Odoo Experience 2024"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Odoo Experience Talks"
msgstr "par ex. Odoo Experience Talks"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Sample Talk"
msgstr "par ex. Sample Talk"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Why people should share on their network?"
msgstr "par ex. Pourquoi les gens devraient-ils communiquer sur leur réseau ?"
