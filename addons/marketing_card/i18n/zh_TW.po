# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_card
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "%(card_campaign_name)s Mailings"
msgstr "%(card_campaign_name)s 的郵件"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\" "
"title=\"Clicks\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"點擊次數\" role=\"img\" "
"title=\"點擊次數\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-share\" aria-label=\"Shares\" role=\"img\" title=\"Shares\"/>"
msgstr "<i class=\"fa fa-fw fa-share\" aria-label=\"分享次數\" role=\"img\" title=\"分享次數\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "<small>Where does this link to?</small>"
msgstr "<small>這是連結至哪裏的？</small>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Cards\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                卡片\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Clicks\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                點擊次數\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Mailings\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                郵件\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Opened\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                已開啟\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Shared\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                已分享\n"
"                            </span>"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__active
#: model:ir.model.fields,field_description:marketing_card.field_card_card__active
msgid "Active"
msgstr "啟用"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_ids
msgid "Activities"
msgstr "活動"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Archived"
msgstr "已封存"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Are you sure you want to update all cards of the campaign?"
msgstr "確定要更新推廣活動的所有卡片？"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_background
msgid "Background"
msgstr "背景"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__body_html
#: model:ir.model.fields,field_description:marketing_card.field_card_template__body
msgid "Body"
msgstr "內文"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_button
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
msgid "Button"
msgstr "按鈕"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Campaign"
msgstr "行銷活動"

#. module: marketing_card
#: model:ir.ui.menu,name:marketing_card.card_campaign_menu
msgid "Campaigns"
msgstr "行銷"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.cards_card_action
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_ids
msgid "Card"
msgstr "卡片"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_campaign_action
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_campaign_id
msgid "Card Campaign"
msgstr "卡片活動"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid "Card Campaign Mailing should target model %(model_name)s"
msgstr "卡片的推廣活動郵件，應針對模型 %(model_name)s"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_click_count
msgid "Card Click Count"
msgstr "卡片點按次數"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_count
msgid "Card Count"
msgstr "卡片數目"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Card Layout"
msgstr "卡片版面"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_requires_sync_count
msgid "Card Requires Sync Count"
msgstr "需要同步卡片數目"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_share_count
msgid "Card Share Count"
msgstr "卡片分享次數"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_template_action
#: model:ir.ui.menu,name:marketing_card.cards_template_menu
msgid "Card Template"
msgstr "卡片範本"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Cards"
msgstr "卡片"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Click here for your reward!"
msgstr "按此處領取獎勵！"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__color
msgid "Color"
msgstr "顏色"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.cards_card_action
msgid "Create a Card Campaign to send cards to your partners"
msgstr "建立卡片活動，以向你的合作夥伴發送卡片"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid "Create a Sharing Campaign!"
msgstr "建立分享推廣活動！"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_template_action
msgid "Create a design to use in Card Campaigns"
msgstr "建立設計供卡片活動使用"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_date
msgid "Created on"
msgstr "建立於"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__default_background
msgid "Default Background"
msgstr "預設背景"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__post_suggestion
msgid "Description below the card and default text when sharing on X"
msgstr "卡片下方的描述，以及在 X 分享時的預設文字"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_template_id
msgid "Design"
msgstr "設計"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_card__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Dynamic Field?"
msgstr "動態欄位？"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image1_path
msgid "Dynamic Image 1"
msgstr "動態圖片 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image2_path
msgid "Dynamic Image 2"
msgstr "動態圖片 2"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_card_campaign_record_unique
msgid "Each record should be unique for a campaign"
msgstr "推廣活動內的每項記錄都應該獨一無二"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mail_compose_message
msgid "Email composition wizard"
msgstr "電郵撰寫精靈"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Filter By"
msgstr "篩選依據"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Group By"
msgstr "分組依據"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header
msgid "Header"
msgstr "頁首"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_color
msgid "Header Color"
msgstr "頁首配色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_path
msgid "Header Path"
msgstr "頁首路徑"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Help us share the news"
msgstr "幫我們廣傳新消息！"

#. module: marketing_card
#: model:ir.module.category,description:marketing_card.module_category_marketing_card
msgid "Helps you manage marketing card campaigns."
msgstr "協助你管理推廣卡片推廣活動。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__id
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__id
#: model:ir.model.fields,field_description:marketing_card.field_card_card__id
#: model:ir.model.fields,field_description:marketing_card.field_card_template__id
msgid "ID"
msgstr "識別號"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__image
msgid "Image"
msgstr "圖片"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__image_preview
msgid "Image Preview"
msgstr "圖片預覽"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_dyn
msgid "Is Dynamic Header"
msgstr "是動態頁首"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_dyn
msgid "Is Dynamic Section"
msgstr "是動態章節"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_dyn
msgid "Is Dynamic Sub-Header"
msgstr "是動態子頁首"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_dyn
msgid "Is Dynamic Sub-Section 1"
msgstr "是動態小分節 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_dyn
msgid "Is Dynamic Sub-Section 2"
msgstr "是動態小分節 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_is_follower
msgid "Is Follower"
msgstr "是關注者"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Join me at this event!"
msgstr "跟我一起參加這個活動吧！"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__lang
msgid "Language"
msgstr "語言"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__link_tracker_id
msgid "Link Tracker"
msgstr "連結追蹤"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_ids
msgid "Mailing"
msgstr "郵寄"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_count
msgid "Mailing Count"
msgstr "郵寄數目"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_card
#: model:ir.module.category,name:marketing_card.module_category_marketing_card
#: model:ir.ui.menu,name:marketing_card.card_menu
#: model:ir.ui.menu,name:marketing_card.marketing_card_menu_technical
msgid "Marketing Card"
msgstr "推廣卡片"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign
msgid "Marketing Card Campaign"
msgstr "推廣卡片推廣活動"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign_tag
msgid "Marketing Card Campaign Tag"
msgstr "推廣卡片活動標籤"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_manager
msgid "Marketing Card Manager"
msgstr "推廣卡片經理"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_template
msgid "Marketing Card Template"
msgstr "推廣卡片範本"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_user
msgid "Marketing Card User"
msgstr "推廣卡片使用者"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mailing_mailing
msgid "Mass Mailing"
msgstr "群發信件"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_ids
msgid "Messages"
msgstr "訊息"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__res_model
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_model
msgid "Model Name"
msgstr "模型名稱"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid ""
"Model of campaign %(campaign)s may not be changed as it already has cards"
msgstr "推廣活動「%(campaign)s」的模型不可更改，因為活動已經有卡片。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "My Campaigns"
msgstr "我的活動"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__name
msgid "Name"
msgstr "名稱"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "No button"
msgstr "沒有按鈕"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of Actions"
msgstr "操作數目"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url_click_count
msgid "Number of Clicks"
msgstr "點選次數"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Odoo"
msgstr "Odoo"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"發送電子郵件時要選擇的可選翻譯語言（ISO 代碼）。如果未設置，將使用英文版本。這通常應該是提供適當語言的佔位符表達式，例如{{ "
"object.partner_id.lang }}。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url
msgid "Post Link"
msgstr "帖文連結"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__post_suggestion
msgid "Post Suggestion"
msgstr "帖文建議"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Powered By"
msgstr "技術提供："

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid ""
"Prepare a design and some content and let your community spread the word!"
msgstr "準備設計和一些內容，讓你的社群傳播消息！"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview"
msgstr "預覽"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__preview_record_ref
msgid "Preview On"
msgstr "預覽"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview on..."
msgstr "預覽於⋯"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_color
msgid "Primary Color"
msgstr "主要色彩"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_text_color
msgid "Primary Text Color"
msgstr "主要文字顏色"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "Profile Picture"
msgstr "主題圖片"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__rating_ids
msgid "Ratings"
msgstr "評分"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipient Message"
msgstr "收件人訊息"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipients"
msgstr "接收者"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "收件人模型"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_id
msgid "Record ID"
msgstr "記錄 ID"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__render_model
msgid "Rendering Model"
msgstr "呈現模型"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_title
msgid "Request"
msgstr "請求"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_description
msgid "Request Description"
msgstr "請求描述"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__requires_sync
msgid "Requires Sync"
msgstr "需要同步"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__user_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Responsible"
msgstr "負責人"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_target_url
msgid "Reward Link"
msgstr "獎勵連結"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Search Card"
msgstr "搜尋卡片"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Search Share Campaign"
msgstr "搜尋分享推廣活動"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_color
msgid "Secondary Color"
msgstr "次要色彩"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_text_color
msgid "Secondary Text Color"
msgstr "次要文字顏色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section
msgid "Section"
msgstr "章節"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_path
msgid "Section Path"
msgstr "章節路徑"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Select a field"
msgstr "選擇一個欄位"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Select where to share"
msgstr "選擇分享位置"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Send"
msgstr "發送"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Send Cards"
msgstr "傳送卡片"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Share Campaign"
msgstr "分享推廣計劃"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_list
msgid "Share Card"
msgstr "分享卡片"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__share_status
msgid "Share Status"
msgstr "分享狀態"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_template_view_form
msgid "Share Template"
msgstr "分享模板"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Share with your community!"
msgstr "與你的社群分享！"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__shared
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Shared"
msgstr "分享"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_color
msgid "Sub Header Color"
msgstr "子頁首顏色"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header
msgid "Sub-Header"
msgstr "副標題"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_path
msgid "Sub-Header Path"
msgstr "副標題路徑"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1
msgid "Sub-Section 1"
msgstr "小節 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_path
msgid "Sub-Section 1 Path"
msgstr "小分節 1 路徑"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2
msgid "Sub-Section 2"
msgstr "小節 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_path
msgid "Sub-Section 2 Path"
msgstr "小分節 2 路徑"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__tag_ids
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Tags"
msgstr "標籤"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_campaign_tag_name_uniq
msgid "Tags may not reuse existing names."
msgstr "標籤不可重複使用現有名稱。"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_message
msgid "Thank You Message"
msgstr "感謝訊息"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/utm_source.py:0
msgid ""
"The UTM source '%s' cannot be deleted as it is used to promote marketing "
"cards campaigns."
msgstr "UTM 來源「%s」不可刪除，因為它用於宣傳推廣卡片的推廣活動。"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_utm_source
msgid "UTM Source"
msgstr "UTM 來源"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update"
msgstr "更新"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update Cards"
msgstr "更新卡片"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__visited
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Visited"
msgstr "已到訪"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_card__requires_sync
msgid "Whether the image needs to be updated to match the campaign template."
msgstr "圖片是否需要更新以符合推廣計劃範本。"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid ""
"You should update all the cards for %(mailing)s before scheduling a mailing."
msgstr "預排寄出郵件之前，你應該更新 %(mailing)s 的所有卡片。"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Your Home Page"
msgstr "你的網站主頁"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "button text"
msgstr "按鈕文字"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"Thanks for sharing, here is your reward!\""
msgstr "例：感謝分享，送上你的獎勵！"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"https://www.mycompany.com/reward\""
msgstr "例：https://www.mycompany.com/reward"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Aug 24, Brussels Expo"
msgstr "例：8 月 24 日，布魯塞爾展覽"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. By Lionel Messy"
msgstr "例：由陳大文主持"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. CFO Chief Football Officer"
msgstr "例：財務總監"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Join Odoo Experience 2024"
msgstr "例：參加 Odoo 體驗展 2024"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Odoo Experience Talks"
msgstr "例：Odoo 體驗展演講"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Sample Talk"
msgstr "例：範例演講"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Why people should share on their network?"
msgstr "例：人們有甚麼理由在自己的社交網絡分享？"
