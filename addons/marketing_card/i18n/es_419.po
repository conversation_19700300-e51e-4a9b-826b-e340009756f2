# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_card
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "%(card_campaign_name)s Mailings"
msgstr "Correos de %(card_campaign_name)s"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\" "
"title=\"Clicks\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clics\" role=\"img\" "
"title=\"Clics\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-share\" aria-label=\"Shares\" role=\"img\" title=\"Shares\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-share\" aria-label=\"Compartidas\" role=\"img\" "
"title=\"Compartidas\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "<small>Where does this link to?</small>"
msgstr "<small>¿A dónde lleva esto?</small>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Cards\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Tarjetas\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Clicks\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Clics\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Mailings\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Correos\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Opened\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Abiertas\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Shared\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Compartida\n"
"                            </span>"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction
msgid "Action Needed"
msgstr "Se requiere una acción"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__active
#: model:ir.model.fields,field_description:marketing_card.field_card_card__active
msgid "Active"
msgstr "Activo"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de la actividad de excepción"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono del tipo de actividad"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Archived"
msgstr "Archivado"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Are you sure you want to update all cards of the campaign?"
msgstr ""
"¿Está seguro de que desea actualizar todas las tarjetas de la campaña?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_background
msgid "Background"
msgstr "Fondo"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__body_html
#: model:ir.model.fields,field_description:marketing_card.field_card_template__body
msgid "Body"
msgstr "Contenido"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_button
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
msgid "Button"
msgstr "Botón"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Campaign"
msgstr "Campaña"

#. module: marketing_card
#: model:ir.ui.menu,name:marketing_card.card_campaign_menu
msgid "Campaigns"
msgstr "Campañas"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.cards_card_action
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_ids
msgid "Card"
msgstr "Tarjeta"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_campaign_action
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_campaign_id
msgid "Card Campaign"
msgstr "Campaña de la tarjeta"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid "Card Campaign Mailing should target model %(model_name)s"
msgstr ""
"El correo de la campaña de tarjetas debe estar dirigido al modelo "
"%(model_name)s"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_click_count
msgid "Card Click Count"
msgstr "Número de clics en la tarjeta"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_count
msgid "Card Count"
msgstr "Número de tarjetas"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Card Layout"
msgstr "Diseño de la tarjeta"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_requires_sync_count
msgid "Card Requires Sync Count"
msgstr "Número de tarjetas que necesitan sincronización"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_share_count
msgid "Card Share Count"
msgstr "Número de veces que se ha compartido la tarjeta"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_template_action
#: model:ir.ui.menu,name:marketing_card.cards_template_menu
msgid "Card Template"
msgstr "Plantilla de la tarjeta"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Cards"
msgstr "Tarjetas"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Click here for your reward!"
msgstr "Haga clic aquí para obtener su recompensa"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__color
msgid "Color"
msgstr "Color"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.cards_card_action
msgid "Create a Card Campaign to send cards to your partners"
msgstr "Cree una campaña de tarjetas para enviárselas a sus contactos"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid "Create a Sharing Campaign!"
msgstr "Cree una campaña para compartir"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_template_action
msgid "Create a design to use in Card Campaigns"
msgstr "Cree un diseño para usarlo en las campañas de tarjeta"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_date
msgid "Created on"
msgstr "Creado el"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__default_background
msgid "Default Background"
msgstr "Fondo predeterminado"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__post_suggestion
msgid "Description below the card and default text when sharing on X"
msgstr ""
"Descripción que aparece abajo de la tarjeta y el texto predeterminado cuando"
" la comparte en X"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_template_id
msgid "Design"
msgstr "Diseño"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_card__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Dynamic Field?"
msgstr "¿Es un campo dinámico?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image1_path
msgid "Dynamic Image 1"
msgstr "Imagen dinámica 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image2_path
msgid "Dynamic Image 2"
msgstr "Imagen dinámica 2"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_card_campaign_record_unique
msgid "Each record should be unique for a campaign"
msgstr "Cada registro de la campaña debe ser unico"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Asistente de redacción de correo electrónico"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Filter By"
msgstr "Filtrar por"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (contactos)"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome, por ejemplo, fa-tasks"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header
msgid "Header"
msgstr "Encabezado"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_color
msgid "Header Color"
msgstr "Color del encabezado"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_path
msgid "Header Path"
msgstr "Ruta del encabezado"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Help us share the news"
msgstr "Ayúdenos a compartir las novedades"

#. module: marketing_card
#: model:ir.module.category,description:marketing_card.module_category_marketing_card
msgid "Helps you manage marketing card campaigns."
msgstr "Le ayuda a gestionar sus campañas de tarjetas de marketing"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__id
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__id
#: model:ir.model.fields,field_description:marketing_card.field_card_card__id
#: model:ir.model.fields,field_description:marketing_card.field_card_template__id
msgid "ID"
msgstr "ID"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono que indica una actividad de excepción."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__image
msgid "Image"
msgstr "Imagen"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__image_preview
msgid "Image Preview"
msgstr "Vista previa de la imagen"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_dyn
msgid "Is Dynamic Header"
msgstr "Es un encabezado dinámico"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_dyn
msgid "Is Dynamic Section"
msgstr "¿Es una sección dinámica?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_dyn
msgid "Is Dynamic Sub-Header"
msgstr "Es un subencabezado dinámico"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_dyn
msgid "Is Dynamic Sub-Section 1"
msgstr "¿Es una subsección dinámica 1?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_dyn
msgid "Is Dynamic Sub-Section 2"
msgstr "¿Es una subsección dinámica 2?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Join me at this event!"
msgstr "¡Acompáñennos en este evento!"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__lang
msgid "Language"
msgstr "Idioma"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__link_tracker_id
msgid "Link Tracker"
msgstr "Rastreador de enlaces"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_ids
msgid "Mailing"
msgstr "Correo electrónico"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_count
msgid "Mailing Count"
msgstr "Número de correos"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_card
#: model:ir.module.category,name:marketing_card.module_category_marketing_card
#: model:ir.ui.menu,name:marketing_card.card_menu
#: model:ir.ui.menu,name:marketing_card.marketing_card_menu_technical
msgid "Marketing Card"
msgstr "Tarjeta de marketing"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign
msgid "Marketing Card Campaign"
msgstr "Campaña de tarjetas de marketing"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign_tag
msgid "Marketing Card Campaign Tag"
msgstr "Etiqueta de la campaña de tarjetas de marketing"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_manager
msgid "Marketing Card Manager"
msgstr "Gestor de tarjetas de marketing"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_template
msgid "Marketing Card Template"
msgstr "Plantilla de tarjeta de marketing"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_user
msgid "Marketing Card User"
msgstr "Usuario de la tarjeta de marketing"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mailing_mailing
msgid "Mass Mailing"
msgstr "Correo masivo"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__res_model
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_model
msgid "Model Name"
msgstr "Nombre del modelo"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid ""
"Model of campaign %(campaign)s may not be changed as it already has cards"
msgstr ""
"No puede cambiar el modelo de la campaña %(campaign)s porque ya tiene "
"tarjetas"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "My Campaigns"
msgstr "Mis campañas"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__name
msgid "Name"
msgstr "Nombre"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente fecha límite de la actividad"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "No button"
msgstr "Sin botón"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url_click_count
msgid "Number of Clicks"
msgstr "Número de clics"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Odoo"
msgstr "Odoo"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma de traducción opcional (código ISO) a seleccionar para el envío de "
"correos electrónicos. Si no se selecciona esta opción, se utilizará la "
"versión en inglés. Por lo general, se usa una expresión de marcador de "
"posición para indicar el idioma adecuado, por ejemplo, {{ "
"object.partner_id.lang }}."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url
msgid "Post Link"
msgstr "Enlace de la publicación"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__post_suggestion
msgid "Post Suggestion"
msgstr "Publicación sugerida"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Powered By"
msgstr "Con la tecnología de"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid ""
"Prepare a design and some content and let your community spread the word!"
msgstr ""
"Elabore un diseño, redacte el contenido y deje que su comunidad comparta la "
"información. "

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview"
msgstr "Previsualizar"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__preview_record_ref
msgid "Preview On"
msgstr "Previsualizar en"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview on..."
msgstr "Previsualizar en..."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_color
msgid "Primary Color"
msgstr "Color primario"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_text_color
msgid "Primary Text Color"
msgstr "Color del texto primario"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "Profile Picture"
msgstr "Imagen de perfil"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipient Message"
msgstr "Mensaje del destinatario"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipients"
msgstr "Destinatarios"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "Modelo para los destinatarios"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_id
msgid "Record ID"
msgstr "ID de registro"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__render_model
msgid "Rendering Model"
msgstr "Modelo de visualización"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_title
msgid "Request"
msgstr "Solicitud"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_description
msgid "Request Description"
msgstr "Descripción de la solicitud"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__requires_sync
msgid "Requires Sync"
msgstr "Necesita sincronización"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__user_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Responsible"
msgstr "Responsable"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_target_url
msgid "Reward Link"
msgstr "Enlace de la recompensa"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error en el envío del SMS"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Search Card"
msgstr "Buscar tarjeta"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Search Share Campaign"
msgstr "Buscar campaña a compartir"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_color
msgid "Secondary Color"
msgstr "Color secundario"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_text_color
msgid "Secondary Text Color"
msgstr "Color del texto secundario"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section
msgid "Section"
msgstr "Sección"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_path
msgid "Section Path"
msgstr "Ruta de la sección"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Select a field"
msgstr "Seleccione un campo"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Select where to share"
msgstr "Seleccione en dónde lo compartirá"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Send"
msgstr "Enviar"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Send Cards"
msgstr "Enviar tarjetas"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Share Campaign"
msgstr "Compartir campaña"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_list
msgid "Share Card"
msgstr "Compartir tarjeta"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__share_status
msgid "Share Status"
msgstr "Compartir estado"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_template_view_form
msgid "Share Template"
msgstr "Compartir plantilla"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Share with your community!"
msgstr "Compártala con su comunidad"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__shared
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Shared"
msgstr "Compartido"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado según las actividades\n"
"Vencida: ya pasó la fecha límite\n"
"Hoy: hoy es la fecha de la actividad\n"
"Planeada: futuras actividades."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_color
msgid "Sub Header Color"
msgstr "Color del subencabezado"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header
msgid "Sub-Header"
msgstr "Subencabezado"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_path
msgid "Sub-Header Path"
msgstr "Ruta del subencabezado"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1
msgid "Sub-Section 1"
msgstr "Subsección 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_path
msgid "Sub-Section 1 Path"
msgstr "Ruta del subencabezado 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2
msgid "Sub-Section 2"
msgstr "Subsección 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_path
msgid "Sub-Section 2 Path"
msgstr "Ruta del subencabezado 2"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__tag_ids
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Tags"
msgstr "Etiquetas"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_campaign_tag_name_uniq
msgid "Tags may not reuse existing names."
msgstr "Las etiquetas no pueden reutilizar nombres existentes."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_message
msgid "Thank You Message"
msgstr "Mensaje de agradecimiento"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/utm_source.py:0
msgid ""
"The UTM source '%s' cannot be deleted as it is used to promote marketing "
"cards campaigns."
msgstr ""
"La fuente UTM %s no se puede eliminar porque se utiliza para promocionar las"
" campañas de las tarjetas de marketing. "

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de la actividad de excepción registrada."

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_utm_source
msgid "UTM Source"
msgstr "Fuente UTM"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update"
msgstr "Actualizar"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update Cards"
msgstr "Actualizar tarjetas"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__visited
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Visited"
msgstr "Visitada"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_card__requires_sync
msgid "Whether the image needs to be updated to match the campaign template."
msgstr ""
"Si es necesario actualizar la imagen para que coincida con la plantilla de "
"la campaña."

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid ""
"You should update all the cards for %(mailing)s before scheduling a mailing."
msgstr ""
"Debe actualizar todas las tarjetas de %(mailing)s antes de programar un "
"correo. "

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Your Home Page"
msgstr "Su página de inicio"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "button text"
msgstr "texto del botón"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"Thanks for sharing, here is your reward!\""
msgstr "Por ejemplo, \"Gracias por compartir, aquí está su recompensa\"."

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"https://www.mycompany.com/reward\""
msgstr "Por ejemplo, \"https://www.miempresa.com/recompensa\""

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Aug 24, Brussels Expo"
msgstr "Por ejemplo, 24 de agosto, Brussels Expo"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. By Lionel Messy"
msgstr "Por ejemplo, Por Lionel Messy"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. CFO Chief Football Officer"
msgstr "Por ejemplo, Director financiero"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Join Odoo Experience 2024"
msgstr "Por ejemplo, Asista a Odoo Experience 2024"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Odoo Experience Talks"
msgstr "Por ejemplo, Plática de Odoo Experience"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Sample Talk"
msgstr "Por ejemplo, Conferencia de ejemplo"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Why people should share on their network?"
msgstr "Por ejemplo, ¿Por qué la gente debería compartir esto en sus redes?"
