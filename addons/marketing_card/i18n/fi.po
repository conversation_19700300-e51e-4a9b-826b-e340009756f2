# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_card
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <kari.<PERSON><PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON> <miu<PERSON><EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Konsta Aavaranta, 2024
# <PERSON><PERSON> <mi<PERSON>.<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON>@obs-solutions.fi>, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# Jessica Jakara, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Jessica Jakara, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "%(card_campaign_name)s Mailings"
msgstr "%(card_campaign_name)s Postitukset"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Clicks\" role=\"img\" "
"title=\"Clicks\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-mouse-pointer\" aria-label=\"Klikkaukset\" "
"role=\"img\" title=\"Klikkaukset\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-share\" aria-label=\"Shares\" role=\"img\" title=\"Shares\"/>"
msgstr "<i class=\"fa fa-fw fa-share\" aria-label=\"Jaot\" role=\"img\" title=\"Jaot\"/>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "<small>Where does this link to?</small>"
msgstr "<small>Mihin tämä linkki johtaa?</small>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Cards\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Kortit\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Clicks\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Klikkaukset\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Mailings\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Postitukset\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Opened\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Avattu\n"
"                            </span>"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                Shared\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                Jaettu\n"
"                            </span>"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__active
#: model:ir.model.fields,field_description:marketing_card.field_card_card__active
msgid "Active"
msgstr "Aktiivinen"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_ids
msgid "Activities"
msgstr "Toimenpiteet"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Toimenpiteen poikkeuksen tyyli"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_state
msgid "Activity State"
msgstr "Toimenpiteen tila"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_icon
msgid "Activity Type Icon"
msgstr "Toimenpiteen ikoni"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Are you sure you want to update all cards of the campaign?"
msgstr "Oletko varma, että haluat päivittää kaikki kampanjan kortit?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_background
msgid "Background"
msgstr "Tausta"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__body_html
#: model:ir.model.fields,field_description:marketing_card.field_card_template__body
msgid "Body"
msgstr "Viesti"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_button
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
msgid "Button"
msgstr "Nappi"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Campaign"
msgstr "Kampanja"

#. module: marketing_card
#: model:ir.ui.menu,name:marketing_card.card_campaign_menu
msgid "Campaigns"
msgstr "Kampanjat"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.cards_card_action
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_ids
msgid "Card"
msgstr "Kortti"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_campaign_action
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_campaign_id
msgid "Card Campaign"
msgstr "Korttikampanja"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid "Card Campaign Mailing should target model %(model_name)s"
msgstr "Korttikampanjan postituksen tulisi kohdistua malliin %(model_name)s"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_click_count
msgid "Card Click Count"
msgstr "Kortin klikkauksen määrä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_count
msgid "Card Count"
msgstr "Korttien määrä"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Card Layout"
msgstr "Kortin asettelu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__card_requires_sync_count
msgid "Card Requires Sync Count"
msgstr "Synkronointia vaativien korttien määrä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_share_count
msgid "Card Share Count"
msgstr "Kortin osuuden määrä"

#. module: marketing_card
#: model:ir.actions.act_window,name:marketing_card.card_template_action
#: model:ir.ui.menu,name:marketing_card.cards_template_menu
msgid "Card Template"
msgstr "Kortin malli"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Cards"
msgstr "Kortit"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Click here for your reward!"
msgstr "Klikkaa tästä saadaksesi palkintosi!"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__color
msgid "Color"
msgstr "Väri"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.cards_card_action
msgid "Create a Card Campaign to send cards to your partners"
msgstr "Luo korttikampanja lähettääksesi kortteja yhteistyökumppaneillesi"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid "Create a Sharing Campaign!"
msgstr "Luo jakokampanja!"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_template_action
msgid "Create a design to use in Card Campaigns"
msgstr "Luo suunnittelu korttikampanjoissa käytettäväksi"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__create_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__create_date
msgid "Created on"
msgstr "Luotu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__default_background
msgid "Default Background"
msgstr "Oletustausta"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__post_suggestion
msgid "Description below the card and default text when sharing on X"
msgstr "Kortin alla oleva kuvaus ja oletusteksti, kun korttia jaetaan X:ssä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__card_template_id
msgid "Design"
msgstr "Suunnittelu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_card__display_name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Dynamic Field?"
msgstr "Dynaaminen kenttä?"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image1_path
msgid "Dynamic Image 1"
msgstr "Dynaaminen kuva 1"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_image2_path
msgid "Dynamic Image 2"
msgstr "Dynaaminen kuva 2"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_card_campaign_record_unique
msgid "Each record should be unique for a campaign"
msgstr "Jokaisen tietueen tulisi olla kampanjan osalta yksilöllinen"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Sähköpostin ohjattu koostaminen"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Filter By"
msgstr "Suodata"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome -ikoni esim.. fa-tasks"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header
msgid "Header"
msgstr "Ylätunniste"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_color
msgid "Header Color"
msgstr "Otsikon väri"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_path
msgid "Header Path"
msgstr "Otsikon polku"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Help us share the news"
msgstr "Auta meitä jakamaan uutisia"

#. module: marketing_card
#: model:ir.module.category,description:marketing_card.module_category_marketing_card
msgid "Helps you manage marketing card campaigns."
msgstr "Auttaa sinua hallinnoimaan markkinointikorttikampanjoita."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__id
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__id
#: model:ir.model.fields,field_description:marketing_card.field_card_card__id
#: model:ir.model.fields,field_description:marketing_card.field_card_template__id
msgid "ID"
msgstr "ID"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon"
msgstr "Kuvake"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kuvake joka kertoo poikkeustoiminnosta."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__image
msgid "Image"
msgstr "Kuva"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__image_preview
msgid "Image Preview"
msgstr "Kuvan esikatselu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_header_dyn
msgid "Is Dynamic Header"
msgstr "Onko dynaaminen otsikko"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_dyn
msgid "Is Dynamic Section"
msgstr "Onko dynaaminen jakso"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_dyn
msgid "Is Dynamic Sub-Header"
msgstr "Onko dynaaminen alaotsikko"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_dyn
msgid "Is Dynamic Sub-Section 1"
msgstr "Onko dynaaminen 1 osa-alue"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_dyn
msgid "Is Dynamic Sub-Section 2"
msgstr "Onko dynaaminen 2 alajakso"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Join me at this event!"
msgstr "Tule mukaan tähän tapahtumaan!"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__lang
msgid "Language"
msgstr "Kieli"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_uid
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_card__write_date
#: model:ir.model.fields,field_description:marketing_card.field_card_template__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__link_tracker_id
msgid "Link Tracker"
msgstr "Klikkiseuranta"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_ids
msgid "Mailing"
msgstr "Sähköpostitus"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__mailing_count
msgid "Mailing Count"
msgstr "Postituksen määrä"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_card
#: model:ir.module.category,name:marketing_card.module_category_marketing_card
#: model:ir.ui.menu,name:marketing_card.card_menu
#: model:ir.ui.menu,name:marketing_card.marketing_card_menu_technical
msgid "Marketing Card"
msgstr "Markkinointikortti"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign
msgid "Marketing Card Campaign"
msgstr "Markkinointikorttikampanja"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_campaign_tag
msgid "Marketing Card Campaign Tag"
msgstr "Markkinointikortin kampanjan tunniste"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_manager
msgid "Marketing Card Manager"
msgstr "Markkinointikorttipäällikkö"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_card_template
msgid "Marketing Card Template"
msgstr "Markkinointikortin malli"

#. module: marketing_card
#: model:res.groups,name:marketing_card.marketing_card_group_user
msgid "Marketing Card User"
msgstr "Markkinointikortin käyttäjä"

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_mailing_mailing
msgid "Mass Mailing"
msgstr "Massapostitus"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__res_model
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_model
msgid "Model Name"
msgstr "Mallin nimi"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid ""
"Model of campaign %(campaign)s may not be changed as it already has cards"
msgstr ""
"Kampanjan %(campaign)s mallia ei välttämättä muuteta, sillä se sisältää jo "
"kortteja"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Toimenpiteeni määräaika"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "My Campaigns"
msgstr "Omat kampanjat"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__name
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign_tag__name
#: model:ir.model.fields,field_description:marketing_card.field_card_template__name
msgid "Name"
msgstr "Nimi"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Seuraavan toimenpiteen kalenterimerkintä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Seuraavan toimenpiteen eräpäivä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_summary
msgid "Next Activity Summary"
msgstr "Seuraavan toimenpiteen kuvaus"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_type_id
msgid "Next Activity Type"
msgstr "Seuraavan toimenpiteen tyyppi"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "No button"
msgstr "Ei painiketta"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url_click_count
msgid "Number of Clicks"
msgstr "Klikkausten lukumäärä"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Odoo"
msgstr "Odoo"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Valinnainen käännöskieli (ISO-koodi), joka valitaan sähköpostia "
"lähetettäessä. Jos sitä ei ole asetettu, käytetään englanninkielistä "
"versiota. Tämän pitäisi yleensä olla sijoitusilmaus, joka antaa sopivan "
"kielen, esim. {{ object.partner_id.lang }}."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__target_url
msgid "Post Link"
msgstr "Artikkelin linkki"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__post_suggestion
msgid "Post Suggestion"
msgstr "Lähetä ehdotus"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Powered By"
msgstr "Moottorina"

#. module: marketing_card
#: model_terms:ir.actions.act_window,help:marketing_card.card_campaign_action
msgid ""
"Prepare a design and some content and let your community spread the word!"
msgstr "Valmistele ulkoasu ja sisältö ja anna yhteisösi levittää sanaa!"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview"
msgstr "Esikatselu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__preview_record_ref
msgid "Preview On"
msgstr "Esikatsele"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Preview on..."
msgstr "Esikatsele..."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_color
msgid "Primary Color"
msgstr "Pääväri"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__primary_text_color
msgid "Primary Text Color"
msgstr "Ensisijainen tekstin väri"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_1
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "Profile Picture"
msgstr "Profiilikuva"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__rating_ids
msgid "Ratings"
msgstr "Arviointi"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipient Message"
msgstr "Tervehdysteksti"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Recipients"
msgstr "Vastaanottajat"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_mailing_mailing__mailing_model_id
msgid "Recipients Model"
msgstr "Vastaanottajien mall"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__res_id
msgid "Record ID"
msgstr "Tietue ID"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__render_model
msgid "Rendering Model"
msgstr "Renderöintimalli"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_title
msgid "Request"
msgstr "Pyyntö"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__request_description
msgid "Request Description"
msgstr "Pyydä Kuvaus"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__requires_sync
msgid "Requires Sync"
msgstr "Vaatii synkronoinnin"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__user_id
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__activity_user_id
msgid "Responsible User"
msgstr "Vastuuhenkilö"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_target_url
msgid "Reward Link"
msgstr "Palkintolinkki"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Search Card"
msgstr "Hakukortin ehdot -alue"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Search Share Campaign"
msgstr "Etsi kampanjan jakoa"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_color
msgid "Secondary Color"
msgstr "Toissijainen väri"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_template__secondary_text_color
msgid "Secondary Text Color"
msgstr "Toissijainen tekstin väri"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section
msgid "Section"
msgstr "Osa"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_section_path
msgid "Section Path"
msgstr "Jakson polku"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Select a field"
msgstr "Valitse kenttä"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Select where to share"
msgstr "Valitse minne jaetaan"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Send"
msgstr "Lähetä"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/card_campaign.py:0
msgid "Send Cards"
msgstr "Lähetä kortteja"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Share Campaign"
msgstr "Jaa kampanja"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_list
msgid "Share Card"
msgstr "Jaa kortti"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_card__share_status
msgid "Share Status"
msgstr "Jaa tila"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_template_view_form
msgid "Share Template"
msgstr "Jaa malli"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_preview
msgid "Share with your community!"
msgstr "Jaa yhteisösi kanssa!"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__shared
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Shared"
msgstr "Jaettu"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tila aktiviteetin perusteella\n"
"Myöhässä: Eräpäivä on menneisyydessä\n"
"Tänään: Eräpäivä on tänään\n"
"Suunniteltu: Tulevaisuudessa."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_color
msgid "Sub Header Color"
msgstr "Alaotsikon väri"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header
msgid "Sub-Header"
msgstr "Alaotsikko"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_header_path
msgid "Sub-Header Path"
msgstr "Alaotsikon polku"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1
msgid "Sub-Section 1"
msgstr "1 alajakso"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section1_path
msgid "Sub-Section 1 Path"
msgstr "1 alajakso Polku"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2
msgid "Sub-Section 2"
msgstr "2 alajakso"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__content_sub_section2_path
msgid "Sub-Section 2 Path"
msgstr "2 alajakso Polku"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__tag_ids
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_search
msgid "Tags"
msgstr "Tunnisteet"

#. module: marketing_card
#: model:ir.model.constraint,message:marketing_card.constraint_card_campaign_tag_name_uniq
msgid "Tags may not reuse existing names."
msgstr "Tunnisteet eivät saa käyttää uudelleen olemassa olevia nimiä."

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__reward_message
msgid "Thank You Message"
msgstr "Kiitosviesti"

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/utm_source.py:0
msgid ""
"The UTM source '%s' cannot be deleted as it is used to promote marketing "
"cards campaigns."
msgstr ""
"UTM-lähdettä \"%s\" ei voi poistaa, koska sitä käytetään "
"markkinointikorttikampanjoiden edistämiseen."

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Poikkeusaktiviteetin tyyppi tietueella."

#. module: marketing_card
#: model:ir.model,name:marketing_card.model_utm_source
msgid "UTM Source"
msgstr "UTM-lähde"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update"
msgstr "Päivitä"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.mailing_mailing_view_form_inherit_marketing_card
msgid "Update Cards"
msgstr "Päivitä kortit"

#. module: marketing_card
#: model:ir.model.fields.selection,name:marketing_card.selection__card_card__share_status__visited
#: model_terms:ir.ui.view,arch_db:marketing_card.card_card_view_search
msgid "Visited"
msgstr "Vierailtu"

#. module: marketing_card
#: model:ir.model.fields,field_description:marketing_card.field_card_campaign__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_campaign__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: marketing_card
#: model:ir.model.fields,help:marketing_card.field_card_card__requires_sync
msgid "Whether the image needs to be updated to match the campaign template."
msgstr "Onko kuva päivitettävä vastaamaan kampanjamallia."

#. module: marketing_card
#. odoo-python
#: code:addons/marketing_card/models/mailing_mailing.py:0
msgid ""
"You should update all the cards for %(mailing)s before scheduling a mailing."
msgstr ""
"Sinun on päivitettävä kaikki %(mailing)s-kortit ennen postituksen "
"suunnittelua."

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "Your Home Page"
msgstr "Kotisivusi"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.template_2
#: model_terms:ir.ui.view,arch_db:marketing_card.template_3
#: model_terms:ir.ui.view,arch_db:marketing_card.template_4
#: model_terms:ir.ui.view,arch_db:marketing_card.template_5
msgid "button text"
msgstr "painikkeen teksti"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"Thanks for sharing, here is your reward!\""
msgstr "esim. \"Kiitos jakamisesta, tässä palkintosi!\""

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. \"https://www.mycompany.com/reward\""
msgstr "esim. \"https://www.mycompany.com/reward\""

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Aug 24, Brussels Expo"
msgstr "esim. 24. elokuuta, Brysselin messut"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. By Lionel Messy"
msgstr "esim. Lionel Messy"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. CFO Chief Football Officer"
msgstr "esim. CFO Chief Football Officer"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Join Odoo Experience 2024"
msgstr "esim. Liity Odoo Experience 2024 -tapahtumaan"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Odoo Experience Talks"
msgstr "esim. Odoo Experience Talks"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Sample Talk"
msgstr "esim. Näytepuhe"

#. module: marketing_card
#: model_terms:ir.ui.view,arch_db:marketing_card.card_campaign_view_form
msgid "e.g. Why people should share on their network?"
msgstr "esim. Miksi verkko tulisi jakaa?"
