# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: auth_totp_mail
#: model:mail.template,body_html:auth_totp_mail.mail_template_totp_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"        <t t-out=\"user.name  or ''\"/> requested you activate two-factor authentication to protect your account.<br/><br/>\n"
"        Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.\n"
"\n"
"        </p><p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Activate my two-factor Authentication\n"
"            </a>\n"
"        </p>\n"
"    \n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        عزيزي <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"       قام <t t-out=\"user.name  or ''\"/> بطلب تفعيلك للمصادقة ثنائية العوامل لحماية حسابك.<br/><br/>\n"
"        المصادقة ثنائية العوامل (\"2FA\") هي نظام للمصادقة المزدوجة.\n"
"        تتم المصادقة الأولى عن طريق كلمة السر والثانية عن طريق رمز تحصل عليه من خلال تطبيق على الهاتف المحمول مخصص لذلك.\n"
"        المفضلة منها تتضمن Authy، مصادقة Google، ومصادقة Microsoft.\n"
"\n"
"        </p><p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                تفعيل المصادقة ثنائية العوامل لدي\n"
"            </a>\n"
"        </p>\n"
"    \n"
"</div>\n"
"        "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "<span>Consider</span>"
msgstr "<span>ضع بعين الاعتبار</span>"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "A trusted device has just been added to your account: %(device_name)s"
msgstr "لقد تمت إضافة جهاز موثوق إلى حسابك للتو: %(device_name)s "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid ""
"A trusted device has just been removed from your account: %(device_names)s"
msgstr "لقد تمت إزالة جهاز موثوق من حسابك للتو: %(device_names)s "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Account Security"
msgstr "أمن الحساب "

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_auth_totp_device
msgid "Authentication Device"
msgstr "جهاز المصادقة "

#. module: auth_totp_mail
#: model:mail.template,subject:auth_totp_mail.mail_template_totp_invite
msgid "Invitation to activate two-factor authentication on your Odoo account"
msgstr "دعوة لتفعيل المصادقة ثنائية العوامل في حساب أودو الخاص بك "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid ""
"Invitation to use two-factor authentication sent for the following user(s): "
"%s"
msgstr ""
"تم إرسال دعوة لاستخدام المصادقة ثنائية العوامل إلى المستخدم (المستخدمين) "
"التاليين: %s"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.view_users_form
msgid "Invite to use 2FA"
msgstr "الدعوة إلى استخدام المصادقة ثنائية العوامل "

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_invite_totp
msgid "Invite to use two-factor authentication"
msgstr "الدعوة لاستخدام المصادقة ثنائية العوامل "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.res_users_view_form
msgid "Name"
msgstr "الاسم"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_activate_two_factor_authentication
msgid "Open two-factor authentication configuration"
msgstr "فتح تهيئة المصادقة ثنائية العوامل "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Security Update: 2FA Activated"
msgstr "تحديث الأمان: تم تفعيل المصادقة ثنائية العوامل "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Security Update: 2FA Deactivated"
msgstr "تحديث الأمان: تم تعطيل المصادقة ثنائية العوامل "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "Security Update: Device Added"
msgstr "تحديث الأمان: تمت إضافة جهاز "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "Security Update: Device Removed"
msgstr "تحديث الأمان: تمت إزالة جهاز "

#. module: auth_totp_mail
#: model:mail.template,name:auth_totp_mail.mail_template_totp_invite
msgid "Settings: 2Fa Invitation"
msgstr "الإعدادات: دعوة إلى إجراء المصادقة ثنائية العوامل "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Two-factor authentication has been activated on your account"
msgstr "تم تفعيل المصادقة ثنائية العوامل في حسابك "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Two-factor authentication has been deactivated on your account"
msgstr "تم تعطيل المصادقة ثنائية العوامل في حسابك "

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "activating Two-factor Authentication"
msgstr "جارِ تفعيل المصادقة ثنائية العوامل "
