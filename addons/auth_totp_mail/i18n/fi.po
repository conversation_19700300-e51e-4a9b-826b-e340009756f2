# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail
# 
# Translators:
# <PERSON><PERSON>ra <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_totp_mail
#: model:mail.template,body_html:auth_totp_mail.mail_template_totp_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"        <t t-out=\"user.name  or ''\"/> requested you activate two-factor authentication to protect your account.<br/><br/>\n"
"        Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.\n"
"\n"
"        </p><p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Activate my two-factor Authentication\n"
"            </a>\n"
"        </p>\n"
"    \n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hyvä <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"        <t t-out=\"user.name  or ''\"/> pyysi sinua aktivoimaan kaksivaiheisen tunnistuksen tilisi suojaamiseksi.<br/><br/>\n"
"        Kaksivaiheinen tunnistus (\"2FA\") on kaksinkertaisen todennuksen järjestelmä.\n"
"        Ensimmäinen tehdään salasanallasi ja toinen koodilla, jonka saat erityisestä mobiilisovelluksesta.\n"
"        Suosittuja ovat Authy, Google Authenticator tai Microsoft Authenticator.\n"
"\n"
"        </p><p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Aktivoi kaksivaiheinen tunnistus\n"
"            </a>\n"
"        </p>\n"
"    \n"
"</div>\n"
"        "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "<span>Consider</span>"
msgstr "<span>Harkitse</span>"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "A trusted device has just been added to your account: %(device_name)s"
msgstr "Tilillesi on juuri lisätty luotettu laite: %(device_name)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid ""
"A trusted device has just been removed from your account: %(device_names)s"
msgstr "Luotettu laite on juuri poistettu tililtäsi: %(device_names)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Account Security"
msgstr "Tilin tietoturva"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_auth_totp_device
msgid "Authentication Device"
msgstr "Vahvistuslaite"

#. module: auth_totp_mail
#: model:mail.template,subject:auth_totp_mail.mail_template_totp_invite
msgid "Invitation to activate two-factor authentication on your Odoo account"
msgstr "Kutsu kaksivaiheisen tunnistautumisen käyttöönottoon Odoo-tililläsi"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid ""
"Invitation to use two-factor authentication sent for the following user(s): "
"%s"
msgstr ""
"Kutsu kaksivaiheisen tunnistautumisen käyttöönottoon lähetettiin seuraaville"
" käyttäjille: %s"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.view_users_form
msgid "Invite to use 2FA"
msgstr "Kutsu käyttämään kaksivaiheista tunnistautumista"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_invite_totp
msgid "Invite to use two-factor authentication"
msgstr "Kutsu käyttämään kaksivaiheista tunnistautumista"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.res_users_view_form
msgid "Name"
msgstr "Nimi"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_activate_two_factor_authentication
msgid "Open two-factor authentication configuration"
msgstr "Avaa kaksivaiheisen tunnistautumisen asetukset"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Security Update: 2FA Activated"
msgstr "Tietoturvapäivitys: 2FA aktivoitu"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Security Update: 2FA Deactivated"
msgstr "Tietoturvapäivitys: 2FA poistettu käytöstä"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "Security Update: Device Added"
msgstr "Tietoturvapäivitys: laite lisätty"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "Security Update: Device Removed"
msgstr "Tietoturvapäivitys: laite poistettu"

#. module: auth_totp_mail
#: model:mail.template,name:auth_totp_mail.mail_template_totp_invite
msgid "Settings: 2Fa Invitation"
msgstr "Asetukset: Kaksivaiheinen tunnistautuminen"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Two-factor authentication has been activated on your account"
msgstr "Kaksivaiheinen tunnistus on aktivoitu tilillesi"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Two-factor authentication has been deactivated on your account"
msgstr "Kaksivaiheinen tunnistus on poistettu tililtäsi käytöstä"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_res_users
msgid "User"
msgstr "Käyttäjä"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "activating Two-factor Authentication"
msgstr "otetaan käyttöön kaksivaiheinen tunnistus"
