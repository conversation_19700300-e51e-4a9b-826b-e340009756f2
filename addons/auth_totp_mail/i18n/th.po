# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_totp_mail
#: model:mail.template,body_html:auth_totp_mail.mail_template_totp_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"        <t t-out=\"user.name  or ''\"/> requested you activate two-factor authentication to protect your account.<br/><br/>\n"
"        Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.\n"
"\n"
"        </p><p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Activate my two-factor Authentication\n"
"            </a>\n"
"        </p>\n"
"    \n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    \n"
"        เรียน <t t-out=\"object.partner_id.name or ''\"/><br/><br/>\n"
"        <t t-out=\"user.name  or ''\"/> ขอให้คุณเปิดใช้งานการตรวจสอบยืนยันสองขั้นตอนเพื่อปกป้องบัญชีของคุณ<br/><br/>\n"
"        การรับรองความถูกต้องแบบสองปัจจัย (\"2FA\") เป็นระบบการยืนยันตัวตนแบบคู่\n"
"        อันแรกใช้รหัสผ่านของคุณและอันที่สองใช้โค้ดที่คุณได้รับจากแอปมือถือเฉพาะ\n"
"        รายการยอดนิยม ได้แก่ Authy, Google Authenticator หรือ Microsoft Authenticator\n"
"\n"
"        <p style=\"margin: 0px; padding: 0px; font-size: 13px;\"><p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"               เปิดใช้งานการตรวจสอบยืนยันสองขั้นตอนของฉัน\n"
"            </a>\n"
"        </p>\n"
"    \n"
"</div>\n"
"        "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "<span>Consider</span>"
msgstr "<span>พิจารณา</span>"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "A trusted device has just been added to your account: %(device_name)s"
msgstr "อุปกรณ์ที่เชื่อถือได้ถูกเพิ่มลงในบัญชีของคุณแล้ว:%(device_name)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid ""
"A trusted device has just been removed from your account: %(device_names)s"
msgstr "อุปกรณ์ที่เชื่อถือได้ถูกลบออกจากบัญชีของคุณแล้ว:%(device_names)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Account Security"
msgstr "ความปลอดภัยของบัญชี"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_auth_totp_device
msgid "Authentication Device"
msgstr "อุปกรณ์ตรวจสอบสิทธิ์"

#. module: auth_totp_mail
#: model:mail.template,subject:auth_totp_mail.mail_template_totp_invite
msgid "Invitation to activate two-factor authentication on your Odoo account"
msgstr ""
"คำเชิญให้เปิดใช้งานการรับรองความถูกต้องแบบ two-factor ในบัญชี Odoo ของคุณ"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid ""
"Invitation to use two-factor authentication sent for the following user(s): "
"%s"
msgstr ""
"คำเชิญให้ใช้การรับรองความถูกต้องแบบ two-factor ที่ส่งถึงผู้ใช้ต่อไปนี้:%s"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.view_users_form
msgid "Invite to use 2FA"
msgstr "คำเชิญให้ใช้งาน 2FA"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_invite_totp
msgid "Invite to use two-factor authentication"
msgstr "เชิญให้ใช้การรับรองความถูกต้องด้วยแบบ two-factor "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.res_users_view_form
msgid "Name"
msgstr "ชื่อ"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_activate_two_factor_authentication
msgid "Open two-factor authentication configuration"
msgstr "เปิดการกำหนดค่าการรับรองความถูกต้องแบบ two-factor "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Security Update: 2FA Activated"
msgstr "อัปเดตความปลอดภัย: เปิดใช้งาน 2FA แล้ว"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Security Update: 2FA Deactivated"
msgstr "อัปเดตความปลอดภัย: ปิดใช้งาน 2FA แล้ว"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "Security Update: Device Added"
msgstr "อัปเดตความปลอดภัย: เพิ่มอุปกรณ์แล้ว"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
msgid "Security Update: Device Removed"
msgstr "อัปเดตความปลอดภัย: อุปกรณ์ถูกลบออกแล้ว"

#. module: auth_totp_mail
#: model:mail.template,name:auth_totp_mail.mail_template_totp_invite
msgid "Settings: 2Fa Invitation"
msgstr "การตั้งค่า: คำเชิญ 2Fa"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Two-factor authentication has been activated on your account"
msgstr "เปิดใช้งานการรับรองความถูกต้องด้วย Two-factor ในบัญชีของคุณแล้ว"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
msgid "Two-factor authentication has been deactivated on your account"
msgstr "ปิดใช้งานการรับรองความถูกต้องด้วย Two-factor ในบัญชีของคุณแล้ว"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_res_users
msgid "User"
msgstr "ผู้ใช้"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "activating Two-factor Authentication"
msgstr "การเปิดใช้งานการรับรองความถูกต้องด้วย Two-factor"
