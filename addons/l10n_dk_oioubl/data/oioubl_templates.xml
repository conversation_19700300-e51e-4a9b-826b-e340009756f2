<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="oioubl_PaymentTermsType" inherit_id="account_edi_ubl_cii.ubl_20_PaymentTermsType" primary="True">
        <xpath expr="//*[local-name()='Amount']" position="after">
            <t xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
               xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                <t t-set="settlement_period" t-value="vals['settlement_period']"/>
                <cac:SettlementPeriod>
                    <cbc:StartDate><t t-out="settlement_period['start_date']"/></cbc:StartDate>
                    <cbc:EndDate><t t-out="settlement_period['end_date']"/></cbc:EndDate>
                </cac:SettlementPeriod>
            </t>
        </xpath>
    </template>

    <template id="oioubl_PaymentMeansType" inherit_id="account_edi_ubl_cii.ubl_20_PaymentMeansType" primary="True">
        <xpath expr="//*[local-name()='PayeeFinancialAccount']//*[local-name()='FinancialInstitution']//*[local-name()='Address']" position="attributes">
            <attribute name="t-if">not invoice.partner_bank_id in supplier.bank_ids</attribute>
        </xpath>
    </template>
</odoo>
