<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="homeworking_own_rule" model="ir.rule">
            <field name="name">homeworking: own</field>
            <field name="model_id" ref="model_hr_employee_location"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="domain_force">[
                ('employee_id', '=', user.employee_id.id)
            ]</field>
        </record>

        <record id="homeworking_admin_rule" model="ir.rule">
            <field name="name">homeworking: admin</field>
            <field name="model_id" ref="model_hr_employee_location"/>
            <field name="groups" eval="[(4, ref('hr.group_hr_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>
    </data>
</odoo>
