# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_homeworking
# 
# Translators:
# <PERSON><PERSON><PERSON> moradi, 2025
# <PERSON>, 2025
# Naser mars, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Naser mars, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid ""
"<span class=\"text-muted fst-italic oe_inline\" colspan=\"2\">Specify your "
"default work location for each day of the week. This schedule will repeat "
"itself each week.</span>"
msgstr ""
"<span class=\"text-muted fst-italic oe_inline\" colspan=\"2\">موقعیت کاری "
"پیش‌فرض خود را برای هر روز هفته مشخص کنید. این برنامه هر هفته تکرار خواهد "
"شد.</span>"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_home
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_home
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_home
msgid "At Home"
msgstr "در خانه"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Idle"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Offline"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Online"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Present"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_office
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_office
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_office
msgid "At Office"
msgstr "در دفتر"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Idle"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Offline"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Online"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Present"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_other
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_other
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_other
msgid "At Other"
msgstr "در سایر موارد"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Other - Idle"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Other - Offline"
msgstr ""

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Other - Online"
msgstr ""

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_employee_base
msgid "Basic Employee"
msgstr "کارمند پایه"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_type
msgid "Cover Image"
msgstr "تصویر رویی"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__exceptional_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__exceptional_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__exceptional_location_id
msgid "Current"
msgstr "جاری"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__date
msgid "Date"
msgstr "تاریخ"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__day_week_string
msgid "Day Week String"
msgstr "رشته هفته روز"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__employee_id
msgid "Employee"
msgstr "کارمند"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_employee_location
msgid "Employee Location"
msgstr "موقعیت کارمند"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__employee_name
msgid "Employee Name"
msgstr "نام کارمند"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__friday_location_id
msgid "Friday"
msgstr "جمعه"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__hr_icon_display
msgid "Hr Icon Display"
msgstr "نمایش آیکون منابع انسانی"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__id
msgid "ID"
msgstr "شناسه"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_id
msgid "Location"
msgstr "مکان"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_name
msgid "Location name"
msgstr "نام مکان"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__monday_location_id
msgid "Monday"
msgstr "دوشنبه"

#. module: hr_homeworking
#: model:ir.model.constraint,message:hr_homeworking.constraint_hr_employee_location_uniq_exceptional_per_day
msgid ""
"Only one default work location and one exceptional work location per day per"
" employee."
msgstr ""
"فقط یک مکان کار پیش‌فرض و یک مکان کار استثنایی در هر روز برای هر کارمند."

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "Present"
msgstr "حاضر"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid "Remote Work"
msgstr "کار از راه دور"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__saturday_location_id
msgid "Saturday"
msgstr "شنبه"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__sunday_location_id
msgid "Sunday"
msgstr "1‌شنبه"

#. module: hr_homeworking
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee__exceptional_location_id
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee_base__exceptional_location_id
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee_public__exceptional_location_id
msgid "This is the exceptional, non-weekly, location set for today."
msgstr "این مکان استثنایی، غیر هفتگی، که برای امروز تنظیم شده است."

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__thursday_location_id
msgid "Thursday"
msgstr "5شنبه"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__today_location_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__today_location_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__today_location_name
msgid "Today Location Name"
msgstr "نام موقعیت امروز"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__tuesday_location_id
msgid "Tuesday"
msgstr "3‌شنبه"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/hr_presence_status/hr_presence_status.js:0
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid "Unspecified"
msgstr "نامشخص"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_res_users
msgid "User"
msgstr "کاربر"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__wednesday_location_id
msgid "Wednesday"
msgstr "4شنبه"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_work_location
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_tree
msgid "Work Location"
msgstr "مکان کاری"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_filter
msgid "Work location"
msgstr "محل کار"

#. module: hr_homeworking
#. odoo-python
#: code:addons/hr_homeworking/models/hr_work_location.py:0
msgid "You cannot delete locations that are being used by your employees"
msgstr ""
"شما نمی‌توانید مکان‌هایی که توسط کارکنانتان استفاده می‌شوند را حذف کنید"
