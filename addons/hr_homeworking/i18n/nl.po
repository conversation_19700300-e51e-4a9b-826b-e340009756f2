# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_homeworking
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid ""
"<span class=\"text-muted fst-italic oe_inline\" colspan=\"2\">Specify your "
"default work location for each day of the week. This schedule will repeat "
"itself each week.</span>"
msgstr ""
"<span class=\"text-muted fst-italic oe_inline\" colspan=\"2\">Bepaal je "
"standaard werklocatie voor elke dag van de week. Dit schema herhaalt zich "
"elke week.</span>"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_home
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_home
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_home
msgid "At Home"
msgstr "Thuis"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Idle"
msgstr "Thuis - Afwezig"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Offline"
msgstr "Thuis - Offline"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Online"
msgstr "Thuis - Online"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Home - Present"
msgstr "Thuis - Aanwezig"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_office
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_office
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_office
msgid "At Office"
msgstr "Op kantoor"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Idle"
msgstr "Op kantoor - Afwezig"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Offline"
msgstr "Op kantoor - Offline"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Online"
msgstr "Op kantoor - Online"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Office - Present"
msgstr "Op kantoor - Aanwezig"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee__hr_icon_display__presence_other
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_base__hr_icon_display__presence_other
#: model:ir.model.fields.selection,name:hr_homeworking.selection__hr_employee_public__hr_icon_display__presence_other
msgid "At Other"
msgstr "Andere"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Other - Idle"
msgstr "Elders - Afwezig"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Other - Offline"
msgstr "Elders - Offline"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/avatar_card_popover_patch.xml:0
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "At Other - Online"
msgstr "Elders - Online"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_employee_base
msgid "Basic Employee"
msgstr "Basis werknemer"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_type
msgid "Cover Image"
msgstr "Omslagafbeelding"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__exceptional_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__exceptional_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__exceptional_location_id
msgid "Current"
msgstr "Huidig"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__date
msgid "Date"
msgstr "Datum"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__day_week_string
msgid "Day Week String"
msgstr "Dag week reeks"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__employee_id
msgid "Employee"
msgstr "Werknemer"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_employee_location
msgid "Employee Location"
msgstr "Locatie werknemer"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__employee_name
msgid "Employee Name"
msgstr "Naam werknemer"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__friday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__friday_location_id
msgid "Friday"
msgstr "Vrijdag"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__hr_icon_display
msgid "Hr Icon Display"
msgstr "HR-pictogramweergave"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__id
msgid "ID"
msgstr "ID"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_id
msgid "Location"
msgstr "Locatie"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_location__work_location_name
msgid "Location name"
msgstr "Locatienaam"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__monday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__monday_location_id
msgid "Monday"
msgstr "Maandag"

#. module: hr_homeworking
#: model:ir.model.constraint,message:hr_homeworking.constraint_hr_employee_location_uniq_exceptional_per_day
msgid ""
"Only one default work location and one exceptional work location per day per"
" employee."
msgstr ""
"Slechts één standaard werklocatie en één uitzonderlijke werklocatie per dag "
"per werknemer."

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "Present"
msgstr "Aanwezig"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid "Remote Work"
msgstr "Op afstand werken"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__saturday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__saturday_location_id
msgid "Saturday"
msgstr "Zaterdag"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__sunday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__sunday_location_id
msgid "Sunday"
msgstr "Zondag"

#. module: hr_homeworking
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee__exceptional_location_id
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee_base__exceptional_location_id
#: model:ir.model.fields,help:hr_homeworking.field_hr_employee_public__exceptional_location_id
msgid "This is the exceptional, non-weekly, location set for today."
msgstr "Dit is de uitzonderlijke, niet-wekelijkse, locatie voor vandaag."

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__thursday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__thursday_location_id
msgid "Thursday"
msgstr "Donderdag"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__today_location_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__today_location_name
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__today_location_name
msgid "Today Location Name"
msgstr "Locatienaam vandaag"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__tuesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__tuesday_location_id
msgid "Tuesday"
msgstr "Dinsdag"

#. module: hr_homeworking
#. odoo-javascript
#: code:addons/hr_homeworking/static/src/components/hr_presence_status/hr_presence_status.js:0
#: model_terms:ir.ui.view,arch_db:hr_homeworking.res_useurs_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_form
msgid "Unspecified"
msgstr "Niet gespecificeerd"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_res_users
msgid "User"
msgstr "Gebruiker"

#. module: hr_homeworking
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_base__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_hr_employee_public__wednesday_location_id
#: model:ir.model.fields,field_description:hr_homeworking.field_res_users__wednesday_location_id
msgid "Wednesday"
msgstr "Woensdag"

#. module: hr_homeworking
#: model:ir.model,name:hr_homeworking.model_hr_work_location
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_tree
msgid "Work Location"
msgstr "Werklocatie"

#. module: hr_homeworking
#: model_terms:ir.ui.view,arch_db:hr_homeworking.view_employee_filter
msgid "Work location"
msgstr "Werklocatie"

#. module: hr_homeworking
#. odoo-python
#: code:addons/hr_homeworking/models/hr_work_location.py:0
msgid "You cannot delete locations that are being used by your employees"
msgstr ""
"Je kunt de locaties die door je werknemers worden gebruikt niet verwijderen"
