<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_employee_filter" model="ir.ui.view">
        <field name="name">view.employee.form.inherit.hr</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='group_department']" position="after">
                <separator/>
                <filter name="_search_today_location" string="Work location" domain="[]" context="{'group_by':'today_location_name'}"/>
            </xpath>
        </field>
    </record>
    <record id="view_employee_form" model="ir.ui.view">
        <field name="name">view.employee.form.inherit.hr</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_location_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//group[@name='departure']" position="after">
                <group string="Remote Work">
                    <span class="text-muted fst-italic oe_inline" colspan="2">Specify your default work location for each day of the week. This schedule will repeat itself each week.</span>
                    <field name="monday_location_id" placeholder="Unspecified" force_save="1"/>
                    <field name="tuesday_location_id" placeholder="Unspecified" force_save="1"/>
                    <field name="wednesday_location_id" placeholder="Unspecified" force_save="1"/>
                    <field name="thursday_location_id" placeholder="Unspecified" force_save="1"/>
                    <field name="friday_location_id" placeholder="Unspecified" force_save="1"/>
                    <field name="saturday_location_id" placeholder="Unspecified" force_save="1"/>
                    <field name="sunday_location_id" placeholder="Unspecified" force_save="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="view_employee_tree" model="ir.ui.view">
        <field name="name">hr.employee.list.timesheet</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_email']" position="after">
            <field name="work_location_name" readonly="0" string="Work Location" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='work_location_id']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>
        </field>
    </record>
</odoo>
