# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_custom
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid ""
"<small class=\"text-center text-wrap lh-sm\">Scan me in your banking "
"app</small>"
msgstr ""
"<small class=\"text-center text-wrap lh-sm\">Scannez-moi dans votre "
"application bancaire</small>"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "<strong class=\"mt-auto\">Communication: </strong>"
msgstr "<strong class=\"mt-auto\">Communication : </strong>"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Bank Account"
msgstr "Compte bancaire"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Bank Accounts"
msgstr "Comptes bancaires"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__code
msgid "Code"
msgstr "Code"

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__code__custom
msgid "Custom"
msgstr "Personnalisé"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "Mode personnalisé"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__qr_code
msgid "Enable QR Codes"
msgstr "Activer les codes QR"

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr ""
"Activez l'utilisation de codes QR lors du paiement par virement bancaire."

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "Finalize your payment"
msgstr "Finaliser votre paiement"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Aucune transaction ne correspond à la référence %s."

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "OR"
msgstr "OU"

#. module: payment_custom
#: model:ir.model.constraint,message:payment_custom.constraint_payment_provider_custom_providers_setup
msgid "Only custom providers should have a custom mode."
msgstr ""
"Seules les fournisseurs personnalisés doivent avoir un mode personnalisé."

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_provider
msgid "Payment Provider"
msgstr "Fournisseur de paiement"

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaction"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Please use the following transfer details"
msgstr ""
"Veuillez utiliser les informations suivantes pour le transfert bancaire."

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.payment_provider_form
msgid "Reload Pending Message"
msgstr "Recharger le message en attente"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
msgid "The customer has selected %(provider_name)s to make the payment."
msgstr "Le client a sélectionné %(provider_name)s pour effectuer le paiement."

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Le code technique de ce fournisseur de paiement."

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__custom_mode__wire_transfer
#: model:payment.method,name:payment_custom.payment_method_wire_transfer
msgid "Wire Transfer"
msgstr "Virement bancaire"
