# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_custom
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON>@obs-solutions.fi>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid ""
"<small class=\"text-center text-wrap lh-sm\">Scan me in your banking "
"app</small>"
msgstr ""
"<small class=\"text-center text-wrap lh-sm\">Skannaa minut "
"pankkisovelluksessasi</small>"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "<strong class=\"mt-auto\">Communication: </strong>"
msgstr "<strong class=\"mt-auto\">Viestintä: </strong>"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Bank Account"
msgstr "Pankkitili"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Bank Accounts"
msgstr "Pankkitilit"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__code
msgid "Code"
msgstr "Koodi"

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__code__custom
msgid "Custom"
msgstr "Mukautettu"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "Mukautettu tila"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__qr_code
msgid "Enable QR Codes"
msgstr "Ota QR-koodit käyttöön"

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr "Ota QR-koodien käyttö käyttöön tilisiirrolla maksettaessa."

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "Finalize your payment"
msgstr "Viimeistele maksusi"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Viitettä %s vastaavaa tapahtumaa ei löytynyt."

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "OR"
msgstr "TAI"

#. module: payment_custom
#: model:ir.model.constraint,message:payment_custom.constraint_payment_provider_custom_providers_setup
msgid "Only custom providers should have a custom mode."
msgstr "Vain mukautetuilla palveluntarjoajilla pitäisi olla mukautettu tila."

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_provider
msgid "Payment Provider"
msgstr "Maksupalveluntarjoaja"

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_transaction
msgid "Payment Transaction"
msgstr "Maksutapahtuma"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Please use the following transfer details"
msgstr "Käytä seuraavia siirtotietoja"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.payment_provider_form
msgid "Reload Pending Message"
msgstr "Lataa odottava viesti uudelleen"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
msgid "The customer has selected %(provider_name)s to make the payment."
msgstr "Asiakas on valinnut %(provider_name)s maksun suorittamiseksi."

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Tämän maksupalveluntarjoajan tekninen koodi."

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__custom_mode__wire_transfer
#: model:payment.method,name:payment_custom.payment_method_wire_transfer
msgid "Wire Transfer"
msgstr "Tilisiirto"
