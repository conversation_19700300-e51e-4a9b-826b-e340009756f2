# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_custom
# 
# Translators:
# Abe Manyo, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid ""
"<small class=\"text-center text-wrap lh-sm\">Scan me in your banking "
"app</small>"
msgstr ""
"<small class=\"text-center text-wrap lh-sm\">Scan saya di app banking "
"Anda</small>"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "<strong class=\"mt-auto\">Communication: </strong>"
msgstr "<strong class=\"mt-auto\">Komunikasi: </strong>"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Bank Account"
msgstr "Akun Bank"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Bank Accounts"
msgstr "Rekening Bank"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__code
msgid "Code"
msgstr "Kode"

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__code__custom
msgid "Custom"
msgstr "Khusus"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "Mode Kustom"

#. module: payment_custom
#: model:ir.model.fields,field_description:payment_custom.field_payment_provider__qr_code
msgid "Enable QR Codes"
msgstr "Aktifkan Kode QR"

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__qr_code
msgid "Enable the use of QR-codes when paying by wire transfer."
msgstr "Aktifkan penggunaan kode QR saat membayar melalui transfer rekening"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "Finalize your payment"
msgstr "Selesaikan pembayaran Anda"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Tidak ada transaksi dengan referensi %s yang cocok."

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.custom_state_header
msgid "OR"
msgstr "ATAU"

#. module: payment_custom
#: model:ir.model.constraint,message:payment_custom.constraint_payment_provider_custom_providers_setup
msgid "Only custom providers should have a custom mode."
msgstr "Hanya penyedia custom yang seharusnya memiliki mode custom."

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_provider
msgid "Payment Provider"
msgstr "Penyedia Pembayaran"

#. module: payment_custom
#: model:ir.model,name:payment_custom.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transaksi pembayaran"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_provider.py:0
msgid "Please use the following transfer details"
msgstr "Silakan gunakan detail transfer berikut"

#. module: payment_custom
#: model_terms:ir.ui.view,arch_db:payment_custom.payment_provider_form
msgid "Reload Pending Message"
msgstr "Muat Ulang Pesan Pending"

#. module: payment_custom
#. odoo-python
#: code:addons/payment_custom/models/payment_transaction.py:0
msgid "The customer has selected %(provider_name)s to make the payment."
msgstr "Pelanggan telah memilih %(provider_name)s untuk melakukan pembayaran."

#. module: payment_custom
#: model:ir.model.fields,help:payment_custom.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Kode teknis penyedia pembayaran ini."

#. module: payment_custom
#: model:ir.model.fields.selection,name:payment_custom.selection__payment_provider__custom_mode__wire_transfer
#: model:payment.method,name:payment_custom.payment_method_wire_transfer
msgid "Wire Transfer"
msgstr "Transfer rekening"
