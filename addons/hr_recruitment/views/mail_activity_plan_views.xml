<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="mail_activity_plan_action_config_hr_applicant" model="ir.actions.act_window">
            <field name="name">Recruitment Plans</field>
            <field name="res_model">mail.activity.plan</field>
            <field name="view_mode">list,kanban,form</field>
            <field name="search_view_id" ref="mail.mail_activity_plan_view_search"/>
            <field name="context">{'default_res_model': 'hr.applicant'}</field>
            <field name="domain">[('res_model', '=', 'hr.applicant')]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a Recruitment Activity Plan
                </p>
                <p>
                    Activity plans are used to assign a list of activities in just a few clicks
                    (e.g. "Language Test", "Prepare Offer", ...)
                </p>
            </field>
        </record>
    </data>
</odoo>
