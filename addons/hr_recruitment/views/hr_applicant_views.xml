<?xml version="1.0"?>
<odoo>
    <record model="ir.ui.view" id="crm_case_tree_view_job">
        <field name="name">Applicants</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <list string="Applicants" class="o_search_matching_applicant" multi_edit="1" sample="1" decoration-danger="application_status == 'refused'">
                <field name="message_needaction" column_invisible="True"/>
                <field name="last_stage_id" column_invisible="True"/>
                <field name="date_last_stage_update" column_invisible="True"/>
                <field name="partner_name" readonly="1" optional="show"/>
                <field name="create_date" readonly="1" optional="show"/>
                <field name="type_id" column_invisible="True"/>
                <field name="job_id" optional="show"/>
                <field name="stage_id" optional="show"/>
                <field name="candidate_id" optional="show"/>
                <field name="application_status" optional="hide" invisible="application_status == 'ongoing'"/>
                <field name="refuse_reason_id" optional='hide'/>
                <field name="activity_ids" widget="list_activity" optional="hide"/>
                <field name="activity_date_deadline" optional="hide"/>
                <field name="priority" widget="priority" optional="show"/>
                <field name="email_from" readonly="1" optional="hide"/>
                <field name="categ_ids" widget="many2many_tags" options="{'color_field': 'color'}" optional="show"/>
                <field name="user_id" widget="many2one_avatar_user" optional="show"/>
                <field name="interviewer_ids" widget="many2many_avatar_user" optional="hide"/>
                <field name="partner_phone" widget="phone" readonly="1" optional="hide"/>
                <field name="medium_id" optional="hide"/>
                <field name="source_id" readonly="1" optional="hide"/>
                <field name="salary_expected" optional="hide"/>
                <field name="salary_proposed" optional="hide"/>
                <field name="availability" optional="hide"/>
                <field name="department_id" readonly="1" column_invisible="context.get('invisible_department', True)" optional="hide"/>
                <field name="company_id" column_invisible="True"/>
                <field name="company_id" groups="base.group_multi_company" readonly="1" optional="hide"/>
            </list>
        </field>
    </record>

    <record id="hr_applicant_view_tree_activity" model="ir.ui.view">
        <field name="name">hr.applicant.view.list.activity</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <list string="Next Activities" decoration-danger="activity_date_deadline &lt; current_date" default_order="activity_date_deadline">
                <field name="partner_id"/>
                <field name="activity_date_deadline"/>
                <field name="activity_type_id"/>
                <field name="activity_summary"/>
                <field name="stage_id"/>
                <field name="activity_exception_decoration" widget="activity_exception"/>
            </list>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_applicant_view_form">
        <field name="name">Jobs - Recruitment Form</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
          <form string="Jobs - Recruitment Form" class="o_applicant_form">
            <field name="company_id" invisible="1"/>
            <field name="application_status" invisible="1"/>
            <field name="employee_id" invisible="1"/>
            <field name="meeting_ids" invisible="1"/>
            <field name="refuse_reason_id" invisible="1"/>
            <field name="email_normalized" invisible="1"/>
            <field name="partner_phone_sanitized" invisible="1"/>
            <field name="emp_is_active" invisible="1"/>
            <header>
                <button string="Create Employee" name="create_employee_from_applicant" type="object" data-hotkey="q" groups="hr.group_hr_user"
                        class="o_create_employee" invisible="employee_id or not active or not date_closed"/>
                <button string="Refuse" name="archive_applicant" type="object" invisible="not active" data-hotkey="d"/>
                <button string="Restore" name="toggle_active" type="object" invisible="active" data-hotkey="x"/>
                <field name="stage_id" widget="statusbar_duration" options="{'clickable': '1', 'fold_field': 'fold'}" invisible="not active and not employee_id"/>
            </header>
            <sheet>
                <div class="oe_button_box" name="button_box">
                    <button name="action_open_employee"
                            type="object"
                            class="oe_stat_button"
                            icon="fa-id-card-o"
                            groups="hr.group_hr_user"
                            invisible="not (employee_id or emp_is_active)">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value"><field name="employee_name" readonly="1"/></span>
                            <span class="o_stat_text">Employee</span>
                        </div>
                    </button>
                    <button name="action_open_other_applications"
                            class="oe_stat_button"
                            icon="fa-pencil"
                            type="object"
                            context="{'active_test': False}"
                            invisible="not other_applications_count">
                        <field name="other_applications_count" widget="statinfo" string="Other applications"/>
                    </button>
                    <button name="action_create_meeting" class="oe_stat_button" icon="fa-calendar" type="object" invisible="not id">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_text"><field name="meeting_display_text" /></span>
                            <span class="o_stat_value"><field name="meeting_display_date" readonly="1"/></span>
                        </div>
                    </button>
                </div>
                <widget name="web_ribbon" title="Refused" bg_color="text-bg-danger" invisible="application_status != 'refused'"/>
                <widget name="web_ribbon" title="Archived" bg_color="text-bg-secondary" invisible="application_status != 'archived'"/>
                <widget name="web_ribbon" title="Hired" invisible="application_status != 'hired'" />
                <field name="active" invisible="1"/>
                <field name="legend_normal" invisible="1"/>
                <field name="legend_blocked" invisible="1"/>
                <field name="legend_done" invisible="1"/>
                <field name="kanban_state" widget="state_selection" invisible="application_status == 'refused' or application_status == 'archived'"/>
                <field name="priority" widget="priority"/>
                <div class="oe_title pe-0">
                    <label for="candidate_id" class="oe_edit_only"/>
                    <h1 class="d-flex justify-content-between align-items-center">
                        <field name="candidate_id" options="{'line_breaks': False}" context="{'default_company_id': company_id}"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="partner_name" invisible="1"/>
                        <field name="partner_id" invisible="1" />
                        <field name="refuse_reason_id" invisible="active"/>
                        <field name="email_from" placeholder="e.g. <EMAIL>" widget="email"/>
                        <field name="partner_phone" widget="phone"/>
                        <field name="linkedin_profile" placeholder="e.g. https://www.linkedin.com/in/..." widget="url"/>
                    </group>
                    <group>
                        <field name="job_id"/>
                        <field name="user_id" widget="many2one_avatar_user" placeholder="Unassigned"/>
                        <field name="interviewer_ids" options="{'no_create': True, 'no_create_edit': True}" widget="many2many_avatar_user" placeholder="Who can access candidates" />
                        <field name="date_closed" invisible="not date_closed" />
                        <field name="categ_ids" placeholder="Tags" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"/>
                    </group>
                </group>
                <field name="applicant_properties" columns="2"/>
                <notebook>
                    <page string="Note" name="application_note">
                        <field name="applicant_notes" type="html" options="{'collaborative': true, 'resizable': false}" placeholder="Internal notes..."/>
                    </page>
                    <page string="Details" name="application_details">
                        <group>
                            <group string="Applicant">
                                <field name="type_id" placeholder="e.g. Masters"/>
                                <field name="availability" placeholder="Directly"/>
                            </group>
                            <group string="Job">
                                <field name="department_id"/>
                                <field name="company_id" groups="base.group_multi_company" options='{"no_open":True}' />
                            </group>
                        </group>
                        <group>
                            <group string="Salary Package" name="recruitment_contract" groups="hr_recruitment.group_hr_recruitment_user">
                                <label for="salary_expected"/>
                                <div class="o_row">
                                    <field name="salary_expected" placeholder="Expected"/>
                                    <span invisible="not salary_expected_extra"> + </span>
                                    <field name="salary_expected_extra" placeholder="Other benefits"/>
                                </div>
                                <label for="salary_proposed"/>
                                <div class="o_row">
                                    <field name="salary_proposed"/>
                                    <span invisible="not salary_proposed_extra"> + </span>
                                    <field name="salary_proposed_extra" placeholder="Other benefits"/>
                                </div>
                            </group>
                            <group string="Sourcing">
                                <field name="source_id"/>
                                <field name="medium_id"/>
                            </group>
                        </group>
                    </page>
                </notebook>
            </sheet>
            <div class="o_attachment_preview" groups="hr_recruitment.group_applicant_cv_display"/>
            <chatter open_attachments="True" reload_on_attachment="True"/>
          </form>
        </field>
    </record>

    <record id="hr_applicant_view_form_interviewer" model="ir.ui.view">
        <field name="model">hr.applicant</field>
        <field name="inherit_id" ref="hr_applicant_view_form"/>
        <field name="priority">50</field>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//form" position="attributes">
                <attribute name="js_class">hr_recruitment_interviewer</attribute>
                <attribute name="class">o_applicant_form</attribute>
            </xpath>
            <xpath expr="//group[@name='recruitment_contract']" position="replace"/>
        </field>
    </record>

    <record model="ir.ui.view" id="crm_case_pivot_view_job">
        <field name="name">Jobs - Recruitment</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <pivot string="Job Applications" sample="1">
                <field name="create_date" type="row"/>
                <field name="stage_id" type="col"/>
                <field name="color" invisible="1"/>
            </pivot>
        </field>
    </record>

    <record model="ir.ui.view" id="crm_case_graph_view_job">
        <field name="name">Jobs - Recruitment Graph</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <graph string="Cases By Stage and Estimates" sample="1">
                <field name="stage_id"/>
            </graph>
        </field>
    </record>

    <record id="hr_applicant_view_search_bis" model="ir.ui.view">
        <field name="name">hr.applicant.view.search</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <search string="Search Applicants">
                <field string="Applicant" name="partner_name"
                    filter_domain="['|', ('partner_name', 'ilike', self), ('email_from', 'ilike', self)]"/>
                <field string="Email" name="email_from" filter_domain="[('email_from', 'ilike', self)]"/>
                <field name="job_id"/>
                <field name="department_id" operator="child_of"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="user_id"/>
                <field name="stage_id" domain="[]"/>
                <field name="categ_ids"/>
                <field name="refuse_reason_id"/>
                <field name="application_status"/>
                <field name="date_closed"/>
                <field name="attachment_ids" filter_domain="[('attachment_ids', 'any', [('index_content', 'ilike', self), ('res_model', '=', 'hr.applicant')])]" string="Resume's content"/>
                <filter string="My Applications" name="my_applications" domain="[('user_id', '=', uid)]"/>
                <filter string="Unassigned" name="unassigned" domain="[('user_id', '=', False)]"/>
                <separator/>
                <filter string="In Progress" name="ongoing" domain="[('date_closed', '=', False), ('active', '=', True), ('refuse_reason_id', '=', False)]"/>
                <filter string="Hired" name="hired" domain="[('date_closed', '!=', False)]"/>
                <separator/>
                <filter string="Ready for Next Stage" name="done" domain="[('kanban_state', '=', 'done')]"/>
                <filter string="Blocked" name="blocked" domain="[('kanban_state', '=', 'blocked')]"/>
                <separator/>
                <filter string="Directly Available" name="applicant_availability" domain="['|',('availability', '&lt;=', context_today().strftime('%Y-%m-%d')),('availability','=', False)]"/>
                <separator/>
                <filter string="Creation Date" name="filter_create" date="create_date"/>
                <filter string="Last Stage Update" name="filter_date_last_stage_update" date="date_last_stage_update"/>
                <separator/>
                <filter string="Unread Messages" name="message_needaction" domain="[('message_needaction', '=', True)]" groups="mail.group_mail_notification_type_inbox"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False), ('refuse_reason_id', '=', False)]"/>
                <filter string="Refused" name="refused" domain="[('active', '=', False), ('refuse_reason_id', '!=', False)]"/>
                <separator/>
                <filter invisible="1" string="Late Activities" name="activities_overdue"
                    domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                    help="Show all records which has next action date is before today"/>
                <filter invisible="1" string="Today Activities" name="activities_today"
                    domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                    domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                <separator/>
                <filter invisible="1" string="Running Applicants" name="running_applicant_activities"
                    domain="[('stage_id.hired_stage', '=', False)]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Job" name="job" domain="[]" context="{'group_by': 'job_id'}"/>
                    <filter string="Stage" name="stage" domain="[]" context="{'group_by': 'stage_id'}"/>
                    <filter string="Candidate" name="candidate" domain="[]" context="{'group_by': 'candidate_id'}"/>
                    <filter string="Responsible" name="responsible" domain="[]"  context="{'group_by': 'user_id'}"/>
                    <filter string="Creation Date" name="creation_date" context="{'group_by': 'create_date'}"/>
                    <filter string="Hiring Date" name="hired_date" context="{'group_by': 'date_closed'}"/>
                    <filter string="Last Stage Update" name="last_stage_update" context="{'group_by': 'date_last_stage_update'}"/>
                    <filter string="Refuse Reason" name="refuse_reason_id" domain="[]" context="{'group_by': 'refuse_reason_id'}"/>
                    <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                </group>
           </search>
        </field>
    </record>

    <record model="ir.ui.view" id="hr_applicant_calendar_view">
        <field name="name">Hr Applicants Calendar</field>
        <field name="model">hr.applicant</field>
        <field name="priority" eval="2"/>
        <field name="arch" type="xml">
            <calendar
                string="Applicants"
                mode="month"
                date_start="activity_date_deadline"
                color="user_id"
                event_limit="5"
                hide_time="true"
                quick_create="0"
            >
                <field name="partner_name"/>
                <field name="job_id"/>
                <field name="priority" widget="priority"/>
                <field name="user_id" filters="1" invisible="1"/>
                <field name="activity_summary"/>
            </calendar>
        </field>
    </record>

    <record id="quick_create_applicant_form" model="ir.ui.view">
        <field name="name">hr.applicant.form.quick_create</field>
        <field name="model">hr.applicant</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="candidate_id" options="{'no_create_edit': True}" placeholder="e.g. John Doe"/>
                    <field name="job_id" options="{'no_open': True}"/>
                    <field name="company_id" invisible="1"/>
                </group>
            </form>
        </field>
    </record>

    <!-- Hr Applicant Kanban View -->
    <record model="ir.ui.view" id="hr_kanban_view_applicant">
        <field name="name">Hr Applicants kanban</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <kanban highlight_color="color" default_group_by="stage_id" class="o_kanban_applicant o_search_matching_applicant" quick_create_view="hr_recruitment.quick_create_applicant_form" sample="1">
                <field name="stage_id" options='{"group_by_tooltip": {"requirements": "Requirements"}}'/>
                <field name="legend_normal"/>
                <field name="legend_blocked"/>
                <field name="legend_done"/>
                <field name="date_closed"/>
                <field name="color"/>
                <field name="user_id"/>
                <field name="active"/>
                <field name="application_status" />
                <field name="company_id" invisible="1"/> <!-- We need to keep this field as it is used in the domain of user_id in the model -->
                <progressbar field="kanban_state" colors='{"done": "success", "blocked": "danger"}'/>
                <templates>
                    <t t-name="menu">
                        <a role="menuitem" name="action_create_meeting" type="object" class="dropdown-item">Schedule Interview</a>
                        <a role="menuitem" name="archive_applicant" type="object" class="dropdown-item">Refuse</a>
                        <a t-if="record.active.raw_value" role="menuitem" type="archive" class="dropdown-item">Archive</a>
                        <a t-if="!record.active.raw_value" role="menuitem" type="unarchive" class="dropdown-item">Unarchive</a>
                        <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                    </t>
                    <t t-name="card">
                        <widget name="web_ribbon" title="Hired" bg_color="text-bg-success" invisible="not date_closed"/>
                        <widget name="web_ribbon" title="Refused" bg_color="text-bg-danger" invisible="application_status != 'refused'"/>
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-secondary" invisible="application_status != 'archived'"/>
                        <field t-if="record.partner_name.raw_value" class="fw-bold fs-5" name="partner_name"/>
                        <field name="job_id" invisible="context.get('search_default_job_id', False)"/>
                        <field name="categ_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
                        <field name="applicant_properties" widget="properties"/>
                        <footer>
                            <field name="priority" widget="priority"/>
                            <field class="ms-1 align-items-center" name="activity_ids" widget="kanban_activity"/>
                            <div class="d-flex ms-auto align-items-center">
                                <a name="action_open_attachments" type="object">
                                    <i class='fa fa-paperclip' role="img" aria-label="Documents"/>
                                    <field name="attachment_number"/>
                                </a>
                                <field name="kanban_state" class="mx-1" widget="state_selection"/>
                                <field name="user_id" widget="many2one_avatar_user"/>
                            </div>
                        </footer>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_applicant_view_activity" model="ir.ui.view">
        <field name="name">hr.applicant.activity</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <activity string="Applicants">
                <templates>
                    <div t-name="activity-box">
                        <field name="user_id" widget="many2one_avatar_user"/>
                        <div class="flex-grow-1">
                            <field name="partner_name" muted="1" display="full" class="o_text_block"/>
                        </div>
                    </div>
                </templates>
            </activity>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_hr_job_applications">
        <field name="name">Applications</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">kanban,list,form,graph,calendar,pivot,activity</field>
        <field name="search_view_id" ref="hr_applicant_view_search_bis"/>
        <field name="context">{'search_default_job_id': [active_id], 'default_job_id': active_id, 'search_default_stage':1, 'dialog_size':'medium', 'allow_search_matching_applicants': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No applications yet
            </p><p>
                Odoo helps you track applicants in the recruitment
                process and follow up all operations: meetings, interviews, etc.
            </p><p>
                Applicants and their attached résumé are created automatically when an email is sent.
                If you install the document management modules, all resumes are indexed automatically,
                so that you can easily search through their content.
            </p>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_hr_applicant_new">
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">form</field>
        <field name="context">{'default_job_id': active_id}</field>
    </record>

    <!-- Job Opportunities (menu) -->
    <record model="ir.actions.act_window" id="crm_case_categ0_act_job">
        <field name="name">Applications</field>
        <field name="res_model">hr.applicant</field>
        <field name="path">recruitment-applications</field>
        <field name="view_mode">kanban,list,form,pivot,graph,calendar,activity</field>
        <field name="view_id" eval="False"/>
        <field name="search_view_id" ref="hr_applicant_view_search_bis"/>
        <field name="context">{'search_default_stage':1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No applications yet
            </p><p>
                Odoo helps you track applicants in the recruitment
                process and follow up all operations: meetings, interviews, etc.
            </p><p>
                Applicants and their attached résumé are created automatically when an email is sent.
                If you install the document management modules, all resumes are indexed automatically,
                so that you can easily search through their content.
            </p>
        </field>
    </record>

    <record id="hr_applicant_action_from_department" model="ir.actions.act_window">
        <field name="name">New Applications</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">list,kanban,form,graph,calendar,pivot</field>
        <field name="context">{
            'search_default_department_id': active_id,
            'default_department_id': active_id,
            'invisible_department': False}
        </field>
        <field name="domain">[('stage_id.sequence','&lt;=','1')]</field>
    </record>

    <record model="ir.actions.act_window.view" id="action_hr_sec_kanban_view_act_job">
        <field name="sequence" eval="1"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="hr_kanban_view_applicant"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record model="ir.actions.act_window.view" id="action_hr_sec_tree_view_act_job">
        <field name="sequence" eval="0"/>
        <field name="view_mode">list</field>
        <field name="view_id" ref="crm_case_tree_view_job"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record model="ir.actions.act_window.view" id="action_hr_sec_form_view_act_job">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="hr_applicant_view_form"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record  id="hr_applicant_action_view_pivot" model="ir.actions.act_window.view">
        <field name="sequence" eval="3"/>
        <field name="view_mode">pivot</field>
        <field name="view_id" ref="crm_case_pivot_view_job"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record id="action_hr_sec_graph_view_act_job" model="ir.actions.act_window.view">
        <field name="sequence" eval="4"/>
        <field name="view_mode">graph</field>
        <field name="view_id" ref="crm_case_graph_view_job"/>
        <field name="act_window_id" ref="crm_case_categ0_act_job"/>
    </record>

    <record id="hr_applicant_view_pivot" model="ir.ui.view">
        <field name="name">hr.applicant.pivot</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <pivot string="Recruitment Analysis" sample="1">
                <field name="stage_id" type="row"/>
                <field name="job_id" type="col"/>
                <field name="candidate_id"/>
            </pivot>
        </field>
    </record>

    <record id="hr_applicant_view_graph" model="ir.ui.view">
        <field name="name">hr.applicant.graph</field>
        <field name="model">hr.applicant</field>
        <field name="arch" type="xml">
            <graph string="Recruitment Analysis" sample="1">
                <field name="stage_id"/>
                <field name="job_id"/>
            </graph>
        </field>
    </record>

    <record id="hr_applicant_view_search" model="ir.ui.view">
        <field name="name">hr.applicant.search</field>
        <field name="model">hr.applicant</field>
        <field name="priority">32</field>
        <field name="arch" type="xml">
            <search string="Recruitment Analysis">
                <field name="job_id"/>
                <field name="department_id" operator="child_of"/>
                <field name="user_id"/>
                <filter string="Creation Date" name="year" date="create_date" default_period="year"/>
                <separator/>
                <filter string="Unassigned" name="unassigned" domain="[('user_id', '=', False)]"/>
                <separator/>
                <filter string="New" name="new" domain="[('stage_id.sequence', '=', 1)]"/>
                <separator/>
                <filter string="Ongoing" name="ongoing" domain="[('active', '=', True)]"/>
                <filter string="Refused" name="refused" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Archived" name="archived" domain="[('active', '=', False)]"/>
                <separator/>
                <group expand="0" string="Extended Filters">
                    <field name="priority"/>
                    <field name="stage_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="create_date"/>
                    <field name="date_closed"/>
                </group>
                <group expand="1" string="Group By">
                    <filter string="Responsible" name='User' context="{'group_by':'user_id'}"/>
                    <filter string="Company" name="company" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                    <filter string="Jobs" name="job" context="{'group_by':'job_id'}"/>
                    <filter string="Department" name="department" context="{'group_by':'department_id'}"/>
                    <filter string="Tags" name="group_by_categ_ids" context="{'group_by':'categ_ids'}"/>
                    <filter string="Stage" name="stage" context="{'group_by':'stage_id'}" />
                    <separator/>
                    <filter string="Creation Date" name="creation_month" context="{'group_by':'create_date:month'}" help="Creation Date"/>
                </group>
            </search>
        </field>
    </record>

    <record id="hr_applicant_action_analysis" model="ir.actions.act_window">
        <field name="name">Recruitment Analysis</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_applicant_view_search"/>
        <field name="view_ids" eval="[
            (5, 0, 0),
            (0, 0, {'view_mode': 'graph', 'view_id': ref('hr_applicant_view_graph')}),
            (0, 0, {'view_mode': 'pivot', 'view_id': ref('hr_applicant_view_pivot')})]"/>
        <field name="context">{'search_default_creation_month': 1, 'search_default_job': 2}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>

    <record id="action_hr_recruitment_report_filtered_department" model="ir.actions.act_window">
        <field name="name">Recruitment Analysis</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_applicant_view_search"/>
        <field name="context">{
            'search_default_department_id': [active_id],
            'default_department_id': active_id}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>

    <record id="action_hr_recruitment_report_filtered_job" model="ir.actions.act_window">
        <field name="name">Recruitment Analysis</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="hr_applicant_view_search"/>
        <field name="context">{
            'search_default_creation_month': 1,
            'search_default_job_id': [active_id],
            'default_job_id': active_id}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data yet!
            </p>
        </field>
    </record>

    <record id="action_applicant_send_mail" model="ir.actions.server">
        <field name="name">Send Email</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="binding_model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = records.action_send_email()</field>
    </record>

    <record id="ir_actions_server_refuse_applicant" model="ir.actions.server">
        <field name="name">Refuse</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="binding_model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="binding_view_types">list,form</field>
        <field name="state">code</field>
        <field name="code">
if records:
    action = records.archive_applicant()
        </field>
    </record>

    <!-- Custom reports (aka filters) -->
    <record id="hr_applicant_filter_recruiter" model="ir.filters">
        <field name="name">By Recruiter</field>
        <field name="model_id">hr.applicant</field>
        <field name="user_id" eval="False"/>
        <field name="action_id" ref="hr_applicant_action_analysis"/>
        <field name="context">{'group_by': ['create_date:month', 'user_id']}</field>
    </record>
    <record id="hr_applicant_filter_job" model="ir.filters">
        <field name="name">By Job</field>
        <field name="model_id">hr.applicant</field>
        <field name="user_id" eval="False"/>
        <field name="action_id" ref="hr_applicant_action_analysis"/>
        <field name="context">{'group_by': ['create_date:month', 'job_id']}</field>
    </record>
    <record id="hr_applicant_filter_department" model="ir.filters">
        <field name="name">By Department</field>
        <field name="model_id">hr.applicant</field>
        <field name="user_id" eval="False"/>
        <field name="action_id" ref="hr_applicant_action_analysis"/>
        <field name="context">{'group_by': ['create_date:month', 'department_id']}</field>
    </record>
</odoo>
