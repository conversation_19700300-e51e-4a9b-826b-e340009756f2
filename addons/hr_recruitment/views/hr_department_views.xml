<odoo>
    <!--Hr Department Inherit Kanban view-->
    <record id="hr_department_view_kanban" model="ir.ui.view">
        <field name="name">hr.department.kanban.inherit</field>
        <field name="model">hr.department</field>
        <field name="inherit_id" ref="hr.hr_department_view_kanban"/>
        <field name="arch" type="xml">
            <data>
                <xpath expr="//div[@name='kanban_primary_right']" position="inside">
                    <div t-if="record.new_applicant_count.raw_value > 0" class="row g-0 ml32" groups="hr_recruitment.group_hr_recruitment_user">
                        <a name="%(hr_applicant_action_from_department)d" class="col" type="action">
                            <field name="new_applicant_count"/> New Applicants
                        </a>
                    </div>
                </xpath>

                <xpath expr="//div[hasclass('o_kanban_manage_reports')]" position="inside">
                    <div role="menuitem">
                        <a class="dropdown-item" name="%(action_hr_recruitment_report_filtered_department)d"
                            type="action" groups="hr_recruitment.group_hr_recruitment_interviewer">
                            Recruitment
                        </a>
                    </div>
                </xpath>
            </data>
        </field>
    </record>

    <record id="action_hr_department" model="ir.actions.act_window">
        <field name="name">Departments</field>
        <field name="res_model">hr.department</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>
