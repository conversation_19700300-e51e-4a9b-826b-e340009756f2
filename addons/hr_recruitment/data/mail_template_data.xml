<?xml version="1.0"?>
<odoo><data noupdate="1">

    <!-- Templates for interest / refusing applicants -->
    <record id="email_template_data_applicant_refuse" model="mail.template">
        <field name="name">Recruitment: Refuse</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="subject">Your Job Application: {{ object.job_id.name }}</field>
        <field name="email_to">{{ (not object.partner_id and object.email_from or '') }}</field>
        <field name="partner_to">{{ object.partner_id.id or '' }}</field>
        <field name="description">When you refuse an application, you can choose this template</field>
        <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" width="590" style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
    <tr>
        <td valign="top">
            <div style="font-size: 13px; margin: 0px; padding: 0px;">
                Hello,<br/><br/>
                Thank you for your interest in joining the
                <b><t t-out="object.company_id.name or ''">YourCompany</t></b> team.  We
                wanted to let you know that, although your resume is
                competitive, our hiring team reviewed your application
                and <b>did not select it for further consideration</b>.
                <br/><br/>
                Please note that recruiting is hard, and we can make
                mistakes. Do not hesitate to reply to this email if you
                think we made a mistake, or if you want more information
                about our decision.
                <br/><br/>
                We will, however, keep your resume on record and get in
                touch with you about future opportunities that may be a
                better fit for your skills and experience.
                <br/><br/>
                We wish you all the best in your job search and hope we
                will have the chance to consider you for another role
                in the future.
                <br/><br/>
                Thank you,
                <div style="font-size: 11px; color: grey;">
                    <t t-if="object.user_id">
                        -- <br/>
                        <strong t-out="object.user_id.name or ''">Mitchell Admin</strong><br/>
                        Email: <t t-out="object.user_id.email or ''"><EMAIL></t><br/>
                        Phone: <t t-out="object.user_id.phone or ''">******-123-4567</t>
                    </t>
                    <t t-else="">
                        -- <br/>
                        <t t-out="object.company_id.name or ''">YourCompany</t><br/>
                        The HR Team
                    </t>
                </div>
            </div>
        </td>
    </tr>
</table>
        </field>
        <field name="auto_delete" eval="True"/>
        <field name="lang">{{ object.partner_id.lang or '' }}</field>
    </record>

    <record id="email_template_data_applicant_interest" model="mail.template">
        <field name="name">Recruitment: Interest</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="subject">Your Job Application: {{ object.job_id.name }}</field>
        <field name="email_to">{{ (not object.partner_id and object.email_from or '') }}</field>
        <field name="partner_to">{{ object.partner_id.id or '' }}</field>
        <field name="description">Set this template to a recruitment stage to send it when applications reach that stage</field>
        <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" width="590" style="background-color: white; border-collapse: collapse; margin-left: 20px;">
    <tr>
        <td valign="top" style="padding: 0px 10px;">
            <div style="text-align: center">
                <h2>Congratulations!</h2>
                <div style="color:grey;">Your resume has been positively reviewed.</div>
            </div>
            <div style="font-size: 13px; margin: 0px; padding: 0px;">
                We just reviewed your resume, and it caught our
                attention. As we think you might be great for the
                position, your application has been short listed for a
                call or an interview.
                <br/><br/>
                <div t-if="'website_url' in object.job_id and object.job_id.website_url" style="padding: 16px 8px 16px 8px;">
                    <a t-att-href="object.job_id.website_url"
                        style="background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;">Job Description</a>
                </div>

                <t t-if="object.user_id">
                    You will soon be contacted by:<br/>
                    <strong t-out="object.user_id.name or ''">Mitchell Admin</strong><br/>
                    <span>Email: <t t-out="object.user_id.email or ''"><EMAIL></t></span><br/>
                    <span>Phone: <t t-out="object.user_id.phone or ''">******-123-4567</t></span>
                    <br/><br/>
                </t>
                See you soon,
                <div style="font-size: 11px; color: grey;">
                    -- <br/>
                    The HR Team
                    <t t-if="'website_url' in object.job_id and hasattr(object.job_id, 'website_url') and object.job_id.website_url">
                        Discover <a href="/jobs" style="text-decoration:none;color:#717188;">all our jobs</a>.<br/>
                    </t>
                </div>

                <hr width="97%" style="background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;"/>
                <h3 style="color:#9A6C8E;"><strong>What is the next step?</strong></h3>
                We usually <strong>answer applications within a few days</strong>.
                <br/><br/>
                The next step is either a call or a meeting in our offices.
                <br/>
                Feel free to <strong>contact us if you want a faster
                feedback</strong> or if you don't get news from us
                quickly enough (just reply to this email).
                <br/>

                <hr width="97%" style="background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;"/>
                <t t-set="location" t-value="''"/>
                <t t-if="object.job_id.address_id.name">
                    <strong t-out="object.job_id.address_id.name or ''">Teksa SpA</strong><br/>
                </t>
                <t t-if="object.job_id.address_id.street">
                    <t t-out="object.job_id.address_id.street or ''">Puerto Madero 9710</t><br/>
                    <t t-set="location" t-value="object.job_id.address_id.street"/>
                </t>
                <t t-if="object.job_id.address_id.street2">
                    <t t-out="object.job_id.address_id.street2 or ''">Of A15, Santiago (RM)</t><br/>
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.street2)"/>
                </t>
                <t t-if="object.job_id.address_id.city">
                    <t t-out="object.job_id.address_id.city or ''">Pudahuel</t>,
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.city)"/>
                </t>
                <t t-if="object.job_id.address_id.state_id.name">
                    <t t-out="object.job_id.address_id.state_id.name or ''">C1</t>,
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.state_id.name)"/>
                </t>
                <t t-if="object.job_id.address_id.zip">
                    <t t-out="object.job_id.address_id.zip or ''">98450</t>
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.zip)"/>
                </t>
                <br/>
                <t t-if="object.job_id.address_id.country_id.name">
                    <t t-out="object.job_id.address_id.country_id.name or ''">Argentina</t><br/>
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.country_id.name)"/>
                </t>
                <br/>
            </div>
        </td>
    </tr>
</table></field>
        <field name="auto_delete" eval="True"/>
        <field name="lang">{{ object.partner_id.lang or '' }}</field>
    </record>

    <record id="email_template_data_applicant_congratulations" model="mail.template">
        <field name="name">Recruitment: Application Acknowledgement</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="subject">Your Job Application: {{ object.job_id.name }}</field>
        <field name="email_to">{{ (not object.partner_id and object.email_from or '') }}</field>
        <field name="partner_to">{{ object.partner_id.id or '' }}</field>
        <field name="description">Confirmation email sent to all new job applications</field>
        <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" width="590" style="background-color: white; border-collapse: collapse; margin-left: 20px;">
    <tr>
        <td valign="top" style="padding: 0px 10px;">
            <div style="font-size: 13px; margin: 0px; padding: 0px;">
                Hello,
                <br/><br/>
                We confirm we successfully received your application for the job
                "<a t-att-href="hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''" style="color:#9A6C8E;"><strong t-out="object.job_id.name or ''">Experienced Developer</strong></a>" at <strong t-out="object.company_id.name or ''">YourCompany</strong>.
                <br/><br/>
                We will come back to you shortly.

                <div t-if="'website_url' in object.job_id and object.job_id.website_url" style="padding: 16px 8px 16px 8px;">
                    <a t-att-href="object.job_id.website_url"
                        style="background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;">Job Description</a>
                </div>

                <hr width="97%" style="background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;"/>
                <t t-if="object.user_id">
                    <h3 style="color:#9A6C8E;"><strong>Your Contact:</strong></h3>
                    <p>
                        <strong t-out="object.user_id.name or ''">Mitchell Admin</strong><br/>
                        <span>Email: <t t-out="object.user_id.email or ''"><EMAIL></t></span><br/>
                        <span>Phone: <t t-out="object.user_id.phone or ''">******-123-4567</t></span>
                    </p>
                    <hr width="97%" style="background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;"/>
                </t>

                <h3 style="color:#9A6C8E;"><strong>What is the next step?</strong></h3>
                We usually <strong>answer applications within a few days.</strong><br/><br/>
                Feel free to <strong>contact us if you want a faster
                feedback</strong> or if you don't get news from us
                quickly enough (just reply to this email).

                <hr width="97%" style="background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;"/>
                <t t-set="location" t-value="''" />
                <t t-if="object.job_id.address_id.name">
                    <strong t-out="object.job_id.address_id.name or ''">Teksa SpA</strong><br/>
                </t>
                <t t-if="object.job_id.address_id.street">
                    <t t-out="object.job_id.address_id.street or ''">Puerto Madero 9710</t><br/>
                    <t t-set="location" t-value="object.job_id.address_id.street"/>
                </t>
                <t t-if="object.job_id.address_id.street2">
                    <t t-out="object.job_id.address_id.street2 or ''">Of A15, Santiago (RM)</t><br/>
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.street2)"/>
                </t>
                <t t-if="object.job_id.address_id.city">
                    <t t-out="object.job_id.address_id.city or ''">Pudahuel</t>,
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.city)"/>
                </t>
                <t t-if="object.job_id.address_id.state_id.name">
                    <t t-out="object.job_id.address_id.state_id.name or ''">C1</t>,
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.state_id.name)"/>
                </t>
                <t t-if="object.job_id.address_id.zip">
                    <t t-out="object.job_id.address_id.zip or ''">98450</t>
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.zip)"/>
                </t>
                <br/>
                <t t-if="object.job_id.address_id.country_id.name">
                    <t t-out="object.job_id.address_id.country_id.name or ''">Argentina</t><br/>
                    <t t-set="location" t-value="'%s, %s' % (location, object.job_id.address_id.country_id.name)"/>
                </t>
                <br/>
            </div>
        </td>
    </tr>
</table></field>
        <field name="auto_delete" eval="True"/>
        <field name="lang">{{ object.partner_id.lang or '' }}</field>
    </record>

    <record id="email_template_data_applicant_not_interested" model="mail.template">
        <field name="name">Recruitment: Not interested anymore</field>
        <field name="model_id" ref="hr_recruitment.model_hr_applicant"/>
        <field name="subject">Your Job Application: {{ object.job_id.name }}</field>
        <field name="email_to">{{ (not object.partner_id and object.email_from or '') }}</field>
        <field name="partner_to">{{ object.partner_id.id or '' }}</field>
        <field name="description">When you refuse an application, you can choose this template</field>
        <field name="body_html" type="html">
<table border="0" cellpadding="0" cellspacing="0" width="590" style="min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;">
    <tr>
        <td valign="top">
            <div style="font-size: 13px; margin: 0px; padding: 0px;">
                Dear,<br/><br/>
                We would like to thank you for your interest and your time.<br/>
                We wish you all the best in your future endeavors.
                <br/><br/>
                Best<br/>
                <div style="font-size: 11px; color: grey;">
                    <t t-if="object.user_id">
                        -- <br/>
                        <strong t-out="object.user_id.name or ''">Marc Demo</strong><br/>
                        Email: <t t-out="object.user_id.email or ''"><EMAIL></t><br/>
                        Phone: <t t-out="object.user_id.phone or ''">******-123-4567</t>
                    </t>
                    <t t-else="">
                        -- <br/>
                        <t t-out="object.company_id.name or ''">YourCompany</t><br/>
                        The HR Team<br/>
                    </t>
                </div>
            </div>
        </td>
    </tr>
</table>
        </field>
        <field name="auto_delete" eval="True"/>
        <field name="lang">{{ object.partner_id.lang or '' }}</field>
    </record>

</data></odoo>
