# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__accepted_applications_count
msgid "# Accepted Offers"
msgstr "已接納聘用邀約數目"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__applications_count
msgid "# Offers"
msgstr "聘用邀約數目"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__refused_applications_count
msgid "# Refused Offers"
msgstr "已拒絕聘用邀約數目"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "1 Meeting"
msgstr "1 個面試"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Click to view</b> the application."
msgstr "<b>點選查看</b>模組。"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Did you apply by sending an email?</b> Check incoming applications."
msgstr "<b>你是發郵件申請的嗎？</b>檢查收信信件的模組。"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Drag this card</b>, to qualify him for a first interview."
msgstr "<b>拖曳這張卡片</b>，讓他有資格參加第一次面試。"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid ""
"<div><b>Try to send an email</b> to the applicant.</div><div><i>Tips: All "
"emails sent or received are saved in the history here</i>"
msgstr ""
"<div><b>試試向</b>申請人發送電子郵件。</div><div><i>提示：所有發送或接收的電子郵件都儲存在此處的歷史記錄中</i>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"公司\" title=\"公司\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"別名\" title=\"別名\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Documents\"/>"
msgstr "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Documents\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid ""
"<span class=\"mx-2\" style=\"padding-top: 1px; padding-bottom: "
"1px;\">to</span>"
msgstr ""
"<span class=\"mx-2\" style=\"padding-top: 1px; padding-bottom: "
"1px;\">至</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_text\">員工</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span class=\"o_stat_text\">Trackers</span>"
msgstr "<span class=\"o_stat_text\">追蹤器</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span invisible=\"address_id\" class=\"oe_read_only\">Remote</span>"
msgstr "<span invisible=\"address_id\" class=\"oe_read_only\">遙距</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"<span invisible=\"not is_warning_visible\">\n"
"                                <span class=\"fa fa-exclamation-triangle text-danger ps-3\">\n"
"                                </span>\n"
"                                <span class=\"text-danger\">\n"
"                                    All applications will lose their hired date and hired status.\n"
"                                </span>\n"
"                            </span>"
msgstr ""
"<span invisible=\"not is_warning_visible\">\n"
"                                <span class=\"fa fa-exclamation-triangle text-danger ps-3\">\n"
"                                </span>\n"
"                                <span class=\"text-danger\">\n"
"                                    所有申請都將失去錄用日期和錄用狀態。\n"
"                                </span>\n"
"                            </span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "<span invisible=\"not salary_expected_extra\"> + </span>"
msgstr "<span invisible=\"not salary_expected_extra\"> + </span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "<span invisible=\"not salary_proposed_extra\"> + </span>"
msgstr "<span invisible=\"not salary_proposed_extra\"> + </span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>New</span>"
msgstr "<span>新建</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>Reporting</span>"
msgstr "<span>報表</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>View</span>"
msgstr "<span>檢視</span>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_congratulations
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,\n"
"                <br/><br/>\n"
"                We confirm we successfully received your application for the job\n"
"                \"<a t-att-href=\"hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''\" style=\"color:#9A6C8E;\"><strong t-out=\"object.job_id.name or ''\">Experienced Developer</strong></a>\" at <strong t-out=\"object.company_id.name or ''\">YourCompany</strong>.\n"
"                <br/><br/>\n"
"                We will come back to you shortly.\n"
"\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <t t-if=\"object.user_id\">\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Your Contact:</strong></h3>\n"
"                    <p>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                        <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    </p>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                </t>\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days.</strong><br/><br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_interest
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"text-align: center\">\n"
"                <h2>Congratulations!</h2>\n"
"                <div style=\"color:grey;\">Your resume has been positively reviewed.</div>\n"
"            </div>\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                We just reviewed your resume, and it caught our\n"
"                attention. As we think you might be great for the\n"
"                position, your application has been short listed for a\n"
"                call or an interview.\n"
"                <br/><br/>\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <t t-if=\"object.user_id\">\n"
"                    You will soon be contacted by:<br/>\n"
"                    <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                    <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                    <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    <br/><br/>\n"
"                </t>\n"
"                See you soon,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    -- <br/>\n"
"                    The HR Team\n"
"                    <t t-if=\"'website_url' in object.job_id and hasattr(object.job_id, 'website_url') and object.job_id.website_url\">\n"
"                        Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.<br/>\n"
"                    </t>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days</strong>.\n"
"                <br/><br/>\n"
"                The next step is either a call or a meeting in our offices.\n"
"                <br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"                <br/>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_not_interested
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Dear,<br/><br/>\n"
"                We would like to thank you for your interest and your time.<br/>\n"
"                We wish you all the best in your future endeavors.\n"
"                <br/><br/>\n"
"                Best<br/>\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Marc Demo</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team<br/>\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_refuse
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                Thank you for your interest in joining the\n"
"                <b><t t-out=\"object.company_id.name or ''\">YourCompany</t></b> team.  We\n"
"                wanted to let you know that, although your resume is\n"
"                competitive, our hiring team reviewed your application\n"
"                and <b>did not select it for further consideration</b>.\n"
"                <br/><br/>\n"
"                Please note that recruiting is hard, and we can make\n"
"                mistakes. Do not hesitate to reply to this email if you\n"
"                think we made a mistake, or if you want more information\n"
"                about our decision.\n"
"                <br/><br/>\n"
"                We will, however, keep your resume on record and get in\n"
"                touch with you about future opportunities that may be a\n"
"                better fit for your skills and experience.\n"
"                <br/><br/>\n"
"                We wish you all the best in your job search and hope we\n"
"                will have the chance to consider you for another role\n"
"                in the future.\n"
"                <br/><br/>\n"
"                Thank you,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "當建立別名的新記錄時，一個Python字典被提供作為預設值."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__active
msgid "Active"
msgstr "啟用"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_activities
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities"
msgstr "活動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_overdue
msgid "Activities Overdue"
msgstr "已過期活動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_today
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities Today"
msgstr "今天活動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_mail_activity_plan
msgid "Activity Plan"
msgstr "活動計劃"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_plan
msgid "Activity Plans"
msgstr "活動計劃"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_state
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_icon
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_type_action_config_hr_applicant
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_type
msgid "Activity Types"
msgstr "活動類型"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Language Test\", \"Prepare Offer\", ...)"
msgstr ""
"「活動計劃」用於簡易分配一系列活動，按幾下便完成。\n"
"                    （例如：語文測試、準備聘用邀約等）"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "活動日程計劃精靈"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid "Add a new stage in the recruitment process"
msgstr "在招募流程中增加一個新階段"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_category_action
msgid "Add a new tag"
msgstr "添加新標籤"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_manager
msgid "Administrator"
msgstr "管理員"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_id
msgid "Alias"
msgstr "別名"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_contact
msgid "Alias Contact Security"
msgstr "別名聯絡人安全"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain_id
msgid "Alias Domain"
msgstr "別名域"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain
msgid "Alias Domain Name"
msgstr "別名域名"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_full_name
msgid "Alias Email"
msgstr "別名電子郵件"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__alias_id
msgid "Alias ID"
msgstr "別名ID"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_name
msgid "Alias Name"
msgstr "別名"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_status
msgid "Alias Status"
msgstr "別名狀態"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_status
msgid "Alias status assessed on the last message received."
msgstr "根據最後收到的一則訊息評估的別名狀態。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_model_id
msgid "Aliased Model"
msgstr "別名的模型"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__all_application_count
msgid "All Application Count"
msgstr "全部應徵人數"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ_all_app
msgid "All Applications"
msgstr "全部應徵申請"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Analysis"
msgstr "分析"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_applicant_new
#: model:ir.model,name:hr_recruitment.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__applicant_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "Applicant"
msgstr "申請人"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_degree
msgid "Applicant Degree"
msgstr "申請人學位"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_emails
msgid "Applicant Emails"
msgstr "申請人電郵"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_hired
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_hired
msgid "Applicant Hired"
msgstr "申請人獲聘"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__applicant_notes
msgid "Applicant Notes"
msgstr "申請人備註"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__applicant_properties_definition
msgid "Applicant Properties"
msgstr "申請人屬性"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_stage_changed
msgid "Applicant Stage Changed"
msgstr "申請人的階段已經變更"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_new
msgid "Applicant created"
msgstr "申請已建立"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_without_email
msgid "Applicant(s) not having email"
msgstr "沒有電子郵件的申請人"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_tree_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_activity
msgid "Applicants"
msgstr "申請人"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__applicant_hired
msgid "Applicants Hired"
msgstr "申請人獲聘"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Applicants and their attached résumé are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""
"收到電郵時，系統會自動為申請人及其附上的履歷，建立記錄。\n"
"                若有安裝文件管理模組，所有履歷都會自動編製索引，\n"
"                讓你日後可輕鬆搜尋相關內容。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Application"
msgstr "應用程式"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__application_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_count
msgid "Application Count"
msgstr "應用數量"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__application_status
msgid "Application Status"
msgstr "申請狀態"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Application email"
msgstr "申請電郵"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Application in Progress"
msgstr "申請進行中"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_applications
#: model:ir.actions.act_window,name:hr_recruitment.crm_case_categ0_act_job
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__applicant_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_applications
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_view_tree_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Applications"
msgstr "應徵者"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_platform__email
msgid ""
"Applications received from this Email won't be linked to a contact.There "
"will be no email address set on the Applicant either."
msgstr "經此電郵收到的申請，不會連結至聯絡人，亦不會對申請人設定電郵地址。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_date
msgid "Applied on"
msgstr "申請於"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_kanban_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Archive"
msgstr "封存"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__archived
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Archived"
msgstr "已封存"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_form_controller.js:0
msgid "Are you sure that you want to archive this job position?"
msgstr "確定要封存此職位？"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_open
msgid "Assigned"
msgstr "已分派"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"At least one applicant doesn't have a email; you can't use send email "
"option."
msgstr "至少一名申請人沒有電郵，故此不能使用傳送電郵選項。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Attach a file"
msgstr "附加一個文件"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_attachment_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__attachment_ids
msgid "Attachments"
msgstr "附件"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__author_id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__author_id
msgid "Author"
msgstr "作者"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__availability
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__availability
msgid "Availability"
msgstr "可用"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bachelor
msgid "Bachelor Degree"
msgstr "學士學位"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__is_blacklisted
msgid "Blacklist"
msgstr "黑名單"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "列入黑名單的電話是手機"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "列入黑名單的電話是市話"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Blocked"
msgstr "已封鎖"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__body_has_template_value
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__body_has_template_value
msgid "Body content is the same as the template"
msgstr "正文內容與範本相同"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_bounce
msgid "Bounce"
msgstr "彈跳"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position_interviewer
msgid "By Job Positions"
msgstr "按職位"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid ""
"By setting an alias to a job position, emails sent to this address create "
"applications automatically. You can even use multiple trackers to get "
"statistics according to the source of the application: LinkedIn, Monster, "
"Indeed, etc."
msgstr ""
"通過為職位設置別名，發送到此地址的電子郵件會自動建立資料。您甚至可以使用多個跟踪器根據來源獲取統計資訊：LinkedIn、Monster、Indeed "
"等。"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_calendar_event
msgid "Calendar Event"
msgstr "日曆活動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__campaign_id
msgid "Campaign"
msgstr "活動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__can_edit_body
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__can_edit_body
msgid "Can Edit Body"
msgstr "允許編輯內文"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Cancel"
msgstr "取消"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__candidate_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__candidate_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__candidate_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Candidate"
msgstr "候選人"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__user_id
msgid "Candidate Manager"
msgstr "候選人管理員"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_company__candidate_properties_definition
msgid "Candidate Properties"
msgstr "應徵者屬性"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Candidate's Name"
msgstr "應徵者姓名"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__candidate_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_candidate
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Candidates"
msgstr "候選人"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__similar_candidates_count
msgid "Candidates with the same email or phone or mobile"
msgstr "使用相同電郵地址、電話號碼或手機號碼的應徵者"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_name
msgid "Candidates's Name"
msgstr "應徵者姓名"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_graph_view_job
msgid "Cases By Stage and Estimates"
msgstr "按階段和估算的案件"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_category
msgid "Category of applicant"
msgstr "申請人類別"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Choose an application email."
msgstr "選擇一個申請電子郵件。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__color
msgid "Color Index"
msgstr "顏色索引"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_company
msgid "Companies"
msgstr "公司"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__company_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Company"
msgstr "公司"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_configuration
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Configuration"
msgstr "配置"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_congratulations
msgid "Confirmation email sent to all new job applications"
msgstr "發送給所有新職位申請的確認電子郵件"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_id
msgid "Contact"
msgstr "聯絡人"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Contact Email"
msgstr "聯繫電郵"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_view_search_inherit_hr_recruitment
msgid "Content"
msgstr "內容"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__body
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__body
msgid "Contents"
msgstr "內容"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job4
msgid "Contract Proposal"
msgstr "契約建議"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job5
msgid "Contract Signed"
msgstr "契約已簽署"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Copy this email address, to paste it in your email composer, to apply."
msgstr "複製此電子郵件地址，將其貼到你的電子郵件編輯器中來進行申請。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "此聯絡人退回信件數量計數器"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create"
msgstr "建立"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Create Employee"
msgstr "新增員工"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.create_job_simple
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create a Job Position"
msgstr "建立職位"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid "Create a Recruitment Activity Plan"
msgstr "建立招聘活動計劃"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "Create a new rule to process emails from specific job boards."
msgstr "建立新規則，以處理來自特定招聘平台的電郵。"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Create your first Job Position."
msgstr "建立公司的第一個工作職位。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_date
msgid "Created on"
msgstr "建立於"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Creation Date"
msgstr "建立日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "自訂彈跳訊息"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__date_from
msgid "Date From"
msgstr "起始日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__date_to
msgid "Date To"
msgstr "結束日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_close
msgid "Days to Close"
msgstr "關閉日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_open
msgid "Days to Open"
msgstr "開啟天數"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_defaults
msgid "Default Values"
msgstr "預設值"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid ""
"Define a regex: Extract the applicant's name from the email's subject or "
"body."
msgstr "定義 regex：從電子郵件的主題或正文中，提取申請人的姓名。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"Define a specific contact address for this job position. If you keep it "
"empty, the default email address will be used which is in human resources "
"settings"
msgstr "為此職位定義特定的聯絡地址。若留空，會使用預設的電郵地址，該地址可在人力資源設定中設置"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
msgid ""
"Define here your stages of the recruitment process, for example:\n"
"                    qualification call, first interview, second interview, refused,\n"
"                    hired."
msgstr ""
"在此處定義招聘流程各階段，例如：\n"
"                    資格評定電話、首次面試、第二次面試、被拒絕、\n"
"                    聘用。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__type_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_tree
msgid "Degree"
msgstr "學位"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__name
msgid "Degree Name"
msgstr "學位名稱"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_degree_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_degree
msgid "Degrees"
msgstr "學位"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__delay_close
msgid "Delay to Close"
msgstr "延遲關閉"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Delete"
msgstr "刪除"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_department
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__department_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Department"
msgstr "部門"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__manager_id
msgid "Department Manager"
msgstr "部門經理"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_department
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_department
msgid "Departments"
msgstr "部門"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__name
msgid "Description"
msgstr "說明"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Details"
msgstr "詳細資訊"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_digest_digest
msgid "Digest"
msgstr "摘要"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Digitize your résumé to extract name and email automatically."
msgstr "將履歷表數碼化，以自動提取姓名及電郵地址等資料。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Directly"
msgstr "直接"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Directly Available"
msgstr "直接可用"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Discard"
msgstr "捨棄"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_applicant_cv_display
msgid "Display CV on application form"
msgstr "在申請表上顯示履歷"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Display résumé on application form"
msgstr "在申請表單顯示履歷"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "沒有存取權限，使用者摘要電郵將跳過此數據"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bac5
msgid "Doctoral Degree"
msgstr "博士學位"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__documents_count
msgid "Document Count"
msgstr "文件數"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__document_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Documents"
msgstr "文件"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_1
msgid "Does not fit the job requirements"
msgstr "不符合職位要求"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid ""
"Don't forget to specify the department if your recruitment process\n"
"                is different according to the job position."
msgstr ""
"如果招聘流程因職位不同而異，\n"
"                不要忘記指定部門。"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_6
msgid "Duplicate"
msgstr "複製"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__duplicates
msgid "Duplicates"
msgstr "副本"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__duplicates_count
msgid "Duplicates Count"
msgstr "重複項目數量"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__email
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Email"
msgstr "電郵"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email Alias"
msgstr "郵箱別名"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__template_id
msgid "Email Template"
msgstr "電郵範本"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_id
msgid ""
"Email alias for this job position. New emails will automatically create new "
"applicants for this job position."
msgstr "此職位的電郵別名。收到新電郵時，會自動為此職位建立新申請人。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_cc
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_cc
msgid "Email cc"
msgstr "電郵副本抄送"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "電子郵件網域，例如 <EMAIL> 之內的「example.com」"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid "Email template must be selected to send a mail"
msgstr "必須選擇電郵範本，才可傳送郵件"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_platforms
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_emails
msgid "Emails"
msgstr "電子郵件"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.model,name:hr_recruitment.model_hr_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__employee_id
msgid "Employee"
msgstr "員工"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__emp_is_active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__emp_is_active
msgid "Employee Active"
msgstr "員工生效"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__employee_name
msgid "Employee Name"
msgstr "員工姓名"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_hired_template
msgid "Employee created:"
msgstr "已建立員工："

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__employee_id
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__employee_id
msgid "Employee linked to the candidate."
msgstr "與應徵者相關聯的員工。"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_employees
msgid "Employees"
msgstr "員工"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_contract_type
msgid "Employment Types"
msgstr "聘用類型"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__priority
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__priority
msgid "Evaluation"
msgstr "評估"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__3
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__3
msgid "Excellent"
msgstr "傑出"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Expected"
msgstr "預期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__expected_employee
msgid "Expected Employee"
msgstr "預期的員工"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Expected Salary Extra"
msgstr "預期額外薪資"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Extended Filters"
msgstr "擴展篩選"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__extended_interviewer_ids
msgid "Extended Interviewer"
msgstr "擴展面試官"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__favorite_user_ids
msgid "Favorite User"
msgstr "最愛使用者"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "用於存儲已清理電話號碼的欄位。説明加快搜索和比較。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "File"
msgstr "文件"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job2
msgid "First Interview"
msgstr "初次面試"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__fold
msgid "Folded in Kanban"
msgstr "在看板中折疊"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_recruitment_stage_kanban
msgid "Folded in Recruitment Pipe:"
msgstr "在招聘管道中已摺疊："

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_follower_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_partner_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_type_icon
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Future Activities"
msgstr "未來活動"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Generate Email"
msgstr "生成電子郵件"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_get_refuse_reason
msgid "Get Refuse Reason"
msgstr "獲取拒絕理由"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__1
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__1
msgid "Good"
msgstr "好"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_graduate
msgid "Graduate"
msgstr "畢業生"

#. module: hr_recruitment
#: model_terms:web_tour.tour,rainbow_man_message:hr_recruitment.hr_recruitment_tour
msgid "Great job! You hired a new colleague!"
msgstr "做得好！你聘請了一位新同事了！"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__done
msgid "Green"
msgstr "綠色"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_done
msgid "Green Kanban Label"
msgstr "綠色看板標籤"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__normal
msgid "Grey"
msgstr "灰色"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "灰色看板標籤"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__group_applicant_cv_display
msgid "Group Applicant Cv Display"
msgstr "團體申請人履歷顯示"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Group By"
msgstr "分組依據"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__has_domain
msgid "Has Domain"
msgstr "有域"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__has_message
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid ""
"Have you tried to <a>add skills to your job position</a> and search into the"
" Reserve ?"
msgstr "試過<a>為工作職位加入技能描述</a>及搜尋候補人才庫嗎？"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_closed
msgid "Hire Date"
msgstr "聘用日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__no_of_hired_employee
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__hired
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Hired"
msgstr "決定聘用"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid "Hired Stage"
msgstr "Hired 階段"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Hiring Date"
msgstr "聘用日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__id
msgid "ID"
msgstr "識別號"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "上級記錄ID支援別名(例如:專案支援任務建立別名)"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_icon
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_icon
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_sms_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid ""
"If checked, this stage is used to determine the hire date of an applicant"
msgstr "若勾選，此階段用於確定申請人的僱用日期"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__template_id
msgid ""
"If set, a message is posted on the applicant using the template when the "
"applicant is set to the stage."
msgstr "如果設定,當應聘者設定此場景時將通過這個模板推送消息給相關應聘者。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "設置後，此內容將自動發送給未經授權的用戶，而不是默認訊息。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__emp_is_active
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__emp_is_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr "如果啟用欄位設定為未啟用，它將允許您隱藏資源記錄而不刪除它."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__active
msgid ""
"If the active field is set to false, it will allow you to hide the case "
"without removing it."
msgstr "如果啟用欄位設為否，它允許您對資料進行隱藏但不刪除它。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr "若電郵地址在黑名單中，相關聯絡人不會再收到任何清單的群發郵件"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr "如果已清理的電話號碼在黑名單中，則該聯絡人將不會再收到來自任何列表的群發簡訊"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "In Progress"
msgstr "進行中"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "In-App Purchases"
msgstr "應用程式內購買"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid ""
"Incoming emails create applications automatically. Use it for direct "
"applications or when posting job offers on LinkedIn, Monster, etc."
msgstr "傳入電子郵件會自動建立應用程式。可用於直接申請，或在 LinkedIn、Monster 等上發佈工作機會時使用。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名單的已清理電話號碼是否為手機號碼。當模型中同時存在手機和電話欄位時，有助於區分哪個號碼被列入黑名單。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr "指示列入黑名單的已清理電話號碼是否為電話號碼。當模型中同時存在手機和電話欄位時，有助於區分哪個號碼被列入黑名單。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__industry_id
msgid "Industry"
msgstr "行業"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job1
msgid "Initial Qualification"
msgstr "初始資歷"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Internal notes..."
msgstr "內部備註..."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_survey
msgid "Interview Forms"
msgstr "面試表單"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_interviewer
msgid "Interviewer"
msgstr "面試官"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__interviewer_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__interviewer_ids
msgid "Interviewers"
msgstr "面試官"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__is_mail_template_editor
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__is_mail_template_editor
msgid "Is Editor"
msgstr "是編輯器"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__is_favorite
msgid "Is Favorite"
msgstr "為最愛"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_is_follower
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_is_follower
msgid "Is Follower"
msgstr "是關注人"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__is_warning_visible
msgid "Is Warning Visible"
msgstr "警告是否可見"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__date_from
msgid ""
"Is set, update candidates availability once hired for that specific mission."
msgstr "如果設置，會在為該特定任務聘用應徵者後，更新應徵者的可用性。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON 將識別碼從 many2one 欄位對應至花費秒數"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Job"
msgstr "工作"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_pivot_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Job Applications"
msgstr "職位申請"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_boards
msgid "Job Boards"
msgstr "招聘平台"

#. module: hr_recruitment
#: model:utm.campaign,title:hr_recruitment.utm_campaign_job
msgid "Job Campaign"
msgstr "招聘活動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__address_id
msgid "Job Location"
msgstr "工作地點"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job_platform
msgid "Job Platforms"
msgstr "招聘平台"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__job_id
msgid "Job Position"
msgstr "工作職稱"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_department_new
msgid "Job Position Created"
msgstr "已建立工作職缺"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_job_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_new
msgid "Job Position created"
msgstr "工作職缺已建立"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_config
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_interviewer
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_jobs
msgid "Job Positions"
msgstr "職務管理"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Job Posting"
msgstr "職位發佈"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_company__job_properties_definition
msgid "Job Properties"
msgstr "職位屬性"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Job Specific"
msgstr "具體職位"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_5
msgid "Job already fulfilled"
msgstr "職位已聘請"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Jobs"
msgstr "工作"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Jobs - Recruitment Form"
msgstr "工作－招募表"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_blocked
msgid "Kanban Blocked"
msgstr "看板阻塞"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_normal
msgid "Kanban Ongoing"
msgstr "看板進展"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__kanban_state
msgid "Kanban State"
msgstr "看板狀態"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_done
msgid "Kanban Valid"
msgstr "看板有效"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues_value
msgid "Kpi Hr Recruitment New Colleagues Value"
msgstr "人力資源新聘員工KPI指標"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__lang
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__lang
msgid "Language"
msgstr "語言"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Last Meeting"
msgstr "上次面試"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__last_stage_id
msgid "Last Stage"
msgstr "最終階段"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Last Stage Update"
msgstr "最後階段更新"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Late Activities"
msgstr "逾期活動"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Let's create a job position."
msgstr "讓我們建立一個工作職位."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid ""
"Let's create the position. An email will be setup for applications, and a "
"public job description, if you use the Website app."
msgstr "讓我們建立工作職位。如果您使用網站模組，將為模組設置電子郵件和對外公開的職位說明。"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let's have a look at how to <b>improve</b> your <b>hiring process</b>."
msgstr "讓我們來看看如何<b>改進</b>您的<b>招募流程</b>."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let’s create this new employee now."
msgstr "現在讓我們建立這個新員工。"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let’s go back to the dashboard."
msgstr "讓我們回到儀表板."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__linkedin_profile
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__linkedin_profile
msgid "LinkedIn Profile"
msgstr "LinkedIn 個人檔案"

#. module: hr_recruitment
#: model:ir.actions.server,name:hr_recruitment.action_load_demo_data
msgid "Load demo data"
msgstr "載入範例資料"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
msgid "Load sample data"
msgstr "載入範例資料"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Load template"
msgstr "載入範本"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "基於本地部件的來件檢測"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__template_id
msgid "Mail Template"
msgstr "信件範本"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Manager"
msgstr "經理"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_licenced
msgid "Master Degree"
msgstr "碩士學位"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__medium_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__medium_id
msgid "Medium"
msgstr "媒體"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_mediums
msgid "Mediums"
msgstr "媒體"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_display_date
msgid "Meeting Display Date"
msgstr "面試顯示日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_text
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_display_text
msgid "Meeting Display Text"
msgstr "面試顯示文本"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_ids
msgid "Meetings"
msgstr "會議"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_ir_ui_menu
msgid "Menu"
msgstr "功能表"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_ids
msgid "Messages"
msgstr "訊息"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Mission Dates"
msgstr "任務日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "My Applications"
msgstr "分派予我之應徵者"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "My Candidates"
msgstr "我的應徵者"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_job_filter_recruitment
msgid "My Favorites"
msgstr "我的最愛"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_search_view
msgid "My Job Positions"
msgstr "我的工作職位"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Name"
msgstr "名稱"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "New"
msgstr "新增"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_applicant_count
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_new
msgid "New Applicant"
msgstr "新應徵"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "New Applicants"
msgstr "新申請人"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_new_application
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__new_application_count
msgid "New Application"
msgstr "新申請"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_from_department
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "New Applications"
msgstr "新應徵者"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues
msgid "New Employees"
msgstr "新員工"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_hired_employee
msgid "New Hired Employee"
msgstr "新聘用的員工"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_tree_activity
msgid "Next Activities"
msgstr "下一步活動"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_date_deadline
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_summary
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Next Meeting"
msgstr "下次面試"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "No Meeting"
msgstr "無面試"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "No application found. Let's create one !"
msgstr "找不到申請。讓我們創建一個"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid "No applications yet"
msgstr "尚無應徵申請"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_candidate
msgid "No candidates yet"
msgstr "未有應徵者"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_type_action_config_hr_applicant
msgid "No data to display"
msgstr "無資料可供顯示"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_action_analysis
msgid "No data yet!"
msgstr "暫無資料！"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "No rules have been defined."
msgstr "未定義任何規則。"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__0
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__0
msgid "Normal"
msgstr "正常（沒有底線）"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_normalized
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_normalized
msgid "Normalized Email"
msgstr "已常規化電郵"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Note"
msgstr "備註"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction_counter
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數量"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_number
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__attachment_count
msgid "Number of Attachments"
msgstr "附件數量"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__new_application_count
msgid ""
"Number of applications that are new in the flow (typically at first step of "
"the flow)"
msgstr "流程中的新應徵數量（通常在流程的第一個階段）"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__delay_close
msgid "Number of days to close"
msgstr "持續天數"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error_counter
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr "招聘階段此職位已經聘請的員工數量。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction_counter
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error_counter
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Odoo 協助你在招聘過程中追蹤申請人，\n"
"                以及跟進所有操作：會議、面試等。"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_user
msgid "Officer: Manage all applicants"
msgstr "主管：管理所有申請人"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__old_application_count
msgid "Old Application"
msgstr "舊申請"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__ongoing
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Ongoing"
msgstr "正在進行"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Online Posting"
msgstr "線上發佈"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Operation not supported"
msgstr "不支援該操作"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "所有的進來的信件都將附上一條潛在商機（記錄）選配的ID，即使它們不曾回覆過它。如果設定了，這個將完全阻止新記錄的建立。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_applicant_send_mail__lang
#: model:ir.model.fields,help:hr_recruitment.field_candidate_send_mail__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"發送電子郵件時要選擇的可選翻譯語言（ISO 代碼）。如果未設置，將使用英文版本。這通常應該是提供適當語言的佔位符表達式，例如{{ "
"object.partner_id.lang }}。"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Or talk about this applicant privately with your colleagues."
msgstr "或者與你的同事私下談論這個申請人。"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Other Applications"
msgstr "其他申請"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__other_applications_count
msgid "Other Applications Count"
msgstr "其他申請數目"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other applications"
msgstr "其他申請"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other benefits"
msgstr "其他福利"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_model_id
msgid "Parent Model"
msgstr "上級模型"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "上級記錄線程ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"載有該別名的母項模型。載有別名參照的模型，不一定是 alias_model_id 提供的模型，例如：專案（parent_model）與任務（model）"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_name
msgid "Partner Name"
msgstr "合作夥伴名稱"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_phone
msgid "Phone"
msgstr "電話"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "電話黑名單"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_mobile_search
msgid "Phone/Mobile"
msgstr "電話/手機"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Please provide an candidate name."
msgstr "請提供應徵者姓名。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"通過信件網關在文件上提交一個訊息政策。\n"
"- 任何人：任何人都可以提交\n"
"- 合作夥伴：只有認證過的合作夥伴\n"
"- 跟隨者：只有相關文件或下列頻道成員的跟隨者\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__probability
msgid "Probability"
msgstr "概率"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Process"
msgstr "處理"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__applicant_properties
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__candidate_properties
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__job_properties
msgid "Properties"
msgstr "屬性"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Proposed"
msgstr "提議"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Proposed Salary Extra"
msgstr "薪酬標準"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Provide an email"
msgstr "請提供電郵"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Publish job offers on your website"
msgstr "在你的網站上發佈工作機會"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__rating_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__rating_ids
msgid "Ratings"
msgstr "評分"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Ready for Next Stage"
msgstr "下一階段就緒"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Ready to recruit more efficiently?"
msgstr "準備更有效地招聘？"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Reason"
msgstr "原因"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_force_thread_id
msgid "Record Thread ID"
msgstr "記錄追蹤ID"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__user_id
msgid "Recruiter"
msgstr "招募人員"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_root
#: model_terms:ir.ui.view,arch_db:hr_recruitment.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment"
msgstr "招聘"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_job_stage_act
msgid "Recruitment / Applicants Stages"
msgstr "招募 / 申請階段"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_analysis
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_report_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Recruitment Analysis"
msgstr "招募分析"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid "Recruitment Plans"
msgstr "招聘計劃"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "招聘階段"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_congratulations
msgid "Recruitment: Application Acknowledgement"
msgstr "招聘：確認申請"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_interest
msgid "Recruitment: Interest"
msgstr "招聘：興趣"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_not_interested
msgid "Recruitment: Not interested anymore"
msgstr "招聘：不再感興趣"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_refuse
msgid "Recruitment: Refuse"
msgstr "招聘：拒絕"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__blocked
msgid "Red"
msgstr "紅色"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "紅色的看板標籤"

#. module: hr_recruitment
#: model:ir.actions.server,name:hr_recruitment.ir_actions_server_refuse_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Refuse"
msgstr "退回"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_date
msgid "Refuse Date"
msgstr "拒絕日期"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: model:ir.actions.act_window,name:hr_recruitment.applicant_get_refuse_reason_action
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__refuse_reason_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_reason_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Refuse Reason"
msgstr "退回原因"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_refuse_reason
msgid "Refuse Reason of Applicant"
msgstr "退回應徵申請原因"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_refuse_reason_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_applicant_refuse_reason
msgid "Refuse Reasons"
msgstr "退回原因"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Refuse the"
msgstr "拒絕該"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__refused
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Refused"
msgstr "已退回"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"Refused automatically because this application has been identified as a "
"duplicate of %(link)s"
msgstr "已自動拒絕，因為識別到此申請與以下項目重複：%(link)s"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_2
msgid "Refused by applicant: job fit"
msgstr "申請人已拒絕：職位不適合"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_8
msgid "Refused by applicant: salary"
msgstr "申請人已拒絕：薪酬"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__regex
msgid "Regex"
msgstr "Regex 表達式"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Remote"
msgstr "遙距工作"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__render_model
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__render_model
msgid "Rendering Model"
msgstr "呈現模型"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.report_hr_recruitment
msgid "Reporting"
msgstr "報告"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__requirements
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Requirements"
msgstr "要求"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Responsible"
msgstr "負責人"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Restore"
msgstr "還原"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Resume's content"
msgstr "履歷內容"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Running Applicants"
msgstr "候選申請人"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Résumé Digitization (OCR)"
msgstr "履歷數碼化（OCR 光學字元辨識）"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Résumé Display"
msgstr "履歷顯示"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_sms_error
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected
msgid "Salary Expected by Applicant"
msgstr "申請人要求的薪酬"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Salary Expected by Applicant, extra advantages"
msgstr "應聘者的預期薪酬，額外優勢"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Salary Package"
msgstr "薪酬組合"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Salary Proposed by the Organisation"
msgstr "該組織的提議薪酬"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Salary Proposed by the Organisation, extra advantages"
msgstr "公司推薦的薪酬標準，額外優勢"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_sanitized
msgid "Sanitized Number"
msgstr "消毒數量"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone_sanitized
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_phone_sanitized
msgid "Sanitized Phone Number"
msgstr "經淨化電話號碼"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Save it!"
msgstr "儲存它！"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Schedule Interview"
msgstr "預約面試"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Search Applicants"
msgstr "搜尋申請人"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_view_search
msgid "Search Source"
msgstr "搜尋來源"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job3
msgid "Second Interview"
msgstr "第二次面試"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__address_id
msgid ""
"Select the location where the applicant will work. Addresses listed here are"
" defined on the company's contact information."
msgstr "選擇申請人的工作地點。此處列出的地址是根據公司的聯絡資料定義的。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Send"
msgstr "發送"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_extract
msgid "Send CV to OCR to fill applications"
msgstr "將履歷發送至 OCR 以填寫申請"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.actions.server,name:hr_recruitment.action_applicant_send_mail
#: model:ir.actions.server,name:hr_recruitment.action_candidate_send_mail
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__send_mail
msgid "Send Email"
msgstr "發送電子郵件"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send Interview Survey"
msgstr "發送採訪調查"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send SMS"
msgstr "發送簡訊"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Send an Interview Survey to the applicant during the recruitment process"
msgstr "在招聘過程中向申請人發送面試調查"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_send_mail
msgid "Send mails to applicants"
msgstr "向申請人發送郵件"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_candidate_send_mail
msgid "Send mails to candidates"
msgstr "向應徵者發送郵件"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "向你的聯絡人發送文字訊息"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Send your email. Followers will get a copy of the communication."
msgstr "發送你的電子郵件。追隨者將獲得通訊的副本。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Sequence"
msgstr "序列號"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_interest
msgid ""
"Set this template to a recruitment stage to send it when applications reach "
"that stage"
msgstr "將此範本設為招聘階段，以便在申請達到該階段時發送"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_configuration
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_global_settings
msgid "Settings"
msgstr "設定"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Show all records which has next action date is before today"
msgstr "顯示在今天之前的下一個行動日期的所有記錄"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Similar Candidates"
msgstr "相似應徵者"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__similar_candidates_count
msgid "Similar Candidates Count"
msgstr "相似應徵者數目"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__single_applicant_email
msgid "Single Applicant Email"
msgstr "單一申請人電郵"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Some values do not exist in the application status"
msgstr "部份數值在應用程式狀態中不存在"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__source_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__source_id
msgid "Source"
msgstr "來源"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "應聘者來源"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_sources
msgid "Sources"
msgstr "來源"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Sources of Applicants"
msgstr "應聘者來源"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Sourcing"
msgstr "來源"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_7
msgid "Spam"
msgstr "垃圾信件"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Specific jobs that use this stage. Other jobs will not use this stage."
msgstr "使用此階段的特定職位。其他工作職位不會使用此階段。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage"
msgstr "階段"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_stage_changed
msgid "Stage Changed"
msgstr "階段已變更"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage Definition"
msgstr "階段定義"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__name
msgid "Stage Name"
msgstr "階段名稱"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_stage_changed
msgid "Stage changed"
msgstr "階段已改變"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__last_stage_id
msgid ""
"Stage of the applicant before being in the current stage. Used for lost "
"cases analysis."
msgstr "目前階段前的申請人階段。用於招募案例分析。"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_stage_act
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_stage
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_tree
msgid "Stages"
msgstr "階段"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_state
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__duration_tracking
msgid "Status time"
msgstr "狀態時間"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__subject
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__subject
msgid "Subject"
msgstr "主題"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__name
msgid "Tag Name"
msgstr "標籤名稱"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_applicant_category_name_uniq
msgid "Tag name already exists!"
msgstr "標籤名稱已存在!"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_category_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__categ_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__categ_ids
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_category_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Tags"
msgstr "標籤"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_job_platform_email_uniq
msgid ""
"The Email must be unique, this one already corresponds to another Job "
"Platform."
msgstr "電子郵件必須獨一無二，此電郵已經對應至另一求職平台。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__interviewer_ids
msgid ""
"The Interviewers set on the job position can see all Applicants in it. They "
"have access to the information, the attachments, the meeting management and "
"they can refuse him. You don't need to have Recruitment rights to be set as "
"an interviewer."
msgstr "工作崗位的全部申請者對面試人員可見。可以查看資料和附件、管理會議、拒絕申請。您無需擁有招聘權限，即可被設為面試人員。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__user_id
msgid ""
"The Recruiter will be the default value for all Applicants in this job"
"             position. The Recruiter is automatically added to all meetings "
"with the Applicant."
msgstr "該招聘人員將成為所有申請此職位的申請人的預設值。該招聘人員將自動加入至與申請人進行的所有會議中。"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_campaign.py:0
msgid ""
"The UTM campaign '%s' cannot be deleted as it is used in the recruitment "
"process."
msgstr "UTM營銷活動\"%s\"不能刪除，因為它用於招聘過程。"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid ""
"The candidate is linked to an employee, to avoid losing information, archive"
" it instead."
msgstr "此應徵者已連結至一名員工，為避免遺失資訊，請改為將它封存。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__availability
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__availability
msgid "The date at which the applicant will be available to start working"
msgstr "申請者可以開始上班的日期"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_send_mail.py:0
msgid "The following applicants are missing an email address: %s."
msgstr "以下申請人缺少電子郵件地址：%s。"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/candidate_send_mail.py:0
msgid "The following candidates are missing an email address: %s."
msgstr "以下應徵者缺漏電郵地址：%s。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"相應於這個別名對應的模型(Odoo單據種類)。任何一封不屬於對某個已存在的記錄的到來信件，將導致此模組中新記錄的建立(例如，一個新專案任務)。"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_recruitment_degree_name_uniq
msgid "The name of the Degree of Recruitment must be unique!"
msgstr "應聘者學歷的名稱必須唯一！"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "郵箱別名，例如填寫 jobs 表示你想捕捉寄往 <EMAIL> 的電郵。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_platform__regex
msgid ""
"The regex facilitates to extract information from the subject or body of the"
" received email to autopopulate the Applicant's name field"
msgstr "該regex表達式有助從收到的電子郵件的主題或正文提取資訊，以自動填入申請人姓名欄位"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_list_controller.js:0
msgid ""
"These job positions and all related applicants will be archived. Are you "
"sure?"
msgstr "這些職位及所有相關申請人都會封存。確定要繼續？"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__medium_id
msgid ""
"This displays how the applicant has reached out, e.g. via Email, LinkedIn, "
"Website, etc."
msgstr "顯示申請人如何接觸此公司，例如：電子郵件、LinkedIn、網站等"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_normalized
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr "此欄位用於搜索電子郵件地址，因為主電子郵件欄位可以包含的不僅僅是電子郵件地址。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr "這是一個幫助您追蹤不同行銷活動的名稱，例如，Fall_Drive、Christmas_Special"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "連結的來源，例：搜尋引擎、其他網站，或電郵清單的名稱"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_form_controller.js:0
#: code:addons/hr_recruitment/static/src/views/recruitment_kanban_view.js:0
#: code:addons/hr_recruitment/static/src/views/recruitment_list_controller.js:0
msgid ""
"This job position and all related applicants will be archived. Are you sure?"
msgstr "此職位及所有相關申請人都會封存。確定要繼續？"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr "當這個階段中沒有任何記錄要呈現的時候，這個階段在看板視圖中被折疊起來."

#. module: hr_recruitment
#: model:digest.tip,name:hr_recruitment.digest_tip_hr_recruitment_0
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Tip: Let candidates apply by email"
msgstr "提示：讓候選人通過電子郵件申請"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "To Recruit"
msgstr "招募"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Today Activities"
msgstr "今天的活動"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Tooltips"
msgstr "提示"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_sources
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Trackers"
msgstr "追蹤工具"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Try creating an application by sending an email to"
msgstr "試試以電郵建立一項申請。請傳送電郵至 "

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Try sending an email"
msgstr "嘗試發送電子郵件"

#. module: hr_recruitment
#: model_terms:web_tour.tour,rainbow_man_message:hr_recruitment.hr_recruitment_tour
msgid "Try the Website app to publish job offers online."
msgstr "試試使用網站製作應用程式，線上發佈工作機會。"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_type_action_config_hr_applicant
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr "請嘗試新增一些記錄，或確保搜尋列沒有生效的篩選器。"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_decoration
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM 行銷活動"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_source
msgid "UTM Source"
msgstr "UTM來源"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm
msgid "UTMs"
msgstr "UTMs"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Unarchive"
msgstr "取消封存"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Unassigned"
msgstr "未分派"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Unread Messages"
msgstr "未讀消息"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Use OCR to fill data from a picture of the Résumr or the file itself"
msgstr "使用 OCR（光學字元辨識），識別履歷內圖片或文件檔本身的資料，用以填寫數據。"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Use emails and links trackers"
msgstr "使用電子郵件和連結追踪器"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Use interview forms tailored to each job position during the recruitment "
"process. Select the form to use in the job position detail form. This relies"
" on the Survey app."
msgstr "在招募過程中使用為每個職位量身訂製的面試表格。選擇要在職位詳細資訊表單中使用的表單。這依賴於問卷模組。"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_users
msgid "User"
msgstr "使用者"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_email
msgid "User Email"
msgstr "使用者電郵"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__2
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__2
msgid "Very Good"
msgstr "非常好"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Waiting"
msgstr "正在等待"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Want to analyse where applications come from ?"
msgstr "要分析應徵者來源嗎?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__website_message_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__website_message_ids
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "What do you want to recruit today? Choose a job title..."
msgstr "你今天想招聘甚麼職位？請選擇職位名稱⋯"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_refuse
msgid "When you refuse an application, you can choose this template"
msgstr "當您拒絕申請時，您可以選擇此範本"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Who can access candidates"
msgstr "誰可存取應徵者資料"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "Without a regex: The applicant's name will be the email's subject."
msgstr "不使用 regex：電子郵件的主題將作為申請人姓名。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Write your message here..."
msgstr "寫下訊息⋯"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "You are not allowed to perform this action."
msgstr "你不允許執行此操作。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define here the labels that will be displayed for the kanban state instead\n"
"                            of the default labels."
msgstr ""
"您可以在此處定義顯示看板狀態的標籤，\n"
"                            而非預設標籤。"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define the requirements here. They will be displayed when you hover "
"over the stage title."
msgstr "你可在此處定義要求。若將滑鼠停留在階段標題上，便會顯示這些要求的資料。"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"You can't select Send email option.\n"
"The email will not be sent to the following applicant(s) as they don't have an email address:"
msgstr ""
"不可選擇傳送電郵選項。\n"
"該電子郵件不會發送給以下申請人，因為他們未有電郵地址："

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid ""
"You cannot create an applicant in a different company than the candidate"
msgstr "你不可在與應徵者不同的其他公司建立申請人"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following recruitment sources in Recruitment:\n"
"%(recruitment_sources)s"
msgstr ""
"您無法刪除這些UTM來源，因為它們連結至招聘中的以下招聘來源：\n"
"%(recruitment_sources)s"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You have been assigned as an interviewer for %s"
msgstr "你已被委派為以下應徵者的面試官：%s"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You have been assigned as an interviewer for the Applicant %s"
msgstr "你已被委派為以下申請人的面試官：%s"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You must define a Contact Name for this applicant."
msgstr "你必須為此申請人定義一個聯絡人姓名。"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "You must define a Contact Name for this candidate."
msgstr "你必須為此應徵者定義一個聯絡人姓名。"

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_congratulations
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_interest
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_refuse
msgid "Your Job Application: {{ object.job_id.name }}"
msgstr "您的工作申請：{{ object.job_id.name }}"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.quick_create_applicant_form
msgid "e.g. John Doe"
msgstr "例：陳大文"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "e.g. LinkedIn"
msgstr "例如 LinkedIn"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. Linkedin"
msgstr "例：Linkedin"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. Masters"
msgstr "例：碩士"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. Sales Manager"
msgstr "例如：銷售經理"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. ^New application:.*from (.*)"
msgstr "例：^新申請：.*由 (.*)"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "e.g. domain.com"
msgstr "例如domain.com"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. https://www.linkedin.com/in/..."
msgstr "例：https://www.linkedin.com/in/…"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "e.g. jobs"
msgstr "例：工作"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. <EMAIL>"
msgstr "例：<EMAIL>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. <EMAIL>"
msgstr "例：<EMAIL>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. mycompany.com"
msgstr "例：mycompany.com"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. sales-manager"
msgstr "例如：銷售經理"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
msgid "or"
msgstr "或"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "other application(s)"
msgstr "其他申請"
