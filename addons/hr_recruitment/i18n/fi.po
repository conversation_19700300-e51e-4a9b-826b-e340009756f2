# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <miu<PERSON><EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Anni Saarelainen, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Martin Trigaux, 2024
# <AUTHOR> <EMAIL>, 2024
# Konsta Aavaranta, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2025
# Jessica Jakara, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Jessica Jakara, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__accepted_applications_count
msgid "# Accepted Offers"
msgstr "# Hyväksytyt tarjoukset"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__applications_count
msgid "# Offers"
msgstr "# Lähetetyt tarjoukset"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__refused_applications_count
msgid "# Refused Offers"
msgstr "# Hylättyjä tarjouksia"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "1 Meeting"
msgstr "1 kokous"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Click to view</b> the application."
msgstr "<b>Klikkaa nähdäksesi</b> hakemuksen."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Did you apply by sending an email?</b> Check incoming applications."
msgstr ""
"<b>Haitko lähettämällä sähköpostia?</b> Tarkista saapuneet hakemukset."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "<b>Drag this card</b>, to qualify him for a first interview."
msgstr ""
"<b>Vedä tätä korttia</b>, jotta hakija hyväksytään ensimmäiseen "
"haastatteluun."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid ""
"<div><b>Try to send an email</b> to the applicant.</div><div><i>Tips: All "
"emails sent or received are saved in the history here</i>"
msgstr ""
"<div><b>Yritä lähettää</b> "
"hakijalle<b>sähköpostia</b>.</div><div><i>Vinkkejä: Kaikki lähetetyt tai "
"vastaanotetut sähköpostiviestit tallentuvat historiaan</i>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Yritys\" title=\"Yritys\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Documents\"/>"
msgstr "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Documents\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid ""
"<span class=\"mx-2\" style=\"padding-top: 1px; padding-bottom: "
"1px;\">to</span>"
msgstr ""
"<span class=\"mx-2\" style=\"padding-top: 1px; padding-bottom: "
"1px;\">osoitteeseen</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_text\">Työntekijä</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span class=\"o_stat_text\">Trackers</span>"
msgstr "<span class=\"o_stat_text\">Seurannat</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span invisible=\"address_id\" class=\"oe_read_only\">Remote</span>"
msgstr "<span invisible=\"address_id\" class=\"oe_read_only\">Etätyö</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"<span invisible=\"not is_warning_visible\">\n"
"                                <span class=\"fa fa-exclamation-triangle text-danger ps-3\">\n"
"                                </span>\n"
"                                <span class=\"text-danger\">\n"
"                                    All applications will lose their hired date and hired status.\n"
"                                </span>\n"
"                            </span>"
msgstr ""
"<span invisible=\"not is_warning_visible\">\n"
"                                <span class=\"fa fa-exclamation-triangle text-danger ps-3\">\n"
"                                </span>\n"
"                                <span class=\"text-danger\">\n"
"                                    Kaikki hakemukset menettävät palkkauspäivämääränsä ja palkatun asemansa.\n"
"                                </span>\n"
"                            </span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "<span invisible=\"not salary_expected_extra\"> + </span>"
msgstr "<span invisible=\"not salary_expected_extra\"> + </span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "<span invisible=\"not salary_proposed_extra\"> + </span>"
msgstr "<span invisible=\"not salary_proposed_extra\"> + </span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>New</span>"
msgstr "<span>Uusi</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Raportointi</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>View</span>"
msgstr "<span>Katso</span>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_congratulations
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,\n"
"                <br/><br/>\n"
"                We confirm we successfully received your application for the job\n"
"                \"<a t-att-href=\"hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''\" style=\"color:#9A6C8E;\"><strong t-out=\"object.job_id.name or ''\">Experienced Developer</strong></a>\" at <strong t-out=\"object.company_id.name or ''\">YourCompany</strong>.\n"
"                <br/><br/>\n"
"                We will come back to you shortly.\n"
"\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <t t-if=\"object.user_id\">\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Your Contact:</strong></h3>\n"
"                    <p>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                        <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    </p>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                </t>\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days.</strong><br/><br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hei,\n"
"               <br/><br/>\n"
"                Vahvistamme, että olemme vastaanottaneet hakemuksesi työpaikkaa varten\n"
"                \"<a t-att-href=\"hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''\" style=\"color:#9A6C8E;\"><strong t-out=\"object.job_id.name or ''\">Kokenut kehittäjä</strong></a>\" yrityksessä <strong t-out=\"object.company_id.name or ''\">YourCompany</strong>.\n"
"               <br/><br/>\n"
"                Otamme sinuun yhteyttä pian.\n"
"\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Työnkuvaus</a>\n"
"                </div>\n"
"\n"
"               <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <t t-if=\"object.user_id\">\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Yhteyshenkilösi:</strong></h3>\n"
"                    <p>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        <span>Sähköposti: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                        <span>Puhelin: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    </p>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                </t>\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>Mikä on seuraava askel?</strong></h3>\n"
"               <strong>Vastaamme hakemuksiin</strong> yleensä <strong>muutaman päivän kuluessa.</strong><br/><br/>\n"
"               <strong>Ota</strong> rohkeasti <strong>yhteyttä, jos haluat nopeamman vastauksen\n"
"                palautetta</strong> tai jos et saa meiltä uutisia\n"
"                tarpeeksi nopeasti (vastaa tähän sähköpostiin).\n"
"\n"
"               <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.street\">\n"
"                   <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                   <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.street2\">\n"
"                   <t t-out=\"object.job_id.address_id.street2 or ''\">A15, Santiago (RM)</t><br/>\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.city\">\n"
"                   <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                   <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.zip\">\n"
"                   <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"               <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                   <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentiina</t><br/>\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"               <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_interest
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"text-align: center\">\n"
"                <h2>Congratulations!</h2>\n"
"                <div style=\"color:grey;\">Your resume has been positively reviewed.</div>\n"
"            </div>\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                We just reviewed your resume, and it caught our\n"
"                attention. As we think you might be great for the\n"
"                position, your application has been short listed for a\n"
"                call or an interview.\n"
"                <br/><br/>\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <t t-if=\"object.user_id\">\n"
"                    You will soon be contacted by:<br/>\n"
"                    <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                    <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                    <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    <br/><br/>\n"
"                </t>\n"
"                See you soon,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    -- <br/>\n"
"                    The HR Team\n"
"                    <t t-if=\"'website_url' in object.job_id and hasattr(object.job_id, 'website_url') and object.job_id.website_url\">\n"
"                        Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.<br/>\n"
"                    </t>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days</strong>.\n"
"                <br/><br/>\n"
"                The next step is either a call or a meeting in our offices.\n"
"                <br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"                <br/>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"text-align: center\">\n"
"                <h2>Onnittelut!</h2>\n"
"                <div style=\"color:grey;\">Olemme lukeneet ansioluettelosi ja pitäneet näkemästämme.</div>\n"
"            </div>\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Katselmoimme juuri ansioluettelosi, ja se kiinnitti \n"
"                huomiomme. Uskomme, että voisit sopia hyvin\n"
"                tehtävään, hakemuksesi on valittu jatkoon\n"
"                soittoa tai haastattelua varten.\n"
"               <br/><br/>\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Tehtävän kuvaus</a>\n"
"                </div>\n"
"\n"
"               <t t-if=\"object.user_id\">\n"
"                    Sinuun ottaa pian yhteyttä:<br/>\n"
"                   <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                   <span>Sähköposti: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                   <span>Puhelin: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                   <br/><br/>\n"
"               </t>\n"
"                Nähdään pian,\n"
"               <div style=\"font-size: 11px; color: grey;\">\n"
"                    -- <br/>\n"
"                    HR-tiimi\n"
"                   <t t-if=\"'website_url' in object.job_id and hasattr(object.job_id, 'website_url') and object.job_id.website_url\">\n"
"                        Tutustu <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">kaikkiin työpaikkoihimme</a>.<br/>\n"
"                   </t>\n"
"               </div>\n"
"\n"
"               <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>Mikä on seuraava askel?</strong></h3>\n"
"               <strong>Vastaamme hakemuksiin</strong> yleensä <strong>muutaman päivän kuluessa</strong>.\n"
"               <br/><br/>\n"
"                Seuraava vaihe on joko puhelu tai tapaaminen toimistossamme.\n"
"               <br/>\n"
"               <strong>Ota</strong> rohkeasti <strong>yhteyttä, jos haluat nopeamman yhteydenoton\n"
"                palautetta</strong> tai jos et saa meiltä mitään uutisia\n"
"                tarpeeksi nopeasti (vastaa tähän sähköpostiin).\n"
"               <br/>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.street\">\n"
"                   <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                   <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.street2\">\n"
"                   <t t-out=\"object.job_id.address_id.street2 or ''\">A15, Santiago (RM)</t><br/>\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.city\">\n"
"                   <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                   <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"               <t t-if=\"object.job_id.address_id.zip\">\n"
"                   <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"               <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                   <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentiina</t><br/>\n"
"                   <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"               <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_not_interested
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Dear,<br/><br/>\n"
"                We would like to thank you for your interest and your time.<br/>\n"
"                We wish you all the best in your future endeavors.\n"
"                <br/><br/>\n"
"                Best<br/>\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Marc Demo</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team<br/>\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hyvä,<br/><br/>\n"
"                Haluamme kiittää kiinnostuksestasi ja ajastasi.<br/>\n"
"                Toivotamme kaikkea hyvää tulevissa pyrkimyksissä.\n"
"               <br/><br/>\n"
"                Terveisin<br/>\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                       <strong t-out=\"object.user_id.name or ''\">Marc Demo</strong><br/>\n"
"                        Sähköposti: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Puhelin: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                       <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        HR-tiimi<br/>\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_refuse
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                Thank you for your interest in joining the\n"
"                <b><t t-out=\"object.company_id.name or ''\">YourCompany</t></b> team.  We\n"
"                wanted to let you know that, although your resume is\n"
"                competitive, our hiring team reviewed your application\n"
"                and <b>did not select it for further consideration</b>.\n"
"                <br/><br/>\n"
"                Please note that recruiting is hard, and we can make\n"
"                mistakes. Do not hesitate to reply to this email if you\n"
"                think we made a mistake, or if you want more information\n"
"                about our decision.\n"
"                <br/><br/>\n"
"                We will, however, keep your resume on record and get in\n"
"                touch with you about future opportunities that may be a\n"
"                better fit for your skills and experience.\n"
"                <br/><br/>\n"
"                We wish you all the best in your job search and hope we\n"
"                will have the chance to consider you for another role\n"
"                in the future.\n"
"                <br/><br/>\n"
"                Thank you,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hei,<br/><br/>\n"
"                Kiitos kiinnostuksestasi liittyä\n"
"                <b><t t-out=\"object.company_id.name or ''\">YourCompany</t></b> tiimiin.  Me\n"
"                halusimme kertoa sinulle, että vaikka ansioluettelosi onkin\n"
"                kilpailukykyinen, mutta rekrytointitiimimme tutki hakemuksesi\n"
"               <b>eikä valinnut sitä jatkoharkintaan</b>.\n"
"               <br/><br/>\n"
"                Huomaa, että rekrytointi on vaikeaa, ja voimme tehdä\n"
"                virheitä. Älä epäröi vastata tähän sähköpostiin, jos olet\n"
"                luulet, että teimme virheen, tai jos haluat lisätietoja\n"
"                päätöksestämme.\n"
"               <br/><br/>\n"
"                Säilytämme kuitenkin ansioluettelosi ja otamme yhteyttä meihin\n"
"                yhteyttä sinuun tulevista mahdollisuuksista, jotka voivat olla sinulle sopivia\n"
"                sopivat paremmin taidoillesi ja kokemuksellesi.\n"
"               <br/><br/>\n"
"                Toivotamme sinulle kaikkea hyvää työnhaussa ja toivomme, että me..\n"
"                että saamme mahdollisuuden harkita sinua toiseen tehtävään\n"
"                tulevaisuudessa.\n"
"               <br/><br/>\n"
"                Kiitos,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                       <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        Sähköposti: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Puhelin: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                       <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        HR-tiimi\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python-kirjasto, joka evaluoidaan oletusarvoja varten, kun tätä aliasta "
"varten luodaan uusia tietueita."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__active
msgid "Active"
msgstr "Aktiivinen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_activities
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities"
msgstr "Toimenpiteet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_overdue
msgid "Activities Overdue"
msgstr "Erääntyneet toimet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_today
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities Today"
msgstr "Toimintaa tänään"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Toimenpiteen poikkeuksen tyyli"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_mail_activity_plan
msgid "Activity Plan"
msgstr "Toimintasuunnitelma"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_plan
msgid "Activity Plans"
msgstr "Toimintasuunnitelmat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_state
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_state
msgid "Activity State"
msgstr "Toimenpiteen tila"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_icon
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_type_icon
msgid "Activity Type Icon"
msgstr "Toimenpiteen ikoni"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_type_action_config_hr_applicant
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_type
msgid "Activity Types"
msgstr "Toimenpiteiden tyypit"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Language Test\", \"Prepare Offer\", ...)"
msgstr ""
"Toimintasuunnitelmien avulla voidaan määrittää luettelo toiminnoista vain muutamalla napsautuksella\n"
"                    (esim. \"Kielikoe\", \"Tarjouksen valmistelu\", ...)"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Toiminta-aikataulun suunnittelun ohjattu toiminto"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid "Add a new stage in the recruitment process"
msgstr "Lisää uusi vaihe rekrytointiprosessiin"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_category_action
msgid "Add a new tag"
msgstr "Lisää uusi tunniste"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_manager
msgid "Administrator"
msgstr "Ylläpitäjä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_id
msgid "Alias"
msgstr "Alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_contact
msgid "Alias Contact Security"
msgstr "Aliaksen kontaktien tietoturva"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain_id
msgid "Alias Domain"
msgstr "Alias Domain"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain
msgid "Alias Domain Name"
msgstr "Alias Verkkotunnus"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_full_name
msgid "Alias Email"
msgstr "Alias Email"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__alias_id
msgid "Alias ID"
msgstr "Alias ID"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_name
msgid "Alias Name"
msgstr "Aliaksen nimi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_status
msgid "Alias Status"
msgstr "Alias Status"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Aliaksen tila arvioituna viimeksi vastaanotetun viestin perusteella."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_model_id
msgid "Aliased Model"
msgstr "Aliasmalli"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__all_application_count
msgid "All Application Count"
msgstr "Kaikkien hakemusten määrä"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ_all_app
msgid "All Applications"
msgstr "Kaikki hakemukset"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Analysis"
msgstr "Analyysi"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_applicant_new
#: model:ir.model,name:hr_recruitment.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__applicant_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "Applicant"
msgstr "Hakija"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_degree
msgid "Applicant Degree"
msgstr "Hakijan tutkinto"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_emails
msgid "Applicant Emails"
msgstr "Hakijan sähköpostit"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_hired
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_hired
msgid "Applicant Hired"
msgstr "Hakija palkattu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__applicant_notes
msgid "Applicant Notes"
msgstr "Hakijan huomautukset"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__applicant_properties_definition
msgid "Applicant Properties"
msgstr "Hakijan ominaisuudet"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_stage_changed
msgid "Applicant Stage Changed"
msgstr "Hakijan tila muuttunut"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_new
msgid "Applicant created"
msgstr "Hakija luotu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_without_email
msgid "Applicant(s) not having email"
msgstr "Hakijat, joilla ei ole sähköpostia"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_tree_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_activity
msgid "Applicants"
msgstr "Hakijat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__applicant_hired
msgid "Applicants Hired"
msgstr "Palkatut hakijat"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Applicants and their attached résumé are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""
"Hakijat ja heidän liitteenä oleva ansioluettelonsa luodaan automaattisesti, kun sähköposti lähetetään.\n"
"                Jos asennat asiakirjojen hallintamoduulit, kaikki ansioluettelot indeksoidaan automaattisesti,\n"
"                jotta voit helposti etsiä niiden sisällöstä."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Application"
msgstr "Hakemus"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__application_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_count
msgid "Application Count"
msgstr "Hakemusten määrä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__application_status
msgid "Application Status"
msgstr "Hakemuksen tila"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Application email"
msgstr "Hakemuksen sähköpostiosoite"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Application in Progress"
msgstr "Käynnissä oleva hakemus"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_applications
#: model:ir.actions.act_window,name:hr_recruitment.crm_case_categ0_act_job
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__applicant_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_applications
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_view_tree_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Applications"
msgstr "Hakemukset"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_platform__email
msgid ""
"Applications received from this Email won't be linked to a contact.There "
"will be no email address set on the Applicant either."
msgstr ""
"Tästä sähköpostista vastaanotettuja hakemuksia ei yhdistetä yhteystietoon. "
"Hakijalle ei myöskään määritetä sähköpostiosoitetta."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_date
msgid "Applied on"
msgstr "Haettu"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_kanban_view.js:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Archive"
msgstr "Arkistoi"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__archived
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Archived"
msgstr "Arkistoitu"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_form_controller.js:0
msgid "Are you sure that you want to archive this job position?"
msgstr "Oletko varma, että haluat arkistoida tämän työpaikan?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_open
msgid "Assigned"
msgstr "Annettu tehtäväksi"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"At least one applicant doesn't have a email; you can't use send email "
"option."
msgstr ""
"Ainakin yhdellä hakijalla ei ole sähköpostia, joten et voi käyttää Lähetä "
"sähköpostia -vaihtoehtoa."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Attach a file"
msgstr "Liitä tiedosto"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_attachment_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__attachment_ids
msgid "Attachments"
msgstr "Liitteet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__author_id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__author_id
msgid "Author"
msgstr "Tekijä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__availability
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__availability
msgid "Availability"
msgstr "Saatavuus"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bachelor
msgid "Bachelor Degree"
msgstr "Kandidaatin tutkinto"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__is_blacklisted
msgid "Blacklist"
msgstr "Markkinointikielto"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "Markkinointikiellossa oleva puhelin on matkapuhelin"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "Markkinointikiellossa oleva puhelin on muu puhelin"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Blocked"
msgstr "Estetty"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__body_has_template_value
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Rungon sisältö on sama kuin mallissa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_bounce
msgid "Bounce"
msgstr "Viestin palautus"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position_interviewer
msgid "By Job Positions"
msgstr "Työtehtävien mukaan"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid ""
"By setting an alias to a job position, emails sent to this address create "
"applications automatically. You can even use multiple trackers to get "
"statistics according to the source of the application: LinkedIn, Monster, "
"Indeed, etc."
msgstr ""
"Asettamalla aliaksen työpaikalle, tähän osoitteeseen lähetetyt sähköpostit "
"luovat hakemuksia automaattisesti. Voit jopa käyttää useita seurantoja "
"saadaksesi tilastoja hakemuksen lähteen mukaan: LinkedIn, Monster, Indeed "
"jne."

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_calendar_event
msgid "Calendar Event"
msgstr "Kalenteritapahtuma"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__can_edit_body
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__can_edit_body
msgid "Can Edit Body"
msgstr "Voi muokata runkoa"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Cancel"
msgstr "Peruuta"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__candidate_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__candidate_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__candidate_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Candidate"
msgstr "Ehdokas"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__user_id
msgid "Candidate Manager"
msgstr "Hakijan päällikkö"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_company__candidate_properties_definition
msgid "Candidate Properties"
msgstr "Ehdokkaan ominaisuudet"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Candidate's Name"
msgstr "Ehdokkaan nimi"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__candidate_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_candidate
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Candidates"
msgstr "Ehdokkaat"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__similar_candidates_count
msgid "Candidates with the same email or phone or mobile"
msgstr "Ehdokkaat, joilla on sama sähköpostiosoite, puhelin tai matkapuhelin"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_name
msgid "Candidates's Name"
msgstr "Ehdokkaan nimi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_graph_view_job
msgid "Cases By Stage and Estimates"
msgstr "Tapaukset vaiheiden ja arvioiden mukaan"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_category
msgid "Category of applicant"
msgstr "Hakijan ryhmä"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Choose an application email."
msgstr "Valitse hakemuksen sähköposti."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__color
msgid "Color Index"
msgstr "Väri-indeksi"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__company_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Company"
msgstr "Yritys"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_configuration
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Configuration"
msgstr "Asetukset"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_congratulations
msgid "Confirmation email sent to all new job applications"
msgstr "Kaikille uusille työhakemuksille lähetetään vahvistussähköposti"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_id
msgid "Contact"
msgstr "Kontakti"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Contact Email"
msgstr "Kontaktin sähköpostiosoite"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_view_search_inherit_hr_recruitment
msgid "Content"
msgstr "Sisältö"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__body
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__body
msgid "Contents"
msgstr "Sisältö"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job4
msgid "Contract Proposal"
msgstr "Sopimusehdotus"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job5
msgid "Contract Signed"
msgstr "Työsopimus allekirjoitettu"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Copy this email address, to paste it in your email composer, to apply."
msgstr ""
"Kopioi tämä sähköpostiosoite, liitä sen sähköpostimuokkaimeen, ja hae."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Tämän yhteystiedon bounced-tilassa olevien sähköpostien laskuri"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create"
msgstr "Luo"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Create Employee"
msgstr "Luo työntekijä"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.create_job_simple
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create a Job Position"
msgstr "Luo tehtävänimike"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid "Create a Recruitment Activity Plan"
msgstr "Luo rekrytointisuunnitelma"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "Create a new rule to process emails from specific job boards."
msgstr ""
"Luo uusi sääntö, joka käsittelee tiettyjen työpaikkataulujen sähköposteja."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Create your first Job Position."
msgstr "Luo ensimmäinen työpaikka."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_date
msgid "Created on"
msgstr "Luotu"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Creation Date"
msgstr "Luontipäivä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mukautettu palautettu viesti"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__date_from
msgid "Date From"
msgstr "Alkupäivä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__date_to
msgid "Date To"
msgstr "Päättymispäivä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_close
msgid "Days to Close"
msgstr "Päivää sulkemiseen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_open
msgid "Days to Open"
msgstr "Päiviä avaamiseen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_defaults
msgid "Default Values"
msgstr "Oletusarvot"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid ""
"Define a regex: Extract the applicant's name from the email's subject or "
"body."
msgstr "Määritä regex: Poimi hakijan nimi sähköpostin otsikosta tai rungosta."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"Define a specific contact address for this job position. If you keep it "
"empty, the default email address will be used which is in human resources "
"settings"
msgstr ""
"Määrittele erillinen kontaktointiosoite tälle työtehtävälle. Jos tämä tietue"
" jätetään tyhjäksi, käytetään henkilöstöhallinnon asetuksista löytyvää "
"oletussähköpostiosoitetta"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
msgid ""
"Define here your stages of the recruitment process, for example:\n"
"                    qualification call, first interview, second interview, refused,\n"
"                    hired."
msgstr ""
"Määrittele tässä esimerkiksi rekrytointiprosessin vaiheet:\n"
"                    karsintakutsu, ensimmäinen haastattelu, toinen haastattelu, kieltäytyminen,\n"
"                    työsopimus."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__type_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_tree
msgid "Degree"
msgstr "Tutkintotaso"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__name
msgid "Degree Name"
msgstr "Tutkinnon nimi"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_degree_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_degree
msgid "Degrees"
msgstr "Tutkintotasot"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__delay_close
msgid "Delay to Close"
msgstr "Sulkemisen viive"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Delete"
msgstr "Poista"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_department
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__department_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Department"
msgstr "Osasto"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__manager_id
msgid "Department Manager"
msgstr "Osastopäällikkö"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_department
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_department
msgid "Departments"
msgstr "Osastot"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__name
msgid "Description"
msgstr "Kuvaus"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Details"
msgstr "Tiedot"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_digest_digest
msgid "Digest"
msgstr "Yhteenveto"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Digitize your résumé to extract name and email automatically."
msgstr ""
"Digitoi ansioluettelosi ja poimi nimi ja sähköpostiosoite automaattisesti."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Directly"
msgstr "Suoraan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Directly Available"
msgstr "Suoraan saatavilla"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Discard"
msgstr "Hylkää"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_applicant_cv_display
msgid "Display CV on application form"
msgstr "Näytä ansioluettelo hakemuslomakkeessa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Display résumé on application form"
msgstr "Näytä ansioluettelo hakemuslomakkeessa"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Sinulla ei ole oikeutta tähän toimintoon. Et voi käyttää tätä tietoa "
"käyttäjien  yhteenvetopostissa"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bac5
msgid "Doctoral Degree"
msgstr "Tohtorin tutkinto"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__documents_count
msgid "Document Count"
msgstr "Asiakirjojen määrä"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__document_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Documents"
msgstr "Dokumentit"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_1
msgid "Does not fit the job requirements"
msgstr "Ei vastaa työn vaatimuksia"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid ""
"Don't forget to specify the department if your recruitment process\n"
"                is different according to the job position."
msgstr ""
"Älä unohda määritellä osastoa, jos rekrytointiprosessisi\n"
"                on erilainen työtehtävän mukaan."

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_6
msgid "Duplicate"
msgstr "Kopioi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__duplicates
msgid "Duplicates"
msgstr "Kaksoiskappaleet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__duplicates_count
msgid "Duplicates Count"
msgstr "Kaksoiskappaleiden määrä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__email
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Email"
msgstr "Sähköposti"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email Alias"
msgstr "Sähköpostialias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__template_id
msgid "Email Template"
msgstr "Sähköpostin mallipohja"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_id
msgid ""
"Email alias for this job position. New emails will automatically create new "
"applicants for this job position."
msgstr ""
"Sähköpostialias tälle työtehtävälle. Uudet sähköpostit luovat "
"automaattisesti uuden hakijan tähän työtehtävään."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_cc
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_cc
msgid "Email cc"
msgstr "Sähköpostin kopio"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"Sähköpostin verkkotunnus esim. 'example.com' kohdassa '<EMAIL>'"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid "Email template must be selected to send a mail"
msgstr "Sähköpostimallin on oltava valittuna sähköpostin lähettämistä varten"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_platforms
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_emails
msgid "Emails"
msgstr "Sähköpostit"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.model,name:hr_recruitment.model_hr_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__employee_id
msgid "Employee"
msgstr "Työntekijä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__emp_is_active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__emp_is_active
msgid "Employee Active"
msgstr "Työntekijä aktiivinen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__employee_name
msgid "Employee Name"
msgstr "Employee Name"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_hired_template
msgid "Employee created:"
msgstr "Työntekijä luotu:"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__employee_id
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__employee_id
msgid "Employee linked to the candidate."
msgstr "Ehdokkaaseen liittyvä työntekijä."

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_employees
msgid "Employees"
msgstr "Työntekijät"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_contract_type
msgid "Employment Types"
msgstr "Työsuhdetyypit"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__priority
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__priority
msgid "Evaluation"
msgstr "Arviointi"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__3
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__3
msgid "Excellent"
msgstr "Erinomainen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Expected"
msgstr "Odotettu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__expected_employee
msgid "Expected Employee"
msgstr "Odotettu työntekijä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Expected Salary Extra"
msgstr "Odotettu palkanlisä"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Extended Filters"
msgstr "Laajennetut suodattimet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__extended_interviewer_ids
msgid "Extended Interviewer"
msgstr "Laajennettu haastattelija"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__favorite_user_ids
msgid "Favorite User"
msgstr "Suosikkikäyttäjä"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Kenttä, jota käytetään puhdistetun puhelinnumeron tallentamiseen. Auttaa "
"nopeuttamaan hakuja ja vertailuja."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "File"
msgstr "Tiedosto"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job2
msgid "First Interview"
msgstr "Ensimmäinen haastattelu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__fold
msgid "Folded in Kanban"
msgstr "Laskostettu Kanbanisa"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_recruitment_stage_kanban
msgid "Folded in Recruitment Pipe:"
msgstr "Taitettu rekrytointiputkessa:"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_follower_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_partner_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_type_icon
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome -ikoni esim.. fa-tasks"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Future Activities"
msgstr "Tulevat toimenpiteet"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Generate Email"
msgstr "Luo sähköposti"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_get_refuse_reason
msgid "Get Refuse Reason"
msgstr "Hae hylkäämisen syy"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__1
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__1
msgid "Good"
msgstr "Hyvä"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_graduate
msgid "Graduate"
msgstr "Valmistunut"

#. module: hr_recruitment
#: model_terms:web_tour.tour,rainbow_man_message:hr_recruitment.hr_recruitment_tour
msgid "Great job! You hired a new colleague!"
msgstr "Hienoa! Palkkasit uuden kollegan!"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__done
msgid "Green"
msgstr "Vihreä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_done
msgid "Green Kanban Label"
msgstr "Vihreä kanban otsikko"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__normal
msgid "Grey"
msgstr "Harmaa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Harmaa kanban otsikko"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__group_applicant_cv_display
msgid "Group Applicant Cv Display"
msgstr "Hakijaryhmän ansioluetteloiden näyttö"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__has_domain
msgid "Has Domain"
msgstr "Sisältää tunnusalueen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__has_message
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid ""
"Have you tried to <a>add skills to your job position</a> and search into the"
" Reserve ?"
msgstr ""
"Oletko yrittänyt <a>lisätä osaamista työtehtävään</a> ja hakea varalla "
"olevista ?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_closed
msgid "Hire Date"
msgstr "Palkkauspäivä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__no_of_hired_employee
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__hired
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Hired"
msgstr "Palkattu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid "Hired Stage"
msgstr "Palkkausvaihe"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Hiring Date"
msgstr "Palkkaamispäivä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Ylätason aliastietueen id (esim. tehtävien luontiin käytettävän aliaksen "
"sisältävä projekti)"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_icon
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_exception_icon
msgid "Icon"
msgstr "Kuvake"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_icon
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kuvake joka kertoo poikkeustoiminnosta."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_sms_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid ""
"If checked, this stage is used to determine the hire date of an applicant"
msgstr ""
"Jos valittu, vaihetta käytetään hakijan palkkaamispäivän määrittämiseen"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__template_id
msgid ""
"If set, a message is posted on the applicant using the template when the "
"applicant is set to the stage."
msgstr ""
"Jos asetettu, hakijalle lähetetään viesti viestipohjaa käyttäen kun hakija "
"siirtyy tähän vaiheeseen."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Jos asetettu, lähetetään automaattisesti luvattomille käyttäjille "
"oletusviestin sijasta."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__emp_is_active
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__emp_is_active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Jos aktiivisen kentän arvo on epätosi (false), voit piilottaa resurssin "
"poistamatta sitä."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__active
msgid ""
"If the active field is set to false, it will allow you to hide the case "
"without removing it."
msgstr ""
"Jos \"aktiivinen\"-kentän arvoksi asetetaan epätosi (false), tietueen voi "
"piilottaa poistamatta sitä."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Jos sähköpostiosoite on markkinointikiellossa, yhteystieto ei vastaanota "
"postituksia miltään postituslistalta."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"Jos puhdistettu puhelinnumero on mustalla listalla, yhteyshenkilö ei enää "
"saa massatekstiviestejä miltään listalta"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "In Progress"
msgstr "Käynnissä"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "In-App Purchases"
msgstr "Sovellusten sisäiset ostot"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid ""
"Incoming emails create applications automatically. Use it for direct "
"applications or when posting job offers on LinkedIn, Monster, etc."
msgstr ""
"Saapuvat sähköpostit luovat hakemuksia automaattisesti. Käytä sitä suoriin "
"hakemuksiin tai kun julkaiset työtarjouksia LinkedInissä, Monsterissa jne."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Ilmaisee, onko mustalle listalle joutunut puhdistettu puhelinnumero "
"matkapuhelinnumero. Auttaa erottamaan, mikä numero on mustalla listalla, kun"
" mallissa on sekä matkapuhelin- että puhelinkenttä."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"Ilmaisee, onko mustalle listalle merkitty puhdistettu puhelinnumero "
"puhelinnumero. Auttaa erottamaan, mikä numero on mustalla listalla, kun "
"mallissa on sekä matkapuhelin- että puhelinkenttä."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__industry_id
msgid "Industry"
msgstr "Toimiala"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job1
msgid "Initial Qualification"
msgstr "Alustava valinta"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Internal notes..."
msgstr "Sisäiset muistiinpanot..."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_survey
msgid "Interview Forms"
msgstr "Haastattelulomakkeet"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_interviewer
msgid "Interviewer"
msgstr "Haastattelija"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__interviewer_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__interviewer_ids
msgid "Interviewers"
msgstr "Haastattelijat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__is_mail_template_editor
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__is_mail_template_editor
msgid "Is Editor"
msgstr "On muokkaaja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__is_favorite
msgid "Is Favorite"
msgstr "On suosikki"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_is_follower
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__is_warning_visible
msgid "Is Warning Visible"
msgstr "Onko varoitus näkyvissä"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__date_from
msgid ""
"Is set, update candidates availability once hired for that specific mission."
msgstr ""
"On asetettu, päivitä ehdokkaiden saatavuus, kun heidät on palkattu kyseiseen"
" tehtävään."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""
"JSON, joka kartoittaa many2one-kentän tunnukset käytetyiksi sekunneiksi"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Job"
msgstr "Tehtävä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_pivot_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Job Applications"
msgstr "Työhakemukset"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_boards
msgid "Job Boards"
msgstr "Työpaikkataulut"

#. module: hr_recruitment
#: model:utm.campaign,title:hr_recruitment.utm_campaign_job
msgid "Job Campaign"
msgstr "Työpaikkakampanja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__address_id
msgid "Job Location"
msgstr "Työn sijainti"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job_platform
msgid "Job Platforms"
msgstr "Työfoorumit"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__job_id
msgid "Job Position"
msgstr "Tehtävänimike"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_department_new
msgid "Job Position Created"
msgstr "Työpaikka Luotu"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_job_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_new
msgid "Job Position created"
msgstr "Työpaikka luotu"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_config
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_interviewer
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_jobs
msgid "Job Positions"
msgstr "Työtehtävät"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Job Posting"
msgstr "Työpaikkailmoitus"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_company__job_properties_definition
msgid "Job Properties"
msgstr "Työn ominaisuudet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Job Specific"
msgstr "Työpaikkakohtainen"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_5
msgid "Job already fulfilled"
msgstr "Työ on jo täytetty"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Jobs"
msgstr "Tehtävät"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Jobs - Recruitment Form"
msgstr "Työtehtävät - työnhakulomake"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_blocked
msgid "Kanban Blocked"
msgstr "Kanban estetty"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_normal
msgid "Kanban Ongoing"
msgstr "Kanban käynnissä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__kanban_state
msgid "Kanban State"
msgstr "Kanban-tila"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_done
msgid "Kanban Valid"
msgstr "Kanban voimassa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues_value
msgid "Kpi Hr Recruitment New Colleagues Value"
msgstr "KPI HR Rekrytointi Uuden kollegan arvo"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__lang
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__lang
msgid "Language"
msgstr "Kieli"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Last Meeting"
msgstr "Viimeisin kokous"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__last_stage_id
msgid "Last Stage"
msgstr "Viimeinen vaihe"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Last Stage Update"
msgstr "Vaihe päivittynyt viimeksi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Late Activities"
msgstr "Myöhässä olevat toimenpiteet"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Let's create a job position."
msgstr "Luodaan työpaikka."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid ""
"Let's create the position. An email will be setup for applications, and a "
"public job description, if you use the Website app."
msgstr ""
"Luodaan asema. Hakemuksia varten perustetaan sähköposti ja julkinen "
"työnkuvaus, jos käytät verkkosivustosovellusta."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let's have a look at how to <b>improve</b> your <b>hiring process</b>."
msgstr ""
"Katsotaanpa, miten voit <b>parantaa</b> <b>rekrytointiprosessiasi</b>."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let’s create this new employee now."
msgstr "Luodaan uusi työntekijä nyt."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Let’s go back to the dashboard."
msgstr "Mennään takaisin työpöydälle."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__linkedin_profile
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__linkedin_profile
msgid "LinkedIn Profile"
msgstr "LinkedIn-profiili"

#. module: hr_recruitment
#: model:ir.actions.server,name:hr_recruitment.action_load_demo_data
msgid "Load demo data"
msgstr "Lataa Demodata"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
msgid "Load sample data"
msgstr "Lataa näytetiedot"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Load template"
msgstr "Lataa malli"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Paikallisiin osiin perustuva saapuvien lähetysten havaitseminen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__template_id
msgid "Mail Template"
msgstr "Viestipohja"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_main_attachment_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pääliitetiedosto"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Manager"
msgstr "Päällikkö"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_licenced
msgid "Master Degree"
msgstr "Maisterin tutkinto"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__medium_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__medium_id
msgid "Medium"
msgstr "Media"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_mediums
msgid "Mediums"
msgstr "Mediat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_display_date
msgid "Meeting Display Date"
msgstr "Kokouksen näyttöpäivämäärä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_text
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_display_text
msgid "Meeting Display Text"
msgstr "Kokousnäytön teksti"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__meeting_ids
msgid "Meetings"
msgstr "Tapaamiset"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_ir_ui_menu
msgid "Menu"
msgstr "Valikko"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Mission Dates"
msgstr "Operaation päivämäärät"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Toimenpiteeni määräaika"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "My Applications"
msgstr "Omat hakemukset"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "My Candidates"
msgstr "Omat ehdokkaani"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_job_filter_recruitment
msgid "My Favorites"
msgstr "Omat suosikkini"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_search_view
msgid "My Job Positions"
msgstr "Omat työpaikkani"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_tree
msgid "Name"
msgstr "Nimi"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "New"
msgstr "Uusi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_applicant_count
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_new
msgid "New Applicant"
msgstr "Uusi hakija"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "New Applicants"
msgstr "Uudet hakijat"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_new_application
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__new_application_count
msgid "New Application"
msgstr "Uusi hakemus"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_from_department
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "New Applications"
msgstr "Uudet hakemukset"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues
msgid "New Employees"
msgstr "Uudet työntekijät"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_hired_employee
msgid "New Hired Employee"
msgstr "Uusi palkattu työntekijä"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_tree_activity
msgid "Next Activities"
msgstr "Seuraavat toimenpiteet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Seuraavan toimenpiteen kalenterimerkintä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_date_deadline
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Seuraavan toimenpiteen eräpäivä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_summary
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_summary
msgid "Next Activity Summary"
msgstr "Seuraavan toimenpiteen kuvaus"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_type_id
msgid "Next Activity Type"
msgstr "Seuraavan toimenpiteen tyyppi"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Next Meeting"
msgstr "Seuraava kokous"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "No Meeting"
msgstr "Ei kokousta"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "No application found. Let's create one !"
msgstr "Hakemusta ei löytynyt. Luodaan sellainen !"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid "No applications yet"
msgstr "Ei vielä hakemuksia"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_candidate
msgid "No candidates yet"
msgstr "Ei vielä ehdokkaita"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_type_action_config_hr_applicant
msgid "No data to display"
msgstr "Ei näytettävää dataa"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_action_analysis
msgid "No data yet!"
msgstr "Ei vielä tietoja!"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "No rules have been defined."
msgstr "Sääntöjä ei ole määritelty."

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__0
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__0
msgid "Normal"
msgstr "Normaali"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_normalized
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__email_normalized
msgid "Normalized Email"
msgstr "Normalisoitu sähköposti"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Note"
msgstr "Muistiinpano"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction_counter
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_number
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__attachment_count
msgid "Number of Attachments"
msgstr "Liitteiden lukumäärä"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__new_application_count
msgid ""
"Number of applications that are new in the flow (typically at first step of "
"the flow)"
msgstr "Uusien hakemusten määrä ketjussa (yleensä ensimmäisessä vaiheessa)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__delay_close
msgid "Number of days to close"
msgstr "Sulkemiseen tarvittavien päivien määrä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error_counter
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__no_of_hired_employee
msgid ""
"Number of hired employees for this job position during recruitment phase."
msgstr ""
"Tähän työtehtävään palkattujen työntekijöiden määrä rekrytointivaiheessa."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction_counter
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error_counter
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Odoo auttaa sinua seuraamaan hakijoita rekrytoinnissa\n"
"                ja organisoimaan kaikkia tähän liittyviä toimintoja: tapaamisia, haastatteluja jne."

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_user
msgid "Officer: Manage all applicants"
msgstr "Esimies: Hallinnoi kaikkia hakijoita"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__old_application_count
msgid "Old Application"
msgstr "Vanha hakemus"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__ongoing
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Ongoing"
msgstr "Jatkuva"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Online Posting"
msgstr "Verkkoilmoitus"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Operation not supported"
msgstr "Toiminto ei ole tuettu"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Valinnainen sen viestiketjun (tietueen) ID, johon kaikki saapuvat viestit "
"liitetään, vaikka niihin ei vastattaisikaan. Jos tämä asetetaan, uusien "
"tietueiden luominen estetään kokonaan."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_applicant_send_mail__lang
#: model:ir.model.fields,help:hr_recruitment.field_candidate_send_mail__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Valinnainen käännöskieli (ISO-koodi), joka valitaan sähköpostia "
"lähetettäessä. Jos sitä ei ole asetettu, käytetään englanninkielistä "
"versiota. Tämän pitäisi yleensä olla sijoitusilmaus, joka antaa sopivan "
"kielen, esim. {{ object.partner_id.lang }}."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Or talk about this applicant privately with your colleagues."
msgstr "Tai puhu tästä hakijasta yksityisesti kollegojesi kanssa."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Other Applications"
msgstr "Muut hakemukset"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__other_applications_count
msgid "Other Applications Count"
msgstr "Muiden hakemusten määrä"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other applications"
msgstr "Muut hakemukset"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other benefits"
msgstr "Muut edut"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_model_id
msgid "Parent Model"
msgstr "Ylätason malli"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Ylätason keskustelun ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Aliaksen hallussa oleva emomalli. Alias-viittauksen sisältävä malli ei "
"välttämättä ole alias_model_id:llä annettu malli (esimerkki: project "
"(parent_model) ja task (model))"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_name
msgid "Partner Name"
msgstr "Kumppanin nimi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_phone
msgid "Phone"
msgstr "Puhelin"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "Puhelin on markkinointikiellossa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_mobile_search
msgid "Phone/Mobile"
msgstr "Puhelin / matkapuhelin"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "Please provide an candidate name."
msgstr "Anna ehdokkaan nimi."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Käytäntö, jolla lähetetään viesti asiakirjaan mailgatewayn avulla.\n"
"- everyone: kaikki voivat lähettää viestin\n"
"- partners: vain todennetut kumppanit\n"
"- seuraajat: vain asiaan liittyvän asiakirjan seuraajat tai seuraavien kanavien jäsenet\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__probability
msgid "Probability"
msgstr "Todennäköisyys"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Process"
msgstr "Käsittele"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__applicant_properties
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__candidate_properties
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__job_properties
msgid "Properties"
msgstr "Ominaisuudet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Proposed"
msgstr "Ehdotettu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Proposed Salary Extra"
msgstr "Ehdotettu palkan lisä"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Provide an email"
msgstr "Anna sähköpostiosoite"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Publish job offers on your website"
msgstr "Julkaise työtarjouksia verkkosivustollasi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__rating_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__rating_ids
msgid "Ratings"
msgstr "Arviointi"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment_stage.py:0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Ready for Next Stage"
msgstr "Valmis seuraavaan vaiheeseen"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Ready to recruit more efficiently?"
msgstr "Oletko valmis rekrytoimaan tehokkaammin?"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Reason"
msgstr "Syy"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Tietueen keskustelun ID"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__user_id
msgid "Recruiter"
msgstr "Rekrytoija"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_root
#: model_terms:ir.ui.view,arch_db:hr_recruitment.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment"
msgstr "Rekrytointi"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_job_stage_act
msgid "Recruitment / Applicants Stages"
msgstr "Rekrytointi / Hakijoiden vaiheet"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_analysis
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_report_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Recruitment Analysis"
msgstr "Rekrytoinnin analyysi"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_plan_action_config_hr_applicant
msgid "Recruitment Plans"
msgstr "Rekrytointisuunnitelmat"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "Rekrytointivaiheet"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_congratulations
msgid "Recruitment: Application Acknowledgement"
msgstr "Rekrytointi: Hakemuksen kuittaus"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_interest
msgid "Recruitment: Interest"
msgstr "Rekrytointi: kiinnostunut"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_not_interested
msgid "Recruitment: Not interested anymore"
msgstr "Rekrytointi: ei enää kiinnostunut"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_refuse
msgid "Recruitment: Refuse"
msgstr "Rekrytointi: Hylätty"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__blocked
msgid "Red"
msgstr "Punainen"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Punainen kanban otsikko"

#. module: hr_recruitment
#: model:ir.actions.server,name:hr_recruitment.ir_actions_server_refuse_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Refuse"
msgstr "Hylkää"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_date
msgid "Refuse Date"
msgstr "Hylkääminen päivä"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: model:ir.actions.act_window,name:hr_recruitment.applicant_get_refuse_reason_action
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__refuse_reason_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_reason_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Refuse Reason"
msgstr "Kieltäytyminen syy"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_refuse_reason
msgid "Refuse Reason of Applicant"
msgstr "Hakijan kieltäytymisen syy"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_refuse_reason_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_applicant_refuse_reason
msgid "Refuse Reasons"
msgstr "Kieltäytyminen syyt"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Refuse the"
msgstr "Kieltäydy"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__refused
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Refused"
msgstr "Hylätty"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"Refused automatically because this application has been identified as a "
"duplicate of %(link)s"
msgstr ""
"Hylätään automaattisesti, koska tämä hakemus on tunnistettu "
"kaksoiskappaleeksi hakemuksesta %(link)s"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_2
msgid "Refused by applicant: job fit"
msgstr "Hakija on hylännyt hakemuksen: työhön sopivuus"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_8
msgid "Refused by applicant: salary"
msgstr "Hakija on kieltäytynyt: palkka"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_platform__regex
msgid "Regex"
msgstr "Säännöllinen lauseke"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Remote"
msgstr "Etänä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__render_model
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__render_model
msgid "Rendering Model"
msgstr "Renderöintimalli"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.report_hr_recruitment
msgid "Reporting"
msgstr "Raportointi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__requirements
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Requirements"
msgstr "Vaatimukset"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__activity_user_id
msgid "Responsible User"
msgstr "Vastuuhenkilö"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Restore"
msgstr "Palauta"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Resume's content"
msgstr "Ansioluettelon sisältö"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Running Applicants"
msgstr "Käynnissä olevat hakijat"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Résumé Digitization (OCR)"
msgstr "Ansioluettelon digitointi (OCR)"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Résumé Display"
msgstr "Ansioluettelon näyttö"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_sms_error
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected
msgid "Salary Expected by Applicant"
msgstr "Hakijan pyytämä palkka"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Salary Expected by Applicant, extra advantages"
msgstr "Hakijan odottama palkka, myös muut edut"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Salary Package"
msgstr "Palkkapaketti"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Salary Proposed by the Organisation"
msgstr "Organisaation ehdottama palkka"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Salary Proposed by the Organisation, extra advantages"
msgstr "Organisaation ehdottama palkka, myös muut hyödyt"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__phone_sanitized
msgid "Sanitized Number"
msgstr "Puhdistettu numero"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone_sanitized
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__partner_phone_sanitized
msgid "Sanitized Phone Number"
msgstr "Puhdistettu puhelinnumero"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Save it!"
msgstr "Tallenna se!"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Schedule Interview"
msgstr "Ajoita haastattelu"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Search Applicants"
msgstr "Etsi hakijoita"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_view_search
msgid "Search Source"
msgstr "Etsi lähde"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job3
msgid "Second Interview"
msgstr "Toinen haastattelu"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__address_id
msgid ""
"Select the location where the applicant will work. Addresses listed here are"
" defined on the company's contact information."
msgstr ""
"Valitse paikka, jossa hakija työskentelee. Tässä luetellut osoitteet "
"määritellään yrityksen yhteystiedoissa."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Send"
msgstr "Lähetä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_extract
msgid "Send CV to OCR to fill applications"
msgstr "Lähetä CV OCR:lle hakemusten täyttämiseksi"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model:ir.actions.server,name:hr_recruitment.action_applicant_send_mail
#: model:ir.actions.server,name:hr_recruitment.action_candidate_send_mail
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__send_mail
msgid "Send Email"
msgstr "Lähetä sähköposti"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send Interview Survey"
msgstr "Lähetä haastattelukysely"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send SMS"
msgstr "Lähetä tekstiviesti"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Send an Interview Survey to the applicant during the recruitment process"
msgstr "Lähetä hakijalle haastattelukysely rekrytointiprosessin aikana"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_send_mail
msgid "Send mails to applicants"
msgstr "Lähetä sähköpostia hakijoille"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_candidate_send_mail
msgid "Send mails to candidates"
msgstr "Lähetä sähköpostia ehdokkaille"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "Lähetä teksti kontakteillesi"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "Send your email. Followers will get a copy of the communication."
msgstr "Lähetä sähköpostia. Seuraajat saavat kopion viestistä."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_interest
msgid ""
"Set this template to a recruitment stage to send it when applications reach "
"that stage"
msgstr ""
"Aseta tämä malli rekrytointivaiheeseen ja lähetä se, kun hakemukset "
"saavuttavat kyseisen vaiheen"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_configuration
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_global_settings
msgid "Settings"
msgstr "Asetukset"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Show all records which has next action date is before today"
msgstr "Näytä kaikki tietueet joissa on toimenpide myöhässä"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_form
msgid "Similar Candidates"
msgstr "Vastaavat ehdokkaat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__similar_candidates_count
msgid "Similar Candidates Count"
msgstr "Samankaltaisten ehdokkaiden määrä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__single_applicant_email
msgid "Single Applicant Email"
msgstr "Yhden hakijan sähköpostiosoite"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Some values do not exist in the application status"
msgstr "Joitakin arvoja ei ole sovelluksen tilassa"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__source_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__source_id
msgid "Source"
msgstr "Lähde"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "Hakemusten lähde"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_sources
msgid "Sources"
msgstr "Lähteet"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Sources of Applicants"
msgstr "Hakemusten lähteet"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Sourcing"
msgstr "Hankinta"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_7
msgid "Spam"
msgstr "Roskaposti"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Specific jobs that use this stage. Other jobs will not use this stage."
msgstr ""
"Tätä vaihetta käyttävät erityistyöt. Muut työt eivät käytä tätä vaihetta."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage"
msgstr "Vaihe"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_stage_changed
msgid "Stage Changed"
msgstr "Vaihe muutettu"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage Definition"
msgstr "Vaiheen määrittely"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__name
msgid "Stage Name"
msgstr "Vaiheen nimi"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_stage_changed
msgid "Stage changed"
msgstr "Vaihe muutettu"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__last_stage_id
msgid ""
"Stage of the applicant before being in the current stage. Used for lost "
"cases analysis."
msgstr ""
"Hakijan edeltävä vaihe ennen nykyistä vaihetta. Käytetään analysoidessa "
"hävittyjä hakemuksia."

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_stage_act
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_stage
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_tree
msgid "Stages"
msgstr "Vaiheet"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_state
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tila aktiviteetin perusteella\n"
"Myöhässä: Eräpäivä on menneisyydessä\n"
"Tänään: Eräpäivä on tänään\n"
"Suunniteltu: Tulevaisuudessa."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__duration_tracking
msgid "Status time"
msgstr "Tila-aika"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__subject
#: model:ir.model.fields,field_description:hr_recruitment.field_candidate_send_mail__subject
msgid "Subject"
msgstr "Aihe"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__name
msgid "Tag Name"
msgstr "Tunnisteen nimi"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_applicant_category_name_uniq
msgid "Tag name already exists!"
msgstr "Tunniste on jo olemassa!"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_category_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__categ_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__categ_ids
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_category_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Tags"
msgstr "Tunnisteet"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_job_platform_email_uniq
msgid ""
"The Email must be unique, this one already corresponds to another Job "
"Platform."
msgstr ""
"Sähköpostin on oltava yksilöllinen, tämä vastaa jo toista "
"rekrytointialustaa."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__interviewer_ids
msgid ""
"The Interviewers set on the job position can see all Applicants in it. They "
"have access to the information, the attachments, the meeting management and "
"they can refuse him. You don't need to have Recruitment rights to be set as "
"an interviewer."
msgstr ""
"Työpaikan haastattelijoille asetetut hakijat näkevät kaikki siinä olevat "
"hakijat. Heillä on pääsy tietoihin, liitteisiin, kokouksen hallintaan ja he "
"voivat kieltäytyä hakijasta. Sinulla ei tarvitse olla rekrytointioikeuksia "
"ollaksesi asetettu haastattelijaksi."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__user_id
msgid ""
"The Recruiter will be the default value for all Applicants in this job"
"             position. The Recruiter is automatically added to all meetings "
"with the Applicant."
msgstr ""
"Rekrytoija on oletusarvo kaikille tämän työpaikan hakijoille. Rekrytoija "
"lisätään automaattisesti kaikkiin tapaamisiin hakijan kanssa."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_campaign.py:0
msgid ""
"The UTM campaign '%s' cannot be deleted as it is used in the recruitment "
"process."
msgstr ""
"UTM-kampanjaa \"%s\" ei voi poistaa, koska sitä käytetään "
"rekrytointiprosessissa."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid ""
"The candidate is linked to an employee, to avoid losing information, archive"
" it instead."
msgstr ""
"Ehdokas on sidoksissa työntekijään, arkistoi se sen sijaan, jotta tietoja ei"
" menetettäisi."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__availability
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__availability
msgid "The date at which the applicant will be available to start working"
msgstr "Päivämäärä, jolloin hakija on käytettävissä työn aloittamiseen"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_send_mail.py:0
msgid "The following applicants are missing an email address: %s."
msgstr "Seuraavilta hakijoilta puuttuu sähköpostiosoite: %s."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/candidate_send_mail.py:0
msgid "The following candidates are missing an email address: %s."
msgstr "Seuraavilta ehdokkailta puuttuu sähköpostiosoite: %s."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Malli (Odoo Document Kind), jota tämä alias vastaa. Kaikki saapuvat "
"sähköpostiviestit, jotka eivät vastaa olemassa olevaan tietueeseen, "
"aiheuttavat uuden tietueen luomisen tähän malliin (esim. projektitehtävä)"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_recruitment_degree_name_uniq
msgid "The name of the Degree of Recruitment must be unique!"
msgstr "Tutkinnon nimen pitää olla yksilöllinen!"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Sähköpostin aliaksen nimi, esim. 'jobs', jos haluat saada sähköposteja "
"osoitteeseen <<EMAIL>>"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_platform__regex
msgid ""
"The regex facilitates to extract information from the subject or body of the"
" received email to autopopulate the Applicant's name field"
msgstr ""
"Regex helpottaa tietojen poimimista vastaanotetun sähköpostiviestin aiheesta"
" tai rungosta hakijan nimikentän automaattista täyttämistä varten"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_list_controller.js:0
msgid ""
"These job positions and all related applicants will be archived. Are you "
"sure?"
msgstr ""
"Nämä työpaikat ja kaikki niihin liittyvät hakijat arkistoidaan. Oletko "
"varma?"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__medium_id
msgid ""
"This displays how the applicant has reached out, e.g. via Email, LinkedIn, "
"Website, etc."
msgstr ""
"Tässä näytetään, miten hakija on ottanut yhteyttä, esimerkiksi "
"sähköpostitse, LinkedInissä, verkkosivustolla jne."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_normalized
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Tätä kenttää käytetään sähköpostiosoitteiden hakuun, koska ensisijainen "
"sähköpostiosoite voi sisältää muutakin kuin vain sähköpostiosoitteen."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Nimi, joka auttaa luokittelemaan erilaiset kampanjatoimet esim. "
"joulutervehdys, kesäkampanja"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Linkin lähde, esim. hakukone, toinen verkkotunnus tai sähköpostilistan nimi"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_form_controller.js:0
#: code:addons/hr_recruitment/static/src/views/recruitment_kanban_view.js:0
#: code:addons/hr_recruitment/static/src/views/recruitment_list_controller.js:0
msgid ""
"This job position and all related applicants will be archived. Are you sure?"
msgstr ""
"Tämä työpaikka ja kaikki siihen liittyvät hakijat arkistoidaan. Oletko "
"varma?"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Tämä vaihe on laskostettuna kanban-näkymässä, kun vaiheessa ei ole yhtään "
"näytettävää tietuetta."

#. module: hr_recruitment
#: model:digest.tip,name:hr_recruitment.digest_tip_hr_recruitment_0
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Tip: Let candidates apply by email"
msgstr "Vinkki: Anna ehdokkaiden hakea sähköpostitse"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "To Recruit"
msgstr "Rekrytointi"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Today Activities"
msgstr "Tämän päivän toimenpiteet"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Tooltips"
msgstr "Työkaluvihjeet"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_sources
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Trackers"
msgstr "Seurannat"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "Try creating an application by sending an email to"
msgstr "Yritä luoda hakemus lähettämällä sähköpostia osoitteeseen"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Try sending an email"
msgstr "Kokeile lähettää sähköpostia"

#. module: hr_recruitment
#: model_terms:web_tour.tour,rainbow_man_message:hr_recruitment.hr_recruitment_tour
msgid "Try the Website app to publish job offers online."
msgstr ""
"Kokeile verkkosivusovellusta, jolla voit julkaista työtarjouksia verkossa."

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.mail_activity_type_action_config_hr_applicant
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""
"Yritä lisätä joitakin tietueita tai varmista, että hakupalkissa ei ole "
"aktiivista suodatinta."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_decoration
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Poikkeusaktiviteetin tyyppi tietueella."

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM-kampanja"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_source
msgid "UTM Source"
msgstr "UTM-lähde"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm
msgid "UTMs"
msgstr "UTM:t"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Unarchive"
msgstr "Palauta arkistosta"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Unassigned"
msgstr "Vastuuttamaton"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Unread Messages"
msgstr "Lukemattomat viestit"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Use OCR to fill data from a picture of the Résumr or the file itself"
msgstr ""
"Käytä OCR:ää tietojen täyttämiseen Résumrin kuvasta tai itse tiedostosta"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Use emails and links trackers"
msgstr "Käytä sähköposteja ja linkkien seurantoja"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Use interview forms tailored to each job position during the recruitment "
"process. Select the form to use in the job position detail form. This relies"
" on the Survey app."
msgstr ""
"Käytä rekrytointiprosessin aikana kuhunkin työtehtävään räätälöityjä "
"haastattelulomakkeita. Valitse käytettävä lomake työtehtävien tarkentavassa "
"lomakkeessa. Tämä perustuu Survey-sovellukseen."

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_users
msgid "User"
msgstr "Käyttäjä"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_email
msgid "User Email"
msgstr "Käyttäjän sähköposti"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__2
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_candidate__priority__2
msgid "Very Good"
msgstr "Erittäin hyvä"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_candidate_view_search
msgid "Waiting"
msgstr "Odottaa"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Want to analyse where applications come from ?"
msgstr "Haluatko analysoida, mistä hakemukset tulevat?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__website_message_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_candidate__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__website_message_ids
#: model:ir.model.fields,help:hr_recruitment.field_hr_candidate__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
msgid "What do you want to recruit today? Choose a job title..."
msgstr "Mitä haluatte rekrytoida tänään? Valitse työnimike..."

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_refuse
msgid "When you refuse an application, you can choose this template"
msgstr "Kun hylkäät hakemuksen, voit valita tämän mallin"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Who can access candidates"
msgstr "Kuka voi käyttää ehdokkaita"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_platforms
msgid "Without a regex: The applicant's name will be the email's subject."
msgstr "Ilman regexiä: Hakijan nimi on sähköpostin aiheena."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.candidate_send_mail_view_form
msgid "Write your message here..."
msgstr "Kirjoita viestisi tähän..."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "You are not allowed to perform this action."
msgstr "Sinulla ei ole oikeutta suorittaa tätä toimintoa."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define here the labels that will be displayed for the kanban state instead\n"
"                            of the default labels."
msgstr ""
"Voit määrittää tässä kanban-tilalle näytettävät tunnisteet\n"
"                            oletustunnisteiden sijaan."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define the requirements here. They will be displayed when you hover "
"over the stage title."
msgstr ""
"Voit määritellä vaatimukset tässä. Ne näytetään, kun viet hiiren kursorin "
"otsikon päälle."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
msgid ""
"You can't select Send email option.\n"
"The email will not be sent to the following applicant(s) as they don't have an email address:"
msgstr ""
"Et voi valita Lähetä sähköpostia -vaihtoehtoa.\n"
"Sähköpostia ei lähetetä seuraavalle hakijalle (seuraaville hakijoille), koska heillä ei ole sähköpostiosoitetta:"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid ""
"You cannot create an applicant in a different company than the candidate"
msgstr "Hakijaa ei voi luoda eri yritykseen kuin ehdokasta"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following recruitment sources in Recruitment:\n"
"%(recruitment_sources)s"
msgstr ""
"Et voi poistaa näitä UTM-lähteitä, koska ne on linkitetty rekrytointilähteisiin Rekrytointi-kohdassa:\n"
"%(recruitment_sources)s"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You have been assigned as an interviewer for %s"
msgstr "Sinut on määrätty haastattelijaksi %s"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You have been assigned as an interviewer for the Applicant %s"
msgstr "Sinut on nimetty hakijan haastattelijaksi %s"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_applicant.py:0
msgid "You must define a Contact Name for this applicant."
msgstr "Hakijalle on määriteltävä yhteyshenkilön nimi."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_candidate.py:0
msgid "You must define a Contact Name for this candidate."
msgstr "Ehdokkaalle on määriteltävä yhteyshenkilön nimi."

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_congratulations
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_interest
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_refuse
msgid "Your Job Application: {{ object.job_id.name }}"
msgstr "Työhakemuksesi: {{ object.job_id.name }}"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.quick_create_applicant_form
msgid "e.g. John Doe"
msgstr "esim. Matti Meikäläinen"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "e.g. LinkedIn"
msgstr "esim. LinkedIn"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. Linkedin"
msgstr "esim. Linkedin"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. Masters"
msgstr "esim. Masters"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. Sales Manager"
msgstr "esim. Myyntipäällikkö"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. ^New application:.*from (.*)"
msgstr "esim. ^Uusi sovellus:.*from (.*)"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "e.g. domain.com"
msgstr "esim. domain.com"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. https://www.linkedin.com/in/..."
msgstr "esim. https://www.linkedin.com/in/..."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "e.g. jobs"
msgstr "esim. työpaikat"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_platform_form
msgid "e.g. <EMAIL>"
msgstr "esim. <EMAIL>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. <EMAIL>"
msgstr "esim. <EMAIL>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. mycompany.com"
msgstr "esim. mycompany.com"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. sales-manager"
msgstr "esim. myyntipäällikkö"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/views/recruitment_helper_view.xml:0
msgid "or"
msgstr "tai"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "other application(s)"
msgstr "muu(t) hakemus(t)"
