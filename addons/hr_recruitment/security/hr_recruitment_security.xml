<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record model="ir.module.category" id="base.module_category_human_resources_recruitment">
        <field name="description">Interviewer right will give access to all job position/applications where the employee is defined. It will allow to refuse, plan meetings.</field>
        <field name="sequence">11</field>
    </record>

    <record id="hr_applicant_comp_rule" model="ir.rule">
        <field name="name">Applicant multi company rule</field>
        <field name="model_id" ref="model_hr_applicant"/>
        <field eval="True" name="global"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="hr_candidate_comp_rule" model="ir.rule">
        <field name="name">Candidate multi company rule</field>
        <field name="model_id" ref="model_hr_candidate"/>
        <field eval="True" name="global"/>
        <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
    </record>

    <record id="group_hr_recruitment_interviewer" model="res.groups">
        <field name="name">Interviewer</field>
        <field name="category_id" ref="base.module_category_human_resources_recruitment"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_hr_recruitment_user" model="res.groups">
        <field name="name">Officer: Manage all applicants</field>
        <field name="category_id" ref="base.module_category_human_resources_recruitment"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_recruitment_interviewer'))]"/>
    </record>

    <record id="group_hr_recruitment_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="category_id" ref="base.module_category_human_resources_recruitment"/>
        <field name="implied_ids" eval="[(4, ref('group_hr_recruitment_user'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
    </record>

    <record id="group_applicant_cv_display" model="res.groups">
        <field name="name">Display CV on application form</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="base.group_user" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('hr_recruitment.group_applicant_cv_display'))]"/>
    </record>

    <!-- Interviewer Access Rules -->
    <record id="hr_applicant_interviewer_rule" model="ir.rule">
        <field name="name">Applicant Interviewer</field>
        <field name="model_id" ref="model_hr_applicant"/>
        <field name="domain_force">[
            '|',
                ('job_id.interviewer_ids', 'in', user.id),
                ('interviewer_ids', 'in', user.id),
        ]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_interviewer'))]"/>
    </record>

    <record id="hr_candidate_interviewer_rule" model="ir.rule">
        <field name="name">Candidate Interviewer</field>
        <field name="model_id" ref="model_hr_candidate"/>
        <field name="domain_force">[
            '|',
                ('applicant_ids.job_id.interviewer_ids', 'in', user.id),
                ('applicant_ids.interviewer_ids', 'in', user.id),
        ]</field>
        <field name="perm_create" eval="False"/>
        <field name="perm_unlink" eval="False"/>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_interviewer'))]"/>
    </record>

    <record id="hr_applicant_user_rule" model="ir.rule">
        <field name="name">User: All Applicants</field>
        <field name="model_id" ref="model_hr_applicant"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_user'))]"/>
    </record>

    <record id="hr_candidate_user_rule" model="ir.rule">
        <field name="name">User: All Candidates</field>
        <field name="model_id" ref="model_hr_candidate"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_user'))]"/>
    </record>

    <record id="hr_job_user_rule" model="ir.rule">
        <field name="name">User: All Applicants</field>
        <field name="model_id" ref="model_hr_job"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_user'))]"/>
    </record>

    <record id="mail_message_user_rule" model="ir.rule">
        <field name="name">User: All Chatter</field>
        <field name="model_id" ref="mail.model_mail_message"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_user'))]"/>
    </record>

    <record id="mail_plan_rule_group_hr_recruitment_manager_applicant" model="ir.rule">
        <field name="name">Manager can manage applicant plans</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
        <field name="model_id" ref="mail.model_mail_activity_plan"/>
        <field name="domain_force">[('res_model', '=', 'hr.applicant')]</field>
        <field name="perm_read" eval="False"/>
    </record>

    <record id="mail_plan_templates_rule_group_hr_recruitment_manager_applicant" model="ir.rule">
        <field name="name">Manager can manage applicant plan templates</field>
        <field name="groups" eval="[(4, ref('hr_recruitment.group_hr_recruitment_manager'))]"/>
        <field name="model_id" ref="mail.model_mail_activity_plan_template"/>
        <field name="domain_force">[('plan_id.res_model', '=', 'hr.applicant')]</field>
        <field name="perm_read" eval="False"/>
    </record>
</odoo>
