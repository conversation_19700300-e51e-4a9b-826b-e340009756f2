<?xml version="1.0"?>
<odoo>
    <record id="candidate_send_mail_view_form" model="ir.ui.view">
        <field name="model">candidate.send.mail</field>
        <field name="arch" type="xml">
            <form>
                <field name="author_id" invisible="1" />
                <field name="lang" invisible="1" />
                <field name="render_model" invisible="1" />
                <field name="template_id" invisible="1" />
                <group>
                    <field name="subject" required="1" />
                    <field name="candidate_ids" widget="many2many_tags"
                        context="{'show_partner_name': 1}" />
                </group>
                <field name="body" nolabel="1" class="oe-bordered-editor"
                    widget="html_mail"
                    placeholder="Write your message here..."
                    force_save="1" />
                <group>
                    <field name="attachment_ids" widget="many2many_binary" string="Attach a file"
                        nolabel="1" colspan="2" />
                    <field name="template_id" string="Load template" options="{'no_create': True}" />
                </group>
                <footer>
                    <button name="action_send" string="Send" type="object" class="btn-primary"
                        data-hotkey="q" />
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x" />
                </footer>
            </form>
        </field>
    </record>
</odoo>