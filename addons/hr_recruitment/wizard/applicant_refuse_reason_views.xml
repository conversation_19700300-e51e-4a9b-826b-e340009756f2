<?xml version="1.0"?>
<odoo>
        <record id="applicant_get_refuse_reason_view_form" model="ir.ui.view">
            <field name="name">applicant.get.refuse.reason.form</field>
            <field name="model">applicant.get.refuse.reason</field>
            <field name="arch" type="xml">
                <form string="Refuse Reason">
                    <group col="1">
                        <field name="refuse_reason_id" string="Reason" widget="selection_badge" options="{'horizontal': true, 'no_create': True, 'no_open': True}"/>
                        <group invisible="not refuse_reason_id">
                            <label for="send_mail" class="me-2" invisible="not refuse_reason_id"/>
                            <div class="d-flex">
                                <field name="send_mail" readonly="applicant_ids.length > 1 and applicant_without_email"/>
                                <span class="mx-2" style="padding-top: 1px; padding-bottom: 1px;">to</span>
                                <field name="applicant_emails" invisible="applicant_ids.length == 1"/>
                                <field
                                    name="single_applicant_email"
                                    placeholder="Provide an email"
                                    invisible="applicant_ids.length != 1"
                                    required="applicant_ids.length == 1 and send_mail"/>
                            </div>
                            <field name="template_id" invisible="not send_mail" required="send_mail"/>
                            <label for="duplicates" invisible="duplicates_count == 0"/>
                            <div class="d-flex" invisible="duplicates_count == 0">
                                <field name="duplicates" nolabel="1"/>
                                <span>Refuse the<field name="duplicates_count" class="oe_inline mx-1"/>other application(s)</span>
                            </div>
                        </group>
                    </group>
                    <div
                        class="alert alert-danger"
                        role="alert"
                        invisible="applicant_ids.length == 1 or not applicant_without_email or not refuse_reason_id">
                        <field name="applicant_without_email" class="mr4"/>
                    </div>
                    <footer>
                        <button name="action_refuse_reason_apply" string="Refuse" type="object" class="btn-primary" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="applicant_get_refuse_reason_action" model="ir.actions.act_window">
            <field name="name">Refuse Reason</field>
            <field name="res_model">applicant.get.refuse.reason</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="applicant_get_refuse_reason_view_form"/>
            <field name="target">new</field>
        </record>
</odoo>
