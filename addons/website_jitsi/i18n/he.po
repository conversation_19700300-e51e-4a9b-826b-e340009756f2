# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_jitsi
# 
# Translators:
# yael terner, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__12
msgid "12"
msgstr "12"

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__16
msgid "16"
msgstr "16"

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__20
msgid "20"
msgstr "20"

#. module: website_jitsi
#: model:ir.model,name:website_jitsi.model_chat_room
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__chat_room_id
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_form
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_search
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_tree
msgid "Chat Room"
msgstr "חדר צ'אט"

#. module: website_jitsi
#: model:ir.model,name:website_jitsi.model_chat_room_mixin
msgid "Chat Room Mixin"
msgstr ""

#. module: website_jitsi
#: model:ir.actions.act_window,name:website_jitsi.chat_room_action
#: model:ir.ui.menu,name:website_jitsi.chat_room_menu
msgid "Chat Rooms"
msgstr ""

#. module: website_jitsi
#. odoo-javascript
#: code:addons/website_jitsi/static/src/xml/chat_room_modal.xml:0
msgid "Close"
msgstr "סגור"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__is_full
msgid "Full"
msgstr "מלא"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__id
msgid "ID"
msgstr "מזהה"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__jitsi_server_domain
msgid "Jitsi Server Domain"
msgstr ""

#. module: website_jitsi
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_join_button
msgid "Join the room"
msgstr ""

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__lang_id
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_lang_id
msgid "Language"
msgstr "שפה"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__last_activity
msgid "Last Activity"
msgstr "פעילות אחרונה"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_last_activity
msgid "Last activity"
msgstr ""

#. module: website_jitsi
#. odoo-javascript
#: code:addons/website_jitsi/static/src/xml/chat_room_modal.xml:0
msgid "Loading your room..."
msgstr ""

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__max_capacity
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_max_capacity
msgid "Max capacity"
msgstr ""

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__max_participant_reached
msgid "Max participant reached"
msgstr ""

#. module: website_jitsi
#: model:ir.model.fields,help:website_jitsi.field_chat_room__max_participant_reached
#: model:ir.model.fields,help:website_jitsi.field_chat_room_mixin__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr ""

#. module: website_jitsi
#: model:ir.model.fields.selection,name:website_jitsi.selection__chat_room__max_capacity__no_limit
msgid "No limit"
msgstr "לא מוגבל"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__participant_count
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_participant_count
msgid "Participant count"
msgstr ""

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_max_participant_reached
msgid "Peak participants"
msgstr ""

#. module: website_jitsi
#: model_terms:ir.ui.view,arch_db:website_jitsi.chat_room_view_form
msgid "Reporting"
msgstr "דו\"חות"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_is_full
msgid "Room Is Full"
msgstr "החדר מלא"

#. module: website_jitsi
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room__name
#: model:ir.model.fields,field_description:website_jitsi.field_chat_room_mixin__room_name
msgid "Room Name"
msgstr "שם החדר"

#. module: website_jitsi
#: model:ir.model.fields,help:website_jitsi.field_chat_room__jitsi_server_domain
msgid ""
"The Jitsi server domain can be customized through the settings to use a "
"different server than the default \"meet.jit.si\""
msgstr ""
