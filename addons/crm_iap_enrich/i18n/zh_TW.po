# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr "<span>找不到基於電子郵件地址或電子郵件地址的公司資料是電子郵件供應商。未消耗任何點數。</span>"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done because the email address does not look "
"valid.</span>"
msgstr "<span>由於電子郵件地址看起來無效，因此無法進行擴充。</span>"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "允許手動豐富"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "An error occurred during lead enrichment"
msgstr "潛在客戶豐富過程中發生錯誤"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
msgid "CRM: enrich leads (IAP)"
msgstr "CRM：潛在商機資訊擴充 （IAP）"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "豐富"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr "利用公司資料擴充潛在客戶資訊"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr "利用公司資料擴充商機資訊"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr "點數用盡"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr "潛在客戶擴充資訊（基於電子郵件地址）"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "Lead enriched based on email address"
msgstr "基於電子郵件地址的潛在商機資訊擴充"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "潛在商機/商機"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "Not enough credits for Lead Enrichment"
msgstr "點數不足以豐富潛在客戶"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "The leads/opportunities have successfully been enriched"
msgstr "潛在客戶/銷售機會已成功豐富"

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr "是否針對此潛在商機執行了基於電子郵件的潛在商機資訊擴充 IAP 服務。"
