# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm_iap_enrich
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid ""
"<span> No company data found based on the email address or email address is "
"one of an email provider. No credit was consumed. </span>"
msgstr ""
"<span> Nenhum dado da empresa foi encontrado com base no endereço de e-mail "
"ou o endereço de e-mail é de um provedor de e-mail. Nenhum crédito foi "
"consumido. </span>"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
msgid ""
"<span>Enrichment could not be done because the email address does not look "
"valid.</span>"
msgstr ""
"<span>O enriquecimento não pôde ser feito porque o endereço de e-mail não "
"parece válido.</span>"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__show_enrich_button
msgid "Allow manual enrich"
msgstr "Permitir o enriquecimento manual"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "An error occurred during lead enrichment"
msgstr "Ocorreu um erro durante o enriquecimento de lead"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.ir_cron_lead_enrichment_ir_actions_server
msgid "CRM: enrich leads (IAP)"
msgstr "CRM: enriquecer leads (IAP)"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: crm_iap_enrich
#: model:ir.actions.server,name:crm_iap_enrich.action_enrich_mail
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich"
msgstr "Enriquecer"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich lead with company data"
msgstr "Enriquecer o lead com dados da empresa"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.crm_lead_view_form
msgid "Enrich opportunity with company data"
msgstr "Enriquecer a oportunidade com dados da empresa"

#. module: crm_iap_enrich
#: model:ir.model.fields,field_description:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid "Enrichment done"
msgstr "Enriquecimento realizado"

#. module: crm_iap_enrich
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_no_email
#: model_terms:ir.ui.view,arch_db:crm_iap_enrich.mail_message_lead_enrich_notfound
msgid "Lead Enrichment (based on email address)"
msgstr "Enriquecimento de leads (com base no endereço de e-mail)"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "Lead enriched based on email address"
msgstr "Lead enriquecido com base no endereço de e-mail"

#. module: crm_iap_enrich
#: model:ir.model,name:crm_iap_enrich.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/oportunidade"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "Not enough credits for Lead Enrichment"
msgstr "Não há créditos suficientes para o enriquecimento de lead"

#. module: crm_iap_enrich
#. odoo-python
#: code:addons/crm_iap_enrich/models/crm_lead.py:0
msgid "The leads/opportunities have successfully been enriched"
msgstr "Os leads/oportunidades foram enriquecidos com sucesso"

#. module: crm_iap_enrich
#: model:ir.model.fields,help:crm_iap_enrich.field_crm_lead__iap_enrich_done
msgid ""
"Whether IAP service for lead enrichment based on email has been performed on"
" this lead."
msgstr ""
"Se o serviço IAP para enriquecimento de leads com base em e-mail foi "
"executado nesse lead."
