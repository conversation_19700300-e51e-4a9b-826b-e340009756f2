# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_peppol
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:38+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Customer not on Peppol)"
msgstr "(Il cliente non è su Peppol)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Demo)"
msgstr " (Demo)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Test)"
msgstr " (Test)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (no VAT)"
msgstr " (no IVA)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid "%(peppol_label)s%(disable_reason)s%(peppol_proxy_mode)s"
msgstr "%(peppol_label)s%(disable_reason)s%(peppol_proxy_mode)s"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "%s has requested electronic invoices reception on Peppol."
msgstr "%s ha richiesto la ricezione di fatture elettroniche via Peppol."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"<br/>\n"
"                                In Belgium, electronic invoicing is <u>mandatory as of January 2026</u> - don't wait to register."
msgstr ""
"<br/>\n"
"                                In Belgio, la fatturazione elettronica sarà <u>obbligatoria da gennaio 2026</u> - Non aspettare per registrarti!"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid ""
"<span class=\"mx-1\" invisible=\"'demo_' not in peppol_message_uuid\"> (Demo)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        The invoice will be sent automatically via Peppol\n"
"                    </span>"
msgstr ""
"<span class=\"mx-1\" invisible=\"'demo_' not in peppol_message_uuid\"> (Demo)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        La fattura verrà inviata automaticamente tramite PEPPOL\n"
"                    </span>"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid ""
"A participant with these details has already been registered on the network."
" If you have previously registered to a Peppol service, please deregister."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "A purchase journal must be used to receive Peppol documents."
msgstr ""
"Per ricevere i documenti Peppol è necessario utilizzare un registro "
"d'acquisto."

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "Account EDI proxy user"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send
msgid "Account Move Send"
msgstr "Movimento contabile inviato"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send_batch_wizard
msgid "Account Move Send Batch Wizard"
msgstr "Procedura guidata invio movimenti contabili lotto"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send_wizard
msgid "Account Move Send Wizard"
msgstr "Procedura guidata invio movimento contabile"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__is_peppol_journal
msgid "Account used for Peppol"
msgstr "Conto utilizzato per Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.js:0
msgid "Activate"
msgstr "Attiva"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Activate Electronic Invoicing"
msgstr "Attiva fatturazione elettronica"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Activate Electronic Invoicing (via Peppol)"
msgstr "Attiva fatturazione elettronica (via Peppol)"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol"
msgstr "Attiva Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol (Demo)"
msgstr "Attiva Peppol (demo)"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol (Test)"
msgstr "Attiva Peppol (test)"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Allow incoming invoices"
msgstr "Consenti fatture in entrata"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Allow reception"
msgstr "Consenti ricezione"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Allow sending and receiving invoices through the PEPPOL network"
msgstr "Consente di inviare e ricevere fatture attraverso la rete PEPPOL"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__available_peppol_edi_formats
#: model:ir.model.fields,field_description:account_peppol.field_res_users__available_peppol_edi_formats
msgid "Available Peppol Edi Formats"
msgstr "Formati EDI Peppol disponibili"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__available_peppol_sending_methods
#: model:ir.model.fields,field_description:account_peppol.field_res_users__available_peppol_sending_methods
msgid "Available Peppol Sending Methods"
msgstr "Metodi di invio Peppol disponibili"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid ""
"By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""
"Facendo clic sul pulsante in basso accetto che Odoo potrebbe elaborare le "
"mie fatture elettroniche."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__receiver
msgid "Can send and receive"
msgstr "Può inviare e ricevere"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__sender
msgid "Can send but not receive"
msgstr "Può inviare ma non ricevere"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__smp_registration
msgid "Can send, pending registration to receive"
msgstr "Può inviare, registrazione in sospeso da ricevere"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_service_configuration
msgid "Cancel"
msgstr "Annulla"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid "Cancel PEPPOL"
msgstr "Annulla PEPPOL"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move.py:0
msgid "Cannot cancel an entry that has already been sent to PEPPOL"
msgstr "Impossibile annulare una registrazione già inviata con PEPPOL"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_valid_format
msgid "Cannot receive this format"
msgstr "Impossibile ricevere il formato"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Cannot register a user with a %s application"
msgstr "Impossibile registrare un utente con un'applicazione %s"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Check Partner(s)"
msgstr "Verifica partner"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""
"Codice utilizzato per identificare l'endpoint di BIS Billing 3.0 e i suoi derivati.\n"
"             Elenco disponibile in https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__company_id
msgid "Company"
msgstr "Azienda"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Configure Peppol Services"
msgstr "Configura servizi Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_service_configuration
msgid "Confirm"
msgstr "Conferma"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Connection error, please try again later."
msgstr "Errore di connessione, prova di nuovo più tardi."

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Contact details were updated."
msgstr "I dettagli di contatto sono stati aggiornati."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid "Contact email and mobile number are required."
msgstr "L'indirizzo e-mail e il numero di telefono sono richiesti."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Contact email and phone number are required."
msgstr "L'indirizzo e-mail e il numero di telefono sono richiesti."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Create, send and receive e-invoices for free."
msgstr "Crea, invia e ricevi fatture elettroniche gratis."

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__create_uid
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__create_uid
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__create_uid
msgid "Created by"
msgstr "Creata da"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__create_date
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__create_date
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__create_date
msgid "Created on"
msgstr "Creata il"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Customer is on Peppol but did not enable receiving documents."
msgstr "Il cliente è su Peppol ma non ha abilitato la ricezione di documenti."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__demo
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__demo
msgid "Demo"
msgstr "Demo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Discard"
msgstr "Annulla"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__display_name
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__display_name
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__document_identifier
msgid "Document Identifier"
msgstr "Identificatvo documento"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__document_name
msgid "Document Name"
msgstr "Nome documento"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__done
msgid "Done"
msgstr "Completata"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "E-invoices will soon be mandatory in many countries"
msgstr "Le fatture elettroniche saranno presto obbligatorie in molti Paesi"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_mode
msgid "EDI mode"
msgstr "Modalità EDI"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_mode
msgid "EDI operating mode"
msgstr "Modalità di funzionamento EDI"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__edi_user_id
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_user_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_user
msgid "EDI user"
msgstr "Utente EDI"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "EDI user should be of type Peppol"
msgstr "Gli utenti EDI dovrebbero essere di tipo Peppol"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "Edi Identification"
msgstr "Identificazione EDI"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_mode_constraint
msgid "Edi Mode Constraint"
msgstr "Limite modalità EDI"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Email"
msgstr "E-mail"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__enabled
msgid "Enabled"
msgstr "Abilitato"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__error
msgid "Error"
msgstr "Errore"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr ""
"Si sono verificati degli errori durante la creazione del documento EDI "
"(formato: %s):"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch Peppol invoice status"
msgstr "Recupera stato fattura Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch from Peppol"
msgstr "Recupera da Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Free on Odoo"
msgstr "Gratis in Odoo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Fully automated"
msgstr "Completamente automatizzato"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.js:0
msgid "Got it !"
msgstr "Ho capito!"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "I want to migrate my existing Peppol connection to Odoo (optional):"
msgstr ""
"Voglio migrare la mia connessione Peppol esistente in Odoo (opzionale):"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__id
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__id
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__id
msgid "ID"
msgstr "ID"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__smp_registration
msgid ""
"If not check, you will only be able to send invoices but not receive them."
msgstr "Se non contrassegnato, potrai solo inviare fatture e non riceverle."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
msgid ""
"If you want to be invoiced by Peppol, your configuration must be valid."
msgstr ""
"Se vuoi essere fatturato da Peppol, le impostazioni devono essere valide."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__in_verification
msgid "In verification"
msgstr "In verifica"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Incoming Invoices Journal"
msgstr "Registro fatture in entrata"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__invoice_sending_method
#: model:ir.model.fields,field_description:account_peppol.field_res_users__invoice_sending_method
msgid "Invoice sending"
msgstr "Invio fattura"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_account_peppol_service_wizard__service_json
msgid ""
"JSON representation of peppol services as retrieved from the peppol server."
msgstr "Rappresentazione JSON dei servizi Peppol recuperata dal server."

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_journal
msgid "Journal"
msgstr "Registro"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move
msgid "Journal Entry"
msgstr "Registrazione contabile"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__write_uid
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__write_uid
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__write_date
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__write_date
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__prod
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__prod
msgid "Live"
msgstr "Dal vivo"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_migration_key
msgid "Migration Key"
msgstr "Chiave di migrazione"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "Mobile number"
msgstr "Numero di cellulare"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_valid
msgid "Not on Peppol"
msgstr "Non su Peppol"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__not_registered
msgid "Not registered"
msgstr "Non registrato"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_verified
msgid "Not verified yet"
msgstr "Ancora non verificato"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "Odoo"
msgstr "Odoo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Odoo keeps you up to date with the new regulation."
msgstr "Odoo ti tiene aggiornato sulla nuova normativa."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_edi_proxy_client_user__proxy_type__peppol
msgid "PEPPOL"
msgstr "PEPPOL"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_purchase_journal_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_purchase_journal_id
msgid "PEPPOL Purchase Journal"
msgstr "PEPPOL Registro acquisti"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid ""
"PEPPOL allows for complete automation of sending and receiving e-invoices."
msgstr ""
"PEPPOL ti consente di automatizzare completamente l'invio e la ricezione di "
"fatture elettroniche."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid ""
"PEPPOL is the secure standard for e-invoices used in EU and across the "
"world."
msgstr ""
"PEPPOL è il nuovo standard sicuro per le fatture elettroniche utilizzato in "
"Europa e in tutto il mondo."

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_message_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_message_uuid
msgid "PEPPOL message ID"
msgstr "PEPPOL ID messaggio"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_proxy_state
msgid "PEPPOL status"
msgstr "PEPPOL stato"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_new_documents_ir_actions_server
msgid "PEPPOL: retrieve new documents"
msgstr "PEPPOL: recupera nuovi documenti"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_message_status_ir_actions_server
msgid "PEPPOL: update message status"
msgstr "PEPPOL: aggiorna stato messaggio"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_participant_status_ir_actions_server
msgid "PEPPOL: update participant status"
msgstr "PEPPOL: aggiorna stato partecipante"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid "Partner doesn't have a valid Peppol configuration."
msgstr "Il partner non dispone di una configurazione Peppol valida."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__processing
msgid "Pending Reception"
msgstr "Ricezione in sospeso"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_endpoint
#: model_terms:ir.ui.view,arch_db:account_peppol.portal_my_details_fields
msgid "Peppol Endpoint"
msgstr "Endpoint Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Peppol ID"
msgstr "ID Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol Ready"
msgstr "Peppol pronto"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_journal.py:0
msgid "Peppol Ready invoices"
msgstr "Fatture pronte Peppol"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_peppol_registration
msgid "Peppol Registration"
msgstr "Registrazione Peppol"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_peppol_service
msgid "Peppol Service"
msgstr "Servizio Peppol"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_peppol_service_wizard
msgid "Peppol Services Wizard"
msgstr "Procedura guidata servizi Peppol"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol document (UUID: %(uuid)s) has been received successfully"
msgstr "Il documento Peppol (UUID: %(uuid)s) è stato ricevuto correttamente"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.portal_my_details_fields
msgid "Peppol e-Address (EAS)"
msgstr "Indirizzo elettronico Peppol (EAS)"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_eas
msgid "Peppol e-address (EAS)"
msgstr "Indirizzo elettronico Peppol (EAS)"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__peppol_verification_state
#: model:ir.model.fields,field_description:account_peppol.field_res_users__peppol_verification_state
msgid "Peppol endpoint verification"
msgstr "Verifica endpoint Peppol"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol error: %s"
msgstr "Errore Peppol: %s"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Peppol ready invoices"
msgstr "Fatture pronte Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol status"
msgstr "Stato Peppol"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol status update: %s"
msgstr "Aggiornamento stato Peppol: %s"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_warnings
msgid "Peppol warnings"
msgstr "Avvisi Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Phone"
msgstr "Telefono"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid ""
"Please enter the mobile number in the correct international format.\n"
"For example: +***********, where +32 is the country code.\n"
"Currently, only European countries are supported."
msgstr ""
"Inserisci il numero di cellulare nel formato internazionale corretto.\n"
"Ad esempio: +***********, dove +39 è il codice Paese.\n"
"Attualmente sono supportati solo i Paesi europei."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Please fill in the EAS code and the Participant ID code."
msgstr "Inserisci il codice EAS e il codice ID partecipante."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "Please install the phonenumbers library."
msgstr "Installa la rubrica."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Please verify partner configuration in partner settings."
msgstr "Verifica la configurazione del partner nelle impostazioni."

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_contact_email
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Primary contact email"
msgstr "E-mail contatto principale"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__contact_email
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_contact_email
msgid "Primary contact email for Peppol-related communication"
msgstr "E-mail contatto principale per comunicazioni legate a Peppol"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr "Tipo di proxy"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__to_send
msgid "Queued"
msgstr "In coda"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__ready
msgid "Ready to send"
msgstr "Pronta all'invio"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__smp_registration
msgid "Register as a receiver"
msgstr "Registrati come ricevitore"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "Registered as a sender (demo)."
msgstr "Registrato come mittente (demo)."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "Registered to receive documents via Peppol (demo)."
msgstr "Registrato per ricevere documenti tramite Peppol (demo)."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid "Registered to receive documents via Peppol."
msgstr "Registrato per ricevere documenti tramite Peppol."

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__rejected
msgid "Rejected"
msgstr "Respinta"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Remove from Peppol"
msgstr "Rimuovi da Peppol"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__peppol_verification_code
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__verification_code
msgid "SMS verification code"
msgstr "Codice di verifica SMS"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Send again"
msgstr "Invia di nuovo"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Send electronic invoices, and receive bills automatically via Peppol"
msgstr ""
"Invia fatture elettroniche e ricevi fatture fornitore automaticamente "
"tramite Peppol"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_ids
msgid "Service"
msgstr "Servizio"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_info
msgid "Service Info"
msgstr "Info servizio"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_json
msgid "Service Json"
msgstr "Json servizio"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__skipped
msgid "Skipped"
msgstr "Saltata"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__test
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__test
msgid "Test"
msgstr "Prova"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid ""
"Test mode allows sending e-invoices through the test Peppol network.\n"
"                                    By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""
"La modalità di test consente l'invio di fatture elettroniche nella rete Peppol di prova.\n"
"                                    Facendo clic sul pulsante in basso accetto che Odoo potrebbe elaborare le mie fatture elettroniche."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
msgid "That country is not available for Peppol."
msgstr "Questo Paese non è disponibile per Peppol."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "The Peppol endpoint identification number is not correct."
msgstr "Il numero di identificazione dell'endpoint Peppol non è corretto."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "The Peppol service that is used is likely to be %s."
msgstr "Il servizio Peppol utilizzato è probabile che sia %s."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "The document has been sent to the Peppol Access Point for processing"
msgstr ""
"Il documento è stato inviato al punto di accesso Peppol per l'elaborazione"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "The e-invoicing network"
msgstr "La rete della fatturazione elettronica"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"The endpoint number might not be correct. Please check if you entered the "
"right identification number."
msgstr ""
"Il numero dell'endpoint potrebbe non essere corretto. Controlla di aver "
"inserito il numero di identificazione giusto."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/service_wizard.py:0
msgid ""
"The following services are listed on your participant but cannot be "
"configured here. If you wish to configure them differently, please contact "
"support."
msgstr ""
"I seguenti servizi sono elencati nel partecipante, ma non possono essere "
"configurati qui. Se si desidera configurarli diversamente, contattare "
"l'assistenza."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "The partner is missing Peppol EAS and/or Endpoint identifier."
msgstr "Codice EAS Peppol e/o endpoint mancante per il partner."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid ""
"The peppol status of the documents has been reset when switching from Demo "
"to Live."
msgstr ""
"Lo stato Peppol dei documenti è stato resettato al momento del passaggio da "
"demo a live."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"The recommended identification method for Belgium is your Company Registry "
"Number."
msgstr ""
"Il metodo di identificazione consigliato per il Belgio è il numero di "
"registro dell'azienda."

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr "L'ID univoco che identifica questo utente, tipicamente l'iva."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "The verification code is not correct"
msgstr "Il codice di verifica non è corretto"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid ""
"This feature is deprecated. Contact Odoo support if you need a migration "
"key."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "This invoice has also been"
msgstr "Anche questa fattura è stata"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "This verification code has expired. Please request a new one."
msgstr "Il codice di verifica è scaduto, richiedine uno nuovo."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"To generate complete electronic invoices, also set a country for this "
"partner."
msgstr ""
"Per generare fatture elettroniche complete, imposta anche un Paese per il "
"partner."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Too many attempts to request an SMS code. Please try again later."
msgstr ""
"Troppi tentativi di richiesta di un codice SMS. Prova di nuovo più tardi."

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""
"Identificatore univoco utilizzato da BIS Billing 3.0 e i suoi derivati, "
"conosciuto anche come \"ID endpoint\"."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Update"
msgstr "Aggiorna"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__edi_mode_constraint
msgid ""
"Using the config params, this field specifies which edi modes may be "
"selected from the UI"
msgstr ""
"Utilizza i parametri di configurazione, questo campo specifica quali "
"modalità EDI possono essere selezionate dall'IU"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__valid
msgid "Valid"
msgstr "Valido"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify"
msgstr "Verifica"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.partner_action_verify_peppol
msgid "Verify Peppol"
msgstr "Verifica Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify partner's PEPPOL endpoint"
msgstr "Verifica l'endpoint PEPPOL del partner"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "View Partner(s)"
msgstr "Mostra partner"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid ""
"We could not find a user with this information on our server. Please check "
"your information."
msgstr ""
"Impossibile trovare un utente con le informazioni fornite sul nostro server."
" Verifica le informazioni."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "What is Peppol and why it's great ?"
msgstr "Cos è Peppol e perché è forte?"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Why should I use PEPPOL ?"
msgstr "Perché dovrei utilizzare PEPPOL?"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Why should you use it ?"
msgstr "Perché dovresti utilizzarlo?"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__wizard_id
msgid "Wizard"
msgstr "Procedura guidata"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "You can now receive demo vendor bills."
msgstr "Ora puoi ricevere fatture fornitore demo."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "You can now send and receive electronic invoices via Peppol"
msgstr "Ora puoi inviare e ricevere fatture elettroniche tramite Peppol"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "You can now send electronic invoices via Peppol."
msgstr "Ora puoi inviare fatture elettroniche tramite Peppol."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "You can now send invoices in demo mode."
msgstr "Ora puoi inviare fatture in modalità demo."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "You can send this invoice electronically via Peppol."
msgstr "Puoi inviare la fattura elettronicamente tramite Peppol."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"You registration has been rejected, the reason has been sent to you via email.\n"
"                            Please contact our support if you need further assistance."
msgstr ""
"La tua registrazione è stata rifiutata, le ragioni sono state inviate tramite e-mail.\n"
"                            Contatta il supporto se hai bisogno di ulteriore assistenza."

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid ""
"You will not be able to send or receive Peppol documents in Odoo anymore. "
"Are you sure you want to proceed?"
msgstr ""
"Non sarai più in grado di inviare o ricevere documenti Peppol in Odoo. Sei "
"sicuro di voler proseguire?"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__phone_number
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "You will receive a verification code to this mobile number"
msgstr "Riceverai un codice di verifica a questo numero di cellulare"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your Peppol ID"
msgstr "Il tuo ID Peppol"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"Your Peppol registration will be activated soon. You can already send "
"invoices."
msgstr ""
"La tua registrazione Peppol verrà attivata a breve. Puoi già inviare "
"fatture."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"Your company is already registered on another Access Point for receiving "
"invoices.We will register you as a sender only."
msgstr ""
"L'azienda è già registrata su un altro Access Point per ricevere fatture. Ti"
" registreremo solo come mittente."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Your endpoint"
msgstr "Il tuo endpoint"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your migration key is:"
msgstr "La tua chiave di migrazione è:"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid ""
"Your registration on Peppol network should be activated within a day. The "
"updated status will be visible in Settings."
msgstr ""
"La registrazione nella rete Peppol può essere effettuata in un giorno. Gli "
"aggiornamenti di stato saranno visibili nelle impostazioni."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your registration should be activated within a day."
msgstr "La registrazione sarà attivata in un giorno."

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__invoice_sending_method__peppol
msgid "by Peppol"
msgstr "via Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "could not be sent via Peppol"
msgstr "non è stato possibile inviarla tramite Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "invoices and credit notes."
msgstr "fatture e note di credito."

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "sent via Peppol"
msgstr "inviata tramite Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "to send invoices, but this one"
msgstr "per inviare fatture, ma questa"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "uses"
msgstr "usa"
