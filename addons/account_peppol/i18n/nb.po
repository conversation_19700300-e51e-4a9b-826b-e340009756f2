# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_peppol
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>e Restad, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Customer not on Peppol)"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Demo)"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (Test)"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid " (no VAT)"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid "%(peppol_label)s%(disable_reason)s%(peppol_proxy_mode)s"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "%s has requested electronic invoices reception on Peppol."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"<br/>\n"
"                            If you need a Peppol compliant software, we recommend"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid ""
"<span class=\"mx-1\" invisible=\"'demo_' not in peppol_message_uuid\"> (Demo)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        The invoice will be sent automatically via Peppol\n"
"                    </span>"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid ""
"A participant with these details has already been registered on the network."
" If you have previously registered to an alternative Peppol service, please "
"deregister from that service, or request a migration key before trying "
"again. "
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "A purchase journal must be used to receive Peppol documents."
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send
msgid "Account Move Send"
msgstr "Kontooverføring Send"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send_batch_wizard
msgid "Account Move Send Batch Wizard"
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send_wizard
msgid "Account Move Send Wizard"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__is_peppol_journal
msgid "Account used for Peppol"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.js:0
msgid "Activate"
msgstr "Aktiver"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Activate Electronic Invoicing"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Activate Electronic Invoicing (via Peppol)"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol (Demo)"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Activate Peppol (Test)"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Allow incoming invoices"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Allow reception"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Allow sending and receiving invoices through the PEPPOL network"
msgstr "Tillat sending og mottak av fakturaer gjennom PEPPOL-nettverket."

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__available_peppol_edi_formats
#: model:ir.model.fields,field_description:account_peppol.field_res_users__available_peppol_edi_formats
msgid "Available Peppol Edi Formats"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__available_peppol_sending_methods
#: model:ir.model.fields,field_description:account_peppol.field_res_users__available_peppol_sending_methods
msgid "Available Peppol Sending Methods"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid ""
"By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__receiver
msgid "Can send and receive"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__sender
msgid "Can send but not receive"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__smp_registration
msgid "Can send, pending registration to receive"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_service_configuration
msgid "Cancel"
msgstr "Avbryt"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid "Cancel PEPPOL"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move.py:0
msgid "Cannot cancel an entry that has already been sent to PEPPOL"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_valid_format
msgid "Cannot receive this format"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Cannot register a user with a %s application"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Check Partner(s)"
msgstr "Sjekk partner(e)"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__company_id
msgid "Company"
msgstr "Firma"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Configure Peppol Services"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_service_configuration
msgid "Confirm"
msgstr "Bekreft"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Connection error, please try again later."
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Contact details were updated."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid "Contact email and mobile number are required."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "Contact email and phone number are required."
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Create, send and receive e-invoices for free."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__create_uid
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__create_uid
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__create_date
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__create_date
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__create_date
msgid "Created on"
msgstr "Opprettet den"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Customer is on Peppol but did not enable receiving documents."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__demo
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__demo
msgid "Demo"
msgstr "Demo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Discard"
msgstr "Avbryt"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__display_name
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__display_name
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__document_identifier
msgid "Document Identifier"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__document_name
msgid "Document Name"
msgstr "Dokumentnavn"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__done
msgid "Done"
msgstr "Fullført"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "E-invoices will soon be mandatory in many countries"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_mode
msgid "EDI mode"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_mode
msgid "EDI operating mode"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__edi_user_id
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_user_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_user
msgid "EDI user"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "EDI user should be of type Peppol"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "Edi Identification"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__edi_mode_constraint
msgid "Edi Mode Constraint"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Email"
msgstr "E-post"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__enabled
msgid "Enabled"
msgstr "Aktivert"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__error
msgid "Error"
msgstr "Feil"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch Peppol invoice status"
msgstr "Fetch Peppol fakturastatus"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch from Peppol"
msgstr "Fetch fra Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Free on Odoo"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Fully automated"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.js:0
msgid "Got it !"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "I want to migrate my existing Peppol connection to Odoo (optional):"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__id
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__id
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__id
msgid "ID"
msgstr "ID"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__smp_registration
msgid ""
"If not check, you will only be able to send invoices but not receive them."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
msgid ""
"If you want to be invoiced by Peppol, your configuration must be valid."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"In Belgium, electronic invoicing will be <u>mandatory as of January "
"2026</u>."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__in_verification
msgid "In verification"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Incoming Invoices Journal"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__invoice_sending_method
#: model:ir.model.fields,field_description:account_peppol.field_res_users__invoice_sending_method
msgid "Invoice sending"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_account_peppol_service_wizard__service_json
msgid ""
"JSON representation of peppol services as retrieved from the peppol server."
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move
msgid "Journal Entry"
msgstr "Bilag"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__write_uid
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__write_uid
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__write_date
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__write_date
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__prod
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__prod
msgid "Live"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_migration_key
msgid "Migration Key"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "Mobile number"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_valid
msgid "Not on Peppol"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__not_registered
msgid "Not registered"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__not_verified
msgid "Not verified yet"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "Odoo"
msgstr "Odoo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "Odoo keeps you up to date with the new regulation."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_edi_proxy_client_user__proxy_type__peppol
msgid "PEPPOL"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_purchase_journal_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_purchase_journal_id
msgid "PEPPOL Purchase Journal"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid ""
"PEPPOL allows for complete automation of sending and receiving e-invoices."
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid ""
"PEPPOL is the secure standard for e-invoices used in EU and across the "
"world."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_message_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_message_uuid
msgid "PEPPOL message ID"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_proxy_state
msgid "PEPPOL status"
msgstr ""

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_new_documents_ir_actions_server
msgid "PEPPOL: retrieve new documents"
msgstr ""

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_message_status_ir_actions_server
msgid "PEPPOL: update message status"
msgstr ""

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_participant_status_ir_actions_server
msgid "PEPPOL: update participant status"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"PS: <b style=\"color: $o-enterprise-action-color;\">We did not send your "
"invoice on Peppol.</b>"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"PS: This invoice has also been <b style=\"color: $o-enterprise-action-"
"color\">sent on Peppol</b>."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send_wizard.py:0
msgid "Partner doesn't have a valid Peppol configuration."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__processing
msgid "Pending Reception"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_endpoint
#: model_terms:ir.ui.view,arch_db:account_peppol.portal_my_details_fields
msgid "Peppol Endpoint"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol Ready"
msgstr "Peppol klar"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_journal.py:0
msgid "Peppol Ready invoices"
msgstr "Peppol klare faktura"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_peppol_registration
msgid "Peppol Registration"
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_peppol_service
msgid "Peppol Service"
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_peppol_service_wizard
msgid "Peppol Services Wizard"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol document (UUID: %(uuid)s) has been received successfully"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.portal_my_details_fields
msgid "Peppol e-Address (EAS)"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_eas
msgid "Peppol e-address (EAS)"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__peppol_verification_state
#: model:ir.model.fields,field_description:account_peppol.field_res_users__peppol_verification_state
msgid "Peppol endpoint verification"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol error: %s"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Peppol ready invoices"
msgstr "Peppol klare faktura"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol status"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol status update: %s"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__peppol_warnings
msgid "Peppol warnings"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Phone"
msgstr "Telefon"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid ""
"Please enter the mobile number in the correct international format.\n"
"For example: +***********, where +32 is the country code.\n"
"Currently, only European countries are supported."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Please fill in the EAS code and the Participant ID code."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "Please install the phonenumbers library."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Please verify partner configuration in partner settings."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_contact_email
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Primary contact email"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__contact_email
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_contact_email
msgid "Primary contact email for Peppol-related communication"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__to_send
msgid "Queued"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__ready
msgid "Ready to send"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__smp_registration
msgid "Register as a receiver"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "Registered as a sender (demo)."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "Registered to receive documents via Peppol (demo)."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid "Registered to receive documents via Peppol."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__rejected
msgid "Rejected"
msgstr "vvist"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid "Remove from Peppol"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__peppol_verification_code
#: model:ir.model.fields,field_description:account_peppol.field_peppol_registration__verification_code
msgid "SMS verification code"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Send again"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Send electronic invoices, and receive bills automatically via Peppol"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_ids
msgid "Service"
msgstr "Tjeneste"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_info
msgid "Service Info"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service_wizard__service_json
msgid "Service Json"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__skipped
msgid "Skipped"
msgstr "Hoppet over"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode__test
#: model:ir.model.fields.selection,name:account_peppol.selection__peppol_registration__edi_mode_constraint__test
msgid "Test"
msgstr "Test"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid ""
"Test mode allows sending e-invoices through the test Peppol network.\n"
"                                    By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
msgid "That country is not available for Peppol."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
msgid "The Peppol endpoint identification number is not correct."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "The Peppol service that is used is likely to be %s."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "The document has been sent to the Peppol Access Point for processing"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "The e-invoicing network"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"The endpoint number might not be correct. Please check if you entered the "
"right identification number."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/service_wizard.py:0
msgid ""
"The following services are listed on your participant but cannot be "
"configured here. If you wish to configure them differently, please contact "
"support."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "The partner is missing Peppol EAS and/or Endpoint identifier."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid ""
"The peppol status of the documents has been reset when switching from Demo "
"to Live."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"The recommended identification method for Belgium is your Company Registry "
"Number."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "The verification code is not correct"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid ""
"This feature is deprecated. Contact odoo support if you need a migration "
"key."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "This verification code has expired. Please request a new one."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"To generate complete electronic invoices, also set a country for this "
"partner."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid "Too many attempts to request an SMS code. Please try again later."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
msgid "Update"
msgstr "Oppdater"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__edi_mode_constraint
msgid ""
"Using the config params, this field specifies which edi modes may be "
"selected from the UI"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__peppol_verification_state__valid
msgid "Valid"
msgstr "Gyldig"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify"
msgstr "Valider"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.partner_action_verify_peppol
msgid "Verify Peppol"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify partner's PEPPOL endpoint"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "View Partner(s)"
msgstr "Vis partner(e)"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
msgid ""
"We could not find a user with this information on our server. Please check "
"your information."
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/peppol_info/peppol_info.xml:0
msgid "What is Peppol and why it's great ?"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Why should I use PEPPOL ?"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "Why should you use it ?"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_peppol_service__wizard_id
msgid "Wizard"
msgstr "Veiviser"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "You can now receive demo vendor bills."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "You can now send and receive electronic invoices via Peppol"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid "You can now send electronic invoices via Peppol."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
msgid "You can now send invoices in demo mode."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move_send.py:0
msgid "You can send this invoice electronically via Peppol."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"You registration has been rejected, the reason has been sent to you via email.\n"
"                            Please contact our support if you need further assistance."
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
msgid ""
"You will not be able to send or receive Peppol documents in Odoo anymore. "
"Are you sure you want to proceed?"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_peppol_registration__phone_number
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "You will receive a verification code to this mobile number"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your Peppol ID"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"Your Peppol registration will be activated soon. You can already send "
"invoices."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/peppol_registration.py:0
msgid ""
"Your company is already registered on another Access Point for receiving "
"invoices.We will register you as a sender only."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.peppol_registration_form
msgid "Your endpoint"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your migration key is:"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
msgid ""
"Your registration on Peppol network should be activated within a day. The "
"updated status will be visible in Settings."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your registration should be activated within a day."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/controllers/portal.py:0
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__invoice_sending_method__peppol
msgid "by Peppol"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "invoices and credit notes."
msgstr ""
