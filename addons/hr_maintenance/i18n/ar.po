# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_maintenance
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "تاريخ الإسناد"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__department_id
msgid "Assigned Department"
msgstr "القسم المسند "

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__employee_id
msgid "Assigned Employee"
msgstr "الموظف المسند "

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_count
msgid "Assigned Equipment"
msgstr "المعدات المسندة "

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "Created By"
msgstr "أنشئ بواسطة"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "أنشئ بواسطة المستخدم"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__department
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Department"
msgstr "القسم"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "معالج المغادرة"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_employee
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__employee_id
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__employee
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Employee"
msgstr "الموظف"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_ids
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__equipment_id
#: model_terms:ir.ui.view,arch_db:hr_maintenance.hr_departure_wizard_view_form
msgid "Equipment"
msgstr "المعدات "

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_count
msgid "Equipment Count"
msgstr "عدد المعدات "

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_departure_wizard__unassign_equipment
msgid "Free Equiments"
msgstr "المعدات المتاحة "

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "معدات الصيانة"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_request
msgid "Maintenance Request"
msgstr "طلب الصيانة"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_ids
msgid "Managed Equipment"
msgstr "المعدات التي تمت إدارتها "

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "My Maintenances"
msgstr "عمليات الصيانة الخاصة بي "

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__other
msgid "Other"
msgstr "غير ذلك"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__owner_user_id
msgid "Owner"
msgstr "المالك"

#. module: hr_maintenance
#: model:ir.model.fields,help:hr_maintenance.field_hr_departure_wizard__unassign_equipment
msgid "Unassign Employee from Equipments"
msgstr "إلغاء تعيين الموظف من المعدات "

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_kanban_inherit_hr
msgid "Unassigned"
msgstr "غير مسند "

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__equipment_assign_to
msgid "Used By"
msgstr "تم استخدامه بواسطة "

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_res_users
msgid "User"
msgstr "المستخدم"
