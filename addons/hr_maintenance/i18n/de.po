# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_maintenance
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "Zugewiesenes Datum"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__department_id
msgid "Assigned Department"
msgstr "Zugewiesene Abteilung"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__employee_id
msgid "Assigned Employee"
msgstr "Zugewiesener Mitarbeiter"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_count
msgid "Assigned Equipment"
msgstr "Zugewiesende Geräte"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "Created By"
msgstr "Erstellt von"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "Erstellt vom Benutzer"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__department
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Department"
msgstr "Abteilung"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Austrittassistent"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_employee
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__employee_id
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__employee
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Employee"
msgstr "Mitarbeiter"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_ids
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__equipment_id
#: model_terms:ir.ui.view,arch_db:hr_maintenance.hr_departure_wizard_view_form
msgid "Equipment"
msgstr "Ausrüstung"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_count
msgid "Equipment Count"
msgstr "Anzahl Geräte"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_departure_wizard__unassign_equipment
msgid "Free Equiments"
msgstr "Kostenlose Ausrüstung"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "Wartungsausrüstung"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_request
msgid "Maintenance Request"
msgstr "Wartungsanfrage"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_ids
msgid "Managed Equipment"
msgstr "Verwaltete Geräte"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "My Maintenances"
msgstr "Meine Wartungsarbeiten"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__other
msgid "Other"
msgstr "Andere"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__owner_user_id
msgid "Owner"
msgstr "Besitzer"

#. module: hr_maintenance
#: model:ir.model.fields,help:hr_maintenance.field_hr_departure_wizard__unassign_equipment
msgid "Unassign Employee from Equipments"
msgstr "Zuweisung von Mitarbeitern zu Ausrüstungen entfernen"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_kanban_inherit_hr
msgid "Unassigned"
msgstr "Nicht zugewiesen"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__equipment_assign_to
msgid "Used By"
msgstr "Verwendet von"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_res_users
msgid "User"
msgstr "Benutzer"
