# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_maintenance
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "할당일"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__department_id
msgid "Assigned Department"
msgstr "지정된 부서"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__employee_id
msgid "Assigned Employee"
msgstr "지정된 직원"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_count
msgid "Assigned Equipment"
msgstr "지정된 장비"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "Created By"
msgstr "작성자"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "사용자가 작성함"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__department
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Department"
msgstr "부서"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "퇴사 마법사"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_hr_employee
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__employee_id
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__employee
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_form_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_search_inherit_hr
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_tree_inherit_hr
msgid "Employee"
msgstr "임직원"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_ids
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_request__equipment_id
#: model_terms:ir.ui.view,arch_db:hr_maintenance.hr_departure_wizard_view_form
msgid "Equipment"
msgstr "장비"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_employee__equipment_count
msgid "Equipment Count"
msgstr "장비 수"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_hr_departure_wizard__unassign_equipment
msgid "Free Equiments"
msgstr "무료 장비"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "유지보수 장비"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_maintenance_request
msgid "Maintenance Request"
msgstr "유지보수 요청"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_res_users__equipment_ids
msgid "Managed Equipment"
msgstr "관리 장비"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_request_view_search_inherit_hr
msgid "My Maintenances"
msgstr "내 유지보수"

#. module: hr_maintenance
#: model:ir.model.fields.selection,name:hr_maintenance.selection__maintenance_equipment__equipment_assign_to__other
msgid "Other"
msgstr "기타"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__owner_user_id
msgid "Owner"
msgstr "소유자"

#. module: hr_maintenance
#: model:ir.model.fields,help:hr_maintenance.field_hr_departure_wizard__unassign_equipment
msgid "Unassign Employee from Equipments"
msgstr "장비에서 직원 할당 해제하기"

#. module: hr_maintenance
#: model_terms:ir.ui.view,arch_db:hr_maintenance.maintenance_equipment_view_kanban_inherit_hr
msgid "Unassigned"
msgstr "미지정"

#. module: hr_maintenance
#: model:ir.model.fields,field_description:hr_maintenance.field_maintenance_equipment__equipment_assign_to
msgid "Used By"
msgstr "사용자"

#. module: hr_maintenance
#: model:ir.model,name:hr_maintenance.model_res_users
msgid "User"
msgstr "사용자"
